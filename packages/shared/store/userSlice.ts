import { createAsyncThunk, createSlice, Slice } from "@reduxjs/toolkit";
import {
  createNotificationToken,
  deleteNotificationsToken,
  fetchAppUserDetails,
  fetchUserDetails,
  getUser,
  logout,
} from "../api/user";
import { Tenant, TypedAsyncThunk, UserSlice } from "./store.types";

import storage from "../platform/storage";
import { getTokens } from "../utils/httpUtils";

const getUserFromStorage = () => {
  const userData = getUser();
  return {
    user: userData?.user || null,
    msp: userData?.msp || null,
    tenant: userData?.tenant || ({} as Tenant),
    // Set loading to false if we have user data, true if we don't
    loading: !userData?.user,
  };
};

const storageData = getUserFromStorage();
const initialState: UserSlice = {
  user: storageData.user,
  msp: storageData.msp,
  appUser: null,
  tenant: storageData.tenant,
  loading: false,
  userType: "portal",
};

export const getUserData: TypedAsyncThunk<typeof fetchUserDetails> =
  createAsyncThunk("user/fetchUser", async () => {
    const token = getTokens()?.accessToken;

    if (!token) {
      throw new Error("User not logged in");
    }
    const data = await fetchUserDetails();

    return data;
  });

export const getAppUserData = createAsyncThunk(
  "user/fetchAppUser",
  async (record?: { token: string; deviceOS: string }) => {
    const data = await fetchAppUserDetails();
    if (record?.token) {
      createNotificationToken({
        email: data.user.email,
        token: record.token,
        contactNumber: data.user.contactNumber,
        deviceOS: record.deviceOS,
      })
        .then((res) => {
          console.log(res.data);
        })
        .catch((err) => {
          console.log(err);
        });
    }

    return data;
  }
);

export const logoutUser = createAsyncThunk(
  "user/logoutUser",
  async (token?: string | null) => {
    const data = await logout();
    if (token) {
      deleteNotificationsToken(token)
        .then((res) => {
          console.log(res);
        })
        .catch((err) => {
          console.log(err);
        });
    }

    return data;
  }
);

export const userSlice: Slice<UserSlice> = createSlice({
  name: "user",
  initialState,
  reducers: {
    addTenantDetails: (state, action) => {
      state.tenant = action.payload;
    },
    stopLoadingUserDetails: (state) => {
      state.loading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getUserData.pending, (state) => {
        // no required at first since initial state it loading true
        state.loading = true;
      })
      .addCase(getUserData.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.tenant = action.payload.tenant;
        state.msp = action.payload.msp;
      })
      .addCase(getUserData.rejected, (state) => {
        storage.clear();
        state.loading = false;
      });

    builder.addCase(logoutUser.fulfilled, (state, action) => {
      state.loading = initialState.loading;
      state.user = initialState.user;
      state.tenant = initialState.tenant;
      state.appUser = initialState.appUser;
    });

    builder.addCase(getAppUserData.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(getAppUserData.fulfilled, (state, action) => {
      state.appUser = action.payload.user;
      state.loading = false;
      state.userType = "app";
      state.tenant = action.payload.tenant;
    });
    builder.addCase(getAppUserData.rejected, (state) => {
      state.appUser = initialState.appUser;
      state.loading = false;
    });
  },
});

export default userSlice.reducer;
export const userActions = userSlice.actions;
