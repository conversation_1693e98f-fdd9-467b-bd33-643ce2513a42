import { AsyncThunk } from "@reduxjs/toolkit";

type FirstParameter<T> = T extends (arg1: infer U, ...args: any[]) => any
  ? U
  : never;

type PromiseResolvedType<T> = T extends Promise<infer U> ? U : never;

export type TypedAsyncThunk<T> = T extends (arg: infer U) => any
  ? AsyncThunk<PromiseResolvedType<ReturnType<T>>, FirstParameter<T>, {}>
  : never;

export interface User {
  role: string;
  email: string;
  phone: string;
  name: string;
  pincode: string;
  address: string;
  tenant: string;
  avatar: string;
  userGroup: string;
  accountType: string;
  dashboardViewId: number | null;
  siteViewId: number | null;
  createdAt: string;
  updatedAt: string;
  homePageView: {
    viewType: "default" | "dashboard" | "site";
    id: number;
  };
  contactId:string;
}

export interface AppUser {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  email: string;
  tenant: string;
  address: string;
  pincode: string;
  contactNumber: string;
  identityID: string | null;
}

export interface Tenant {
  pincode: string;
  setupStatus: string;
  mcCount: number;
  website: string;
  access: number;
  address: string;
  notificationCount: number;
  smsCount: number;
  companyName: string;
  accountType: string;
  callCount: number;
  subscriptionType: string;
  phone: string;
  name: string;
  email: string;
  createdAt: number;
  updatedAt: number;
  avatar: string;
  featureType: "geo" | "classic";
  subFeature?:
    | "basicAssetTracking"
    | "studentSafety"
    | "basic"
    | "fleetManagement";
  homePageView: {
    viewType: "default" | "dashboard" | "site";
    id: number;
  };
}

type MSPData = {
  mspId: string;
  mspFullName: string;
  tenants: string[];
  canCreateTenants: boolean;
  email: string;
  phone: string;
  website: string;
  address: string;
  pincode: string;
  createdAt: string; // or Date if you plan to parse it
  updatedAt: string; // or Date if you plan to parse it
};
export interface UsageChartData {
  series: Series;
  labels: string[];
  total: number;
  loading: boolean;
}

export interface UserUsage {
  data: Daum[];
  loading: boolean;
}

export interface Daum {
  event: string;
  count: number;
}

export interface Series {
  publish_resp: { values: number[] };
  delivered_resp: { values: number[] };
  dropped_resp: { values: number[] };
  sent_resp: { values: number[] };
  bytes_received_resp: { values: number[] };
  bytes_sent_resp: { values: number[] };
  rules_success_resp: { values: number[] };
  rules_failed_resp: { values: number[] };
  rules_total_resp: { values: number[] };
  rules_exec_success_resp: { values: number[] };
  rules_exec_failed_resp: { values: number[] };
  rules_exec_total_resp: { values: number[] };
}

export type UserSlice = {
  user: User | null;
  userType: "portal" | "app";
  tenant: Tenant | null;
  appUser: AppUser | null;
  loading: boolean;
  msp: MSPData | null;
};

// Dashboard
export type ChartType =
  | "Bar"
  | "Area"
  | "Line"
  | "Pie"
  | "Donut"
  | "Table"
  | "Bar-H"
  | "Polar"
  | "Radar"
  | "Count"
  | "Logs"
  | "Meter"
  | "Map";

export type ChartSize = "1X" | "2X" | "4X";

export type GraphColor = "pink" | "blue" | "green" | "purple" | "amber";
export type Operation = "avg" | "min" | "max" | "latest";
export type ColorScheme = "default" | "retro" | "spring" | "bold";
export type LooseAutoComplete<T extends string> = T | Omit<string, T>;

export type DashboardGraphItem = {
  id: string;
  chartType: ChartType;
  dataIndex: string;
  dataKey: string;
  size: ChartSize;
  label: string;
  aggregation: {
    sum: string;
    average: string;
  };
  color: GraphColor;
  dataType: "string" | "number" | "boolean";
  expanded?: boolean;
  stacked?: boolean;
  filterBy: string;
  columnVisibility: Record<string, boolean>;
  gaugeColors?: { id: string; color: string; start: string }[];
  gaugeType?: string;
  unit: string;
  decimal: number;
  hidden: Record<string, boolean>;
  aggregationType: string;
  colorScheme: ColorScheme;
  widget?: boolean;
  iconScheme: string;
  filters: {
    field: LooseAutoComplete<
      "productName" | "event" | "thingName" | "field" | "operations"
    >;
    operation:
      | "is"
      | "is_not"
      | "contains"
      | "does_not_contain"
      | "exists"
      | "does_not_exist";
    value: string[];
    enabled?: boolean;
    id: string;
    ignoreIfNotPayload?: boolean;
    isFieldFilter?: boolean;
    mergeGraph?: boolean;
  }[];
  comparer?: string;
};
