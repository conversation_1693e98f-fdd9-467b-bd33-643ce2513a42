import storage from "../platform/storage";

const getEnvValue = (key: string): string => {
  if (navigator.product != "ReactNative") {
    // @ts-expect-error env is not resolvable
    return window.__ENV__?.[`VITE_${key}`] ?? "";
  } else {
    if (key === "APP_API_URL") {
      const isAvailableInStorage = storage.getItem("rn-url");

      if (isAvailableInStorage) {
        return isAvailableInStorage;
      }
      return "https://services-dev.oneiot.io";
    }

    if (key === "APP_URL") {
      return (
        process.env.EXPO_PUBLIC_APP_URL ?? "https://services-dev.oneiot.io"
      );
    }
    if (key === "APP_SECRET_KEY") {
      return process.env.EXPO_PUBLIC_APP_SECRET_KEY ?? "";
    }

    return process.env[`EXPO_PUBLIC_${key}`] ?? "";
  }
};

export const SECRET_KEY = getEnvValue("APP_SECRET_KEY");

// NEW URLS
const BASE = getEnvValue("APP_API_URL");
const API_URL = `${BASE}/em/api`;
const IOT_URL = `${BASE}/fm/api`;
const PRODUCT_URL = `${BASE}/pm/api`;
const THING_URL = `${BASE}/tm/api`;
const OTA_URL = `${BASE}/ota/api`;
const PKI_URL = `${BASE}/ocm/api`;
const INCIDENT_URL = `${BASE}/inc/api`;
const STATS_URL = `${BASE}/anl/api/`;
const COMM_URL = `${BASE}/int/api`;
const PROCESSING_URL = `${BASE}/nm/api`;
const IOT_MESSAGE = `${BASE}/ahe/api`;
const GEO_URL = `${BASE}/geo/api`;
// export const GEO_URL = `http://localhost:8026/api`;
const SIMULATOR_URL = `${BASE}/sim/api`;
const APP_URL = `${BASE}/ab/api`;
// export const APP_URL = `http://localhost:8022/api`;

const URL_MAP = {
  API_URL,
  IOT_URL,
  PRODUCT_URL,
  THING_URL,
  OTA_URL,
  PKI_URL,
  INCIDENT_URL,
  STATS_URL,
  COMM_URL,
  PROCESSING_URL,
  IOT_MESSAGE,
  GEO_URL,
  SIMULATOR_URL,
  APP_URL,
};

export default URL_MAP;
