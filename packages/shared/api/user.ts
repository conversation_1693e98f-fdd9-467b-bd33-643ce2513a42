import { DEFAULT_PAGE_COUNT } from "../config/defaults";
import storage from "../platform/storage";
import {
  fetchApi,
  getTokens,
  getUrlParams,
  setTokens,
} from "../utils/httpUtils";
import URL_MAP from "../config/apiUrls";
import { AppUser } from "../store/store.types";

export const addAppUserApi = async (body: {
  userName: string;
  email: string;
  address: string;
  phone: string;
  pincode: string;
  password: string;
  tenant: string;
}) => {
  const fetchResponse = await fetchApi(
    `/app/users`,
    {
      method: "POST",
      body,
    },
    URL_MAP.APP_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }

  throw new Error(res.message);
};

export const addAppUserRegisterApi = async (body: {
  userName: string;
  email: string;
  address: string;
  phone: string;
  pincode: string;
  password: string;
  tenant: string;
}) => {
  const fetchResponse = await fetchApi(
    `/app/users/new`,
    {
      method: "POST",
      body,
    },
    URL_MAP.APP_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }

  throw new Error(res.message);
};

export const appUserLoginApi = async (body: {
  username: string;
  password: string;
}) => {
  const fetchResponse = await fetchApi(
    `/app/auth/login`,
    {
      method: "POST",
      body,
    },
    URL_MAP.APP_URL
  );
  const res = await fetchResponse.json();

  if (res.status === "Success") {
    return res as { token: string; refreshToken: string };
  }

  throw new Error(res.message);
};

export const updateUserRoleApi = async ({
  thingName,
  role,
  email,
}: {
  thingName: string;
  role: string;
  email: string;
}) => {
  const fetchResponse = await fetchApi(
    `/app-things/user-role`,
    {
      method: "PATCH",
      body: {
        thingName,
        role,
        email,
      },
    },
    URL_MAP.APP_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

const setUser = (user) => {
  storage.setItem("user", JSON.stringify(user));
};
export function getUser() {
  const user = storage.getItem("user");
  return user ? JSON.parse(user) : null;
}

export async function login({
  email,
  password,
  newPassword,
  rememberMe,
}: {
  email: string;
  password: string;
  newPassword?: string;
  rememberMe: boolean;
}) {
  const resp = await fetchApi("/auth/login", {
    method: "POST",
    body: {
      email,
      password,
      rememberMe,
      ...(newPassword ? { newPassword } : {}),
    },
    credentials: "include",
  });
  const data = await resp.json();

  if (data.status === "Success") {
    storage.clear();
    setTokens({ accessToken: data.token, refreshToken: data.refreshToken });
  }
  return data;
}

export async function logout() {
  await fetchApi("/auth/logout", { method: "POST", credentials: "include" });
  storage.clear();
  return { status: "success" };
}

export const fetchAppUserDetails = async () => {
  const fetchResponse = await fetchApi(`/app/auth/self`, {}, URL_MAP.APP_URL);
  const res = await fetchResponse.json();

  if (res.status === "Success") {
    return res.data as { user: AppUser; tenant: any };
  }

  if (res.status === "Failure") {
    throw new Error(res.message);
  }

  throw new Error("Something went wrong with loading user!");
};

export async function fetchUserDetails({ email, userType, tenant } = {}) {
  const self = !email;
  let resp;
  if (self) {
    resp = await fetchApi("/auth/self");
  } else if (userType === "firewires") {
    resp = await fetchApi(`/msp/users/user?tenant=${tenant}&email=${email}`);
  } else {
    resp = await fetchApi(`/tnt/users/user?tenant=${tenant}&email=${email}`);
  }

  if (!resp.ok) {
    throw new Error("Failed to fetch user details");
  }

  const { status, data } = await resp.json();

  if (status === "Success") {
    if (self) {
      data.user.avatar = `https://api.dicebear.com/8.x/initials/svg?seed=${data.user.name}`;
      if (data.tenant?.avatar) {
        data.tenant.avatar = `https://api.dicebear.com/8.x/initials/svg?seed=${data.tenant.name}`;
      }
      setUser(data);
    } else {
      data.avatar = `https://api.dicebear.com/8.x/initials/svg?seed=${data.name}`;
    }
  }

  return { status, ...data };
}

export async function refreshUser() {
  const refreshToken = getTokens()?.refreshToken;
  const resp = await fetchApi("/auth/refresh", {
    method: "POST",
    body: {
      email: getUser().user.email,
      refreshToken,
    },
    credentials: "include",
  });
  const data = await resp.json();
  if (data.status === "Success") {
    setTokens({ accessToken: data.token, refreshToken });
  }
  return data;
}

export async function fetchMspList({
  search,
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
}) {
  // const [_, page, limit, search] = queryKey;

  const resp = await fetchApi(
    `/operator/msp/list?${getUrlParams({
      page,
      limit,
    })}&search=${search}`
  );

  return resp.json();
}

export async function fetchMspDetails({ queryKey }) {
  const [_, mspId] = queryKey;

  const resp = await fetchApi(`/operator/msp/${mspId}`);

  return resp.json();
}

export async function fetchOperatorViewUsers({ queryKey }) {
  const [_, page, limit, debouncedSearchQuery, userType] = queryKey;
  const { onlyOpUsers, onlyMspUsers, onlyTenantUsers, mspAndTenantUsers } =
    userType || {};

  const resp = await fetchApi(
    `/operator/users/list?search=${debouncedSearchQuery}&${getUrlParams({
      page,
      limit,
      onlyOpUsers,
      onlyMspUsers,
      onlyTenantUsers,
      mspAndTenantUsers,
    })}`
  );

  return resp.json();
}

export async function fetchTenantUsers({ queryKey }) {
  const [_, page, limit, debouncedSearchQuery] = queryKey;
  const resp = await fetchApi(
    `/tnt/users/list?search=${debouncedSearchQuery}&${getUrlParams({
      page,
      limit,
    })}`
  );
  return resp.json();
}

export async function fetchMspUserDetial({ queryKey }) {
  const [_, userEmail] = queryKey;

  const resp = await fetchApi(`/msp/users/user?email=${userEmail}`);

  return resp.json();
}

export async function fetchAllUserDetails({ queryKey }) {
  const [_, userEmail, type] = queryKey;
  let resp;
  if (type === "operator") {
    resp = await fetchApi(`/operator/users?email=${userEmail}`);
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/users/user?email=${userEmail}`);
  }
  if (type === "tenant") {
    resp = await fetchApi(`/tnt/users/user?email=${userEmail}`);
  }

  return resp.json();
}

export async function fetchUserGroups({ queryKey }) {
  const [_, type, page, limit, search] = queryKey;
  let resp;
  if (type === "operator") {
    resp = await fetchApi(
      `/operator/users/group/list?${getUrlParams({
        page,
        limit,
        search,
      })}`
    );
  }
  if (type === "msp") {
    resp = await fetchApi(
      `/msp/users/group/list?${getUrlParams({
        page,
        limit,
        search,
      })}`
    );
  }
  if (type === "tenant") {
    resp = await fetchApi(
      `/tnt/users/group/list?${getUrlParams({
        page,
        limit,
        search,
      })}`
    );
  }

  return resp.json();
}

export async function fetchgroupUserCount() {
  const resp = await fetchApi(
    `/stats/count/user-groups/users`,
    {},
    URL_MAP.STATS_URL
  );
  return resp.json();
}

export async function fetchUserGroupDetail({ queryKey }) {
  const [_, type, userGroupId] = queryKey;
  let resp;
  if (type === "operator") {
    resp = await fetchApi(`/operator/users/group/${userGroupId}`);
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/users/group/${userGroupId}`);
  }
  if (type === "tenant") {
    resp = await fetchApi(`/tnt/users/group/${userGroupId}`);
  }

  return resp.json();
}

export async function fetchAccessTemplate() {
  const resp = await fetchApi(`/access-template/list`);
  return resp.json();
}

export async function fetchUserMetricUsage({ duration, tenant = undefined }) {
  const resp = await fetchApi(
    `/stats/metric/usage?${getUrlParams({
      duration,
      tenant,
    })}`,
    {},
    URL_MAP.STATS_URL
  );

  const data = await resp.json();
  if (!resp.ok) throw new Error(data.message);
  return data;
}

export const createNotificationToken = async (record: {
  email: string;
  token: string;
  contactNumber: string;
  deviceOS: string;
}) => {
  const body = {
    email: record.email,
    token: record.token,
    contactNumber: record.contactNumber,
    deviceOS: record.deviceOS,
  };

  const resp = await fetchApi(
    "/in/token",
    { method: "POST", body },
    URL_MAP.APP_URL
  );
  return resp.json();
};

export const deleteNotificationsToken = async (token: string) => {
  const resp = await fetchApi(
    `/in/token/${token}`,
    { method: "DELETE" },
    URL_MAP.APP_URL
  );
  return resp.json();
};

// =========================== NON-FETCH REQUESTS =========================== //

export async function registerUser({ role, ...payload }) {
  const signupRoles = {
    OperatorAdmin: "/operator/users/register/admin",
    OperatorSupport: "/operator/users/register/support",
    MSPAdmin: "/msp/users/register/admin",
    MSPSupport: "/msp/users/register/support",
    TenantAdmin: "/tnt/users/register/admin",
    TenantSupport: "/tnt/users/register/support",
  };

  const resp = await fetchApi(signupRoles[role], {
    method: "POST",
    body: payload,
  });
  return resp.json();
}
export async function updateAllUser(body) {
  const { type, ...payload } = body;

  let resp;
  if (type === "operator") {
    resp = await fetchApi("/operator/users/update", {
      method: "PATCH",
      body: payload,
    });
  }
  if (type === "msp") {
    resp = await fetchApi("/msp/users/update", {
      method: "PATCH",
      body: payload,
    });
  }
  if (type === "tenant") {
    resp = await fetchApi("/tnt/users/update", {
      method: "PATCH",
      body: payload,
    });
  }

  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateUser({
  userType,
  email,
  tenant,
  phone,
  address,
  pincode,
}) {
  let resp;
  if (userType === "msp") {
    resp = await fetchApi("/msp/users/manage/update", {
      method: "PATCH",
      body: { email, phone, address, tenant, pincode },
    });
  } else {
    resp = await fetchApi("/tnt/users/update", {
      method: "PATCH",
      body: { email, phone, address, tenant, pincode },
    });
  }

  return resp.json();
}

export async function createMsp(body) {
  const resp = await fetchApi("/operator/msp", {
    method: "POST",
    body,
  });

  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateMsp(payload) {
  const { mspId, ...body } = payload;
  const resp = await fetchApi(`/operator/msp/${mspId}`, {
    method: "Put",
    body,
  });
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function deleteMsp(mspId) {
  const resp = await fetchApi(`/operator/msp/${mspId}`, {
    method: "DELETE",
  });
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}
export async function deleteUser({ type = "tenant", userEmail }) {
  let resp;

  if (type === "operator") {
    resp = await fetchApi(`/operator/users/delete?email=${userEmail}`, {
      method: "DELETE",
    });
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/users/delete?email=${userEmail}`, {
      method: "DELETE",
    });
  }
  if (type === "tenant") {
    resp = await fetchApi(`/tnt/users/delete?email=${userEmail}`, {
      method: "DELETE",
    });
  }

  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateUserStatus({ email, status }) {
  const resp = await fetchApi(`/users/${status}`, {
    method: "PATCH",
    body: { email },
  });
  return resp.json();
}

export async function createUserApi(body, userType) {
  let resp;
  if (userType === "msp") {
    resp = await fetchApi("/msp/key/generate", {
      method: "POST",
      body,
    });
  } else {
    resp = await fetchApi("/tnt/key/generate", {
      method: "POST",
      body,
    });
  }

  return resp.json();
}

export async function createUserGroup(type, body) {
  let resp;
  if (type === "operator") {
    resp = await fetchApi("/operator/users/group", {
      method: "POST",
      body,
    });
  }
  if (type === "msp") {
    resp = await fetchApi("/msp/users/group", {
      method: "POST",
      body,
    });
  }
  if (type === "tenant") {
    resp = await fetchApi("/tnt/users/group", {
      method: "POST",
      body,
    });
  }

  return resp.json();
}

export async function updateUserGroup(type, groupName, body) {
  let resp;
  if (type === "operator") {
    resp = await fetchApi(`/operator/users/group/${groupName}`, {
      method: "PUT",
      body,
    });
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/users/group/${groupName}`, {
      method: "PUT",
      body,
    });
  }
  if (type === "tenant") {
    resp = await fetchApi(`/tnt/users/group/${groupName}`, {
      method: "PUT",
      body,
    });
  }

  return resp.json();
}

export async function deleteUserGroup(type, groupName) {
  let resp;
  if (type === "operator") {
    resp = await fetchApi(`/operator/users/group/${groupName}`, {
      method: "DELETE",
    });
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/users/group/${groupName}`, {
      method: "DELETE",
    });
  }
  if (type === "tenant") {
    resp = await fetchApi(`/tnt/users/group/${groupName}`, {
      method: "DELETE",
    });
  }

  return resp.json();
}

// export async function deleteUser({ email, tenant, userType }) {
//   let resp;

//   if (userType === "firewires") {
//     resp = await fetchApi("/msp/users/manage/delete", {
//       method: "DELETE",
//       body: { email, tenant }
//     });
//   } else {
//     resp = await fetchApi("/tnt/users/manage/delete", {
//       method: "DELETE",
//       body: { email, tenant }
//     });
//   }

//   return resp.json();
// }

export async function deleteUserApi({ userType, keyId }) {
  let resp;

  if (userType === "msp") {
    resp = await fetchApi(`/msp/key/delete?${getUrlParams({ keyId })}`, {
      method: "PUT",
    });
  } else {
    resp = await fetchApi(`/tnt/key/delete?${getUrlParams({ keyId })}`, {
      method: "PUT",
    });
  }

  return resp.json();
}

export async function sendResetCode(email) {
  const resp = await fetchApi("/auth/verify/code", {
    method: "POST",
    body: { email },
  });
  return resp.json();
}

export async function changePassword({ email, password, newPassword }) {
  const resp = await fetchApi("/auth/verify/changePW", {
    method: "POST",
    body: { email, newPassword, password },
  });
  return resp.json();
}

export async function resetPassword({ email, newPassword, verificationCode }) {
  const resp = await fetchApi("/auth/verify/forgetPW", {
    method: "POST",
    body: { email, newPassword, verificationCode },
  });
  return resp.json();
}

export async function setupPartner() {
  const resp = await fetchApi("/tnt/setup ", {
    method: "POST",
    body: {},
  });
  return resp.json();
}
