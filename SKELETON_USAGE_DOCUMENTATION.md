# OneIoT Platform - Skeleton Components Usage Documentation

This document provides a comprehensive overview of all pages and components that use skeleton loading states across the OneIoT platform.

## Table of Contents
- [Core Skeleton Components](#core-skeleton-components)
- [Web Portal Usage](#web-portal-usage)
- [Mobile App Usage](#mobile-app-usage)
- [Loading Patterns](#loading-patterns)
- [Component Categories](#component-categories)

## Core Skeleton Components

### 1. Base Skeleton Component
**File:** `apps/web-portal/src/components/shadcn/components/skeleton.tsx`
- **Type:** Core UI component
- **Usage:** Foundation for all skeleton implementations
- **Features:** `animate-pulse`, `bg-secondary`, rounded corners

### 2. Specialized Skeleton Components

| Component | File Path | Purpose |
|-----------|-----------|---------|
| `CardLoadingSkeleton` | `apps/web-portal/src/components/Card/CardSkeleton.tsx` | Generic card content loading |
| `PageSekeleton` | `apps/web-portal/src/components/PageSekeleton.tsx` | Full page layout loading |
| `SkeletonList` | `apps/web-portal/src/pages/UserTypes/Tracking/HomeSection/Components/SkeletonList.tsx` | List item loading |
| `DeviceSkeleton` | `apps/web-portal/src/pages/UserTypes/Tracking/HomeSection/Components/DeviceSkeleton.tsx` | Device grid loading |
| `ShadcnTableSkeleton` | `apps/web-portal/src/components/Table/ShadcnTableSkeleton.tsx` | Shadcn table loading |
| `TableRowsSkeleton` | `apps/web-portal/src/components/Table/TableRowsSkeleton.tsx` | Custom table loading |
| `CharLoadingSkeleton` | `apps/web-portal/src/components/Chatbot/CharLoadingSkeleton.tsx` | Chat message loading |
| `LoggerLoaderItem` | `apps/web-portal/src/pages/Logger/layout/LoggerLoaderItem.tsx` | Logger row loading |
| `SidebarMenuSkeleton` | `apps/web-portal/src/components/shadcn/components/sidebar.tsx` | Sidebar menu loading |

## Web Portal Usage

### 1. Route-Level Loading (Suspense Fallbacks)

#### **DefaultRoutes.tsx**
- **File:** `apps/web-portal/src/Routes/DefaultRoutes.tsx`
- **Component:** `PageSekeleton`
- **Usage:** Lazy-loaded route fallback
- **Context:** All lazy-loaded pages use this as fallback

#### **Main Application Entry**
- **File:** `apps/web-portal/src/main.jsx`
- **Component:** `LogoLoader`
- **Usage:** App-level loading state
- **Context:** Initial app load

#### **App.tsx**
- **File:** `apps/web-portal/src/App.tsx`
- **Component:** `LogoLoader`
- **Usage:** Authentication and app initialization
- **Context:** User authentication flow

### 2. Page-Level Skeleton Usage

#### **Home Section**
- **File:** `apps/web-portal/src/pages/HomeSection/index.tsx`
- **Components:** `PageSekeleton`
- **Usage:** Dashboard loading state
- **Context:** Main dashboard initialization

#### **Monitor Page**
- **File:** `apps/web-portal/src/pages/MonitorPage/index.tsx`
- **Components:** `CircularProgress`
- **Usage:** Dashboard list loading
- **Context:** Monitor dashboard loading

#### **Monitor Dashboard Creation**
- **File:** `apps/web-portal/src/pages/MonitorPage/CreateDashboard.tsx`
- **Components:** `CircularProgress`
- **Usage:** Dashboard building state
- **Context:** Dashboard creation and editing

#### **Notification Pages**
- **File:** `apps/web-portal/src/pages/NotificationPage/Schedules/ScheduleDetailPage.tsx`
- **Components:** `PageSekeleton`
- **Usage:** Schedule details loading
- **Context:** Notification schedule details

- **File:** `apps/web-portal/src/pages/NotificationPage/Notification/NotificationDetails.tsx`
- **Components:** `PageSekeleton`
- **Usage:** Notification template loading
- **Context:** Notification template details

### 3. Card-Level Skeleton Usage

#### **App User Details**
- **File:** `apps/web-portal/src/pages/AppUsers/components/AppUserDetailsCard.tsx`
- **Components:** `CardLoadingSkeleton`
- **Usage:** User details loading
- **Context:** App user profile loading

#### **Student Management**
- **File:** `apps/web-portal/src/pages/UserTypes/Tracking/ClassManagement/StudentManagement/StudentDetialPage.tsx`
- **Components:** `CardLoadingSkeleton`
- **Usage:** Student details loading
- **Context:** Student profile and details

#### **Gateway Details**
- **File:** `apps/web-portal/src/pages/GatewayPage/GatewayDetailPage.tsx`
- **Components:** `CardLoadingSkeleton`
- **Usage:** Gateway information loading
- **Context:** Gateway configuration details

#### **Product Details - Simulators**
- **File:** `apps/web-portal/src/pages/ProductPage/ProductDetailSection/DetailsComponents/Simulators.tsx`
- **Components:** `CardLoadingSkeleton`
- **Usage:** Simulator list loading
- **Context:** Product simulator configuration

### 4. Table-Level Skeleton Usage

#### **Security - Listener Page**
- **File:** `apps/web-portal/src/pages/SecurityPage/ListenerPage.tsx`
- **Components:** `TableRowsSkeleton`, `CardLoadingSkeleton`
- **Usage:** Thing listeners table loading
- **Context:** Security configuration tables

#### **Logger Table**
- **File:** `apps/web-portal/src/pages/Logger/layout/LoggerTable.tsx`
- **Components:** `LoggerLoaderItem`, `CircularProgress`
- **Usage:** Infinite scroll logger loading
- **Context:** System logs display

#### **Thing Logger Tab**
- **File:** `apps/web-portal/src/pages/UserTypes/Tracking/HomeSection/DeviceDetialSection/ThingLoggerTab.tsx`
- **Components:** `LoggerLoaderItem`, `CircularProgress`
- **Usage:** Device-specific logs
- **Context:** Device log history

### 5. List-Level Skeleton Usage

#### **Device List Sidebar**
- **File:** `apps/web-portal/src/pages/UserTypes/Tracking/HomeSection/DeviceListSidebar.tsx`
- **Components:** `SkeletonList`
- **Usage:** Device list loading
- **Context:** Tracking device management

#### **History List Sidebar**
- **File:** `apps/web-portal/src/pages/UserTypes/Tracking/HistoryPage/HistoryListSidebar.tsx`
- **Components:** `SkeletonList`
- **Usage:** History timeline loading
- **Context:** Device history tracking

#### **Product List Sidebar**
- **File:** `apps/web-portal/src/pages/UserTypes/Tracking/ProductPage/ProductListSidebar.tsx`
- **Components:** `SkeletonList`
- **Usage:** Product list loading
- **Context:** Product management

### 6. Chart-Level Skeleton Usage

#### **Site 3D Model**
- **File:** `apps/web-portal/src/pages/site/components/Site3DModel.tsx`
- **Components:** `ChartLoader`
- **Usage:** 3D model loading
- **Context:** Digital twin visualization

#### **Recommendations List**
- **File:** `apps/web-portal/src/pages/site/components/RecommendationsList.tsx`
- **Components:** `ChartLoader`
- **Usage:** Recommendations loading
- **Context:** Site recommendations

#### **Trigger Card**
- **File:** `apps/web-portal/src/pages/MonitorPage/layout/TriggerCard.tsx`
- **Components:** `ChartLoader`, `CircularProgress`
- **Usage:** Trigger configuration loading
- **Context:** Dashboard trigger widgets

### 7. Statistics Card Usage

#### **Product Page**
- **File:** `apps/web-portal/src/pages/ProductPage/index.tsx`
- **Components:** `StatCard` (with loading states)
- **Usage:** Product statistics loading
- **Context:** Product overview dashboard

#### **Inventory Page**
- **File:** `apps/web-portal/src/pages/InventoryPage/InventoryCardSection.tsx`
- **Components:** `Skeleton`, `StatCardSkeleton`
- **Usage:** Inventory statistics loading
- **Context:** Inventory management dashboard

#### **Security - Certificates**
- **File:** `apps/web-portal/src/pages/SecurityPage/ThingCertificate/Certificates.tsx`
- **Components:** `StatCard` (with loading states), `StatCardSkeleton`
- **Usage:** Certificate statistics loading
- **Context:** Security certificate management

#### **Usage Page**
- **File:** `apps/web-portal/src/pages/UsagePage.tsx`
- **Components:** `ChartCard` (with loading states)
- **Usage:** Usage statistics loading
- **Context:** Platform usage analytics

### 8. UI Component Loading States

#### **Card Examples**
- **File:** `apps/web-portal/src/components/ui/card-examples.tsx`
- **Components:** `StatCard`, `MetricCard`, `ChartCard` (loading states)
- **Usage:** Component documentation examples
- **Context:** UI component showcase

## Mobile App Usage

### 1. React Native Components

#### **Button Component**
- **File:** `apps/oneiottrack/app/components/Button.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Button loading states
- **Context:** All button interactions

#### **Device Details Screen**
- **File:** `apps/oneiottrack/app/screens/DeviceDetailsScreen.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Device details loading
- **Context:** Device information display

#### **Device List Screen**
- **File:** `apps/oneiottrack/app/screens/DeviceListScreen.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Device list loading
- **Context:** Device list with infinite scroll

### 2. Table Components

#### **Activity Table**
- **File:** `apps/oneiottrack/app/components/Screen/DeviceDetails/ActivityTable.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Activity data loading
- **Context:** Device activity history

#### **Attendance Table**
- **File:** `apps/oneiottrack/app/components/Screen/DeviceDetails/AttendanceTable.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Attendance data loading
- **Context:** Student attendance tracking

#### **Scrollable Table**
- **File:** `apps/oneiottrack/app/components/ScrollableTable.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Table footer loading
- **Context:** Infinite scroll tables

### 3. Header Components

#### **Home Header**
- **File:** `apps/oneiottrack/app/components/Layout/HomeHeader.tsx`
- **Components:** `ActivityIndicator`
- **Usage:** Search and filter loading
- **Context:** Main navigation header

## Loading Patterns

### 1. Animation Types
- **Pulse Animation:** `animate-pulse` class (most common)
- **Bounce Animation:** Custom CSS for chart loaders
- **Circular Progress:** Material UI spinner
- **Activity Indicator:** React Native spinner
- **Lottie Animation:** Custom logo loader

### 2. Color Schemes
- **Primary:** `bg-secondary`, `bg-muted`
- **Gray Scale:** `bg-gray-300`, `bg-gray-500/40`
- **Theme Aware:** `dark:bg-gray-700`, `dark:bg-gray-600`

### 3. Size Variations
- **Small:** `h-3`, `h-4` (text lines)
- **Medium:** `h-6`, `h-7` (list items)
- **Large:** `h-10`, `h-11` (cards, headers)
- **Custom:** Component-specific dimensions

## Component Categories

### 1. Data Display Loading
- Tables, lists, cards, statistics
- Used in: Product pages, device management, analytics

### 2. Navigation Loading
- Sidebars, menus, headers
- Used in: Main navigation, filtering, search

### 3. Content Loading
- Charts, 3D models, images, text content
- Used in: Dashboards, visualizations, reports

### 4. Form Loading
- Buttons, inputs, dropdowns
- Used in: Configuration, user management, settings

### 5. Page Loading
- Full page, sections, modals
- Used in: Route transitions, lazy loading, authentication

---

*This documentation covers all identified skeleton usage patterns as of the current codebase state. For updates or additions, please refer to the individual component files.*
