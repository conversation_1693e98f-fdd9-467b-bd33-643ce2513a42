/* @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap"); */
@import url("./global.css");
@import url("./custom.css");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");
body {
  margin: 0;
  /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif; */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: overlay;
}
* {
  /* font-family: "Poppins", sans-serif; */
  /* font-family: "Inter", sans-serif; */
  font-family: "Geist", sans-serif;
  /* font-family: "Open Sans", sans-serif; */
  /* font-family: "DM Sans", sans-serif; */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}

.scrollbar::-webkit-scrollbar-thumb,
.scrollbar-thin::-webkit-scrollbar-thumb {
  border-radius: 999px;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 0.3rem;
  height: 0.3rem;
}

.scrollbar-mozilla {
  scrollbar-color: #6b7280 transparent;
  overflow-y: scroll;
}

/*  Input, dropdowns & button */
.MuiOutlinedInput-root,
.MuiPaper-root {
  border-radius: 10px !important;
}

.MuiModal-root > .MuiBackdrop-root {
  backdrop-filter: blur(2.5px);
}

/* Hide number input spinner/arrows */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}

input:-webkit-autofill,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 50px transparent inset !important; /*your box-shadow*/
  transition: background-color 600000s 0s;
}

:root {
  scrollbar-width: thin !important;
  scrollbar-color: #6b7280 transparent;
}

::-webkit-scrollbar {
  width: 0.4rem;
  height: 0.4rem;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #6b7280 !important;
  border-radius: 999px;
}

.rule-section::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.dropdown-scroll::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.dropdown-scroll,
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.leaflet-tooltip:has(.small-padding) {
  padding: 2px 4px !important; /* Adjust the padding as needed */
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px; /* Optional: adjust the border radius */
  border: 1px solid #ccc; /* Optional: adjust the border */
}

.leaflet-tooltip-top:before {
  @apply border-t-white dark:!border-t-secondary;
}

.leaflet-tooltip:has(.no-padding) {
  padding: 0 !important;
}

/* dropdown list */
/* .MuiPaper-root {
  margin-top: 6px !important;
  padding: 0 8px !important;
} */

/* dropdown items */
/* .MuiMenuItem-root {
  border-radius: 8px !important;
  margin: 4px 0 !important;
} */

/* html {
  font-size: 95%;
}

@media (max-width: 1400px) {
  html {
    font-size: 85%;
  }
}

@media (max-width: 1280px) {
  html {
    font-size: 80%;
  }
}

@media (max-width: 1024px) {
  html {
    font-size: 75%;
  }
} */

/* html {
  font-size: 50% !important;

} */

.Toastify__toast {
  @apply !bg-card;
}
.Toastify__progress-bar--success {
  @apply !bg-green-400;
}
.Toastify__progress-bar--error {
  @apply !bg-red-400;
}
.react-grid-placeholder {
  background: #4ade80 !important; /* Green-400 from Tailwind */
  opacity: 0.4;
  border-radius: 0.375rem;
}
