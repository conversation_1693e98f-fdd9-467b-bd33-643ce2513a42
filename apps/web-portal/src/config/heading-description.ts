import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";

export const HEADING_DESCRIPTION = {
  userAuth:
    " Access to this IoT system is restricted to authorized users only. By accessing and using this system, you agree to abide by the terms and conditions set forth by the system administrator. Unauthorized access or misuse of this system is strictly prohibited and may result in disciplinary actions. Please ensure you have the necessary permissions to use this IoT system. For assistance or authorization requests, please contact the system administrator.",
  certAuth:
    "This IoT system utilizes certificate-based authentication to ensure secure and authorized access for both users and IoT devices. All users and devices connecting to this system are required to present valid certificates issued by the system administrator. Certificates must adhere to the established security protocols and expiration dates. Any attempts to connect without a valid certificate will be denied. For certificate issuance and renewal, please follow the guidelines provided by the system administrator. Only certificates issued by the authorized Certificate Authority (CA) will be accepted for authentication.",
  certificate:
    "This certificate is issued to authenticate the identity of the IoT device/user listed above and is valid for secure communication within the designated IoT system. The included public key serves for encryption and authentication purposes, ensuring the integrity and security of data exchange. Any unauthorized use or reproduction of this certificate is strictly prohibited. Issued by [Name of Certificate Authority] on [Issuance Date].",
  policy:
    "This policy is designed to ensure the secure and reliable operation of our IoT system. All users and connected IoT devices are required to adhere to the following security guidelines: Authentication and authorization protocols must be strictly followed for all access to the IoT system. Data transmission within the system must be encrypted to maintain confidentiality and integrity. Regular patch management and firmware updates are mandatory to address potential vulnerabilities. Access control mechanisms based on the principle of least privilege must be enforced to protect sensitive resources. Clear procedures for incident response and reporting of security breaches are outlined, and all users and administrators must comply with regulatory standards and prescribed security configurations. Non-compliance with this policy may result in restricted access or other appropriate disciplinary actions.",
  policyTemplate:
    "Our IoT security policy template provides a comprehensive framework for ensuring the integrity and confidentiality of our IoT system. It establishes guidelines for authentication, encryption, access control, patch management, and incident response. By adhering to this policy, all users and connected devices are committed to maintaining the highest standards of security within our IoT environment. This template serves as a foundational document that can be tailored to specific organizational needs, ensuring that our IoT infrastructure remains resilient against potential threats and vulnerabilities.",
  userMetrics:
    "The data collected from user metrics provides valuable insights into user behavior, engagement, and preferences within our platform. By analyzing user metrics such as active users, session duration, conversion rates, and retention rates, we gain a deeper understanding of how users interact with our services. These statistics guide decision-making processes, helping us to enhance user experience, optimize content delivery, and tailor marketing strategies to better meet the needs and expectations of our user base. User metrics statistics play a pivotal role in driving data-driven initiatives and continuously improving our platform to better serve our users.",
  notificationTemplate:
    "These templates serve as standardized formats for delivering important messages, updates, and alerts to users in a clear and consistent manner. By designing notification templates with relevant content, personalized details, and engaging visuals, we aim to capture users' attention and convey information efficiently. Customizing templates for different types of notifications, such as transactional alerts, product updates, or promotional messages, empowers us to maintain brand consistency and enhance user engagement. With strategically designed notification templates, we can deliver timely and impactful messages that resonate with our users, ultimately fostering better communication and building stronger relationships with our audience.",
  notificationContacts:
    "Establishing effective notification contacts is essential for ensuring timely and accurate communication with key stakeholders and individuals. These contacts serve as the primary points of contact for receiving critical alerts, updates, and information across various channels. By maintaining up-to-date contact information for relevant personnel, departments, and external partners, we can swiftly disseminate important messages and coordinate response efforts in case of emergencies or urgent situations. Clear protocols for managing and updating notification contacts are crucial to ensure that communication channels remain reliable and accessible when needed. Establishing a network of notification contacts helps to streamline communication processes, improve response times, and enhance overall preparedness across the organization.",
  notificationAssets:
    "By allowing members to customize their notification settings based on their interests, communication preferences, and frequency of updates, we empower them to tailor their notification experience to suit their individual needs. Acknowledging and honoring these preferences not only enhances user satisfaction but also fosters a sense of control and ownership over their interactions with our platform. By regularly reviewing and updating member preferences, we can ensure that our notifications remain relevant, timely, and valuable to each member, ultimately leading to increased engagement, retention, and overall user satisfaction.",
  rules:
    "Rules are essential for governing the behavior and interactions of interconnected devices within an IoT ecosystem. These rules define the conditions, actions, and triggers that dictate how devices communicate, process data, and respond to various events. By establishing well-defined IoT rules, organizations can ensure efficient and secure operation of IoT networks, enforce compliance with industry standards, and mitigate potential risks associated with device malfunctions or security breaches. Furthermore, IoT rules play a significant role in optimizing resource utilization, enabling automation, and maintaining a cohesive IoT environment that aligns with the desired operational outcomes. Implementing and managing IoT rules effectively is critical for harnessing the full potential of IoT technology and realizing its benefits across diverse industry sectors.",
  destinations:
    "Destinations for rules define where the data is routed when specific conditions are met. IoT rules act as triggers that monitor incoming device data and apply logic to determine when an action should occur. When a rule is triggered, the destination is the endpoint where the resulting data or action is sent. These destinations can range from cloud services, databases, messaging queues, and other APIs, to storage systems or even direct alerts. This system allows organizations to automate workflows, ensure data is processed in real-time, and make intelligent decisions based on the behavior of connected devices. Configuring appropriate destinations ensures that data flows efficiently from devices to the systems that need to act on it.",
  credentials:
    "Credentials for Rules are essential for securing data transmission and ensuring that only authorized entities can interact with IoT systems. When configuring destinations for IoT rules, credentials such as API keys, OAuth tokens, or authentication certificates are used to verify and authenticate requests. These credentials ensure that data is routed to the correct endpoint securely, preventing unauthorized access and tampering. By using robust credential management practices, organizations can protect their IoT infrastructure from potential security threats and ensure that data flows seamlessly between IoT devices and their designated destinations. Properly managing and rotating these credentials is crucial for maintaining the integrity and confidentiality of IoT data.",
  incidents:
    "Incidents refer to any disruptions, failures, or security breaches affecting IoT systems and devices. These incidents can range from hardware malfunctions and software bugs to more serious security issues such as data breaches, unauthorized access, or attacks that exploit vulnerabilities in IoT networks. When an incident occurs, it can lead to significant operational impacts, including data loss, compromised privacy, or disruption of services. Addressing IoT incidents typically involves identifying the root cause, implementing corrective measures, and enhancing system defenses to prevent future occurrences. Effective incident management also includes real-time monitoring, prompt response protocols, and regular security audits to safeguard against potential threats and maintain the reliability and security of IoT systems.",
  inventory:
    "Inventory refers to the comprehensive management and tracking of all Internet of Things devices and their associated components within an organization. This inventory includes various elements such as sensors, actuators, gateways, and connected devices, along with their specifications, configurations, and deployment locations. Effective IoT inventory management is crucial for maintaining operational efficiency, ensuring device performance, and facilitating timely updates or replacements. It enables organizations to monitor the health and status of their IoT assets, track usage patterns, and manage software or firmware versions. By maintaining an accurate and up-to-date IoT inventory, organizations can optimize device management, streamline maintenance processes, and enhance overall system security.",
  staticThingGroup:
    "Static Thing groups refer to predefined collections of IoT devices that are organized based on fixed criteria such as functionality, location, or user roles. Unlike dynamic groups, which adjust their membership based on real-time data or conditions, static device groups remain constant unless manually modified. These groups simplify management tasks by allowing administrators to apply uniform settings, updates, or policies to all devices within the group simultaneously. For instance, a static device group could include all temperature sensors in a particular building or all security cameras within a specific zone. This approach facilitates efficient control and monitoring, helps in streamlined troubleshooting, and ensures consistency in device management across the IoT network.",
  gateways:
    "Gateways are critical components that serve as intermediaries between IoT devices and the cloud or central management systems. They handle data collection, processing, and transmission from various connected devices, ensuring that data is securely and efficiently communicated across the network. IoT gateways often perform essential functions such as protocol conversion, data aggregation, and local processing, which helps to reduce latency and improve the overall performance of IoT systems. Additionally, they enhance security by providing a layer of protection against potential threats, encrypting data, and managing access controls. By integrating with cloud platforms or on-premises systems, IoT gateways enable seamless connectivity and interoperability, allowing organizations to monitor and manage their IoT infrastructure effectively",
  things:
    "Things are interconnected physical objects embedded with sensors, actuators, and communication technologies that enable them to collect and exchange data over the internet. These devices can range from simple sensors measuring temperature or humidity to complex machinery with multiple functionalities. IoT devices gather real-time data from their environment, which can be used for monitoring, automation, and decision-making processes. They play a crucial role in various applications, from smart homes and industrial automation to healthcare and environmental monitoring. By enabling devices to interact and share information, IoT technology transforms how data is utilized, driving efficiency, innovation, and improved decision-making across diverse sectors.",
  bulkThings:
    "Bulk Things are large quantities of interconnected devices that are deployed simultaneously within an IoT ecosystem. These bulk deployments are often used to scale operations or manage extensive networks of sensors, actuators, or other IoT equipment. Examples include thousands of smart meters in a utility grid, numerous environmental sensors in a large agricultural setting, or extensive networks of security cameras across multiple locations. Managing bulk IoT devices requires robust infrastructure to handle the increased data flow, ensure consistent performance, and maintain security. Effective strategies for bulk deployments include automated provisioning, centralized management, and real-time monitoring to streamline operations, optimize resource use, and address potential issues promptly.",
  products: `${PRODUCT_VAR} commercially available solution that integrates Internet of Things technology to deliver specific functionalities and benefits. These ${PRODUCT_VAR}s combine hardware, software, and connectivity features to enable devices to collect, process, and transmit data over the internet. Examples of IoT ${PRODUCT_VAR}s include smart thermostats, wearable health monitors, connected security cameras, and industrial sensors. Designed to enhance convenience, efficiency, and automation, IoT ${PRODUCT_VAR}s are often tailored to address particular needs within various sectors such as home automation, healthcare, agriculture, and manufacturing. By leveraging real-time data and remote monitoring capabilities, IoT ${PRODUCT_VAR}s provide users with actionable insights, improved control, and the ability to optimize operations or personal experiences`,
  otaReleases:
    "OTA (Over-The-Air) releases is the process of updating the firmware or software on IoT devices remotely and wirelessly. This method allows manufacturers and operators to deploy updates, patches, or new features to a large fleet of devices without requiring physical access. OTA releases are crucial for maintaining the security, functionality, and performance of IoT devices, as they enable timely fixes for vulnerabilities, bug resolutions, and the introduction of enhancements. The OTA process typically involves sending the update package to the devices, verifying its integrity, and applying the update with minimal disruption to the device’s operation. By leveraging OTA technology, organizations can ensure that their IoT devices remain up-to-date and secure, while also reducing the costs and logistical challenges associated with manual updates",
  appUser:
    "App Users are users who have access to the IoT platform and its features. They can create, manage, and monitor devices, rules, and other resources within the system. App Users can be assigned specific roles and permissions, allowing them to perform specific tasks and access specific areas of the platform. App Users are essential for managing and controlling the IoT ecosystem, ensuring data security, and optimizing system performance. By carefully managing App Users, organizations can maintain a secure and efficient IoT environment, while also providing seamless access to its features and services"
};

export const FORM_INFORMATION = {
  createUserStep1: [
    {
      title: "Admin User",
      description:
        "Create an admin user with full access to manage devices, users, and system settings for overall platform control."
    },
    {
      title: "Support User",
      description:
        "Create a support user with limited access for troubleshooting, diagnostics, and assisting users without system management privileges."
    }
  ],
  createUserStep2: {
    title: "User Profile",
    description:
      "Create an admin user with full access to manage devices, users, and system settings for overall platform control, Create a support user with limited access for troubleshooting, diagnostics, and assisting users without system management privileges."
  },
  productTopicsStep: {
    title: `${THING_VAR} Topics`,
    description: `${THING_VAR} topics serve as channels for communication between ${THING_VAR}s and the server, facilitating efficient data exchange and command execution. Adopting a structured topic and payload format allows for more efficient use of payload data in Actions and Notifications`
  }
};
