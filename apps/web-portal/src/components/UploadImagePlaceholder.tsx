import { Icon<PERSON>utton } from "@mui/material";
import deviceMapping from "@utils/deviceMapping";
import { showErrorToast } from "@utils/index";
import clsx from "clsx";
import { Upload, X } from "lucide-react";
import { ChangeEvent } from "react";

const UploadImagePlaceholder = ({
  onUpload,
  onCancel,
  imagePreview,
  className,
  required
}: {
  onUpload?: (file: ChangeEvent<HTMLInputElement>) => void;
  imagePreview?: any;
  onCancel?: () => void;
  className?: string;
  required?: boolean;
}) => {
  function handleFileUpload(event: ChangeEvent<HTMLInputElement>) {
    if (!event.target.files) return;
    const file = event.target.files[0];

    if (file) {
      const maxSizeInBytes = 3 * 1024 * 1024;

      if (file.size > maxSizeInBytes) {
        showErrorToast("File size must be less than 3 MB");
        event.target.value = "";
        return;
      }
      onUpload(event);
    }
  }

  return (
    <div className={clsx("center mx-auto aspect-video max-w-[12rem] flex-col", className)}>
      <label htmlFor="deviceImage-input" className="center relative rounded-2xl overflow-hidden ">
        <img alt="img" src={imagePreview || deviceMapping.default.image} />
        <div
          className={clsx(
            "absolute center w-full h-full bg-gray-200 cursor-pointer group transition bg-opacity-0 hover:bg-opacity-30 "
          )}
        >
          {!imagePreview && <Upload size="4rem" className={clsx("z-50 text-blue-400  block")} />}
        </div>
        {imagePreview && onCancel && (
          <IconButton
            onClick={onCancel}
            className=" !bg-red-500/30 !absolute right-1 top-0 rounded-full !p-1 !text-red-500"
          >
            <X size={12} />
          </IconButton>
        )}
      </label>
      <input
        id="deviceImage-input"
        required={required}
        onChange={handleFileUpload}
        accept=".jpeg,.jpg,.png,.webp"
        className="hidden"
        type="file"
      />
      <p className="text-gray-400 text-xs mt-2 text-center">
        Allowed *.png *.jpeg *.jpg format only, max size of 3 MB{" "}
      </p>
    </div>
  );
};

export default UploadImagePlaceholder;
