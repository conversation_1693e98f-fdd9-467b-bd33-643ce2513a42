import { CircularProgress } from "@mui/material";
import clsx from "clsx";
import { LucideIcon } from "lucide-react";
import React, { ReactNode, Suspense } from "react";

const NotFoundAnimation = React.lazy(() => import("./NotFoundAnimation"));

const DataNotFound = ({
  title = "No Data Available",
  content = "It seems there is currently no data to display in the table, add new entries to see the data here.",
  isTable = false,
  className,
  Icon
}: {
  title?: string;
  className?: string;
  content?: ReactNode;
  isTable?: boolean;
  Icon?: LucideIcon;
}) => {
  const Layout = (
    <main className={clsx(" center flex-col text-center p-4", className)}>
      {Icon ? (
        <Icon className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
      ) : (
        <div className="center   mb-4 ">
          <Suspense
            fallback={
              <div>
                <CircularProgress size={24} />
              </div>
            }
          >
            <NotFoundAnimation />
          </Suspense>
        </div>
      )}
      <div className="space-y-2">
        <h2 className="text-lg font-medium text-foreground mb-2">{title}</h2>
        {typeof content === "string" ? (
          <p className="text-muted-foreground mb-4">{content}</p>
        ) : (
          content
        )}
      </div>
    </main>
  );

  if (isTable) {
    return (
      <tr>
        <td colSpan={100}>{Layout}</td>
      </tr>
    );
  }

  return Layout;
};

export default DataNotFound;
