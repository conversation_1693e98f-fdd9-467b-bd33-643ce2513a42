import { HEADING_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { ReactElement, ReactNode } from "react";

const HeadingIcon = ({
  Icon,
  title,
  className = "",
  labelClassName = "heading-2",
  noTitle
}: {
  Icon?: ReactNode | ReactElement | any;
  title: string;
  className?: string;
  labelClassName?: string;
  description?: string;
  noTitle?: boolean;
  color?: string;
}) => {
  return (
    <div className={clsx(" flex gap-4 items-center  ", className)}>
      {Icon && (
        <div
          className=" rounded-md p-1.5 bg-gray-700 text-background dark:bg-gray-200 "
          // style={{
          //   backgroundColor: alpha(colorCode, 0.2),
          //   color: colorCode
          // }}
        >
          <Icon size={HEADING_ICON_SIZE} />
        </div>
      )}
      {!noTitle && <h3 className={labelClassName}>{title}</h3>}
    </div>
  );
};

export default HeadingIcon;
