/* eslint-disable no-use-before-define */
import { DashboardGraphItem } from "@/index";
import useChartData from "@hooks/classic/useChartData";
import useTimeSeriesData from "@hooks/timeseries/useTimeSeriesData";
import { getGaugeFieldsFromFilters } from "@src/pages/MonitorPage/Components/ChartOverview";
import {
  colorAtI,
  formattedNumber,
  isChartCircular,
  SENSOR_KEY
} from "@src/pages/MonitorPage/utils";
import { useAppSelector } from "@src/store";
import React, { useMemo } from "react";

const ChartRendererTable = ({ graphItem }: { graphItem: DashboardGraphItem }) => {
  const { data, isLoading } = useChartData(graphItem);

  const { productName } = getGaugeFieldsFromFilters(graphItem.filters, false);

  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);

  const isCircularChart = isChartCircular(graphItem.chartType);
  const { data: sensorData, isLoading: isSensorLoading } = useTimeSeriesData({
    productName,
    key: graphItem.dataKey,
    enabled: graphItem.dataIndex === SENSOR_KEY,
    duration,
    filters: graphItem.filters,
    circular: isCircularChart
  });

  const {
    labels: _labels,
    datapoints: _datapoints,
    elaboratedDataPoint,
    elaboratedDataKeys
  } = data || {
    labels: [],
    datapoints: [],
    elaboratedDataPoint: [],
    elaboratedDataKeys: []
  };

  const elaboratedPointArray = useMemo(() => {
    try {
      const res = new Array(elaboratedDataKeys.length).fill(0);
      elaboratedDataPoint.forEach((item) => {
        elaboratedDataKeys.forEach((key, index) => {
          res[index] += item[key] || 0;
        });
      });
      return res;
    } catch (error) {
      return [];
    }
  }, [elaboratedDataPoint, elaboratedDataKeys]);

  if (graphItem.chartType === "Map" || graphItem.chartType === "Meter") return null;
  if (isLoading || isSensorLoading) return <div>Loading...</div>;
  if (!data && !sensorData) return <div>No data available</div>;

  const labels = sensorData?.labels || _labels;
  const datapoints = sensorData?.datapoints || _datapoints;

  if (typeof labels[0] === "string" && !(graphItem.dataIndex === SENSOR_KEY && !isCircularChart)) {
    return (
      <TableBody
        title={graphItem.dataIndex === SENSOR_KEY ? "Property" : graphItem.dataKey}
        extras={["Value"]}
      >
        {labels.map((label, index) => (
          <div key={label} className="flex items-center gap-4 mb-1 px-3">
            <div
              style={{ backgroundColor: colorAtI(index, graphItem.colorScheme) }}
              className="w-6 h-4 rounded-md"
            />
            <p key={label} className="w-48 truncate ">
              {label}
            </p>
            <p>{formattedNumber(datapoints[index] || 0)}</p>
          </div>
        ))}
      </TableBody>
    );
  }

  if (sensorData?.aggregations) {
    return (
      <TableBody title="Property" extras={["min", "max", "avg", "latest", "sum"]}>
        {Object.entries(sensorData.aggregations).map(([key, value], index) => (
          <div key={key} className="flex items-center gap-4 mb-1 px-3">
            <div
              style={{ backgroundColor: colorAtI(index, graphItem.colorScheme) }}
              className="w-6 h-4 rounded-md"
            />
            <p key={key} className="w-48 truncate ">
              {key}
            </p>
            {Object.entries(value).map(([aggkey, aggvalue]) => (
              <p key={aggkey} className="w-12 truncate ">
                {formattedNumber(aggvalue.value)}
              </p>
            ))}
          </div>
        ))}
      </TableBody>
    );
  }

  if (elaboratedDataKeys.length > 0) {
    return (
      <TableBody title={graphItem.dataKey} extras={["count"]}>
        {elaboratedDataKeys.map((key, index) => (
          <div className="flex items-center gap-4 mb-1 px-3 py-0.5">
            <div style={{ backgroundColor: colorAtI(index) }} className="w-6 h-4 rounded-md" />
            <p key={key} className="w-48 truncate ">
              {key}
            </p>
            <p>{formattedNumber(elaboratedPointArray[index] || 0)}</p>
          </div>
        ))}
      </TableBody>
    );
  }

  return <div>No data available</div>;
};

const TableBody = ({
  children,
  title,
  extras
}: {
  children: React.ReactNode;
  title: string;
  extras: string[];
}) => {
  return (
    <div className="mt-6 mb-3 border border-solid border-gray-300 dark:border-gray-500 rounded-md divide-y divide-gray-300 dark:divide-gray-500">
      <div className=" mb-1 flex gap-4 pt-1">
        <div className="w-9 h-4 rounded-md" />
        <p className="w-48 truncate capitalize  text-gray-600 dark:text-gray-500 text-sm font-medium">
          {title}
        </p>
        {extras.map((item) => (
          <p
            key={item}
            className="w-12 truncate capitalize text-gray-600 dark:text-gray-500 text-sm font-medium"
          >
            {item}
          </p>
        ))}
      </div>
      {children}
    </div>
  );
};

export default ChartRendererTable;
