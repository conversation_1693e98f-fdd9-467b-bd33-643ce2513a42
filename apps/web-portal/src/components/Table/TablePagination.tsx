import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon
} from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";
import { Button } from "@components/shadcn/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@components/shadcn/components/selelct";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { MODE } from "@api/index";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
}
export const PAGE_LIMIT = MODE === "dev" ? [10, 25, 50, 100, 200, 500, 1000] : [10, 25, 50, 100];

export function TablePagination<TData>({ pagination }: DataTablePaginationProps<TData>) {
  return (
    <div className="flex ml-auto items-center space-x-6 lg:space-x-8">
      <div className="flex items-center space-x-2">
        <p className="text-sm font-medium">Rows per page</p>
        <Select
          defaultValue={String(DEFAULT_PAGE_COUNT)}
          onValueChange={(value) => {
            pagination.setLimit(Number(value));
            pagination.setPage(1);
          }}
        >
          <SelectTrigger className="h-8 w-[5rem]">
            <SelectValue placeholder={PAGE_LIMIT[1]} />
          </SelectTrigger>
          <SelectContent>
            {PAGE_LIMIT.map((pageSize) => (
              <SelectItem key={pageSize} defaultValue={PAGE_LIMIT[1]} value={`${pageSize}`}>
                {pageSize}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="flex w-[100px] items-center justify-center text-sm font-medium">
        Page {pagination.page || 1} of {pagination.totalPages || 1}
      </div>
      <div className="flex items-center space-x-2">
        <Button
          type="button"
          variant="outline"
          className="hidden h-8 w-8 p-0 lg:flex"
          onClick={() => pagination?.setPage(1)}
          disabled={pagination.page <= 1}
        >
          <span className="sr-only">Go to first page</span>
          <DoubleArrowLeftIcon className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          className="h-8 w-8 p-0"
          onClick={() => pagination?.setPage(pagination.page - 1)}
          disabled={pagination.page <= 1}
        >
          <span className="sr-only">Go to previous page</span>
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          className="h-8 w-8 p-0"
          onClick={() => pagination?.setPage(pagination.page + 1)}
          disabled={pagination.page === pagination.totalPages}
        >
          <span className="sr-only">Go to next page</span>
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          className="hidden h-8 w-8 p-0 lg:flex"
          onClick={() => pagination?.setPage(pagination.totalPages)}
          disabled={pagination.page === pagination.totalPages}
        >
          <span className="sr-only">Go to last page</span>
          <DoubleArrowRightIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
