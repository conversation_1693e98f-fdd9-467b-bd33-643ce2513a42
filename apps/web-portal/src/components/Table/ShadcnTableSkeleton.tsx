import { TableCell, TableRow } from "@components/shadcn/components/table";

const ShadcnTableSkeleton = ({ rowCount = 8 }: { rowCount?: number }) => {
  return (
    <>
      {[...Array(rowCount)].map((_, i) => (
        <TableRow className="animate-pulse" key={i}>
          <TableCell className="h-11  heading-3">{i + 1}</TableCell>
          <TableCell colSpan={100} className="h-11 text-center">
            <div className="rounded w-full h-3 bg-gray-500/40" />
          </TableCell>
        </TableRow>
      ))}
    </>
  );
};

export default ShadcnTableSkeleton;
