import React, { useState } from "react";
import clsx from "clsx";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@components/shadcn/components/dropdown-menu";
import { Button } from "@components/shadcn/components/button";
import { ArrowDownIcon, ArrowUpIcon } from "lucide-react";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { cn } from "@components/shadcn/utils";

const sortOrders = ["asc", "desc"];

const ShadTableHead = ({
  children,
  onSort,
  className
}: {
  children: React.ReactNode;
  onSort?: (order: string) => void;
  dense?: boolean;
  className?: string;
}) => {
  const [sortOrder, setSortOrder] = useState(-1);

  // const handleSort = () => {
  //   const newSortOrder = (sortOrder + 1) % sortOrders.length;
  //   onSort(sortOrders[newSortOrder]);
  //   setSortOrder(newSortOrder);
  // };

  return (
    <th
      scope="col"
      className={clsx("h-10 heading-4  px-4", onSort && "cursor-pointer")}
      // onClick={() => onSort && handleSort()}
    >
      <span className={clsx("flex items-center  gap-2 child:min-w-fit min-w-max text-sm")}>
        {onSort ? (
          <div className={cn("flex items-center space-x-2 ", className)}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="-ml-3 bg:accent hover:bg-accent-foreground hover:text-accent dark:hover:bg-accent dark:hover:text-accent-foreground h-8 data-[state=open]:bg-accent-foreground  dark:data-[state=open]:bg-accent focus-visible:!ring-0"
                >
                  <span>{children}</span>
                  {sortOrders[sortOrder] === "desc" ? (
                    <ArrowDownIcon className="ml-2 h-4 w-4" />
                  ) : sortOrders[sortOrder] === "asc" ? (
                    <ArrowUpIcon className="ml-2 h-4 w-4" />
                  ) : (
                    <CaretSortIcon className="ml-2 h-4 w-4" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem
                  onClick={() => {
                    onSort(sortOrders[0]);
                    setSortOrder(0);
                  }}
                >
                  <ArrowUpIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                  Asc
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    onSort(sortOrders[1]);
                    setSortOrder(1);
                  }}
                >
                  <ArrowDownIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                  Desc
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ) : (
          children
        )}

        {/* {onSort &&
          (sortOrders[sortOrder] === "asc" ? (
            <ArrowDown size={BUTTON_ICON_SIZE} />
          ) : sortOrders[sortOrder] === "desc" ? (
            <ArrowUp size={BUTTON_ICON_SIZE} />
          ) : (
            <Code className="rotate-90" size={BUTTON_ICON_SIZE} />
          ))} */}
      </span>
    </th>
  );
};
export default ShadTableHead;
