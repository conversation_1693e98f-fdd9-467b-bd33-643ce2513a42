/* eslint-disable max-len */
import { useRemainingHeight } from "@hooks/useRemaningHeight";
import clsx from "clsx";
import React from "react";
import { TablePagination } from "./TablePagination";

type Props = {
  body: React.ReactNode;
  head?: React.ReactNode;
  className?: string;
  wrapperClassName?: string;
  endLoader?: React.ReactNode;
  pagination?: {
    page: number;
    setPage: any;
    setLimit: any;
    totalPages: number;
  };
  resizable?: boolean;
  checkable?: Record<string, any>[];
};

function Table({
  head,
  body,
  pagination,
  className,
  endLoader,
  checkable,
  resizable,
  wrapperClassName
}: Props): React.ReactElement {
  // Calculate bottom padding based on what's below the table
  const bottomPadding = pagination
    ? 80 // Space for pagination (48px) + gap (16px) + extra padding (16px)
    : checkable
      ? 60 // Space for checkable info + gap + padding
      : 40; // Default padding for other elements

  const { ref, remainingHeight } = useRemainingHeight(undefined, bottomPadding);

  return (
    <div className={clsx("space-y-4", wrapperClassName)}>
      <div
        ref={ref}
        className={clsx(
          "grid grid-cols-1 relative overflow-x-auto table-style card-border",
          className
        )}
        style={{
          maxHeight: resizable === false ? "none" : `${remainingHeight}px`
        }}
      >
        <table>
          <thead
            // style={{
            //   backgroundImage: `linear-gradient(to top, ${
            //     colors[color][darkMode ? 600 : 700]
            //   }, ${colors[color][darkMode ? 500 : 600]})`,
            // }}
            className="  shadow-[0_.5px_0_0_rgb(209,213,219)] dark:shadow-[0_1px_0_0_rgb(31,41,55)] bg-secondary sticky top-0 z-10"
          >
            <tr className="table-head">{head}</tr>
          </thead>
          <tbody className="table-body-text">{body}</tbody>
        </table>
        {endLoader}
      </div>

      <div className="flex items-center   ">
        {checkable && (
          <div className="flex-1 text-sm text-muted-foreground">
            {checkable.length} row(s) selected.
          </div>
        )}
        {pagination && <TablePagination pagination={pagination} />}
      </div>
    </div>
  );
}

export default Table;
