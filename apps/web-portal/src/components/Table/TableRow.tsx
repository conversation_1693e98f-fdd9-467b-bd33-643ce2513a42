import React, { HTMLAttributes } from "react";
import clsx from "clsx";
import { TableCell } from "@components/shadcn/components/table";

type Props = Omit<HTMLAttributes<HTMLTableCellElement>, "title"> & {
  children: React.ReactNode;
  dense?: boolean;
  title?: boolean;
  colSpan?: number;
};

const TableRow = ({ children, title, className,colSpan ,...props }: Props) => {
  return (
    <TableCell
      colSpan={colSpan}
      className={clsx(
        "p-4 px-4 !font-medium  text-sm text-muted-foreground truncate",
        title && " !text-foreground",
        className
      )}
      {...props}
    >
      {children ?? "N/A"}
    </TableCell>
  );
};

export default TableRow;
