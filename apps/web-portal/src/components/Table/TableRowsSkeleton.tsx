import TableRow from "./TableRow";

const TableRowsSkeleton = ({
  rowCount = 20,
  colCount,
  dense
}: {
  rowCount?: number;
  colCount?: number;
  dense?: boolean;
}) => {
  return (
    <>
      {[...Array(rowCount)].map((_, i) => (
        <tr className="animate-pulse" key={`${i}-${rowCount}`}>
          <TableRow dense={dense}>{i + 1}</TableRow>
          {colCount ? (
            [...Array(colCount)].map((__, j) => (
              <td className="px-4">
                <div
                  className="rounded w-full h-3 bg-gray-500/40"
                  aria-label={`Loading cell content for row ${i + 1}, column ${j + 1}`}
                />
              </td>
            ))
          ) : (
            <td colSpan={100} className="pr-4">
              <div
                className="rounded w-full h-3 bg-gray-500/40"
                aria-label={`Loading cell content for row ${i + 1}`}
              />
            </td>
          )}
        </tr>
      ))}
    </>
  );
};

export default TableRowsSkeleton;
