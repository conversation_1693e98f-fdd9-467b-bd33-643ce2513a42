import React, { useState } from "react";

import { Button } from "@components/shadcn/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@components/shadcn/components/dropdown-menu";
import { TableHead as ShadcnTableHead } from "@components/shadcn/components/table";
import { cn } from "@components/shadcn/utils";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { ArrowDownIcon, ArrowUpIcon } from "lucide-react";
import clsx from "clsx";

const sortOrders = ["asc", "desc"];

const TableHead = ({
  children,
  onSort,
  className,
  center
}: {
  children: React.ReactNode;
  onSort?: (order: string) => void;
  className?: string;
  center?: boolean;
}) => {
  const [sortOrder, setSortOrder] = useState(-1);

  // const handleSort = () => {
  //   const newSortOrder = (sortOrder + 1) % sortOrders.length;
  //   onSort(sortOrders[newSortOrder]);
  //   setSortOrder(newSortOrder);
  // };

  return (
    <ShadcnTableHead className={clsx("uppercase text-sm", center ? "!text-center" : "")}>
      {onSort ? (
        <div className={cn("flex items-center space-x-2 ", className)}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="-ml-3 bg:accent text-sm font-semibold uppercase hover:bg-gray-200  dark:hover:bg-gray-900 dark:hover:text-accent-foreground h-8 data-[state=open]:bg-gray-200  dark:data-[state=open]:bg-gray-900 focus-visible:!ring-0"
              >
                <span>{children}</span>
                {sortOrders[sortOrder] === "desc" ? (
                  <ArrowDownIcon className="ml-2 h-4 w-4" />
                ) : sortOrders[sortOrder] === "asc" ? (
                  <ArrowUpIcon className="ml-2 h-4 w-4" />
                ) : (
                  <CaretSortIcon className="ml-2 h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem
                onClick={() => {
                  onSort(sortOrders[0]);
                  setSortOrder(0);
                }}
              >
                <ArrowUpIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Asc
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  onSort(sortOrders[1]);
                  setSortOrder(1);
                }}
              >
                <ArrowDownIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Desc
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ) : (
        children
      )}

      {/* {onSort &&
          (sortOrders[sortOrder] === "asc" ? (
            <ArrowDown size={BUTTON_ICON_SIZE} />
          ) : sortOrders[sortOrder] === "desc" ? (
            <ArrowUp size={BUTTON_ICON_SIZE} />
          ) : (
            <Code className="rotate-90" size={BUTTON_ICON_SIZE} />
          ))} */}
    </ShadcnTableHead>
  );
};

export default TableHead;
