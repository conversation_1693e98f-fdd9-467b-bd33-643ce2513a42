import React from "react";
import { default as <PERSON><PERSON><PERSON>nac<PERSON>bar } from "@mui/material/Snackbar";
import Slide from "@mui/material/Slide";
import Alert from "@mui/material/Alert";

function Snackbar({ id, show, onClose, text, type }) {
  return (
    <MuiSnackbar
      open={show}
      onClose={onClose}
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      TransitionComponent={(props) => <Slide {...props} direction="down" />}
      autoHideDuration={6000}
      key={id}
    >
      <Alert onClose={onClose} severity={type} sx={{ width: "100%" }}>
        {text}
      </Alert>
    </MuiSnackbar>
  );
}

export default Snackbar;
