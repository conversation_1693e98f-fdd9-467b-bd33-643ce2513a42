import Step from "@mui/material/Step";
import StepConnector, { stepConnectorClasses } from "@mui/material/StepConnector";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
import { styled } from "@mui/material/styles";
import { darkColors } from "@utils/color";

const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: "1.125rem"
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage: `linear-gradient(to right, ${darkColors.brandColors}, ${darkColors.brandLightColors})`
    }
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: darkColors.brandColors
    }
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: ".3rem",

    border: 0,
    backgroundColor: theme.palette.mode === "dark" ? "#9ea6b0" : "#726d6c",
    borderRadius: 0
  }
}));

const ColorlibStepIconRoot = styled("div")(({ theme, ownerState }) => ({
  backgroundColor: theme.palette.mode === "dark" ? "#9ea6b0" : "#726d6c",
  zIndex: 1,
  color: "#fff",
  width: "2.5rem",
  height: "2.5rem",
  display: "flex",
  borderRadius: "50%",
  justifyContent: "center",
  alignItems: "center",
  ...(ownerState.active && {
    backgroundColor: darkColors.brandLightColors,
    color: "black",
    boxShadow: "0 4px 10px 0 rgba(0,0,0,.25)"
  }),
  ...(ownerState.completed && {
    color: "black",
    backgroundColor: darkColors.brandColors
  })
}));

function ColorlibStepIcon(props) {
  const { active, completed, className, icon, Icon } = props;

  return (
    <ColorlibStepIconRoot ownerState={{ completed, active }} className={className}>
      {/* {completed ? <Check size="1.5rem" /> : icon} */}
      <Icon size="1.3rem" />
    </ColorlibStepIconRoot>
  );
}

const StepperBar = ({ activeStep, steps }) => {
  const newSteps = steps.filter((item) => !!item);

  return (
    <Stepper
      alternativeLabel
      activeStep={activeStep}
      sx={{ width: "100%" }}
      connector={<ColorlibConnector />}
    >
      {newSteps.map((step, i) => (
        <Step key={step.label}>
          <StepLabel StepIconComponent={(props) => ColorlibStepIcon({ ...props, Icon: step.Icon })}>
            {step.label}
          </StepLabel>
        </Step>
      ))}
    </Stepper>
  );
};

export default StepperBar;
