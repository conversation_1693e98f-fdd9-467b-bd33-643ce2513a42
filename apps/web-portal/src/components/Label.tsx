import { alpha } from "@mui/material";
import clsx from "clsx";
import { ReactElement, ReactNode } from "react";
import colors from "tailwindcss/colors";

type LabelType = {
  icon?: ReactElement;
  text: string | ReactNode;
  color?: keyof typeof colors;
  lowercase?: boolean;
  className?: string;
};
const getEventColor = (color) => {
  if (color) {
    return colors[color][500];
  }
  return colors.grey[500];
};
const Label = ({
  icon,
  text,
  color = "blue",
  lowercase = false,
  className = "",
  ...props
}: LabelType) => {
  const colorCode = getEventColor(color);

  return (
    <div
      className={clsx("label py-1 text-center rounded-lg", className)}
      style={{
        backgroundColor: alpha(colorCode, 0.1)
      }}
      {...props}
    >
      <p className="text-xs font-medium" style={{ color: colorCode }}>
        {text}
      </p>
    </div>
  );
};

export default Label;
