import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { colors } from "@mui/material";
import { useAppSelector } from "@src/store";
import { rankItem } from "@tanstack/match-sorter-utils";
import { GEO_PRODUCT_VAR, GEO_THING_VAR, PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { ALL_ROLES, MSP_ROLES, OPERATOR_ROLES, TENANT_ROLES } from "@utils/utilities";
import clsx from "clsx";
import {
  Activity,
  Airplay,
  AlertTriangle,
  Archive,
  Bell,
  BookMarked,
  BrainCircuit,
  Bus,
  ClipboardList,
  Command,
  CornerDownLeft,
  Cpu,
  Database,
  DownloadCloud,
  Factory,
  Folder,
  History,
  Home,
  Layout,
  LocateFixed,
  Route,
  School,
  Search,
  Settings,
  Shield,
  Sliders,
  Truck,
  User,
  UserPen,
  UserRound,
  <PERSON>,
  Wrench
} from "lucide-react";
import { useContext, useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { DarkModeContext } from "../hooks/useDarkMode";
import useDebounce from "../hooks/useDebounce";

import Input from "./Input";
import { Dialog, DialogContent, DialogTrigger } from "./shadcn/components/dialog";
import { ScrollArea } from "./shadcn/components/scroll-area";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";

const DEFAULT_OPTIONS = [
  { title: "Home", to: "/", Icon: Layout, availableRoles: ALL_ROLES },
  {
    title: `${THING_VAR} Management`,
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${PRODUCT_VAR}s`,
    to: "/products",
    Icon: Archive,
    availableRoles: TENANT_ROLES,
    featureName: "product"
  },
  {
    title: `${THING_VAR}s`,
    to: "/things",
    Icon: Cpu,

    availableRoles: TENANT_ROLES,
    featureName: "thing"
  },
  // { title: "Gateways", to: "/Gateway", Icon: LogIn, availableRoles: TENANT_ROLES },
  {
    title: `${THING_VAR}s Inventory`,
    to: "/inventory",
    Icon: ClipboardList,
    availableRoles: TENANT_ROLES,
    featureName: "inventory"
  },
  {
    title: "Dynamic Groups",
    to: "/thingGroup/dynamic",
    Icon: Database,
    featureName: "thingGroup",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Static Groups",
    to: "/thingGroup/static",
    Icon: Database,
    featureName: "thingGroup",
    availableRoles: TENANT_ROLES
  },

  {
    title: "Root Certificate",
    to: "/security/certificateAuth",
    featureName: "tls",
    Icon: Shield,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${THING_VAR} Certificate`,
    to: "/security/certificate",
    featureName: "tls",
    Icon: Shield,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Policy Template",
    to: "/security/template",
    featureName: "policy",
    Icon: Shield,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${THING_VAR} Policy`,
    to: "/security/policy",
    Icon: Shield,
    featureName: "policy",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Authentication",
    to: "/security/authentication",
    Icon: Shield,
    featureName: "authentication",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Protocol Services",
    to: "/security/protocol_Services",
    Icon: Shield,
    featureName: "listners",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Operations",
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: "OTA Releases",
    to: "/jobs/ota",
    featureName: "ota",
    Icon: DownloadCloud,
    availableRoles: TENANT_ROLES
  },
  {
    title: `Bulk ${THING_VAR}`,
    to: "/jobs/bulk-things",
    featureName: "thing",
    Icon: DownloadCloud,
    availableRoles: TENANT_ROLES
  },
  {
    title: `Bulk Inventory ${THING_VAR}`,
    to: "/jobs/inventory-bulk-things",
    featureName: "inventory",
    Icon: DownloadCloud,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Rules",
    to: "/actions/rules",
    featureName: "rules",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Workflow",
    to: "/actions/workflow",
    featureName: "rules",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },

  {
    title: "Destinations",
    to: "/actions/destinations",
    featureName: "destinations",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Credentials",
    to: "/actions/integrations",
    featureName: "destinations",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Dashboard",
    to: "/monitor/dashboard",
    featureName: "stats",
    Icon: Airplay,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Logs",
    to: "/monitor/logs",
    featureName: "stats",
    Icon: Airplay,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Sites",
    to: "/processIQ/list",
    featureName: "site",
    Icon: Factory,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Assets",
    to: "/processIQ/assets",
    featureName: "site",
    Icon: Factory,
    availableRoles: TENANT_ROLES
  },
  {
    title: "AI Agent",
    to: "/advance/ai-agent",
    featureName: "site",
    Icon: BrainCircuit,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Digital Twin",
    to: "/advance/digital-twin",
    featureName: "site",
    Icon: BrainCircuit,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Contacts",
    to: "/notifications/contacts",
    featureName: "contacts",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Escalations",
    to: "/notifications/escalations",
    featureName: "escalations",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Templates",
    to: "/notifications/templates",
    featureName: "notificationTemplates",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "History",
    to: "/notifications/history",
    featureName: "notificationHistory",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Category",
    to: "/incidents/category",
    featureName: "incidents",
    Icon: AlertTriangle,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Tickets",
    to: "/incidents/tickets",
    featureName: "incidents",
    Icon: AlertTriangle,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Administration",
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: "White Labeling",
    to: "/settings/white-labeling",
    featureName: "whiteLabel",
    Icon: Settings,
    availableRoles: TENANT_ROLES
  },
  {
    title: "App Users",
    to: "/app-users",
    featureName: "appUser",
    Icon: Users,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Users",
    to: "/UM/users",
    featureName: "tntUsers",
    Icon: User,
    availableRoles: TENANT_ROLES
  },
  {
    title: "User Groups",
    to: "/UM/userGroups",
    featureName: "tntUsers",
    Icon: User,
    availableRoles: TENANT_ROLES
  },
  {
    title: "API Keys",
    to: "/UM/apiKeys",
    featureName: "tntKey",
    Icon: User,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Usage",
    to: "/usage",
    Icon: Sliders,
    availableRoles: TENANT_ROLES,
    featureName: "stats"
  },
  {
    title: "All Users",
    to: "/UM/users",
    Icon: User,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "Operator Users",
    to: "/UM/opUsers",
    Icon: User,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "User Groups",
    to: "/UM/userGroups",
    Icon: User,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "Platform Groups",
    to: "/UM/platformGroups",
    Icon: User,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "MSPs",
    to: "/mspManagement/msps",
    Icon: User,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "MSP Users",
    to: "/mspManagement/mspUsers",
    Icon: User,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "Tenants",
    to: "/TM/tenants",
    Icon: Users,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "Tenant Users",
    to: "/TM/tenantUsers",
    Icon: Users,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "Tenants Health",
    to: "/tenant-health",
    Icon: Activity,
    availableRoles: OPERATOR_ROLES
  },
  {
    title: "MSP Users",
    to: "/UM/mspUsers",
    featureName: "mspUSers",
    availableRoles: MSP_ROLES,
    Icon: User
  },
  {
    title: "User Groups",
    to: "/UM/userGroups",
    featureName: "mspUSers",
    availableRoles: MSP_ROLES,
    Icon: User
  },
  {
    title: "API Keys",
    to: "/UM/apiKeys",
    featureName: "mspKey",
    availableRoles: MSP_ROLES,
    Icon: User
  },
  {
    title: "Tenants",
    Icon: Users,
    to: "/TM/tenants",
    availableRoles: MSP_ROLES
  },
  {
    title: "Tenants Access",
    Icon: UserPen,
    to: "/tenants-access",
    availableRoles: MSP_ROLES
  },
  {
    title: "Logs",
    Icon: Airplay,
    to: "/logs",
    availableRoles: MSP_ROLES
  },
  {
    title: "Products Templates",
    to: "/templates/product-templates",
    Icon: Folder,
    availableRoles: [...OPERATOR_ROLES, ...MSP_ROLES]
  },
  {
    title: "Simulator Templates",
    to: "/templates/simulator-templates",
    Icon: Folder,
    availableRoles: [...OPERATOR_ROLES, ...MSP_ROLES]
  },

  { title: "Profile", to: "/profile", Icon: User, availableRoles: ALL_ROLES },
  { title: "Organization", to: "/organization", Icon: Settings, availableRoles: TENANT_ROLES }
];
const GEO_OPTIONS = [
  { title: "Home", to: "/", Icon: Home, availableRoles: ALL_ROLES },
  {
    title: `${GEO_THING_VAR} Management`,
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${GEO_THING_VAR}s`,
    to: "/things",
    Icon: Truck,
    availableRoles: TENANT_ROLES,
    featureName: "geoThing",
    shouldShow: (subFeature) => subFeature == "fleetManagement"
  },
  {
    title: "Classes",
    to: "/classes",
    Icon: School,
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) => subFeature === "studentSafety"
  },
  {
    title: "Buses",
    to: "/buses-list",
    Icon: Bus,
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) => subFeature === "studentSafety"
  },

  {
    title: `${GEO_PRODUCT_VAR}s`,
    to: "/products",
    Icon: BookMarked,
    availableRoles: TENANT_ROLES,
    featureName: "geoProduct"
  },

  {
    title: "Geofences",
    to: "/geofences",
    Icon: LocateFixed,
    availableRoles: TENANT_ROLES,
    featureName: "geofence"
  },
  {
    title: "History",
    to: "/history",
    Icon: History,
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) =>
      subFeature === "fleetManagement" || subFeature === "basicAssetTracking"
  },
  {
    title: "Drivers",
    to: "/drivers",
    Icon: UserRound,
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) => subFeature === "fleetManagement" || subFeature === "studentSafety"
  },
  {
    title: "Routes",
    to: "/routes",
    Icon: Route,
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) => subFeature === "fleetManagement" || subFeature === "studentSafety"
  },
  {
    title: "Dynamic Groups",
    to: "/thingGroup/dynamic",
    Icon: Database,
    featureName: "thingGroup",
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) => subFeature === "fleetManagement"
  },
  {
    title: "Static Groups",
    to: "/thingGroup/static",
    Icon: Database,
    featureName: "thingGroup",
    availableRoles: TENANT_ROLES,
    shouldShow: (subFeature) => subFeature === "fleetManagement"
  },
  {
    title: "Root Certificate",
    to: "/security/certificateAuth",
    featureName: "tls",
    Icon: Shield,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${THING_VAR} Certificate`,
    to: "/security/certificate",
    featureName: "tls",
    Icon: Shield,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Policy Template",
    to: "/security/template",
    featureName: "policy",
    Icon: Shield,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${THING_VAR} Policy`,
    to: "/security/policy",
    Icon: Shield,
    featureName: "policy",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Authentication",
    to: "/security/authentication",
    Icon: Shield,
    featureName: "authentication",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Protocol Services",
    to: "/security/protocol_Services",
    Icon: Shield,
    featureName: "listners",
    availableRoles: TENANT_ROLES
  },
  {
    title: "Operations",
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: "OTA Releases",
    to: "/jobs/ota",
    featureName: "ota",
    Icon: DownloadCloud,
    availableRoles: TENANT_ROLES
  },
  {
    title: `Bulk ${THING_VAR}`,
    to: "/jobs/bulk-things",
    featureName: "thing",
    Icon: DownloadCloud,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Rules",
    to: "/actions/rules",
    featureName: "rules",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Workflow",
    to: "/actions/workflow",
    featureName: "rules",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },

  {
    title: "Destinations",
    to: "/actions/destinations",
    featureName: "destinations",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Credentials",
    to: "/actions/integrations",
    featureName: "destinations",
    Icon: Wrench,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Dashboard",
    to: "/monitor/dashboard",
    featureName: "stats",
    Icon: Airplay,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Logs",
    to: "/monitor/logs",
    featureName: "stats",
    Icon: Airplay,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Contacts",
    to: "/notifications/contacts",
    featureName: "contacts",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Escalations",
    to: "/notifications/escalations",
    featureName: "escalations",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Templates",
    to: "/notifications/templates",
    featureName: "notificationTemplates",
    Icon: Bell,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Administration",
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: "White Labeling",
    to: "/settings/white-labeling",
    featureName: "whiteLabel",
    Icon: Settings,
    availableRoles: TENANT_ROLES
  },
  {
    title: "App Users",
    to: "/app-users",
    featureName: "appUser",
    Icon: Users,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Users",
    to: "/UM/users",
    featureName: "tntUsers",
    Icon: User,
    availableRoles: TENANT_ROLES
  },
  {
    title: "User Groups",
    to: "/UM/userGroups",
    featureName: "tntUsers",
    Icon: User,
    availableRoles: TENANT_ROLES
  },
  {
    title: "API Keys",
    to: "/UM/apiKeys",
    featureName: "tntKey",
    Icon: User,
    availableRoles: TENANT_ROLES
  },
  { title: "Profile", to: "/profile", Icon: User, availableRoles: ALL_ROLES },
  { title: "Organization", to: "/organization", Icon: Settings, availableRoles: TENANT_ROLES }
];

const fuzzyFilter = (match, subCategory, value) => {
  const itemRank = rankItem([match, subCategory], value);
  return itemRank.passed;
};

const GlobalSearch = () => {
  const [searchedText, setSearchedText] = useState("");
  const [searchFocused, setSearchFocused] = useState(false);
  const [darkMode] = useContext(DarkModeContext);
  const navigate = useCustomNavigate();
  const popoverRef = useRef(null);
  const searchRef = useRef(null);
  const debouncedSearchText = useDebounce(searchedText, 500);
  const [selectedResult, setSelectedResult] = useState(0);
  const [isMouseOverList, setIsMouseOverList] = useState(false);
  const user = useAppSelector((state) => state.user.user);
  const tenant = useAppSelector((state) => state.user.tenant);
  const { partnerName, featureType } = useParams();
  const isTenantView = Boolean(partnerName) && Boolean(featureType);
  const { data: permissions } = useUserGroupPermissions();
  const options = useMemo(() => {
    const searchOptions = (
      tenant?.featureType === "geo" || featureType === "geo" ? GEO_OPTIONS : DEFAULT_OPTIONS
    ).filter((option) => {
      const role = isTenantView ? "TenantSuperUser" : user?.role;
      if (!option.availableRoles.includes(role)) return false;
      if (option.shouldShow) {
        return option.shouldShow(tenant?.subFeature);
      }

      if (option.featureName) {
        return (
          permissions[option.featureName] === "write" || permissions[option.featureName] === "read"
        );
      }

      return true;
    });

    if (debouncedSearchText === "") {
      return searchOptions;
    }
    return searchOptions.filter(({ title, subCategory }) =>
      fuzzyFilter(title, subCategory, debouncedSearchText)
    );
  }, [debouncedSearchText, featureType]);

  useEffect(() => {
    // Scroll into view when the selectedResult changes
    if (selectedResult !== null && popoverRef.current) {
      const selectedElement = popoverRef.current.querySelector(
        `.dropdown-item:nth-child(${selectedResult + 1})`
      );

      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: "nearest"
        });
      } else if (options.length > 0 && selectedResult >= options.length) {
        setSelectedResult(0);
      }
    }
  }, [selectedResult, options.length]);

  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        setSearchFocused(false);
        return;
      }
    };

    const handleEscapeKey = (event) => {
      if (event.key === "Escape") {
        setSearchFocused(false);
        return;
      }
      if (event.key === "k" && (event.metaKey || event.ctrlKey)) {
        // Your action here
        setSearchFocused(true);
      }
    };

    if (searchFocused) {
      document.addEventListener("mousedown", handleOutsideClick);
    }
    document.addEventListener("keydown", handleEscapeKey);

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [searchFocused]);

  const handleSearchClick = (to) => {
    setSearchFocused(false);
    navigate(to);
    searchRef.current?.blur();
  };

  const handleKeyDown = (e) => {
    if (isMouseOverList) {
      return;
    }
    if (e.key === "ArrowUp" && selectedResult > 0) {
      setSelectedResult(selectedResult - 1);
    } else if (e.key === "ArrowDown" && selectedResult < options.length - 1) {
      setSelectedResult(selectedResult + 1);
    } else if (e.key === "Enter" && selectedResult !== null) {
      if (options[selectedResult].to) {
        handleSearchClick(options[selectedResult].to);
      }
    }
  };

  return (
    <Dialog open={searchFocused} onOpenChange={setSearchFocused}>
      <DialogTrigger asChild>
        <div className="w-60 h-10 cursor-pointer bg-transparent border  rounded-lg flex items-center justify-between px-4 gap-2">
          <div className="flex gap-4 items-center">
            <Search className="h-5 w-5 text-muted-foreground" />
            <span className="text-sm">Search</span>
          </div>

          <div className="flex items-center gap-1 h-8 text-sm px-3">
            <Command className="h-3 w-3" />
            <span>K</span>
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[35rem] p-0 gap-0">
        <div className="flex items-center border-b px-4 py-4">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100">
            <Search className="h-5 w-5 text-amber-600" />
          </div>
          <div className="ml-4 text-xl font-semibold">Search</div>
        </div>
        <div className="relative px-4 py-3">
          <Input
            value={searchedText}
            placeholder="Search for any page"
            onFocus={() => {
              setSearchFocused(true);
              setSelectedResult(0);
            }}
            ref={searchRef}
            onKeyDown={handleKeyDown}
            onChange={({ target: { value } }) => setSearchedText(value)}
          />
        </div>
        <ScrollArea className="max-h-[60vh] overflow-y-auto">
          <div
            className="px-2 py-2"
            onMouseEnter={() => setIsMouseOverList(true)}
            onMouseLeave={() => {
              setIsMouseOverList(false);
              setSelectedResult(0);
            }}
          >
            {options.length > 0 ? (
              options.map(({ title, to, Icon, isCategory }, i) => {
                const showInAction = i === selectedResult && !isMouseOverList;
                return isCategory ? (
                  <div key={title} className="px-4 py-2 text-sm font-medium text-muted-foreground">
                    {title}
                  </div>
                ) : (
                  <div
                    key={to}
                    onClick={() => handleSearchClick(to)}
                    className={clsx(
                      "flex w-full items-center gap-3 rounded-md p-4 text-sm transition-colors hover:bg-accent",
                      showInAction && "bg-accent/50"
                    )}
                  >
                    <div className="flex items-center gap-4">
                      {Icon && (
                        <Icon
                          className="h-5 w-5 mx-2"
                          color={darkMode ? colors.grey[200] : colors.grey[800]}
                        />
                      )}
                      <p className="heading-4"> {title}</p>
                    </div>
                    {showInAction && (
                      <CornerDownLeft
                        size={BUTTON_ICON_SIZE}
                        className="text-gray-600 dark:text-gray-300"
                      />
                    )}
                  </div>
                );
              })
            ) : (
              <div className="flex py-5 z-30 items-center justify-center">
                <p className="heading-6">No match found</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default GlobalSearch;
