import clsx from "clsx";
import { motion } from "framer-motion";
import { forwardRef } from "react";

const DEFAULT_VARIANT = {
  hidden: {
    scale: 0.85,
    opacity: 0,
    transition: {
      duration: 0.15,
      ease: "easeIn"
    }
  },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      duration: 0.15,
      ease: "easeIn"
    }
  }
};

type Props = import("framer-motion").HTMLMotionProps<"div">;

const Card: import("react").ForwardRefRenderFunction<HTMLDivElement, Props> = (
  { children, className, variants = DEFAULT_VARIANT, ...props },
  forwardedRef
) => {
  return (
    <motion.div
      variants={variants}
      initial="hidden"
      animate="visible"
      className={clsx("card", className)}
      ref={forwardedRef}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default forwardRef(Card);
