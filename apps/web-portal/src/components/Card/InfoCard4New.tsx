import React from "react";
import { StatCard } from "@components/ui";
import { LucideIcon } from "lucide-react";

// Color mapping from old system to new
const colorMapping = {
  green: "success",
  red: "danger", 
  blue: "info",
  yellow: "warning",
  purple: "primary",
  orange: "warning",
  rose: "danger",
} as const;

type OldColor = keyof typeof colorMapping;

interface InfoCard4NewProps {
  title: string;
  data?: string | number;
  icon?: React.ReactElement;
  color: OldColor;
  footer?: string;
  className?: string;
  selected?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

/**
 * New InfoCard4 component using the standardized StatCard
 * This is a drop-in replacement for the old InfoCard4 component
 * 
 * @deprecated Use StatCard directly instead of this wrapper
 */
const InfoCard4New: React.FC<InfoCard4NewProps> = ({
  title,
  data,
  icon,
  color,
  footer,
  className,
  selected,
  loading,
  onClick,
}) => {
  // Extract icon component from React element
  const getIconComponent = (iconElement?: React.ReactElement): LucideIcon | undefined => {
    if (!iconElement) return undefined;
    return iconElement.type as LucideIcon;
  };

  const iconComponent = getIconComponent(icon);
  const colorScheme = colorMapping[color] || "default";

  return (
    <StatCard
      title={title}
      value={data}
      icon={iconComponent}
      colorScheme={colorScheme}
      description={footer}
      selected={selected}
      loading={loading}
      onClick={onClick}
      className={className}
      layout="vertical"
    />
  );
};

export default InfoCard4New;

// Example usage comparison:
/*
// OLD WAY:
<InfoCard4
  title="Online Devices"
  data={1234}
  color="green"
  icon={<CircleCheck size="1.625rem" />}
  footer="85.2% uptime"
  selected={isSelected}
  onClick={handleClick}
/>

// NEW WAY (recommended):
<StatCard
  title="Online Devices"
  value={1234}
  colorScheme="success"
  icon={CircleCheck}
  description="85.2% uptime"
  selected={isSelected}
  onClick={handleClick}
/>

// MIGRATION WAY (temporary):
<InfoCard4New
  title="Online Devices"
  data={1234}
  color="green"
  icon={<CircleCheck size="1.625rem" />}
  footer="85.2% uptime"
  selected={isSelected}
  onClick={handleClick}
/>
*/
