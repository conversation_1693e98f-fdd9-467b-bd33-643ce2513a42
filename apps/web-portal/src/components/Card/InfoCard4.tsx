/* eslint-disable max-len */
import { alpha } from "@mui/material";
import clsx from "clsx";
import React from "react";
import colors from "tailwindcss/colors";
import Card from "./Card";

const getEventColor = (color: keyof typeof colors) => {
  if (color) {
    return colors[color][500];
  }
  return colors.gray[500];
};

type Props = Omit<import("react").HTMLProps<HTMLInputElement>, "data"> & {
  title: string;
  icon?: React.ReactElement;
  flexDirectionRow?: boolean;
  titleWidth?: string;
  footer?: string;
  color: keyof typeof colors;
  loading?: boolean;
  data?: string | number;
};

const intl = new Intl.NumberFormat("en");

function InfoCard4({
  color,
  title,
  data,
  icon,
  className,
  selected,
  footer,
  ...props
}: Props): React.ReactElement {
  const colorCode = getEventColor(color);

  return (
    <Card
      className={clsx(
        "group info-card-2 min-w-[200px] flex-1  p-3 relative overflow-hidden",
        "flex flex-col justify-between",
        props.onClick && "cursor-pointer",
        selected && "ring-2",
        className
      )}
      style={{
        "--tw-ring-color": `${colors[color][400]}80`
      }}
      {...props}
    >
      <div className="flex justify-between items-center ">
        {data === undefined ? (
          <section className="w-1/2 flex flex-col animate-pulse child:bg-gray-500/40 gap-2 space-y-">
            <div className="rounded h-4 w-full" />
            <div className="rounded h-12 w-8" />
            {footer && <div className="rounded w-8" />}
          </section>
        ) : (
          <section className="flex flex-col  ">
            <h4 className="heading-3 mb-2">{title}</h4>
            <span className="heading-lg  ">
              {typeof data === "number" ? intl.format(data) : data}
            </span>
            <p className="mt-3 min-h-6">{footer !== "NaN%" && footer}</p>
          </section>
        )}

        <div
          style={{ color: colorCode, backgroundColor: alpha(colorCode, 0.1) }}
          className={clsx(
            "w-14 h-14 center absolute right-4 top-[50%] -translate-y-[50%] p-4 duration-300 rounded-full group-hover:scale-105",
            "shadow-md text-white z-10 "
          )}
        >
          {icon}
        </div>
      </div>
    </Card>
  );
}

export default InfoCard4;
