import clsx from "clsx";

export const CardLoadingSkeleton = ({ col = 5, className = "" }) => {
  return (
    <div
      role="status"
      className={clsx(
        " p-3 w-1/2 space-y-3 divide-y divide-gray-200 rounded  animate-pulse dark:divide-gray-700 dark:border-gray-700",
        className
      )}
    >
      {[...Array(col).keys()].map((i) => (
        <div key={i} className="flex items-center justify-between pt-4">
          <div>
            <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5" />
            <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700" />
          </div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12" />
        </div>
      ))}
    </div>
  );
};
