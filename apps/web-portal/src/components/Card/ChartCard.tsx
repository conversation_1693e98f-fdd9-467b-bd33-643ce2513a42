import React, { useContext } from "react";
import Card from "./Card";
import colors from "tailwindcss/colors";
import { DarkModeContext } from "../../hooks/useDarkMode";
import { formatBytes } from "../../utils/misc";
import { formattedNumber } from "@src/pages/MonitorPage/utils";

const themeColors = [
  "blue",
  "red",
  "green",
  "purple",
  "orange",
  "teal",
  "amber",
  "sky",
  "rose",
  "indigo",
  "pink",
  "rose"
];

function ChartCard({ title, total, children, className = "", themePallet = 0 }) {
  const [darkMode, _] = useContext(DarkModeContext);
  return (
    <Card
      className={`!p-0  flex-1 ${className} flex flex-col justify-between dark:bg-transparent rounded-lg`}
    >
      <div className="px-4 py-3 flex gap-2 justify-between items-center    border-b shadow-sm dark:shadow-black/50 ">
        <div>
          {typeof title === "string" ? (
            <div className="flex items-center gap-2">
              <div
                style={{
                  background:
                    colors[themeColors[themePallet % themeColors.length]][darkMode ? 400 : 500]
                }}
                className="h-4 w-4 rounded-full"
              />
              <h4 className="heading-3">{title}</h4>
            </div>
          ) : (
            <div className="flex gap-6">
              {title.map((item, idx) => (
                <div className="flex items-center gap-2">
                  <div
                    key={item}
                    style={{
                      background:
                        colors[themeColors[themePallet[idx] % themeColors.length]][
                          darkMode ? 400 : 500
                        ]
                    }}
                    className="h-4 w-4 rounded-full"
                  />
                  <h4 className="heading-3">{item}</h4>
                </div>
              ))}
            </div>
          )}
        </div>

        <span className="heading-2 ">
          {(Array.isArray(title) ? title.some((t) => t.includes("Bytes")) : title.includes("Bytes"))
            ? formatBytes(total)
            : formattedNumber(total)}
        </span>
      </div>
      {/* <div className="rounded-xl"> */}
      <div className="h-48 rounded-md overflow-hidden">{children}</div>
    </Card>
  );
}

export default ChartCard;
