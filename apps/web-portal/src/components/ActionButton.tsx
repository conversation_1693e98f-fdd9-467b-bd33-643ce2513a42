import { CircularProgress, IconButton, IconButtonProps } from "@mui/material";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Edit, Trash } from "lucide-react";
import type { LucideIcon } from "lucide-react";

type Props = Omit<IconButtonProps, "type"> & {
  type?: "delete" | "edit" | "trash";
  Icon?: LucideIcon;
  iconSize?: number | string;
  visibleBg?: boolean;
  loading?: boolean;
};

const ActionButton = ({
  type,
  Icon = Edit,
  iconSize = BUTTON_ICON_SIZE,
  visibleBg,
  className,
  loading,
  ...props
}: Props) => {
  return (
    <IconButton
      className={clsx("!p-2", visibleBg && "!bg-accent-foreground/10 ", className)}
      {...props}
    >
      {loading ? (
        <CircularProgress size={iconSize} />
      ) : type === "delete" ? (
        <Trash size={iconSize} />
      ) : type === "edit" ? (
        <Edit size={iconSize} />
      ) : (
        <Icon size={iconSize} />
      )}
    </IconButton>
  );
};

export default ActionButton;
