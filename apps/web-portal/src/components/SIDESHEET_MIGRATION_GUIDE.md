# SideSheet Migration Guide

## Overview

This guide shows how to migrate from `FormDialog` (Dialog-based) to `SideSheet` (Sheet-based) for a proper sidebar experience.

## Key Differences

| Feature         | FormDialog (Dialog)      | SideSheet (Sheet)        |
| --------------- | ------------------------ | ------------------------ |
| **Behavior**    | Modal overlay            | Slides from side         |
| **Animation**   | Fade in/out              | Slide in/out             |
| **Positioning** | Center of screen         | From edge                |
| **Backdrop**    | Full screen overlay      | Partial overlay          |
| **Mobile UX**   | Takes full screen        | Slides from edge         |
| **Dropdowns**   | May have overflow issues | Renders properly outside |

## Migration Steps

### 1. Import Change

```tsx
// Before
import FormDialog from "@components/FormDialog";

// After
import SideSheet from "@components/SideSheet";
```

### 2. Component Props Comparison

```tsx
// FormDialog Props
interface SideDrawerProps {
  children: React.ReactNode;
  title?: string;
  open: boolean;
  onClose: () => void;
  description?: string;
  notDismissable?: boolean;
  footer?: React.ReactNode;
}

// SideSheet Props (Extended)
interface SideSheetProps {
  children: React.ReactNode;
  title?: string;
  open: boolean;
  onClose: () => void;
  description?: string;
  notDismissable?: boolean;
  footer?: React.ReactNode;
  // New props
  side?: "left" | "right" | "top" | "bottom";
  size?: "sm" | "md" | "lg" | "xl" | "full";
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;
  showCloseButton?: boolean;
  showBackdrop?: boolean; // NEW: Control backdrop visibility
}
```

### 3. Basic Migration Example

```tsx
// Before - FormDialog
<FormDialog
  open={isOpen}
  onClose={handleClose}
  title="Edit Widget"
>
  <div className="space-y-4">
    <Input label="Title" />
    <Dropdown options={options} />
  </div>
</FormDialog>

// After - SideSheet
<SideSheet
  open={isOpen}
  onClose={handleClose}
  title="Edit Widget"
  side="right"
  size="lg"
  showBackdrop={false}  // NEW: No backdrop behind sheet
>
  <div className="space-y-4">
    <Input label="Title" />
    <Dropdown options={options} />
  </div>
</SideSheet>
```

### 4. Advanced Migration with Footer

```tsx
// Before
<FormDialog
  open={isOpen}
  onClose={handleClose}
  title="Create Area"
  footer={
    <div className="flex gap-4 justify-end">
      <Button onClick={handleClose}>Cancel</Button>
      <Button onClick={handleSave}>Save</Button>
    </div>
  }
>
  {/* content */}
</FormDialog>

// After
<SideSheet
  open={isOpen}
  onClose={handleClose}
  title="Create Area"
  side="right"
  size="lg"
  footer={
    <>
      <Button variant="outline" onClick={handleClose}>Cancel</Button>
      <Button onClick={handleSave}>Save</Button>
    </>
  }
>
  {/* content */}
</SideSheet>
```

## Size Guidelines

| Size   | Width (Left/Right) | Height (Top/Bottom) | Use Case                    |
| ------ | ------------------ | ------------------- | --------------------------- |
| `sm`   | 384px (24rem)      | 300px               | Quick forms, simple content |
| `md`   | 448px (28rem)      | 400px               | Standard forms              |
| `lg`   | 512px (32rem)      | 500px               | Complex forms, default      |
| `xl`   | 576px (36rem)      | 600px               | Detailed configurations     |
| `full` | 100%               | 100%                | Full-screen editing         |

## Side Options

- **`right`** (default): Slides from right edge - best for forms and details
- **`left`**: Slides from left edge - good for navigation or filters
- **`top`**: Slides from top - good for notifications or quick actions
- **`bottom`**: Slides from bottom - good for mobile-first designs

## Files to Migrate

Based on the codebase analysis, these files need migration:

### Dashboard/Monitor Pages

1. `apps/web-portal/src/pages/MonitorPage/Components/DashboardConfigSidebar.tsx`
2. `apps/web-portal/src/pages/MonitorPage/layout/ShareDashboardLink.tsx`

### Site Pages

3. `apps/web-portal/src/pages/site/layouts/AddAreaDrawer.tsx`
4. `apps/web-portal/src/pages/site/layouts/AddAssetDrawer.tsx`
5. `apps/web-portal/src/pages/site/layouts/AddSiteWidget.tsx`
6. `apps/web-portal/src/pages/site/layouts/SiteTriggersSideBar.tsx`
7. `apps/web-portal/src/pages/site/layouts/SiteAutomationFlow.tsx`

### Other

8. `apps/web-portal/src/pages/RulePage/DestinationPage/CreateDestinationSideBar.tsx`

## Benefits After Migration

1. **Better UX**: Proper sidebar behavior instead of modal
2. **Mobile Friendly**: Slides from edge on mobile devices
3. **Dropdown Fix**: Dropdowns render outside sheet boundaries
4. **Consistent Design**: Follows shadcn/ui design patterns
5. **Better Performance**: Sheet animations are more performant
6. **Accessibility**: Better screen reader support

## Testing Checklist

After migration, test:

- [ ] Sheet opens/closes properly
- [ ] Dropdowns render outside sheet boundaries
- [ ] Mobile responsiveness
- [ ] Keyboard navigation (ESC key)
- [ ] Click outside to close (if dismissable)
- [ ] Footer buttons work correctly
- [ ] Content scrolling works
- [ ] Form submissions work
