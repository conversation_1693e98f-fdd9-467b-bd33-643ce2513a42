import clsx from "clsx"
import { forwardRef, useEffect, useState } from "react"
import Label from "./Label"

const Toggle = forwardRef(
  ({ enabled, className, children, ...props }, forwardedRef) => {
    const [enabledState, setEnabledState] = useState(enabled)

    useEffect(() => {
      setEnabledState(enabled)
    }, [enabled])

    return (
      <label
        className={clsx(
          "toggle",
          enabledState && "toggle-enabled",
          "flex flex-col gap-2 w-full h-full",
          !props.disabled && "cursor-pointer",
          className
        )}
      >
        {children}
        {/* {enabledState ? (
          <Label color="green" text="enabled" className="mx-auto" invert />
        ) : (
          <Label color="gray" text="disabled" className="mx-auto" invert />
        )} */}
        <input
          type="checkbox"
          className="hidden"
          ref={forwardedRef}
          {...props}
          onChange={(e) => {
            if (props.onChange) {
              setEnabledState(e.target.checked)
              props.onChange(e)
            }
          }}
        />
      </label>
    )
  }
)

export default Toggle
