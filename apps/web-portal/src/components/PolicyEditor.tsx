import { DarkModeContext } from "@hooks/useDarkMode";
import { Editor } from "@monaco-editor/react";
import { useContext } from "react";
import cobalt from "../../assets/monaco-themes/Cobalt.json";
import dawn from "../../assets/monaco-themes/Dawn.json";
import nightOwl from "../../assets/monaco-themes/NightOwl.json";

// const loadTheme = () => {
//   try {
//     loader.init().then((monaco) => {
//       // @ts-expect-error - TS doesn't know this is a valid theme
//       import("../../assets/monaco-themes/Cobalt.json").then((data) => {
//         monaco.editor.defineTheme("cobalt", data);
//       });
//       // @ts-expect-error - TS doesn't know this is a valid theme
//       import("../../assets/monaco-themes/Dawn.json").then((data) => {
//         monaco.editor.defineTheme("dawn", data);
//       });
//     });
//   } catch (error) {
//     console.log(error);
//   }
// };

// loadTheme();

const remToPx = (rem: number): number =>
  rem * parseFloat(getComputedStyle(document.documentElement).fontSize);

const PolicyEditor = ({
  height = "70vh",
  value,
  onChange,
  defaultValue,
  readOnly = false,
  className
}: {
  height?: string | number;
  value?: string;
  defaultValue?: string;
  readOnly?: boolean;
  onChange?: (str?: string) => void;
  className?: string;
}) => {
  const darkMode = useContext(DarkModeContext)[0];
  return (
    <Editor
      options={{
        fontSize: remToPx(1),
        minimap: { enabled: false },
        readOnly,
        scrollBeyondLastLine: false
      }}
      height={height}
      defaultLanguage="json"
      theme={darkMode ? "nightOwl" : "dawn"}
      value={value}
      defaultValue={defaultValue}
      onChange={onChange}
      beforeMount={(monaco) => {
        monaco.editor.defineTheme("nightOwl", nightOwl);
        monaco.editor.defineTheme("dawn", dawn);
      }}
      className={className}
    />
  );
};

export default PolicyEditor;
