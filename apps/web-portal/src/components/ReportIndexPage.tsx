import ReportTop from "../../assets/images/report-top.png";
import ReportBottom from "../../assets/images/report-bottom.png";
import BrandLogo from "./BrandLogo";
import { useAppSelector } from "@src/store";

const ReportIndexPage = ({
  title,
  description,
  startDate,
  endDate,
  duration
}: {
  title: string;
  description: string;
  startDate?: string;
  endDate?: string;
  duration?: string;
}) => {
  const tenantName = useAppSelector((state) => state.user.tenant?.name);
  return (
    <div className=" border-2 !p-0 relative min-h-[60vh] flex items-center justify-center page-component index-page">
      <img className="absolute top-0 left-0" src={ReportTop} alt="report-top" />
      <img className="absolute bottom-0 right-0" src={ReportBottom} alt="report-top" />
      <BrandLogo mode="light" className="w-36 absolute top-4 right-4" />

      <div className="flex flex-col items-start mx-auto">
        <p className="font-semibold text-2xl capitalize"> {title}</p>
        <p className="mt-1 text-muted-foreground text-xl">{description}</p>

        <p className="mt-4 text-md">created by: {tenantName}</p>
        <p>created on: {new Date().toLocaleString()}</p>

        {startDate && <p className="mt-4 text-lg font-semibold">Start Date: {startDate}</p>}
        {endDate && <p className="mt-4 text-lg font-semibold">End Date: {endDate}</p>}

        {!startDate && !endDate && duration && (
          <p className="mt-2 text-lg font-semibold">Duration: {duration}</p>
        )}
      </div>
    </div>
  );
};
export default ReportIndexPage;
