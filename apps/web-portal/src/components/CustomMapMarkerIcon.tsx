import L from "leaflet";
import { LucideIcon } from "lucide-react";
import { renderToStaticMarkup } from "react-dom/server";

// Type for preset color options
type PresetColor = "green" | "blue" | "red" | "yellow" | "purple";

// Preset color values
const PRESET_COLORS: Record<PresetColor, string> = {
  green: "#3C8A5F",
  blue: "#4A6FA5",
  red: "#D32F2F",
  yellow: "#F9A825",
  purple: "#7B1FA2"
};

interface CustomMapMarkerIconProps {
  icon: LucideIcon;
  color?: PresetColor | string;
  iconSize?: number;
}

interface MarkerUIProps {
  Icon: LucideIcon;
  color?: PresetColor | string;
  iconSize?: number;
  markerWidth?: number;
  markerHeight?: number;
}

export const MarkerUI = ({
  Icon,
  color,
  markerWidth = 30,
  markerHeight = 42,
  iconSize = 42
}: MarkerUIProps) => {
  const markerColor = PRESET_COLORS[color as PresetColor] || color;
  return (
    <div className="flex flex-col items-center">
      <div style={{ position: "relative", overflow: "visible" }}>
        {/* SVG with icon embedded directly to ensure visibility */}
        <svg
          viewBox="0 0 100 130"
          width={markerWidth}
          height={markerHeight}
          style={{ position: "relative" }}
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Marker shape with white border */}
          <path
            d="M50,0 C22.4,0 0,22.4 0,50 C0,77.6 50,130 50,130 C50,130 100,77.6 100,50 C100,22.4 77.6,0 50,0 Z"
            fill={markerColor}
            stroke="white"
            strokeWidth="4"
          />

          {/* Reduced circle size for the icon */}
          <circle cx="50" cy="50" r="32" fill="white" />

          {/* Icon is placed directly in the SVG using foreignObject - larger sizing */}
          <foreignObject x="25" y="25" width="50" height="50">
            <div
              style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              <Icon color={markerColor} size={iconSize} />
            </div>
          </foreignObject>
        </svg>
      </div>
    </div>
  );
};

export const createCustomMapMarker = ({
  icon: Icon,
  color = "green",
  iconSize = 48 // Increased from original 16
}: CustomMapMarkerIconProps): L.DivIcon => {
  const markerColor = PRESET_COLORS[color as PresetColor] || color;

  const markerWidth = 30; // Increased from original 30
  const markerHeight = 42; // Increased from original 40

  // Create the marker UI as a React component

  // Convert React component to HTML string
  const iconMarkup = renderToStaticMarkup(
    <MarkerUI
      Icon={Icon}
      color={markerColor}
      markerWidth={markerWidth}
      markerHeight={markerHeight}
      iconSize={iconSize}
    />
  );

  // Calculate icon size and anchor points
  const iconHeight = 52; // Increased for larger marker

  // Create a Leaflet DivIcon using the HTML string
  return L.divIcon({
    html: iconMarkup,
    className: "custom-map-marker",
    iconSize: [markerWidth, iconHeight * 0.7], // Reduced size
    iconAnchor: [markerWidth / 2, iconHeight * 0.7], // Center horizontally, bottom of the marker
    popupAnchor: [0, -iconHeight * 0.7] // Center popup above the icon
  });
};

// Helper function for use with React Leaflet Marker
export const CustomMapMarkerIcon = (props: CustomMapMarkerIconProps) => {
  return createCustomMapMarker(props);
};

// Usage example:
// <Marker
//   position={[latitude, longitude]}
//   icon={CustomMapMarkerIcon({ icon: Home, color: "blue", iconSize: 12 })}
// />
