import { IconButton, Tooltip } from "@mui/material";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Dices } from "lucide-react";

const GenerateInputValue = ({ title, onClick }: { title: string; onClick: () => void }) => {
  return (
    <Tooltip arrow placement="top" title={title}>
      <IconButton
        className="!p-1.5"
        onClick={(e) => {
          e.stopPropagation();
          onClick();
        }}
      >
        <Dices size={BUTTON_ICON_SIZE} />
      </IconButton>
    </Tooltip>
  );
};

export default GenerateInputValue;
