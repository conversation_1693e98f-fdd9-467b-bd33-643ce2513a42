import * as SheetPrimitive from "@radix-ui/react-dialog";
import {
  Sheet,
  SheetDes<PERSON>,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  SheetClose,
  SheetPortal,
  SheetOverlay
} from "@components/shadcn/components/sheet";
import { cva } from "class-variance-authority";
import { But<PERSON> } from "@components/shadcn/components/button";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import React from "react";
import { X } from "lucide-react";
import { cn } from "@components/shadcn/utils";

// Custom sheet variants (copied from shadcn sheet.tsx)
const sheetVariants = cva(
  "fixed z-50 gap-4 bg-card p-6 py-4 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
  {
    variants: {
      side: {
        top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
        bottom:
          "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
        left: "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
        right:
          "inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"
      }
    },
    defaultVariants: {
      side: "right"
    }
  }
);

export type SideSheetProps = {
  children: React.ReactNode;
  title?: string;
  open: boolean;
  onClose: () => void;
  description?: string;
  notDismissable?: boolean;
  footer?: React.ReactNode;
  side?: "left" | "right" | "top" | "bottom";
  size?: "sm" | "md" | "lg" | "xl" | "full";
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;
  showCloseButton?: boolean;
  showBackdrop?: boolean; // New prop to control backdrop visibility
};

const SideSheet = ({
  children,
  title,
  open,
  onClose,
  description,
  notDismissable = false,
  footer,
  side = "right",
  size = "lg",
  className,
  headerClassName,
  contentClassName,
  footerClassName,
  showCloseButton = true,
  showBackdrop = true
}: SideSheetProps) => {
  // Size mappings for different sheet sizes
  const getSizeClasses = () => {
    if (side === "left" || side === "right") {
      switch (size) {
        case "sm":
          return "w-full sm:max-w-sm";
        case "md":
          return "w-full sm:max-w-md";
        case "lg":
          return "w-full sm:max-w-lg";
        case "xl":
          return "w-full sm:max-w-xl";
        case "full":
          return "w-full max-w-full";
        default:
          return "w-full sm:max-w-lg";
      }
    } else {
      // For top/bottom sheets, use height instead of width
      switch (size) {
        case "sm":
          return "h-[300px]";
        case "md":
          return "h-[400px]";
        case "lg":
          return "h-[500px]";
        case "xl":
          return "h-[600px]";
        case "full":
          return "h-full";
        default:
          return "h-[500px]";
      }
    }
  };

  return (
    <Sheet open={open} onOpenChange={notDismissable ? undefined : (isOpen) => !isOpen && onClose()}>
      <SheetPortal>
        {showBackdrop && <SheetOverlay className="" />}
        <SheetPrimitive.Content
          className={cn(
            sheetVariants({ side }),
            "flex flex-col p-0 gap-0 z-[1001]",
            getSizeClasses(),
            className
          )}
          onPointerDownOutside={notDismissable ? (e: any) => e.preventDefault() : undefined}
          onEscapeKeyDown={notDismissable ? (e: any) => e.preventDefault() : undefined}
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <SheetHeader
              className={cn(
                "flex-shrink-0 p-6 pb-4 border-b border-border",
                "flex flex-row items-start justify-between space-y-0",
                headerClassName
              )}
            >
              <div className="flex flex-col gap-2 flex-1">
                {title && (
                  <SheetTitle className="text-left text-xl font-semibold">{title}</SheetTitle>
                )}
                {description && (
                  <SheetDescription className="text-muted-foreground text-left">
                    {description}
                  </SheetDescription>
                )}
              </div>

              {showCloseButton && (
                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 p-0 -mt-1 ml-4 flex-shrink-0"
                    onClick={onClose}
                  >
                    <X size={BUTTON_ICON_SIZE} />
                  </Button>
                </SheetClose>
              )}
            </SheetHeader>
          )}

          {/* Content */}
          <div
            className={cn(
              "flex-1 p-6 overflow-y-auto",
              !title && !showCloseButton && "pt-6",
              contentClassName
            )}
          >
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <SheetFooter
              className={cn(
                "flex-shrink-0 p-6 pt-4 border-t border-border",
                "flex flex-row gap-4 justify-end",
                footerClassName
              )}
            >
              {footer}
            </SheetFooter>
          )}
        </SheetPrimitive.Content>
      </SheetPortal>
    </Sheet>
  );
};

export default SideSheet;
