import { NavLink, useParams } from "react-router-dom";
import useBreadcrumbs from "use-react-router-breadcrumbs";
import useIsMobile from "../hooks/useIsMobile";

export default function Breadcrumbs({
  routes
}: {
  routes: {
    path: string;
    title?: string;
    help?: string;
    breadcrumb: string;
  };
}) {
  const isMobile = useIsMobile();
  const { partnerName, featureType } = useParams();
  const isTenantView = Boolean(partnerName) && Boolean(featureType);
  const allBreadCrumbs = useBreadcrumbs(routes);
  const breadcrumbs = isTenantView ? allBreadCrumbs?.slice(4) : allBreadCrumbs;

  return (
    <ol className="flex gap-1 page-title  items-center px-2 ">
      {(isMobile ? breadcrumbs.slice(-1) : breadcrumbs).map(
        ({ match, breadcrumb, location }, i) => {
          // const isFirst = i === 0;
          const isLast = i === breadcrumbs.length - 1;
          const decodedBreadcrumb = decodeURIComponent(breadcrumb.props.children);
          return (
            !match?.route?.notLink && (
              <li className="center gap-1" key={match.pathname}>
                {!isLast ? (
                  <NavLink
                    className="center gap-1 text-base font-medium  rounded-lg px-2  hover:sidebar-link-active duration-500"
                    to={match?.route?.notLink ? null : match.pathname}
                    state={location.state}
                  >
                    <span>{decodedBreadcrumb}</span>
                  </NavLink>
                ) : (
                  <span className="flex !text-lg gap-2 ">
                    {/* {isFirst && <Home size="1.2rem" />} */}
                    <span className=" text-muted-foreground">{decodedBreadcrumb}</span>
                  </span>
                )}
                {!isLast && !isMobile && (
                  <span className="text-gray-600 dark:text-gray-400 mx-0.5">/</span>
                  // <ChevronRight
                  //   className="text-gray-600 dark:text-gray-400"
                  //   size={BUTTON_ICON_SIZE}
                  // />
                )}
              </li>
            )
          );
        }
      )}
    </ol>
  );
}
