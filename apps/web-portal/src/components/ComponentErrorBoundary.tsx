/* eslint-disable react/destructuring-assignment */
import React, { Component } from "react";
import Crash from "../../assets/icons/crash.png";

type Props = {
  children: React.ReactNode;
  customError?: React.ReactNode;
};

class ComponentErrorBoundary extends Component<Props> {
  constructor(props) {
    super(props);
    this.state = { hasError: false, message: "hello" };
  }

  componentDidUpdate(prevProps: Props) {
    if (prevProps.children !== this.props.children && this.state.hasError) {
      console.log("Children have changed! in error");
      this.setState({ hasError: false, message: "" });
      // Your logic for handling the change goes here
    }
  }

  componentDidCatch(error, info) {
    // console.error(error, info);
    console.log(error.message);
    this.setState({ hasError: true, message: error.message });
  }

  render() {
    if (this.state.hasError) {
      if (this.props.customError) {
        return this.props.customError;
      }
      // You can display a custom error message or component here
      return (
        <div className="absolute top-0 left-0 bottom-0 right-0 z-10 flex items-center justify-center">
          <div className="py-7 flex items-center justify-center flex-col gap-3 px-6">
            <img src={Crash} alt="crashed" className="w-16 h-16" />
            <h1 className="font-medium text-center">Something went wrong here.</h1>
            <p className="-mt-2 text-center">{this.state.message}</p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ComponentErrorBoundary;
