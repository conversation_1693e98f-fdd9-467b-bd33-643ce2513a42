import { Skeleton } from "@components/shadcn/components/skeleton";
import { Card, CardContent } from "@components/ui";
import useDarkMode from "@hooks/useDarkMode";

interface LoadingSkeletonProps {
  showStats?: boolean;
  showFilters?: boolean;
  showTable?: boolean;
  statsCount?: number;
  tableRows?: number;
  tableColumns?: number;
}

export default function PageLoadingSkeleton({
  showStats = true,
  showFilters = true,
  showTable = true,
  statsCount = 4,
  tableRows = 6,
  tableColumns = 6
}: LoadingSkeletonProps) {
  const [darkMode] = useDarkMode();

  // Theme-aware classes
  const themeClasses = {
    background: darkMode ? "bg-slate-950" : "bg-gray-50",
    text: darkMode ? "text-white" : "text-gray-900",
    headerBg: darkMode ? "bg-slate-900/50" : "bg-white/80",
    headerBorder: darkMode ? "border-slate-800" : "border-gray-200",
    cardBg: darkMode ? "bg-card" : "bg-white",
    cardBorder: darkMode ? "border-slate-800" : "border-gray-200",
    skeleton: darkMode ? "bg-slate-700" : "bg-gray-200",
    divider: darkMode ? "divide-slate-800" : "divide-gray-200",
    borderColor: darkMode ? "border-slate-800" : "border-gray-200"
  };
  return (
    <div className="">
      {/* Page Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <Skeleton className={`mb-2 h-8 w-32 ${themeClasses.skeleton}`} />
          <Skeleton className={`h-4 w-48 ${themeClasses.skeleton}`} />
        </div>
        <Skeleton className={`h-10 w-32 ${themeClasses.skeleton}`} />
      </div>

      {/* Stats Cards */}
      {showStats && (
        <div
          className={`mb-8 grid gap-6 ${
            statsCount === 4
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
              : statsCount === 3
                ? "grid-cols-1 md:grid-cols-3"
                : statsCount === 2
                  ? "grid-cols-1 md:grid-cols-2"
                  : "grid-cols-1"
          }`}
        >
          {Array.from({ length: statsCount }).map((_, i) => (
            <Card key={i} className={`${themeClasses.cardBorder} ${themeClasses.cardBg} shadow-sm`}>
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className={`h-4 w-32 ${themeClasses.skeleton}`} />
                  <Skeleton className={`h-8 w-20 ${themeClasses.skeleton}`} />
                  <Skeleton className={`h-3 w-12 ${themeClasses.skeleton}`} />
                </div>
                <Skeleton className={`h-10 w-10 rounded-lg ${themeClasses.skeleton}`} />
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Filters and Search */}
      {showFilters && (
        <div className="mb-6">
          <div className="mb-4 flex items-center justify-between">
            <Skeleton className={`h-6 w-32 ${themeClasses.skeleton}`} />
          </div>
          <div className="flex items-center justify-between space-x-4">
            <Skeleton className={`h-10 w-80 ${themeClasses.skeleton}`} />
            <div className="flex justify-between gap-4">
              <Skeleton className={`h-10 w-32 ${themeClasses.skeleton}`} />
              <Skeleton className={`h-10 w-24 ${themeClasses.skeleton}`} />
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      {showTable && (
        <Card className={`${themeClasses.cardBorder} ${themeClasses.cardBg} shadow-sm`}>
          <CardContent className="p-0">
            {/* Table Header */}
            <div className={`border-b ${themeClasses.borderColor} p-4 pt-2`}>
              <div
                className="grid gap-4"
                style={{ gridTemplateColumns: `repeat(${tableColumns}, 1fr)` }}
              >
                {Array.from({ length: tableColumns }).map((_, i) => (
                  <Skeleton key={i} className={`h-4 w-24 ${themeClasses.skeleton}`} />
                ))}
              </div>
            </div>

            {/* Table Rows */}
            <div className={`${themeClasses.divider}`}>
              {Array.from({ length: tableRows }).map((_, rowIndex) => (
                <div key={rowIndex} className="p-4">
                  <div
                    className="grid gap-4"
                    style={{ gridTemplateColumns: `repeat(${tableColumns}, 1fr)` }}
                  >
                    {Array.from({ length: tableColumns }).map((_, colIndex) => (
                      <div key={colIndex} className="flex items-center space-x-3">
                        {colIndex === 0 && (
                          <Skeleton className={`h-8 w-8 rounded ${themeClasses.skeleton}`} />
                        )}
                        <div className="space-y-1">
                          <Skeleton
                            className={`h-4 ${themeClasses.skeleton} ${
                              colIndex === 0
                                ? "w-20"
                                : colIndex === 1
                                  ? "w-16"
                                  : colIndex === 2
                                    ? "w-24"
                                    : "w-12"
                            }`}
                          />
                          {colIndex === 0 && (
                            <Skeleton className={`h-3 w-32 ${themeClasses.skeleton}`} />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div
              className={`flex items-center justify-between border-t ${themeClasses.borderColor} p-4 pb-0`}
            >
              <div className="flex items-center space-x-2">
                {/* Left side pagination info can be added here */}
              </div>
              <div className="flex items-center space-x-4">
                <Skeleton className={`h-8 w-40 ${themeClasses.skeleton}`} />
                <div className="flex space-x-1">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className={`h-8 w-8 ${themeClasses.skeleton}`} />
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
