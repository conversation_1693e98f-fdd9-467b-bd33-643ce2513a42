import { Card, CardContent, CardHeader } from "@components/shadcn/components/card";
import { Skeleton } from "@components/shadcn/components/skeleton";
import { cn } from "@components/shadcn/utils";

// Base skeleton components
function SkeletonText({ className = "" }: { className?: string }) {
  return <Skeleton className={cn("h-4 bg-muted-foreground/20", className)} />;
}

function SkeletonTitle({ className = "" }: { className?: string }) {
  return <Skeleton className={cn("h-6 bg-muted-foreground/20", className)} />;
}

function SkeletonAvatar({ className = "" }: { className?: string }) {
  return <Skeleton className={cn("h-10 w-10 rounded-full bg-muted-foreground/20", className)} />;
}

function SkeletonButton({ className = "" }: { className?: string }) {
  return <Skeleton className={cn("h-9 w-20 rounded-md bg-muted-foreground/20", className)} />;
}

// USAGE
// <LoadingSkeleton variant="device-detail" />
// <LoadingSkeleton variant="table" rows={8} showActions={true} />
// <LoadingSkeleton variant="grid" columns={3} gridRows={2} showHeader={true} />
// <LoadingSkeleton variant="form" showHeader={true} />
// <LoadingSkeleton variant="list-item" showAvatar={true} showActions={true} />
// <LoadingSkeleton variant="card" showHeader={true} showFooter={true} />
// <LoadingSkeleton variant="empty-state" height="400px" />

export interface LoadingSkeletonProps {
  variant:
    | "device-detail"
    | "table"
    | "grid"
    | "form"
    | "list-item"
    | "card"
    | "empty-state"
    | "stats";

  // Common props
  className?: string;

  // Table/List specific
  rows?: number;

  // Grid specific
  columns?: number;
  gridRows?: number;

  // Card specific
  showHeader?: boolean;
  showFooter?: boolean;

  // List item specific
  showAvatar?: boolean;
  showActions?: boolean;

  // Custom sizing
  height?: string;
  width?: string;

  // Animation
  animate?: boolean;
  statsCount?: number;
}

export function LoadingSkeleton({
  variant,
  className = "",
  rows = 6,
  columns = 4,
  gridRows = 2,
  showHeader = true,
  showFooter = false,
  showAvatar = true,
  showActions = true,
  height,
  width,
  animate = true,
  statsCount = 4
}: LoadingSkeletonProps) {
  const baseClassName = cn(animate && "animate-pulse", className);

  const customStyle = {
    ...(height && { height }),
    ...(width && { width })
  };

  switch (variant) {
    case "device-detail":
      return (
        <div className={cn("space-y-6 p-6", baseClassName)} style={customStyle}>
          {/* Header with device info */}
          <div className="flex items-start gap-4">
            <Skeleton className="h-16 w-24 rounded-lg bg-muted-foreground/20" />
            <div className="flex-1 space-y-2">
              <SkeletonTitle className="w-32" />
              <SkeletonText className="w-48" />
              <SkeletonText className="w-40" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4 rounded-full bg-green-500/50" />
              <SkeletonText className="w-8" />
            </div>
          </div>

          {/* Details section */}
          <div className="space-y-4">
            {[
              { label: "Version:", width: "w-16" },
              { label: "Created:", width: "w-32" },
              { label: "Last Updated:", width: "w-36" },
              { label: "Address:", width: "w-full" }
            ].map((item, index) => (
              <div key={index} className="flex items-start gap-4">
                <div className="flex items-center gap-2 w-24">
                  <Skeleton className="h-4 w-4 bg-muted-foreground/20" />
                  <SkeletonText className="w-16" />
                </div>
                <SkeletonText className={item.width} />
              </div>
            ))}
          </div>

          {/* Tags section */}
          <div className="space-y-3">
            <SkeletonTitle className="w-12" />
            <SkeletonText className="w-32" />
          </div>
        </div>
      );
    case "stats":
      return (
        <div
          className={` grid gap-6 ${
            statsCount === 4
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
              : statsCount === 3
                ? "grid-cols-1 md:grid-cols-3"
                : statsCount === 2
                  ? "grid-cols-1 md:grid-cols-2"
                  : "grid-cols-1"
          }`}
        >
          {Array.from({ length: statsCount }).map((_, i) => (
            <Card className="p-6" key={i}>
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className={`h-4 w-32 `} />
                  <Skeleton className={`h-8 w-20 `} />
                  <Skeleton className={`h-3 w-12 `} />
                </div>
                <Skeleton className={`h-10 w-10 rounded-lg `} />
              </div>
            </Card>
          ))}
        </div>
      );
    case "table":
      return (
        <div className={cn("space-y-3", baseClassName)} style={customStyle}>
          {Array.from({ length: rows }).map((_, index) => (
            <div key={index} className="flex items-center gap-4 p-3 border-b last:border-b-0">
              <SkeletonText className="w-32" />
              <SkeletonText className="w-48 flex-1" />
              <SkeletonText className="w-24" />
              {showActions && <SkeletonButton className="w-16" />}
            </div>
          ))}
        </div>
      );

    case "grid":
      return (
        <div className={cn("space-y-6", baseClassName)} style={customStyle}>
          <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${columns} gap-4`}>
            {Array.from({ length: columns * gridRows }).map((_, index) => (
              <Card key={index} className="p-4">
                <div className="space-y-3">
                  <SkeletonTitle className="w-3/4" />
                  <SkeletonText className="w-full" />
                  <SkeletonText className="w-2/3" />
                </div>
              </Card>
            ))}
          </div>
        </div>
      );

    case "form":
      return (
        <Card className={baseClassName} style={customStyle}>
          {showHeader && (
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5 bg-muted-foreground/20" />
                <SkeletonTitle className="w-48" />
              </div>
              <div className="flex gap-2">
                <SkeletonButton className="w-24" />
                <SkeletonButton className="w-16" />
              </div>
            </CardHeader>
          )}
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4 bg-muted-foreground/20" />
                    <SkeletonText className="w-20" />
                  </div>
                  <SkeletonText className="w-full" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      );

    case "list-item":
      return (
        <div
          className={cn("flex items-center gap-3 p-3 border-b last:border-b-0", baseClassName)}
          style={customStyle}
        >
          {showAvatar && <SkeletonAvatar />}
          <div className="flex-1 space-y-1">
            <SkeletonText className="w-3/4" />
            <SkeletonText className="w-1/2" />
          </div>
          {showActions && <SkeletonText className="w-16" />}
        </div>
      );

    case "card":
      return (
        <Card className={baseClassName} style={customStyle}>
          {showHeader && (
            <CardHeader>
              <SkeletonTitle className="w-3/4" />
              <SkeletonText className="w-full" />
            </CardHeader>
          )}
          <CardContent className="space-y-4">
            <SkeletonText className="w-full" />
            <SkeletonText className="w-4/5" />
            <SkeletonText className="w-3/5" />
          </CardContent>
          {showFooter && (
            <div className="p-6 pt-0 flex gap-2">
              <SkeletonButton />
              <SkeletonButton />
            </div>
          )}
        </Card>
      );

    case "empty-state":
      return (
        <div
          className={cn(
            "h-full rounded-lg bg-muted/20 flex items-center justify-center",
            baseClassName
          )}
          style={customStyle}
        >
          <div className="text-center space-y-4">
            <Skeleton className="h-32 w-32 rounded-full mx-auto bg-muted-foreground/20" />
            <SkeletonTitle className="w-48 mx-auto" />
            <SkeletonText className="w-64 mx-auto" />
            <SkeletonButton className="w-32 mx-auto" />
          </div>
        </div>
      );

    default:
      return (
        <div className={cn("p-4", baseClassName)} style={customStyle}>
          <SkeletonText className="w-full" />
        </div>
      );
  }
}
