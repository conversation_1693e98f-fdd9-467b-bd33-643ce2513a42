import clsx from "clsx";
import { X } from "lucide-react";
import React, { KeyboardEvent, useId, useRef, useState } from "react";

const CommaSeparatorInput = ({
  label = "Comma Separated Tags",
  description = "Enter a comma after each tag",
  value,
  onAddField,
  onRemoveField,
  className,
  ...props
}: {
  label?: string;
  description?: string;
  value: string[];
  onAddField: (val: string) => void;
  onRemoveField: (val: string) => void;
  className?: string;
  [key: string]: any;
}) => {
  const id = useId();

  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      addTag(value.replace(",", ""));
    } else {
      setInputValue(value);
    }
  };

  const handleBlur = () => {
    if (inputValue.trim()) {
      addTag(inputValue);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && !inputValue && value.length > 0) {
      e.preventDefault();
      removeTag(value[value.length - 1]);
      setInputValue(value[value.length - 1]);
    }
  };

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !value.includes(trimmedTag)) {
      onAddField(trimmedTag);
      setInputValue("");
    }
  };

  const removeTag = (option: number) => {
    onRemoveField(value.filter((val) => val !== option));
  };

  return (
    <div className={clsx("relative", className)}>
      {label && (
        <label htmlFor={`input-${id}`} className="input-label-text">
          {label}
          {props.required && <span className="ml-1 opacity-70">*</span>}
        </label>
      )}
      <div className="  input-container flex flex-wrap gap-2   px-3 py-2">
        {value.map((tag, index) => (
          <div key={index} className="dropdown-tag-item text-white whitespace-nowrap">
            {tag}
            <span
              onClick={() => removeTag(tag)}
              className="dropdown-tag-close bg-transparent rounded-full ml-1 cursor-pointer  hover:bg-gray-800/20 "
              style={{ padding: "1px" }}
            >
              <X size=".75rem" color="black" />
            </span>
          </div>
        ))}

        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          className=" input flex-1 p-0"
          placeholder={value.length === 0 ? "Enter a comma after each tag" : ""}
        />
      </div>
      {description && <p className="helper-text  ">{description}</p>}
    </div>
  );
};

export default CommaSeparatorInput;
