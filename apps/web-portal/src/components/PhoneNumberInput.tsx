/* eslint-disable no-use-before-define */
import { <PERSON>I<PERSON>, ChevronsUpDown } from "lucide-react";
import * as React from "react";
import * as RPNInput from "react-phone-number-input";
import flags from "react-phone-number-input/flags";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "./shadcn/components/command";
import { Popover, PopoverContent, PopoverTrigger } from "./shadcn/components/popover";
import { ScrollArea } from "./shadcn/components/scroll-area";
import { Button } from "./shadcn/components/button";
import { cn } from "./shadcn/utils";
import Input from "./Input";
import clsx from "clsx";

type PhoneInputProps = Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange" | "value"> & {
  onChange: (value: RPNInput.Value) => void;
  value: RPNInput.Value;
  error?: boolean;
  label: string;
  helperText?: string;
  labelClassName?: string;
};

const PhoneInput: React.ForwardRefExoticComponent<PhoneInputProps> = React.forwardRef<
  React.ElementRef<typeof RPNInput.default>,
  PhoneInputProps
>(({ className, onChange, onBlur, label, helperText, labelClassName, ...props }, ref) => {
  return (
    <div>
      {label && (
        <label
          htmlFor={`input-${props.id}`}
          className={clsx(
            "input-label-text",
            props.error && "!text-red-500 dark:!text-red-400",
            labelClassName && `${labelClassName} !ml-0`
          )}
        >
          {label}
          {props.required && <span className="ml-1 opacity-70">*</span>}
        </label>
      )}
      <RPNInput.default
        ref={ref}
        className={cn("flex", className)}
        flagComponent={FlagComponent}
        countrySelectComponent={CountrySelect}
        inputComponent={InputComponent}
        value={props.value}
        required={props.required}
        defaultCountry="IN"
        /**
         * Handles the onChange event.
         *
         * react-phone-number-input might trigger the onChange event as undefined
         * when a valid phone number is not entered. To prevent this,
         * the value is coerced to an empty string.
         *
         * @param {E164Number | undefined} value - The entered value
         */
        onChange={(value) => {
          if (!value && props.value) return;

          onChange?.(value as any);
        }}
        onBlur={(e) => {
          onBlur?.(e);
        }}
      />
      {helperText && (
        <p className={clsx("helper-text", props.error && "input-helper-text-error")}>
          {helperText}
        </p>
      )}
    </div>
  );
});
PhoneInput.displayName = "PhoneInput";

const InputComponent = React.forwardRef<HTMLInputElement, any>(({ className, ...props }, ref) => (
  <Input className={cn("rounded-e-lg rounded-s-none w-full", className)} {...props} ref={ref} />
));
InputComponent.displayName = "InputComponent";

type CountrySelectOption = { label: string; value: RPNInput.Country };

type CountrySelectProps = {
  disabled?: boolean;
  value: RPNInput.Country;
  onChange: (value: RPNInput.Country) => void;
  options: CountrySelectOption[];
};

const CountrySelect = ({ disabled, value, onChange, options }: CountrySelectProps) => {
  const handleSelect = React.useCallback(
    (country: RPNInput.Country) => {
      onChange(country);
    },
    [onChange]
  );
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          className={cn("flex gap-1 input-container rounded-s-lg !max-w-[4rem] mt-auto mr-2")}
          disabled={disabled}
        >
          <FlagComponent country={value} countryName={value} />
          <ChevronsUpDown
            className={cn("-mr-2 h-4 w-4 opacity-50", disabled ? "hidden" : "opacity-100")}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0 !z-[2000]">
        <Command>
          <CommandList>
            <ScrollArea className="h-72">
              <CommandInput placeholder="Search country..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {options
                  .filter((x) => x.value)
                  .map((option) => (
                    <CommandItem
                      className="gap-2"
                      key={option.value}
                      onSelect={() => handleSelect(option.value)}
                    >
                      <FlagComponent country={option.value} countryName={option.label} />
                      <span className="flex-1 text-sm">{option.label}</span>
                      {option.value && (
                        <span className="text-sm text-foreground/50">
                          {`+${RPNInput.getCountryCallingCode(option.value)}`}
                        </span>
                      )}
                      <CheckIcon
                        className={cn(
                          "ml-auto h-4 w-4",
                          option.value === value ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

const FlagComponent = ({ country, countryName }: RPNInput.FlagProps) => {
  const Flag = flags[country];

  return (
    <span className="flex h-4 w-6 overflow-hidden rounded-sm bg-foreground/20">
      {Flag && <Flag title={countryName} />}
    </span>
  );
};
FlagComponent.displayName = "FlagComponent";

export { PhoneInput };
