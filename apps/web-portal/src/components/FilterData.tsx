import { CheckIcon } from "@radix-ui/react-icons";

import { Badge } from "@components/shadcn/components/badge";
import { But<PERSON> } from "@components/shadcn/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from "@components/shadcn/components/command";
import { Popover, PopoverContent, PopoverTrigger } from "@components/shadcn/components/popover";
import { Separator } from "@components/shadcn/components/separator";
import { cn } from "@components/shadcn/utils";
import { separateCamelCase } from "@utils/utilities";
import { Filter } from "lucide-react";

interface DataTableFacetedFilterProps {
  title?: string;
  options: string[];
  filters: string[];
  setFilters: (item: string[]) => void;
}

export function FilterData({
  title = `Filters`,
  filters,
  setFilters,
  options
}: DataTableFacetedFilterProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="h-10 border-dashed bg-secondary border-muted-foreground/50 rounded-lg !min-w-40"
        >
          <Filter className="mr-2 h-4 w-4" />
          {title}
          {filters?.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4 bg-foreground" />
              <Badge variant="secondary" className="rounded-sm px-1 font-normal lg:hidden">
                {filters.length}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {filters.length > 1 ? (
                  <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                    {filters.length} selected
                  </Badge>
                ) : (
                  options
                    .filter((option) => filters.includes(option))
                    .map((option) => (
                      <Badge
                        variant="secondary"
                        key={option}
                        className="rounded-sm px-1 font-normal"
                      >
                        {separateCamelCase(option)}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0" align="start">
        <Command>
          <CommandInput placeholder={title} />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => {
                const isSelected = filters.includes(option);
                return (
                  <CommandItem
                    key={option}
                    onSelect={() => {
                      const newFilters = isSelected
                        ? filters.filter((item) => item !== option)
                        : [...filters, option];

                      setFilters(newFilters);
                    }}
                  >
                    <div
                      className={cn(
                        "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                        isSelected
                          ? "bg-primary text-primary-foreground"
                          : "opacity-50 [&_svg]:invisible"
                      )}
                    >
                      <CheckIcon className={cn("h-4 w-4")} />
                    </div>
                    <span>{separateCamelCase(option)}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {filters.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => setFilters([])}
                    className="justify-center text-center"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
