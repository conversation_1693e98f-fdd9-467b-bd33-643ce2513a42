/* eslint-disable max-len */
import { Check, Code } from "lucide-react";
import { CircularProgress } from "@mui/material";
import clsx from "clsx";
import React, { useEffect, useState } from "react";

type Props = import("react").InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
  showIcon?: boolean;
  datatestid?: string;
  checked?: boolean;
  isLoading?: boolean;
};

const Switch: import("react").ForwardRefRenderFunction<HTMLInputElement, Props> = (
  { label, className, datatestid, showIcon = true, isLoading, ...props },
  forwardedRef
) => {
  const [enabled, setEnabled] = useState(props.defaultChecked || props.checked);

  useEffect(() => {
    setEnabled(props.defaultChecked || props.checked);
  }, [props.defaultChecked, props.checked]);

  return (
    <label
      className={clsx(
        "flex w-fit items-center cursor-not-allowed",
        !props.disabled && "cursor-pointer",
        className
      )}
    >
      <div className={clsx("switch ", enabled ? "switch-enabled" : "switch-disabled")}>
        <div
          className={clsx(
            "switch-toggle text-white",
            enabled ? "switch-toggle-enabled" : "switch-toggle-disabled"
          )}
        >
          <div className="w-[.9rem] h-[.9rem] flex items-center justify-center">
            {!showIcon ? null : isLoading ? (
              <CircularProgress size={12} />
            ) : enabled ? (
              <Check className=" !rounded-full !text-black text-sm" />
            ) : (
              <Code
                // sx={{ fontSize: ".875rem" }}
                className=" !rounded-full !text-black text-sm"
              />
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center min-w-0" data-testid={datatestid || `switch-${label}`}>
        {label ? (
          <div className="flex items-center ml-2 heading-4">
            <span>{label}</span>
          </div>
        ) : (
          <div className="w-1 h-1" />
        )}
        <input
          type="checkbox"
          className="hidden"
          ref={forwardedRef}
          {...props}
          onChange={(e) => {
            if (props.onChange) {
              setEnabled(e.target.checked);
              props.onChange(e);
            }
          }}
        />
      </div>
    </label>
  );
};

export default React.forwardRef(Switch);
