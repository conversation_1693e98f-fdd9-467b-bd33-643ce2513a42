import React from "react";

const TopicHighlighter = ({ topicTemplate }: { topicTemplate: string }) => {
  // The component now accepts the topic string as a prop with a default value

  // Find the variable part in the string
  const parts = topicTemplate.split(/(\$\{[^}]+\})/);

  return (
    <div className=" font-mono">
      {parts.map((part, index) => {
        if (part.startsWith("${") && part.endsWith("}")) {
          // This is the variable part, highlight it
          return (
            <span key={index} className="bg-yellow-200 text-blue-700 font-bold px-1 py-0.5 rounded">
              {part}
            </span>
          );
        } else {
          // Regular text
          return <span key={index}>{part}</span>;
        }
      })}
    </div>
  );
};
export default TopicHighlighter;
