import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@components/shadcn/utils";

import { LucideIcon } from "lucide-react";

const statCardVariants = cva("", {
  variants: {
    colorScheme: {
      default: "",
      success: "ring-success/50 hover:ring-success/30",
      warning: "ring-warning/50 hover:ring-warning/30",
      danger: "ring-destructive/50 hover:ring-destructive/30",
      info: "ring-info/50 hover:ring-info/30",
      primary: "ring-primary/50 hover:ring-primary/30"
    },
    layout: {
      horizontal: "flex-row items-center",
      vertical: "flex-col",
      compact: "flex-row items-center justify-between"
    },
    selected: {
      true: "ring-1",
      false: ""
    }
  },
  defaultVariants: {
    colorScheme: "default",
    layout: "vertical",
    selected: false
  }
});

const iconVariants = cva("flex items-center justify-center rounded-lg", {
  variants: {
    colorScheme: {
      default: "bg-muted/20 text-muted-foreground",
      success: "bg-success/20 text-success",
      warning: "bg-warning/20 text-warning",
      danger: "bg-destructive/20 text-destructive",
      info: "bg-info/20 text-info",
      primary: "bg-primary/20 text-primary"
    },
    size: {
      sm: "h-8 w-8 p-1.5",
      default: "h-12 w-12 p-3",
      lg: "h-12 w-12 p-2.5"
    },
    position: {
      default: "",
      floating: "-mt-6 -ml-2 shadow-lg"
    }
  },
  defaultVariants: {
    colorScheme: "default",
    size: "default",
    position: "default"
  }
});

export interface StatCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statCardVariants> {
  title: string;
  value?: string | number;
  description?: string;
  icon?: LucideIcon | React.ReactNode;
  iconSize?: VariantProps<typeof iconVariants>["size"];
  iconPosition?: VariantProps<typeof iconVariants>["position"];
  loading?: boolean;
  trend?: {
    value: number;
    label?: string;
    direction: "up" | "down" | "neutral";
  };
  interactive?: boolean;
  footer?: React.ReactNode;
}

const StatCard = React.forwardRef<HTMLDivElement, StatCardProps>(
  (
    {
      className,
      title,
      value,
      description,
      icon,
      iconSize = "default",
      iconPosition = "default",
      colorScheme = "default",
      layout = "vertical",
      selected = false,
      loading = false,
      trend,
      interactive = false,
      footer,
      onClick,
      ...props
    },
    ref
  ) => {
    const IconComponent = icon as LucideIcon;
    const isClickable = !!onClick || interactive;

    const formatValue = (val: string | number | undefined) => {
      if (val === undefined) return "—";
      if (typeof val === "number") {
        return new Intl.NumberFormat("en").format(val);
      }
      return val;
    };

    const renderIcon = () => {
      if (!icon) return null;

      return (
        <div className={cn(iconVariants({ colorScheme, size: iconSize, position: iconPosition }))}>
          {React.isValidElement(icon) ? (
            icon
          ) : IconComponent ? (
            <IconComponent className="h-full w-full" />
          ) : null}
        </div>
      );
    };

    const renderTrend = () => {
      if (!trend) return null;

      const trendColors = {
        up: "text-success",
        down: "text-destructive",
        neutral: "text-muted-foreground"
      };

      const trendSymbol = {
        up: "↗",
        down: "↘",
        neutral: "→"
      };

      return (
        <div
          className={cn("flex items-center space-x-1 mt-2 text-sm", trendColors[trend.direction])}
        >
          {trendSymbol[trend.direction]}
          <span>
            {trend.value > 0 ? "+" : ""}
            {trend.value}% {trend.label}
          </span>
        </div>
      );
    };

    const renderContent = () => {
      if (loading) {
        return (
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-8 bg-muted rounded w-1/2"></div>
            {description && <div className="h-3 bg-muted rounded w-full"></div>}
          </div>
        );
      }

      if (layout === "horizontal") {
        return (
          <div className="flex items-center space-x-4">
            {renderIcon()}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-muted-foreground truncate">{title}</p>
              <p className="text-2xl font-bold">{formatValue(value)}</p>
              {description && (
                <p className="text-sm text-muted-foreground truncate">{description}</p>
              )}
            </div>
            {renderTrend()}
          </div>
        );
      }

      if (layout === "compact") {
        return (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {renderIcon()}
              <div>
                <p className="text-sm font-medium text-muted-foreground">{title}</p>
                <p className="text-xl font-bold">{formatValue(value)}</p>
              </div>
            </div>
            {renderTrend()}
          </div>
        );
      }

      // Vertical layout (default) - matches the provided layout structure
      return (
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-muted-foreground text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-foreground mt-1">{formatValue(value)}</p>
            {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
            {renderTrend()}
          </div>
          {renderIcon()}
        </div>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          "bg-card rounded-lg border border-border p-4 hover:border-border/80 transition-all duration-200",
          statCardVariants({ colorScheme, layout, selected }),
          isClickable && "cursor-pointer hover:shadow-card-hover",
          "min-w-[200px] flex-1",
          className
        )}
        onClick={onClick}
        {...props}
      >
        {renderContent()}
        {footer && <div className="mt-3 pt-3 border-t border-border">{footer}</div>}
      </div>
    );
  }
);

StatCard.displayName = "StatCard";

export { StatCard, statCardVariants };
