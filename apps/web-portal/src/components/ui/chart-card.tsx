import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@components/shadcn/utils";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "./card";
import { LucideIcon } from "lucide-react";

const chartCardVariants = cva("", {
  variants: {
    colorScheme: {
      default: "",
      success: "border-success/20",
      warning: "border-warning/20",
      danger: "border-destructive/20",
      info: "border-info/20",
      primary: "border-primary/20"
    },
    chartHeight: {
      sm: "[&_.chart-container]:h-32",
      default: "[&_.chart-container]:h-48",
      lg: "[&_.chart-container]:h-64",
      xl: "[&_.chart-container]:h-80",
      "2xl": "[&_.chart-container]:h-96"
    }
  },
  defaultVariants: { colorScheme: "default", chartHeight: "default" }
});

export interface ChartCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chartCardVariants> {
  title: string;
  description?: string;
  icon?: LucideIcon | React.ReactNode;
  total?: string | number;
  loading?: boolean;
  interactive?: boolean;
  selected?: boolean;
  headerAction?: React.ReactNode;
  footer?: React.ReactNode;
  children: React.ReactNode; // Chart component
  showHeader?: boolean;
  showBorder?: boolean;
  themePallet?: number;
}

const ChartCard = React.forwardRef<HTMLDivElement, ChartCardProps>(
  (
    {
      className,
      title,
      description,
      icon,
      total,
      colorScheme = "default",
      chartHeight = "default",
      loading = false,
      interactive = false,
      selected = false,
      headerAction,
      footer,
      children,
      showHeader = true,
      showBorder = false,
      onClick,
      ...props
    },
    ref
  ) => {
    const IconComponent = icon as LucideIcon;
    const isClickable = !!onClick || interactive;

    const formatTotal = (val: string | number | undefined) => {
      if (val === undefined) return null;
      if (typeof val === "number") {
        return new Intl.NumberFormat("en").format(val);
      }
      return val;
    };

    const renderHeader = () => {
      if (!showHeader) return null;

      return (
        <CardHeader
          size="sm"
          className={cn(
            "flex flex-row items-center justify-between p-0 pb-2",
            showBorder && "border-b border-border"
          )}
        >
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            {icon && (
              <div className="flex-shrink-0">
                {React.isValidElement(icon) ? (
                  icon
                ) : IconComponent ? (
                  <IconComponent className="h-5 w-5 text-muted-foreground" />
                ) : null}
              </div>
            )}
            <div className="min-w-0 flex-1 ">
              <div className="flex items-center justify-between">
                <CardTitle size="default" className="truncate">
                  {title}
                </CardTitle>
                {total && (
                  <span className="text-xl font-bold text-foreground flex-shrink-0">
                    {formatTotal(total)}
                  </span>
                )}
              </div>
              {description && (
                <CardDescription size="default" className="mt-1">
                  {description}
                </CardDescription>
              )}
            </div>
          </div>
          {headerAction && <div className="flex-shrink-0 ml-4">{headerAction}</div>}
        </CardHeader>
      );
    };

    const renderChart = () => {
      if (loading) {
        return (
          <div className="chart-container flex items-center justify-center">
            <div className="animate-pulse space-y-4 w-full">
              <div className="h-4 bg-muted rounded w-3/4 mx-auto"></div>
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div
                    key={i}
                    className="h-8 bg-muted rounded"
                    style={{ width: `${Math.random() * 40 + 40}%` }}
                  ></div>
                ))}
              </div>
            </div>
          </div>
        );
      }

      return <div className="chart-container overflow-hidden rounded-md">{children}</div>;
    };

    return (
      <Card
        ref={ref}
        className={cn(
          chartCardVariants({ colorScheme, chartHeight }),
          isClickable && "cursor-pointer hover:shadow-card-hover transition-all duration-200",
          selected && "ring-4 ring-current ring-opacity-20",
          "flex flex-col p-4",
          className
        )}
        size="none"
        interactive={isClickable}
        onClick={onClick}
        {...props}
      >
        {renderHeader()}

        <CardContent size="none" className={cn("flex-1 flex flex-col", !showHeader && "pt-3")}>
          {renderChart()}

          {footer && <div className="p-3">{footer}</div>}
        </CardContent>
      </Card>
    );
  }
);

ChartCard.displayName = "ChartCard";

// Helper component for chart legends
export interface ChartLegendProps {
  items: Array<{ label: string; color: string; value?: string | number }>;
  orientation?: "horizontal" | "vertical";
  className?: string;
}

const ChartLegend = React.forwardRef<HTMLDivElement, ChartLegendProps>(
  ({ items, orientation = "horizontal", className, ...props }, ref) => {
    const formatValue = (val: string | number | undefined) => {
      if (val === undefined) return "";
      if (typeof val === "number") {
        return ` (${new Intl.NumberFormat("en").format(val)})`;
      }
      return ` (${val})`;
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex gap-4",
          orientation === "vertical" ? "flex-col" : "flex-wrap",
          className
        )}
        {...props}
      >
        {items.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="h-3 w-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: item.color }}
            />
            <span className="text-sm text-muted-foreground">
              {item.label}
              {formatValue(item.value)}
            </span>
          </div>
        ))}
      </div>
    );
  }
);

ChartLegend.displayName = "ChartLegend";

export { ChartCard, ChartLegend, chartCardVariants };
