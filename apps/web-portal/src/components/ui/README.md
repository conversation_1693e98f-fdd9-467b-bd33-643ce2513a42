# OneIoT Card Component System

A comprehensive, standardized card component system built with shadcn/ui principles and tweakcn-style CSS variables.

## Overview

This card system provides a unified approach to displaying content in card format across the OneIoT platform. It replaces the previous mixed approach with a consistent, type-safe, and highly customizable solution.

## Features

- 🎨 **Consistent Design**: Unified styling using CSS variables
- 🌙 **Dark Mode**: Automatic dark mode support
- 📱 **Responsive**: Mobile-first responsive design
- ♿ **Accessible**: ARIA compliant and keyboard navigable
- 🔧 **Customizable**: Extensive variant system
- 📦 **Type Safe**: Full TypeScript support
- 🚀 **Performance**: Optimized rendering and animations

## Components

### Base Components

#### Card
The foundational card component with variant support.

```tsx
import { Card, CardHeader, CardTitle, CardContent } from "@components/ui";

<Card variant="elevated" size="lg" interactive>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    Card content goes here
  </CardContent>
</Card>
```

**Props:**
- `variant`: `"default" | "elevated" | "outlined" | "ghost" | "gradient"`
- `size`: `"sm" | "default" | "lg" | "xl"`
- `interactive`: `boolean` - Adds hover effects and cursor pointer
- `rounded`: `"none" | "sm" | "default" | "lg" | "xl" | "full"`

### Specialized Components

#### StatCard
Perfect for displaying metrics, statistics, and KPIs.

```tsx
import { StatCard } from "@components/ui";
import { TrendingUp } from "lucide-react";

<StatCard
  title="Active Users"
  value={1234}
  colorScheme="success"
  icon={TrendingUp}
  description="Currently online"
  trend={{ value: 12, direction: "up", label: "vs last month" }}
  onClick={handleClick}
/>
```

**Use Cases:**
- Dashboard metrics
- KPI displays
- Status indicators
- Performance stats

#### MetricCard
Ideal for displaying multiple related metrics in a structured format.

```tsx
import { MetricCard } from "@components/ui";

const metrics = [
  { label: "CPU Usage", value: "45%", icon: Cpu },
  { label: "Memory", value: "2.1GB", icon: HardDrive },
  { label: "Network", value: "1.2MB/s", icon: Wifi },
];

<MetricCard
  title="System Resources"
  description="Current system utilization"
  metrics={metrics}
  layout="grid"
  columns={2}
  colorScheme="info"
/>
```

**Use Cases:**
- System dashboards
- Resource monitoring
- Multi-metric displays
- Comparison views

#### ChartCard
Designed specifically for containing charts and data visualizations.

```tsx
import { ChartCard } from "@components/ui";
import { BarChart } from "recharts";

<ChartCard
  title="Revenue Trends"
  description="Monthly revenue over time"
  total="$45,231"
  chartHeight="lg"
  colorScheme="primary"
>
  <BarChart data={chartData} />
</ChartCard>
```

**Use Cases:**
- Data visualizations
- Chart containers
- Analytics displays
- Report sections

#### ActionCard
Perfect for interactive cards with actions and navigation.

```tsx
import { ActionCard } from "@components/ui";
import { Edit2, Trash2 } from "lucide-react";

<ActionCard
  title="Project Alpha"
  description="IoT monitoring project"
  image="/project.jpg"
  primaryAction={{
    label: "View Details",
    onClick: handleView
  }}
  actions={[
    { label: "Edit", icon: Edit2, onClick: handleEdit },
    { label: "Delete", icon: Trash2, onClick: handleDelete, variant: "destructive" }
  ]}
/>
```

**Use Cases:**
- Project cards
- Item listings
- Navigation cards
- Action-heavy interfaces

## Color Schemes

All components support consistent color schemes:

- `default`: Standard neutral colors
- `success`: Green tones for positive states
- `warning`: Yellow/orange for warnings
- `danger`: Red tones for errors/destructive actions
- `info`: Blue tones for informational content
- `primary`: Brand colors

## CSS Variables

The system uses CSS variables for consistent theming:

```css
:root {
  /* Card System */
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --card-border: 214.3 31.8% 91.4%;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1);

  /* Semantic Colors */
  --success: 142 76% 36%;
  --warning: 38 92% 50%;
  --destructive: 0 100% 50%;
  --info: 221 83% 53%;
  --primary: 222.2 47.4% 11.2%;
}
```

## Responsive Design

All components are mobile-first and responsive:

```tsx
// Responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  <StatCard title="Mobile" value={123} />
  <StatCard title="Tablet" value={456} />
  <StatCard title="Desktop" value={789} />
</div>
```

## Accessibility

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- Focus management

## Performance

- Optimized re-renders
- Lazy loading support
- Efficient animations
- Minimal bundle impact

## Migration

See [Migration Guide](./card-migration-guide.md) for detailed migration instructions from old card components.

## Examples

See [Card Examples](./card-examples.tsx) for comprehensive usage examples.

## Best Practices

1. **Choose the Right Component**: Use StatCard for metrics, ActionCard for interactive content, etc.
2. **Consistent Color Schemes**: Stick to the defined color schemes for consistency
3. **Responsive Design**: Always test on different screen sizes
4. **Loading States**: Implement loading states for better UX
5. **Accessibility**: Ensure proper ARIA labels and keyboard navigation

## Customization

### Custom Variants

Extend existing variants:

```tsx
import { cva } from "class-variance-authority";
import { cardVariants } from "@components/ui";

const customVariants = cva(cardVariants(), {
  variants: {
    special: {
      glow: "shadow-lg shadow-primary/25 border-primary/50",
    },
  },
});
```

### Theme Overrides

Override CSS variables for custom themes:

```css
.theme-dark-blue {
  --card: 220 13% 18%;
  --card-foreground: 220 9% 46%;
  --primary: 217 91% 60%;
}
```

## Support

For questions, issues, or feature requests:
1. Check the migration guide
2. Review the examples
3. Create an issue in the project repository
4. Consult the team documentation
