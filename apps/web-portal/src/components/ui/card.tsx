import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@components/shadcn/utils";

const cardVariants = cva("rounded-lg border bg-card text-card-foreground  transition-all ", {
  variants: {
    variant: {
      default: "border-card-border",
      second: "bg-card !p-4",
      third: "bg-secondary !p-4 ",
      elevated: "shadow-card-hover border-card-border",
      outlined: "border-2 border-card-border bg-transparent",
      ghost: "border-transparent bg-transparent shadow-none",
      gradient: "bg-gradient-to-br from-card to-card/80 border-card-border"
    },
    size: {
      none: "p-0",
      sm: "p-4",
      default: "p-6",
      lg: "p-8",
      xl: "p-10"
    },
    interactive: {
      true: "cursor-pointer hover:shadow-sm hover:scale-[1.01] active:scale-[0.98]",
      false: ""
    },
    rounded: {
      none: "rounded-none",
      sm: "rounded-sm",
      default: "rounded-lg",
      lg: "rounded-xl",
      xl: "rounded-2xl",
      full: "rounded-full"
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    interactive: false,
    rounded: "default"
  }
});

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, interactive, rounded, asChild = false, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, size, interactive, rounded, className }))}
        {...props}
      />
    );
  }
);
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    size?: "sm" | "default" | "lg";
  }
>(({ className, size = "default", ...props }, ref) => {
  const sizeClasses = {
    none: "p-0 space-y-1",
    sm: "p-3 space-y-1",
    default: "p-6 space-y-1.5",
    lg: "p-8 space-y-2"
  };

  return <div ref={ref} className={cn("flex flex-col", sizeClasses[size], className)} {...props} />;
});
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement> & {
    size?: "sm" | "default" | "lg" | "xl";
    as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  }
>(({ className, size = "default", as: Component = "h3", ...props }, ref) => {
  const sizeClasses = {
    sm: "text-sm font-medium",
    default: "text-lg font-semibold leading-none tracking-tight",
    lg: "text-xl font-semibold leading-none tracking-tight",
    xl: "text-2xl font-bold leading-none tracking-tight"
  };

  return <Component ref={ref} className={cn(sizeClasses[size], className)} {...props} />;
});
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement> & {
    size?: "sm" | "default" | "lg";
  }
>(({ className, size = "default", ...props }, ref) => {
  const sizeClasses = {
    sm: "text-xs text-muted-foreground",
    default: "text-sm text-muted-foreground",
    lg: "text-base text-muted-foreground"
  };

  return <p ref={ref} className={cn(sizeClasses[size], className)} {...props} />;
});
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    size?: "sm" | "default" | "lg";
    noPadding?: boolean;
  }
>(({ className, size = "default", noPadding = false, ...props }, ref) => {
  if (noPadding) {
    return <div ref={ref} className={cn(className)} {...props} />;
  }

  const sizeClasses = {
    sm: "p-3 pt-0",
    default: "p-6 pt-0",
    lg: "p-8 pt-0"
  };

  return <div ref={ref} className={cn(sizeClasses[size], className)} {...props} />;
});
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    size?: "sm" | "default" | "lg";
  }
>(({ className, size = "default", ...props }, ref) => {
  const sizeClasses = {
    sm: "flex items-center p-3 pt-0",
    default: "flex items-center p-6 pt-0",
    lg: "flex items-center p-8 pt-0"
  };

  return <div ref={ref} className={cn(sizeClasses[size], className)} {...props} />;
});
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, cardVariants };
