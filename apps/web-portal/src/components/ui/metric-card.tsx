import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@components/shadcn/utils";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "./card";
import { LucideIcon } from "lucide-react";

const metricCardVariants = cva("", {
  variants: {
    colorScheme: {
      default: "",
      success: "border-success/20 bg-success/5",
      warning: "border-warning/20 bg-warning/5",
      danger: "border-destructive/20 bg-destructive/5",
      info: "border-info/20 bg-info/5",
      primary: "border-primary/20 bg-primary/5",
    },
    size: {
      sm: "min-w-[180px]",
      default: "min-w-[240px]",
      lg: "min-w-[300px]",
      xl: "min-w-[360px]",
    },
  },
  defaultVariants: {
    colorScheme: "default",
    size: "default",
  },
});

export interface MetricItem {
  label: string;
  value: string | number;
  icon?: LucideIcon | React.ReactNode;
  color?: string;
  trend?: {
    value: number;
    direction: "up" | "down" | "neutral";
  };
}

export interface MetricCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof metricCardVariants> {
  title: string;
  description?: string;
  icon?: LucideIcon | React.ReactNode;
  metrics: MetricItem[];
  loading?: boolean;
  interactive?: boolean;
  layout?: "grid" | "list";
  columns?: 1 | 2 | 3;
  showDividers?: boolean;
  footer?: React.ReactNode;
  headerAction?: React.ReactNode;
}

const MetricCard = React.forwardRef<HTMLDivElement, MetricCardProps>(
  ({
    className,
    title,
    description,
    icon,
    metrics = [],
    colorScheme = "default",
    size = "default",
    loading = false,
    interactive = false,
    layout = "list",
    columns = 2,
    showDividers = true,
    footer,
    headerAction,
    onClick,
    ...props
  }, ref) => {
    const IconComponent = icon as LucideIcon;
    const isClickable = !!onClick || interactive;

    const formatValue = (val: string | number) => {
      if (typeof val === "number") {
        return new Intl.NumberFormat("en").format(val);
      }
      return val;
    };

    const renderMetricIcon = (metric: MetricItem) => {
      if (!metric.icon) return null;

      const MetricIconComponent = metric.icon as LucideIcon;
      
      return (
        <div className="flex-shrink-0">
          {React.isValidElement(metric.icon) ? (
            metric.icon
          ) : MetricIconComponent ? (
            <MetricIconComponent className="h-4 w-4 text-muted-foreground" />
          ) : null}
        </div>
      );
    };

    const renderTrend = (trend?: MetricItem["trend"]) => {
      if (!trend) return null;

      const trendColors = {
        up: "text-success",
        down: "text-destructive",
        neutral: "text-muted-foreground",
      };

      const trendSymbol = {
        up: "↗",
        down: "↘", 
        neutral: "→",
      };

      return (
        <span className={cn("text-xs ml-2", trendColors[trend.direction])}>
          {trendSymbol[trend.direction]} {Math.abs(trend.value)}%
        </span>
      );
    };

    const renderMetrics = () => {
      if (loading) {
        return (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="animate-pulse flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="h-4 w-4 bg-muted rounded"></div>
                  <div className="h-4 bg-muted rounded w-20"></div>
                </div>
                <div className="h-6 bg-muted rounded w-16"></div>
              </div>
            ))}
          </div>
        );
      }

      if (layout === "grid") {
        const gridCols = {
          1: "grid-cols-1",
          2: "grid-cols-2",
          3: "grid-cols-3",
        };

        return (
          <div className={cn("grid gap-4", gridCols[columns])}>
            {metrics.map((metric, index) => (
              <div
                key={index}
                className={cn(
                  "space-y-2 p-3 rounded-lg bg-muted/30",
                  showDividers && index < metrics.length - 1 && "border-r border-border"
                )}
              >
                <div className="flex items-center space-x-2">
                  {renderMetricIcon(metric)}
                  <span className="text-sm text-muted-foreground">{metric.label}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-lg font-semibold">{formatValue(metric.value)}</span>
                  {renderTrend(metric.trend)}
                </div>
              </div>
            ))}
          </div>
        );
      }

      // List layout (default)
      return (
        <div className="space-y-3">
          {metrics.map((metric, index) => (
            <div
              key={index}
              className={cn(
                "flex items-center justify-between py-2",
                showDividers && index < metrics.length - 1 && "border-b border-border"
              )}
            >
              <div className="flex items-center space-x-3 min-w-0 flex-1">
                {renderMetricIcon(metric)}
                <span className="text-sm font-medium text-muted-foreground truncate">
                  {metric.label}
                </span>
              </div>
              <div className="flex items-center flex-shrink-0">
                <span className="text-lg font-semibold">{formatValue(metric.value)}</span>
                {renderTrend(metric.trend)}
              </div>
            </div>
          ))}
        </div>
      );
    };

    return (
      <Card
        ref={ref}
        className={cn(
          metricCardVariants({ colorScheme, size }),
          isClickable && "cursor-pointer hover:shadow-card-hover transition-all duration-200",
          className
        )}
        interactive={isClickable}
        onClick={onClick}
        {...props}
      >
        <CardHeader size="sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {icon && (
                <div className="flex-shrink-0">
                  {React.isValidElement(icon) ? (
                    icon
                  ) : IconComponent ? (
                    <IconComponent className="h-5 w-5 text-muted-foreground" />
                  ) : null}
                </div>
              )}
              <div className="min-w-0 flex-1">
                <CardTitle size="sm">{title}</CardTitle>
                {description && (
                  <CardDescription size="sm">{description}</CardDescription>
                )}
              </div>
            </div>
            {headerAction && <div className="flex-shrink-0">{headerAction}</div>}
          </div>
        </CardHeader>

        <CardContent size="sm">
          {renderMetrics()}
          {footer && (
            <div className="mt-3 pt-3 border-t border-border">
              {footer}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }
);

MetricCard.displayName = "MetricCard";

export { MetricCard, metricCardVariants };
