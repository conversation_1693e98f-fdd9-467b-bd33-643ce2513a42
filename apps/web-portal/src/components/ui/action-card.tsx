import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@components/shadcn/utils";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "./card";
import { LucideIcon, ExternalLink } from "lucide-react";

const actionCardVariants = cva("", {
  variants: {
    colorScheme: {
      default: "",
      success: "hover:border-success/40 hover:bg-success/5",
      warning: "hover:border-warning/40 hover:bg-warning/5",
      danger: "hover:border-destructive/40 hover:bg-destructive/5",
      info: "hover:border-info/40 hover:bg-info/5",
      primary: "hover:border-primary/40 hover:bg-primary/5",
    },
    layout: {
      default: "",
      horizontal: "flex-row",
      compact: "p-4",
    },
  },
  defaultVariants: {
    colorScheme: "default",
    layout: "default",
  },
});

export interface ActionItem {
  label: string;
  icon?: LucideIcon | React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: "default" | "destructive" | "secondary";
}

export interface ActionCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof actionCardVariants> {
  title: string;
  description?: string;
  icon?: LucideIcon | React.ReactNode;
  image?: string;
  loading?: boolean;
  actions?: ActionItem[];
  primaryAction?: ActionItem;
  secondaryAction?: ActionItem;
  showExternalLink?: boolean;
  externalLinkTo?: string;
  footer?: React.ReactNode;
  badge?: React.ReactNode;
  children?: React.ReactNode;
}

const ActionCard = React.forwardRef<HTMLDivElement, ActionCardProps>(
  ({
    className,
    title,
    description,
    icon,
    image,
    colorScheme = "default",
    layout = "default",
    loading = false,
    actions = [],
    primaryAction,
    secondaryAction,
    showExternalLink = false,
    externalLinkTo,
    footer,
    badge,
    children,
    onClick,
    ...props
  }, ref) => {
    const IconComponent = icon as LucideIcon;
    const isClickable = !!onClick;

    const renderIcon = () => {
      if (image) {
        return (
          <div className="flex-shrink-0">
            <img
              src={image}
              alt={title}
              className="h-12 w-12 rounded-lg object-cover"
            />
          </div>
        );
      }

      if (icon) {
        return (
          <div className="flex-shrink-0">
            {React.isValidElement(icon) ? (
              icon
            ) : IconComponent ? (
              <IconComponent className="h-12 w-12 text-muted-foreground" />
            ) : null}
          </div>
        );
      }

      return null;
    };

    const renderActions = () => {
      const allActions = [
        ...(primaryAction ? [{ ...primaryAction, isPrimary: true }] : []),
        ...(secondaryAction ? [{ ...secondaryAction, isSecondary: true }] : []),
        ...actions,
      ];

      if (allActions.length === 0) return null;

      return (
        <div className="flex items-center space-x-2">
          {allActions.map((action, index) => {
            const ActionIconComponent = action.icon as LucideIcon;
            const isPrimary = "isPrimary" in action && action.isPrimary;
            const isSecondary = "isSecondary" in action && action.isSecondary;

            return (
              <button
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  action.onClick?.();
                }}
                disabled={action.disabled}
                className={cn(
                  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
                  "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  "disabled:pointer-events-none disabled:opacity-50",
                  isPrimary && "bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3",
                  isSecondary && "border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3",
                  !isPrimary && !isSecondary && "hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0",
                  action.variant === "destructive" && "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                )}
                title={action.label}
              >
                {action.icon && (
                  <div className={cn(isPrimary || isSecondary ? "mr-2" : "")}>
                    {React.isValidElement(action.icon) ? (
                      action.icon
                    ) : ActionIconComponent ? (
                      <ActionIconComponent className="h-4 w-4" />
                    ) : null}
                  </div>
                )}
                {(isPrimary || isSecondary) && action.label}
              </button>
            );
          })}
        </div>
      );
    };

    const renderContent = () => {
      if (loading) {
        return (
          <div className="animate-pulse space-y-3">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-muted rounded-lg"></div>
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            </div>
            {children && <div className="h-20 bg-muted rounded"></div>}
          </div>
        );
      }

      if (layout === "horizontal") {
        return (
          <div className="flex items-center space-x-4">
            {renderIcon()}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <CardTitle size="default" className="truncate">
                  {title}
                </CardTitle>
                {badge && <div className="flex-shrink-0 ml-2">{badge}</div>}
              </div>
              {description && (
                <CardDescription size="default" className="mt-1">
                  {description}
                </CardDescription>
              )}
              {children && <div className="mt-3">{children}</div>}
            </div>
            <div className="flex items-center space-x-2">
              {renderActions()}
              {showExternalLink && (
                <ExternalLink className="h-4 w-4 text-muted-foreground" />
              )}
            </div>
          </div>
        );
      }

      // Default vertical layout
      return (
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 min-w-0 flex-1">
              {renderIcon()}
              <div className="min-w-0 flex-1">
                <div className="flex items-center justify-between">
                  <CardTitle size="default" className="truncate">
                    {title}
                  </CardTitle>
                  {badge && <div className="flex-shrink-0 ml-2">{badge}</div>}
                </div>
                {description && (
                  <CardDescription size="default" className="mt-1">
                    {description}
                  </CardDescription>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
              {showExternalLink && (
                <ExternalLink className="h-4 w-4 text-muted-foreground" />
              )}
            </div>
          </div>
          
          {children && <div>{children}</div>}
          
          {(actions.length > 0 || primaryAction || secondaryAction) && (
            <div className="flex items-center justify-end">
              {renderActions()}
            </div>
          )}
        </div>
      );
    };

    return (
      <Card
        ref={ref}
        className={cn(
          actionCardVariants({ colorScheme, layout }),
          isClickable && "cursor-pointer hover:shadow-card-hover transition-all duration-200",
          className
        )}
        interactive={isClickable}
        onClick={onClick}
        {...props}
      >
        <CardContent size="default">
          {renderContent()}
        </CardContent>
        
        {footer && (
          <CardFooter size="default" className="border-t border-border">
            {footer}
          </CardFooter>
        )}
      </Card>
    );
  }
);

ActionCard.displayName = "ActionCard";

export { ActionCard, actionCardVariants };
