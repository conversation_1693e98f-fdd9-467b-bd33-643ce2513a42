import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@components/shadcn/utils";
import { Card } from "./card";
import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react";
import { formattedNumber } from "../../pages/MonitorPage/utils";
import clsx from "clsx";

const chartStatCardVariants = cva("", {
  variants: {
    colorScheme: {
      default: "",
      success: "ring-success/20 hover:ring-success/30",
      warning: "ring-warning/20 hover:ring-warning/30", 
      danger: "ring-destructive/20 hover:ring-destructive/30",
      info: "ring-info/20 hover:ring-info/30",
      primary: "ring-primary/20 hover:ring-primary/30",
    },
  },
  defaultVariants: {
    colorScheme: "default",
  },
});

const iconVariants = cva("", {
  variants: {
    colorScheme: {
      default: "text-muted-foreground bg-muted/10",
      success: "text-success bg-success/10",
      warning: "text-warning bg-warning/10",
      danger: "text-destructive bg-destructive/10", 
      info: "text-info bg-info/10",
      primary: "text-primary bg-primary/10",
    },
  },
  defaultVariants: {
    colorScheme: "default",
  },
});

export interface ChartStatCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chartStatCardVariants> {
  title: string;
  value?: string | number;
  icon?: LucideIcon;
  trending?: number;
  children: React.ReactNode; // Chart component
  loading?: boolean;
  interactive?: boolean;
}

const ChartStatCard = React.forwardRef<HTMLDivElement, ChartStatCardProps>(
  ({
    className,
    title,
    value,
    icon,
    trending = 0,
    colorScheme = "default",
    loading = false,
    interactive = false,
    children,
    onClick,
    ...props
  }, ref) => {
    const IconComponent = icon as LucideIcon;
    const isClickable = !!onClick || interactive;

    const formatValue = (val: string | number | undefined) => {
      if (val === undefined) return "—";
      if (typeof val === "number") {
        return formattedNumber(val);
      }
      return val;
    };

    const getIconColor = () => {
      const colors = {
        success: "rgb(34, 197, 94)", // emerald-500
        danger: "rgb(239, 68, 68)", // red-500  
        info: "rgb(59, 130, 246)", // blue-500
        primary: "rgb(147, 51, 234)", // purple-500
        warning: "rgb(245, 158, 11)", // amber-500
        default: "rgb(107, 114, 128)", // gray-500
      };
      return colors[colorScheme || "default"];
    };

    const getIconBgColor = () => {
      const iconColor = getIconColor();
      // Convert RGB to RGBA with 0.1 alpha
      const rgb = iconColor.match(/\d+/g);
      if (rgb) {
        return `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 0.1)`;
      }
      return "rgba(107, 114, 128, 0.1)";
    };

    if (loading) {
      return (
        <Card className={cn("flex flex-col gap-4 !p-0", className)}>
          <div className="p-4 flex items-start justify-between">
            <div className="space-y-2">
              <div className="h-4 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded w-20" />
            </div>
            <div className="w-14 h-14 bg-muted animate-pulse rounded-full" />
          </div>
          <div className="h-16 bg-muted animate-pulse" />
        </Card>
      );
    }

    return (
      <Card
        ref={ref}
        className={cn(
          chartStatCardVariants({ colorScheme }),
          isClickable && "cursor-pointer hover:shadow-card-hover transition-all duration-200",
          "flex flex-col gap-4 !p-0",
          className
        )}
        interactive={isClickable}
        onClick={onClick}
        {...props}
      >
        <div className="p-4 flex items-start justify-between">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">{title}</h3>
            <div className="flex items-end gap-2">
              <p className="text-2xl font-bold tracking-tight">{formatValue(value)}</p>
              <div
                className={clsx(
                  "flex items-center gap-1 font-semibold text-sm",
                  trending >= 0 ? "text-success" : "text-destructive"
                )}
                style={{ marginBottom: ".15rem" }}
              >
                {trending >= 0 ? <TrendingUp size="1rem" /> : <TrendingDown size="1rem" />}
                <span>{formattedNumber(trending)}%</span>
              </div>
            </div>
          </div>
          {IconComponent && (
            <div
              style={{ 
                color: getIconColor(), 
                backgroundColor: getIconBgColor()
              }}
              className={clsx(
                "w-14 h-14 flex items-center justify-center p-4 duration-300 rounded-full",
                "shadow-md text-white z-10 group-hover:scale-105"
              )}
            >
              <IconComponent size="1.5rem" />
            </div>
          )}
        </div>
        <div className="flex-1">
          {children}
        </div>
      </Card>
    );
  }
);

ChartStatCard.displayName = "ChartStatCard";

export { ChartStatCard, chartStatCardVariants };
