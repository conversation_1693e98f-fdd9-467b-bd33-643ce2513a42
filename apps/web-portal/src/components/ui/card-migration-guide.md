# Card Component Migration Guide

This guide helps you migrate from the old card components to the new standardized card system.

## Overview

The new card system provides:
- Consistent styling using CSS variables (tweakcn-style)
- Type-safe props with TypeScript
- Variant-based styling using class-variance-authority
- Better accessibility and responsive design
- Unified API across all card types

## Component Mapping

### Old → New Component Mapping

| Old Component | New Component | Notes |
|---------------|---------------|-------|
| `InfoCard` | `ActionCard` | For cards with navigation/links |
| `InfoCard2` | `StatCard` | For metric/stat display |
| `InfoCard3` | `ActionCard` | For cards with actions |
| `InfoCard4` | `StatCard` | For metric/stat display |
| `CardGraph` | `ChartCard` | For chart containers |
| `TransparentCards` | `StatCard` | Use ghost variant |
| `ProjectCard` | `ActionCard` | For project/item cards |
| `ChartCard` | `ChartCard` | Direct replacement |
| `PageInfoCard` | `MetricCard` | For multiple metrics |

## Migration Examples

### InfoCard2 → StatCard

**Before:**
```tsx
<InfoCard2
  color="green"
  title="Online Devices"
  data={1234}
  icon={<CircleCheck size="1.625rem" />}
  footer="85.2% uptime"
  selected={isSelected}
  onClick={handleClick}
/>
```

**After:**
```tsx
<StatCard
  colorScheme="success"
  title="Online Devices"
  value={1234}
  icon={CircleCheck}
  description="85.2% uptime"
  selected={isSelected}
  onClick={handleClick}
/>
```

### CardGraph → ChartCard

**Before:**
```tsx
<CardGraph
  color="blue"
  title="Usage Chart"
  data={total}
  graph={<PieChart data={chartData} />}
  footer="Last 30 days"
/>
```

**After:**
```tsx
<ChartCard
  colorScheme="info"
  title="Usage Chart"
  total={total}
  footer="Last 30 days"
>
  <PieChart data={chartData} />
</ChartCard>
```

### ProjectCard → ActionCard

**Before:**
```tsx
<ProjectCard
  name="My Project"
  imgSrc="/project.jpg"
  onEdit={handleEdit}
  onDelete={handleDelete}
>
  Project description here
</ProjectCard>
```

**After:**
```tsx
<ActionCard
  title="My Project"
  image="/project.jpg"
  actions={[
    { label: "Edit", icon: Edit2, onClick: handleEdit },
    { label: "Delete", icon: Trash2, onClick: handleDelete, variant: "destructive" }
  ]}
>
  Project description here
</ActionCard>
```

## Color Scheme Migration

### Old Color Props → New ColorScheme

| Old Color | New ColorScheme | CSS Variable |
|-----------|-----------------|--------------|
| `"green"` | `"success"` | `--success` |
| `"red"` | `"danger"` | `--destructive` |
| `"blue"` | `"info"` | `--info` |
| `"yellow"` | `"warning"` | `--warning` |
| `"purple"` | `"primary"` | `--primary` |

## CSS Variables

The new system uses CSS variables for consistent theming:

```css
:root {
  /* Card System */
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --card-border: 214.3 31.8% 91.4%;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1);

  /* Semantic Colors */
  --success: 142 76% 36%;
  --warning: 38 92% 50%;
  --destructive: 0 100% 50%;
  --info: 221 83% 53%;

  /* Radius System */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}
```

## Step-by-Step Migration

### 1. Update Imports

**Before:**
```tsx
import InfoCard2 from "@components/Card/InfoCard2";
import CardGraph from "@components/Card/CardGraph";
```

**After:**
```tsx
import { StatCard, ChartCard } from "@components/ui";
```

### 2. Update Props

- `color` → `colorScheme`
- `data` → `value`
- `footer` → `description` or `footer`
- `selected` → `selected`
- `graph` → children

### 3. Update Styling

Remove hardcoded colors and use the new color schemes:
- Use `colorScheme` prop instead of `color`
- Remove inline styles with hardcoded colors
- Use CSS variables for custom styling

### 4. Test Dark Mode

The new components automatically support dark mode through CSS variables.

## Advanced Usage

### Custom Variants

You can extend the card variants:

```tsx
import { cva } from "class-variance-authority";
import { cardVariants } from "@components/ui";

const customCardVariants = cva(cardVariants(), {
  variants: {
    customVariant: {
      special: "bg-gradient-to-r from-purple-500 to-pink-500",
    },
  },
});
```

### Theming

Override CSS variables for custom themes:

```css
.theme-custom {
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --success: 142 76% 36%;
  --warning: 38 92% 50%;
}
```

## Breaking Changes

1. **Color prop removed**: Use `colorScheme` instead
2. **Different prop names**: `data` → `value`, etc.
3. **Icon handling**: Pass icon component directly, not as JSX
4. **Layout changes**: Some layouts may need adjustment

## Benefits

1. **Consistency**: All cards follow the same design system
2. **Type Safety**: Full TypeScript support
3. **Accessibility**: Better ARIA support and keyboard navigation
4. **Performance**: Optimized rendering and animations
5. **Maintainability**: Single source of truth for card styling
6. **Dark Mode**: Automatic dark mode support
7. **Customization**: Easy to extend and customize

## Support

For questions or issues during migration, please refer to the component documentation or create an issue in the project repository.
