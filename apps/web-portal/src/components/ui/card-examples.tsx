import React from "react";
import {
  StatCard,
  MetricCard,
  ChartCard,
  ActionCard,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "./index";
import {
  CircleCheck,
  XCircle,
  TrendingUp,
  Users,
  Server,
  Activity,
  Edit2,
  Trash2,
  ExternalLink,
  BarChart3,
} from "lucide-react";

// Example data
const chartData = [
  { name: "Jan", value: 400 },
  { name: "Feb", value: 300 },
  { name: "Mar", value: 600 },
  { name: "Apr", value: 800 },
  { name: "May", value: 500 },
];

const metrics = [
  { label: "Total Users", value: 1234, icon: Users },
  { label: "Active Sessions", value: 89, icon: Activity },
  { label: "Server Uptime", value: "99.9%", icon: Server },
  { label: "Response Time", value: "120ms", icon: TrendingUp },
];

export const CardExamples = () => {
  return (
    <div className="space-y-8 p-6">
      <h1 className="text-3xl font-bold">Card Component Examples</h1>

      {/* Basic Card */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Basic Card</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Default Card</CardTitle>
              <CardDescription>A simple card with default styling</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This is the basic card content.</p>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardHeader>
              <CardTitle>Elevated Card</CardTitle>
              <CardDescription>Card with elevated shadow</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card has more prominent shadow.</p>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardHeader>
              <CardTitle>Outlined Card</CardTitle>
              <CardDescription>Card with border emphasis</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card emphasizes the border.</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Stat Cards */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Stat Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatCard
            title="Online Devices"
            value={1234}
            colorScheme="success"
            icon={CircleCheck}
            description="85.2% uptime"
            trend={{ value: 12, direction: "up", label: "vs last month" }}
          />

          <StatCard
            title="Offline Devices"
            value={89}
            colorScheme="danger"
            icon={XCircle}
            description="14.8% downtime"
            trend={{ value: 5, direction: "down", label: "vs last month" }}
          />

          <StatCard
            title="Total Revenue"
            value="$45,231"
            colorScheme="info"
            icon={TrendingUp}
            description="Monthly earnings"
            trend={{ value: 8, direction: "up", label: "vs last month" }}
          />

          <StatCard
            title="Active Users"
            value={2345}
            colorScheme="warning"
            icon={Users}
            description="Currently online"
            layout="compact"
          />
        </div>
      </section>

      {/* Metric Cards */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Metric Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <MetricCard
            title="System Overview"
            description="Key performance indicators"
            icon={Activity}
            metrics={metrics}
            colorScheme="primary"
          />

          <MetricCard
            title="Server Statistics"
            description="Infrastructure metrics"
            icon={Server}
            metrics={metrics.slice(0, 2)}
            layout="grid"
            columns={2}
            colorScheme="success"
          />
        </div>
      </section>

      {/* Chart Cards */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Chart Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ChartCard
            title="Monthly Revenue"
            description="Revenue trends over time"
            total="$45,231"
            icon={BarChart3}
            colorScheme="info"
            chartHeight="lg"
          >
            <div className="h-full flex items-center justify-center bg-muted/30 rounded">
              <p className="text-muted-foreground">Chart Component Goes Here</p>
            </div>
          </ChartCard>

          <ChartCard
            title="User Activity"
            total={1234}
            showBorder={false}
            chartHeight="default"
          >
            <div className="h-full flex items-center justify-center bg-muted/30 rounded">
              <p className="text-muted-foreground">Activity Chart</p>
            </div>
          </ChartCard>
        </div>
      </section>

      {/* Action Cards */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Action Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ActionCard
            title="Project Alpha"
            description="A cutting-edge IoT project"
            image="/api/placeholder/64/64"
            colorScheme="primary"
            primaryAction={{
              label: "View Details",
              icon: ExternalLink,
              onClick: () => console.log("View details"),
            }}
            actions={[
              {
                label: "Edit",
                icon: Edit2,
                onClick: () => console.log("Edit"),
              },
              {
                label: "Delete",
                icon: Trash2,
                onClick: () => console.log("Delete"),
                variant: "destructive",
              },
            ]}
            badge={<span className="px-2 py-1 bg-success/20 text-success text-xs rounded">Active</span>}
          >
            <div className="mt-3 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Devices</span>
                <span className="font-medium">24</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Status</span>
                <span className="font-medium text-success">Online</span>
              </div>
            </div>
          </ActionCard>

          <ActionCard
            title="Quick Actions"
            description="Common tasks and operations"
            icon={Activity}
            layout="horizontal"
            colorScheme="default"
            actions={[
              {
                label: "Settings",
                icon: Edit2,
                onClick: () => console.log("Settings"),
              },
            ]}
          >
            <p className="text-sm text-muted-foreground">
              Access frequently used features and tools.
            </p>
          </ActionCard>
        </div>
      </section>

      {/* Interactive Examples */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Interactive Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            title="Clickable Stat"
            value={999}
            colorScheme="info"
            icon={TrendingUp}
            interactive
            onClick={() => alert("Stat card clicked!")}
          />

          <Card interactive onClick={() => alert("Basic card clicked!")}>
            <CardHeader>
              <CardTitle>Clickable Card</CardTitle>
              <CardDescription>This entire card is clickable</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Click anywhere on this card to trigger an action.</p>
            </CardContent>
          </Card>

          <ActionCard
            title="Navigation Card"
            description="Click to navigate"
            icon={ExternalLink}
            colorScheme="primary"
            onClick={() => alert("Navigation triggered!")}
            showExternalLink
          />
        </div>
      </section>

      {/* Loading States */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Loading States</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            title="Loading Stat"
            colorScheme="default"
            loading
          />

          <MetricCard
            title="Loading Metrics"
            metrics={[]}
            loading
          />

          <ChartCard
            title="Loading Chart"
            loading
          >
            <div />
          </ChartCard>
        </div>
      </section>
    </div>
  );
};

export default CardExamples;
