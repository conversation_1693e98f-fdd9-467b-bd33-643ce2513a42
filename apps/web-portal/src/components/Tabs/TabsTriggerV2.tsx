import { TabsTrigger } from "@components/shadcn/components/tabs";
import { TabsTriggerProps } from "@radix-ui/react-tabs";
import clsx from "clsx";
import React from "react";

const TabsTriggerV2 = ({
  value,
  children,
  className,
  ...props
}: {
  value: string;
  children: React.ReactNode;
  className?: string;
} & TabsTriggerProps) => {
  return (
    <TabsTrigger
      value={value}
      className={clsx(
        " data-[state=active]:bg-transparent data-[state=active]:text-brandColor  rounded-none border-b-2 border-transparent data-[state=active]:border-brandColor  data-[state=active]:font-medium",
        className
      )}
      {...props}
    >
      {children}
    </TabsTrigger>
  );
};

export default TabsTriggerV2;
