import { TabsList } from "@components/shadcn/components/tabs";
import clsx from "clsx";
import React from "react";

const TabsListV2 = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return (
    <TabsList
      className={clsx(
        "w-full justify-start items-center !border-gray-500/50  shadow-none border-t-0 border-x-0 rounded-none pb-0  !border-b-1  mb-6 !bg-transparent space-x-4",
        className
      )}
    >
      {children}
    </TabsList>
  );
};

export default TabsListV2;
