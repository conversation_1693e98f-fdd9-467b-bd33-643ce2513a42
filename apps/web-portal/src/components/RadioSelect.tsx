import clsx from "clsx";
import { Card } from "@components/ui";
import { Radio } from "@mui/material";
import colors from "tailwindcss/colors";
import { darkColors } from "@utils/color";
import { ReactElement} from "react";


type Props = React.HTMLAttributes<HTMLDivElement> & {
  selected?: boolean;
  data: React.ReactNode;
  disabled?: boolean;
  radioAlternate?: ReactElement;
  description?: React.ReactNode;
};

function RadioSelect({
  selected,
  data,
  radioAlternate,
  className,
  disabled,
  description,
  ...props
}: Props) {
  return (
    <Card
      size="sm"
      className={clsx(
        "group min-w-[200px] flex-1 mt-2 relative overflow-hidden",
        "flex cursor-pointer",
        selected && "!ring-1 !ring-primary !bg-primary/5",
        disabled && "hover:!ring-0 !ring-gray-600 text- !cursor-default",
        className
      )}
      data-testid={data}
      {...props}
    >
      {radioAlternate || (
        <Radio
          checked={selected}
          sx={{
            height: "20px",
            width: "20px",
            marginRight: "8px",
            color: colors.gray[400],
            "&.Mui-checked": {
              color: darkColors.brandColors
            }
          }}
        />
      )}
      <div className="">
        <div className={clsx("heading-3 mb-1", disabled && "text-gray-400 dark:text-gray-500")}>
          {data}
        </div>
        <p
          className={clsx(
            "content text-muted-foreground",
            selected && "!text-foreground",
            disabled && "!text-gray-400 dark:!text-gray-500"
          )}
        >
          {description}
        </p>
      </div>
    </Card>
  );
}

export default RadioSelect;
