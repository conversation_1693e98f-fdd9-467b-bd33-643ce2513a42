import { DarkModeContext } from "@hooks/useDarkMode";
import { Settings2 } from "lucide-react";
import { useContext, useEffect, useState } from "react";
import { Button } from "./shadcn/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "./shadcn/components/dropdown-menu";
import { Tabs, TabsList, TabsTrigger } from "./shadcn/components/tabs";

export const ZOOM_LEVELS = [
  { label: "small", value: "85%" },
  { label: "medium", value: "100%" },
  { label: "large", value: "115%" }
];

export function HeaderSettings() {
  const [darkMode, setDarkMode] = useContext(DarkModeContext);

  const [zoomLevel, setZoomLevel] = useState(localStorage.getItem("portalZoomLevel") || "medium");

  const zoomLevelHandler = (level: "small" | "medium" | "large") => {
    localStorage.setItem(`portalZoomLevel`, level);
    setZoomLevel(level);
  };

  useEffect(() => {
    const currentZoom = ZOOM_LEVELS.find((item) => item.label === zoomLevel);
    if (currentZoom) {
      document.documentElement.style.fontSize = currentZoom.value;
    }
  }, [zoomLevel]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="secondary" size="icon" className="size-9 border bg-background">
          <Settings2 size="1.2rem" className="text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-[16rem] space-y-1 p-2 !z-[1000]" align="end">
        <DropdownMenuLabel>Theme Mode</DropdownMenuLabel>
        <Tabs defaultValue={darkMode ? "dark" : "light"}>
          <TabsList className="grid  grid-cols-2 ">
            <TabsTrigger value="light" onClick={() => setDarkMode(false)}>
              Light
            </TabsTrigger>
            <TabsTrigger value="dark" onClick={() => setDarkMode(true)}>
              Dark
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <DropdownMenuSeparator />
        <DropdownMenuLabel>Adust Size</DropdownMenuLabel>
        <Tabs defaultValue={zoomLevel}>
          <TabsList className="grid  grid-cols-3">
            <TabsTrigger value="small" onClick={() => zoomLevelHandler("small")}>
              Small
            </TabsTrigger>
            <TabsTrigger value="medium" onClick={() => zoomLevelHandler("medium")}>
              Medium
            </TabsTrigger>
            <TabsTrigger value="large" onClick={() => zoomLevelHandler("large")}>
              Large
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default HeaderSettings;
