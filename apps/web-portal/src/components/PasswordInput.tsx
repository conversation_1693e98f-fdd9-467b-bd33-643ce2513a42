import { useState } from "react";
import Input from "./Input";
import { <PERSON><PERSON><PERSON>utton, LinearProgress, Tooltip, linearProgressClasses } from "@mui/material";
import { Eye, EyeOff, Info } from "lucide-react";
import styled from "@emotion/styled";
import CustomTooltip from "./CustomTooltip";
import { INPUT_ICON_SIZE } from "@utils/utilities";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";

const getActiveColor = (type) => {
  if (type === "Strong") return "#22c55e";
  if (type === "Medium") return "#eab308";
  return "#ef4444";
};

const BorderLinearProgress = styled(LinearProgress)(({ theme, message }) => ({
  height: ".25rem",
  borderRadius: 2,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[theme.palette.mode === "light" ? 200 : 800]
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: getActiveColor(message)
  }
}));

const PasswordInput = ({ register, showProgress = false, ...props }) => {
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [progress, setProgress] = useState("");
  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasDigit: false,
    hasSpecialChar: false
  });

  const handlePassword = (passwordValue) => {
    const strengthChecks = {
      length: false,
      hasUpperCase: false,
      hasLowerCase: false,
      hasDigit: false,
      hasSpecialChar: false
    };

    strengthChecks.length = passwordValue.length >= 8;
    strengthChecks.hasUpperCase = /[A-Z]+/.test(passwordValue);
    strengthChecks.hasLowerCase = /[a-z]+/.test(passwordValue);
    strengthChecks.hasDigit = /[0-9]+/.test(passwordValue);
    strengthChecks.hasSpecialChar = /[^A-Za-z0-9]+/.test(passwordValue);
    setPasswordStrength(strengthChecks);
    const verifiedList = Object.values(strengthChecks).filter((value) => value);

    const strength =
      verifiedList.length === 5 ? "Strong" : verifiedList.length >= 2 ? "Medium" : "Weak";

    setPassword(passwordValue);
    setProgress((verifiedList.length / 5) * 100);
    setMessage(strength);
  };

  const [hidePassword, setHidePassword] = useState(false);

  return (
    <div>
      <Input
        {...props}
        {...register}
        onChange={({ target }) => {
          handlePassword(target.value);
        }}
        type={hidePassword ? "text" : "password"}
        endIcon={
          <Tooltip title={hidePassword ? "Hide Password" : "Show Password"} arrow>
            <IconButton
              onClick={() => {
                setHidePassword((prev) => !prev);
              }}
            >
              {hidePassword ? <EyeOff size={INPUT_ICON_SIZE} /> : <Eye size={INPUT_ICON_SIZE} />}
            </IconButton>
          </Tooltip>
        }
      />
      {showProgress && password.length !== 0 && (
        <div className="mt-2 space-y-1">
          <BorderLinearProgress variant="determinate" message={message} value={progress} />
          <div className="between items-center">
            <p className="description" style={{ color: getActiveColor(message) }}>
              Password strength is {message}
            </p>
            <CustomTooltip
              title={
                <ul className="list-disc list-inside space-y-1 text-muted-foreground text-xs">
                  <li className={passwordStrength.length ? "text-green-500" : "text-red-500"}>
                    Minimum of 8 Characters
                  </li>
                  <li
                    className={passwordStrength.hasSpecialChar ? "text-green-500" : "text-red-500"}
                  >
                    At least one special character
                  </li>
                  <li className={passwordStrength.hasDigit ? "text-green-500" : "text-red-500"}>
                    At least one number
                  </li>
                  <li className={passwordStrength.hasUpperCase ? "text-green-500" : "text-red-500"}>
                    At least one Uppercase Letter
                  </li>
                  <li className={passwordStrength.hasLowerCase ? "text-green-500" : "text-red-500"}>
                    At least one Lowercase Letter
                  </li>
                </ul>
              }
            >
              <IconButton className="!p-0.5">
                <Info size={BUTTON_ICON_SIZE} />
              </IconButton>
            </CustomTooltip>
          </div>
        </div>
      )}
    </div>
  );
};

export default PasswordInput;
