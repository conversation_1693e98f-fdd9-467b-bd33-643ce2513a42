import { Button } from "@components/shadcn/components/button";
import { ScrollArea } from "@components/shadcn/components/scroll-area";
import { Card, CardTitle } from "@components/ui";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import clsx from "clsx";
import { MessageSquare, Plus } from "lucide-react";
import { Dispatch, SetStateAction } from "react";
import { QueryHistoryItem, SessionItem } from ".";

type MessageItem = {
  text: string;
  isBot: boolean;
  data?: {
    key: string;
    thing_name: string;
    value: string;
  }[];
};
const chatHistoryFormatter = (historyList: QueryHistoryItem[]) => {
  const formatted = historyList.flatMap((item) => [
    { text: item.nlQuery, isBot: false },
    {
      text: item.summary || "N/A",
      isBot: true,
      ...(item.data && { data: item.data })
    }
  ]);
  return formatted;
};
const ChatbotHistory = ({
  setCurrentSessionId,
  currentSessionId,
  chatHistory,
  setMessages
}: {
  setCurrentSessionId: Dispatch<SetStateAction<string | null>>;
  currentSessionId: string | null;
  chatHistory: SessionItem[];
  setMessages: Dispatch<SetStateAction<MessageItem[]>>;
}) => {
  return (
    <div className="w-80 flex-shrink-0">
      <Card className="h-full card-compact">
        <div className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center">
              <MessageSquare className="w-5 h-5 mr-2" />
              Recent Chats
            </CardTitle>
          </div>
          <Button
            className="w-full mt-2"
            size="sm"
            onClick={() => {
              setCurrentSessionId(null);
              setMessages([]);
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </Button>
        </div>
        <ScrollArea className="space-y-1 h-[calc(100vh-20rem)]">
          <div className=" space-y-2">
            {chatHistory.map((chat) => (
              <div
                key={chat.sessionId}
                className={clsx(
                  "p-2 rounded-lg hover:bg-secondary/50 cursor-pointer transition-colors  ",
                  currentSessionId === chat.sessionId ? "bg-secondary " : ""
                )}
                onClick={() => {
                  setMessages(chatHistoryFormatter(chat.history));
                  setCurrentSessionId(chat.sessionId);
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-sm truncate">{chat.title}</h4>
                    </div>
                    <p className="text-xs text-muted-foreground m truncate">
                      {chat.history[chat.history.length - 1]?.nlQuery || ""}
                    </p>
                    <div className="flex  items-center space-x-4 mt-1">
                      <span className="text-xs text-muted-foreground">
                        {chat.history.length} msgs
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {convetUTCToLocal(chat.history[chat.history.length - 1]?.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </Card>
    </div>
  );
  // return (
  //   <div className="hidden md:block w-80 shrink-0">
  //     <Card className=" h-full p-4 space-y-4">
  //       <Button
  //         className="w-full "
  //         onClick={() => {
  //           setCurrentSessionId(null);
  //           setMessages([]);
  //         }}
  //       >
  //         <PlusCircle className="mr-2 h-4 w-4" />
  //         New Chat
  //       </Button>
  //       <hr className="hr" />
  //       <div>
  //         <h3 className="mb-2 px-2 text-sm font-semibold text-muted-foreground">RECENT CHATS</h3>
  //         <ScrollArea className="space-y-1 h-[calc(100vh-15rem)]">
  //           {chatHistory
  //             .filter((item) => item.history.length)
  //             .map((chat) => (
  //               <Button
  //                 key={chat.sessionId}
  //                 variant="ghost"
  //                 onClick={() => {
  //                   setMessages(chatHistoryFormatter(chat.history));
  //                   setCurrentSessionId(chat.sessionId);
  //                 }}
  //                 className={clsx(
  //                   "w-full rounded-md  text-left transition-colors truncate justify-start  h-auto py-2 px-3",
  //                   currentSessionId === chat.sessionId
  //                     ? "bg-accent text-accent-foreground"
  //                     : "hover:bg-accent"
  //                 )}
  //               >
  //                 <MessageSquare className="mr-2 h-4 w-4" /> {chat.title}
  //               </Button>
  //             ))}
  //         </ScrollArea>
  //       </div>
  //     </Card>
  //   </div>
  // );
};

export default ChatbotHistory;
