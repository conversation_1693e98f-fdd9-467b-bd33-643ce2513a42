import { fetchApi } from "@api/_helpers";
import { RAG_API } from "@api/index";
import useLlmHistory from "@hooks/classic/LLM/useLlmHitstory";
import { useAppSelector } from "@src/store";
import { useMutation, useQuery } from "@tanstack/react-query";
import clsx from "clsx";
import { Bot, MessagesSquare, SendHorizonal, User } from "lucide-react";
import type React from "react";
import { FormEvent, useEffect, useRef, useState } from "react";
import Input from "../Input";
import { CardContent, CardFooter } from "../shadcn/components/card";
import { ScrollArea } from "../shadcn/components/scroll-area";
import { Card } from "@components/ui";
import CharLoadingSkeleton from "./CharLoadingSkeleton";
import ChatMessageHandler, { Message } from "./ChatMessageHandler";
import ChatbotHistory from "./ChatbotHistory";
import Button from "@components/Button";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import HeaderSection from "@components/layout/HeaderSection";

export type QueryHistoryItem = {
  id: number;
  nlQuery: string;
  data: Array<{
    key: string;
    thing_name: string;
    value: string;
  }>;
  summary: string;
  createdAt: string;
};

export type SessionItem = {
  sessionId: string;
  title: string;
  userEmail: string;
  history: QueryHistoryItem[];
};

const getResponseFromLLM = async (body: {
  question: string;
  tenant: string;
  sessionId?: string;
}) => {
  const response = await fetchApi(
    "/query-llm-2",
    {
      method: "POST",
      body
    },
    RAG_API
  );

  if (!response.ok) {
    throw new Error("Failed to communicate with LLM agent.");
  }

  const data = await response.json();

  if (data.status === "Failure") {
    throw new Error(data.message);
  }
  if (data.error) {
    throw new Error(data.error);
  }

  return data?.data as {
    summary: string;
    sessionId: string;
    data: Record<string, any>[];
  };
};
const useChatSuggestions = () => {
  return useQuery({
    queryKey: ["chatbot-suggestions"],
    queryFn: async () => {
      const response = await fetchApi("/suggestions", {}, RAG_API);
      if (!response.ok) {
        throw new Error("Failed to communicate with LLM agent.");
      }
      const data = await response.json();
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      return data.data as string[];
    }
  });
};

const ChatBot = ({ view = "largeScreen" }: { view: "largeScreen" | "smallScreen" }) => {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatHistory, setChatHistory] = useState<SessionItem[]>([]);
  const tenant = useAppSelector(({ user }) => user.tenant?.name) || "";

  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement | null>(null);

  // Function to scroll to bottom
  const scrollToBottom = () => {
    if (messagesEndRef.current && view === "largeScreen") {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const { data: suggestions, isLoading: isSuggestionLoading } = useChatSuggestions();

  const {
    isLoading: isHistoryLoading,
    data: previousMessages,
    refetch: refetchHistory
  } = useLlmHistory();

  useEffect(() => {
    if (previousMessages) {
      setChatHistory(previousMessages);
    }
  }, [previousMessages]);

  const askLLMMutation = useMutation({
    mutationFn: getResponseFromLLM,
    onSuccess: (data) => {
      setMessages((prev) => [...prev, { text: data.summary, isBot: true, data: data.data }]);
      refetchHistory();
      setCurrentSessionId(data.sessionId);
    },
    onError: (error) => {
      setMessages((prev) => [...prev, { text: error.message, isBot: true }]);
    }
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>, input: string) => {
    e.preventDefault();

    const userMessage = { text: input, isBot: false };
    setMessages((prev) => [...prev, userMessage]);
    askLLMMutation.mutate({
      question: input,
      tenant,
      ...(currentSessionId && { sessionId: currentSessionId })
    });
  };

  if (isHistoryLoading || isSuggestionLoading) {
    return <CharLoadingSkeleton rows={3} />;
  }

  return (
    <div className="space-y-4">
      {view === "largeScreen" && (
        <HeaderSection
          title="AI Assistant"
          description="Intelligent IoT data analysis and insights"
        />
      )}
      <div className="flex gap-6">
        {view === "largeScreen" && (
          <ChatbotHistory
            setCurrentSessionId={setCurrentSessionId}
            currentSessionId={currentSessionId}
            chatHistory={chatHistory}
            setMessages={setMessages}
          />
        )}
        <Card
          className={clsx(
            "flex-1 relative ",
            view === "smallScreen" && "p-0 bg-transparent border-none"
          )}
        >
          <div className="w-full  flex flex-col  flex-1  ">
            <CardContent className="flex-grow p-0 flex flex-col h-full ">
              <ScrollArea
                className={clsx(
                  "flex-grow px-2 ",
                  view === "largeScreen" ? "h-[calc(100vh-22rem)]" : "h-[23rem]"
                )}
                ref={scrollAreaRef}
              >
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={`flex  space-x-2 mb-8
                  ${message.isBot ? "" : "flex-row-reverse"}
                  `}
                  >
                    {message.isBot && (
                      <div
                        className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center bg-foreground mr-2
                                `}
                      >
                        <Bot className=" h-5 w-5 text-background " />
                      </div>
                    )}

                    <ChatMessageHandler message={message} />
                  </div>
                ))}
                {askLLMMutation.isPending && (
                  <div className="flex items-start space-x-2">
                    <div
                      className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center bg-foreground mr-2
                                `}
                    >
                      <Bot className=" h-5 w-5 text-background " />
                    </div>

                    <div className="animate-pulse px-5 py-3 shadow-sm rounded-2xl card-border bg-white text-black   rounded-tl-none">
                      AI is typing...
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </ScrollArea>
            </CardContent>
          </div>
          {suggestions && messages.length === 0 && view === "largeScreen" && (
            <div className=" absolute top-1/2   -translate-y-1/2 left-0 right-0 px-8">
              <div className="flex gap-4 items-center mb-8 justify-center">
                {" "}
                <MessagesSquare className="text-muted-foreground" />
                <h2 className="heading-lg text-center  text-muted-foreground">
                  Hi <span className="capitalize font-bold text-foreground">{tenant}</span>. What
                  can I help with?
                </h2>
              </div>

              {Array.isArray(suggestions) && (
                <div className="flex gap-4 flex-wrap justify-center">
                  {suggestions?.map((suggestion) => (
                    <Card
                      className="heading-3 cursor-pointer"
                      onClick={(e) => {
                        handleSubmit(e, suggestion);
                      }}
                    >
                      {suggestion}
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
          <CardFooter className=" p-0 pt-4 justify-end">
            <ChatBotInput
              handleSubmit={handleSubmit}
              isLoading={askLLMMutation.isPending}
              view={view}
            />
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

const ChatBotInput = ({
  handleSubmit,
  isLoading,
  view
}: {
  handleSubmit: (e: FormEvent<HTMLFormElement>, input: string) => void;
  isLoading: boolean;
  view: "largeScreen" | "smallScreen";
}) => {
  const [input, setInput] = useState("");
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter") {
      if (e.shiftKey) {
        // Shift + Enter: Add new line
        return;
      } else {
        // Enter: Submit form
        e.preventDefault();
        if (!isLoading && input.trim()) {
          handleSubmit(e, input);
          setInput("");
        }
      }
    }
  };

  return (
    <form
      className="flex w-full space-x-2 items-center relative"
      onSubmit={(e) => {
        handleSubmit(e, input);
        setInput("");
      }}
    >
      <Input
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder="Type your message..."
        className="flex-grow "
        innerClasses="rounded-md"
        inputType={view === "largeScreen" ? "textarea" : "input"}
        onKeyDown={handleKeyDown}
      />
      <Button
        type="submit"
        disabled={isLoading || input.trim() === ""}
        className={view === "largeScreen" ? "absolute right-2.5 bottom-2.5" : ""}
        startIcon={<SendHorizonal size={BUTTON_ICON_SIZE} />}
      >
        Send
      </Button>
    </form>
  );
};

export default ChatBot;
