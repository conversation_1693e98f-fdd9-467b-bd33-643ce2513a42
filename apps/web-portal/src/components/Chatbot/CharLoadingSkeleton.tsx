import { Card, CardContent } from "@components/shadcn/components/card";
import { Skeleton } from "@components/shadcn/components/skeleton";
import React from "react";

const CharLoadingSkeleton = ({ rows = 1 }: { rows?: number }) => {
  return [...Array(rows)].map((_, i) => (
    <div key={i}>
      <div className="flex justify-end mb-6 ]">
        <div className="flex items-start w-[40%]">
          <Card className="shadow-sm p-2 bg-secondary flex-1">
            <CardContent className="p-3 pt-3 space-y-2">
              <Skeleton className="h-4 w-[80%] bg-muted-foreground" />
              <Skeleton className="h-4 w-full bg-muted-foreground" />
              <Skeleton className="h-4 w-[50%] bg-muted-foreground" />
            </CardContent>
          </Card>
          <div className="ml-2 mt-1">
            <Skeleton className="h-12 w-12 rounded-full bg-secondary" />
          </div>
        </div>
      </div>

      {/* Bot message with typing animation */}
      <div className="flex mb-6">
        <div className="flex items-start w-[50%]">
          <div className="mr-2 mt-1">
            <Skeleton className="h-12 w-12 rounded-full bg-secondary" />
          </div>
          <div className="space-y-1 flex-1">
            <Card className="shadow-sm p-2 bg-secondary">
              <CardContent className="p-3 pt-3 space-y-2">
                <Skeleton className="h-4 w-[80%] bg-muted-foreground" />
                <Skeleton className="h-4 w-full bg-muted-foreground" />
                <Skeleton className="h-4 w-[50%] bg-muted-foreground" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  ));
};

export default CharLoadingSkeleton;
