import { useMemo, useState } from "react";
import Table, { TableHead, TableRow } from "@components/Table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@components/shadcn/components/tabs";
import { Card } from "@components/ui";
import { camelCaseToWords, colorAtI, getTextForValue } from "@src/pages/MonitorPage/utils";
import Chart from "@src/pages/MonitorPage/Charts/BarChart";

export type Message = {
  isBot: boolean;
  text: string;
  data?: any[];
};

const ChatMessageHandler = ({ message }: { message: Message }) => {
  return (
    <div
      className={`py-2 max-w-[80%] ${
        message.isBot ? "mb-4 " : "px-5   bg-card shadow-sm rounded-2xl card-border  "
      }`}
    >
      <p className="">{message.text}</p>
      {message.isBot && Boolean(message.data?.length) && <ChatDataVisualizer data={message.data} />}
    </div>
  );
};
type VisualizationType = "Table" | "Chart";
const ChatDataVisualizer = ({ data }: { data: Message["data"] }) => {
  const [visualizationType, setVisualizationType] = useState<VisualizationType>("Table");
  const handleChange = (newValue: VisualizationType) => {
    setVisualizationType(newValue);
  };
  return (
    <div className="dark:!bg-transparent overflow-hidden mt-2">
      <Tabs value={visualizationType} className="relative">
        <TabsList className="gap-1 ">
          <TabsTrigger value="Table" className="w-[8rem]" onClick={() => handleChange("Table")}>
            Table
          </TabsTrigger>

          <TabsTrigger value="Chart" className="w-[8rem]" onClick={() => handleChange("Chart")}>
            Chart
          </TabsTrigger>
        </TabsList>

        <TabsContent value="Table">
          <ChatTable data={data} />
        </TabsContent>
        <TabsContent value="Chart">
          <ChatGraph data={data} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const ChatTable = ({ data }: { data: Message["data"] }) => {
  if (!data?.length) {
    return null;
  }
  const keys = Object.keys(data[0]);
  return (
    <div className="flex flex-col gap-2 p-0 overflow-y-auto max-h-[20rem]">
      <Table
        wrapperClassName="!space-y-2"
        head={keys.map((key, index) => (
          <TableHead key={index}>{camelCaseToWords(key)}</TableHead>
        ))}
        body={data.map((item, i) => (
          <tr key={i}>
            {keys.map((key, index) => (
              <TableRow key={index}>{getTextForValue(item[key])}</TableRow>
            ))}
          </tr>
        ))}
      />
    </div>
  );
};

const ChatGraph = ({ data }: { data: Message["data"] }) => {
  const { labels, datasets } = useMemo(() => {
    if (!data?.length) {
      return { labels: [], datasets: [] };
    }
    const labels = data.map((item) => item.thing_name);
    const dataKeys = Object.keys(data[0]);

    const datasets = dataKeys.includes("value")
      ? [
          {
            label: data[0].key,
            data: data.map((item) => item.value),
            borderColor: colorAtI(1),
            backgroundColor: colorAtI(1),
            fill: false
          }
        ]
      : dataKeys
          .filter((item) => item !== "thing_name")
          .map((key) => ({
            label: key,
            data: data.map((item) => item[key]),
            borderColor: colorAtI(dataKeys.indexOf(key)),
            backgroundColor: colorAtI(dataKeys.indexOf(key)),
            fill: false
          }));
    return { labels, datasets };
  }, [data]);

  if (!data?.length) {
    return null;
  }

  return (
    <Card className="flex flex-col gap-2 p-0 overflow-y-auto h-[20rem]">
      <Chart
        data={{
          labels,
          datasets
        }}
        timeSeries={false}
      />
    </Card>
  );
};

export default ChatMessageHandler;
