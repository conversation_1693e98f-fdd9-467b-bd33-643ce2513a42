import { DashboardGraphItem } from "@/index";
import useAddReportMutation from "@hooks/reports/useAddReportMutation";
import { Report } from "@hooks/reports/useReportList";
import useUpdateReport from "@hooks/reports/useUpdateReport";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Check, Plus, X } from "lucide-react";
import {
  FormEvent,
  ForwardedRef,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState
} from "react";
import Button from "./Button";
import Dropdown from "./Dropdown";
import {
  getInitialState,
  parseCronExpression,
  UpdateOneTimeReportForm,
  UpdateScheduleReportForm
} from "./GenerateReportModal";
import FormDialog from "./FormDialog";
import useProductList from "@hooks/classic/useProductList";
import useThingList from "@hooks/classic/useThingList";
import useDebounce from "@hooks/useDebounce";
import { useQuery } from "@tanstack/react-query";
import { fetchUserDashboardList } from "@api/dashboardBuilder";
import { useParams } from "react-router-dom";

type ref = {
  openDrawer: (item?: Report) => void;
  closeDrawer: () => void;
};

const REPORT_OPTIONS = [
  { title: "Trips", value: "trip" },
  { title: "Stops", value: "stop" },
  { title: "Events", value: "event" },
  { title: "Timeseries", value: "table" },
  { title: "Dashboard", value: "dashboard" }
];

const AddReportRequestSidebar = (props: unknown, ref: ForwardedRef<ref>) => {
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState<
    {
      id?: number;
      type: string;
      filters: DashboardGraphItem["filters"];
      mode: string;
    } & ReturnType<typeof getInitialState>
  >({
    type: "table",
    filters: [],
    ...getInitialState(),
    mode: "onetime"
  });

  const partnerName = useParams().partnerName;
  const { isLoading: dashboardListLoading, data: dashboardList } = useQuery({
    queryKey: ["userDashboard-list", partnerName],
    queryFn: () => fetchUserDashboardList({}),
    enabled: show
  });

  const openDrawer = useCallback((report?: Report) => {
    if (report) {
      const parsedSchedule = parseCronExpression(report.schedule || "0 9 * * 1");

      setFormData({
        id: report.id,
        title: report.title,
        type: report.type,
        filters: report.filters as any,
        mode: report.schedule ? "schedule" : "onetime",
        schedule: report.schedule || "",
        duration: report.duration,
        emails: report.emails,
        scheduleType: parsedSchedule?.scheduleType || "daily",
        scheduleDetails: parsedSchedule?.scheduleDetails || {
          hour: "9",
          minute: "0",
          daysOfWeek: ["1"], // Monday
          daysOfMonth: ["1"] // 1st day
        }
      });
    }
    setShow(true);
  }, []);

  const { productFilters, thingFilters, eventFilters } = useMemo(() => {
    const productFilters = formData.filters.find((item) => item.field === "productName") || {
      field: "productName",
      operation: "is",
      value: [],
      enabled: false,
      id: "productName",
      new: true
    };
    const thingFilters = formData.filters.find((item) => item.field === "thingName") || {
      field: "thingName",
      operation: "is",
      value: [],
      enabled: false,
      id: "thingName",
      new: true
    };
    const eventFilters = formData.filters.find((item) => item.field === "event") || {
      field: "event",
      operation: "is",
      value: [],
      enabled: false,
      id: "event",
      new: true
    };
    return {
      productFilters,
      thingFilters,
      eventFilters
    };
  }, [formData.filters]);

  const [searchedThingName, setSearchedThingName] = useState<string>("");
  const debouncedSearch = useDebounce(searchedThingName, 500);

  const [searchProduct, setSearchProduct] = useState<string>("");
  const debouncedSearchProduct = useDebounce(searchProduct, 500);

  const { data: productList, isLoading: productLisLoading } = useProductList({
    enabled: show,
    search: debouncedSearchProduct
  });

  const { data: thingList, isLoading: thingLoading } = useThingList({
    productName: productFilters.value?.[0] || "",
    enabled: show,
    search: debouncedSearch
  });

  const closeDrawer = useCallback(() => {
    setFormData({
      type: "table",
      filters: [],
      ...getInitialState(),
      mode: "onetime"
    });
    setShow(false);
  }, []);
  const [emailInput, setEmailInput] = useState("");
  const [emailError, setEmailError] = useState("");

  const addReportMutation = useAddReportMutation({
    onSuccess: () => {
      showSuccessToast("Report request added successfully");
      queryClient.invalidateQueries({ queryKey: ["reportList"] });
      closeDrawer();
    }
  });
  const editReportMutation = useUpdateReport({
    onSuccess: () => {
      showSuccessToast("Report updated successfully");
      queryClient.invalidateQueries({ queryKey: ["reportList"] });
      closeDrawer();
    }
  });

  useImperativeHandle(ref, () => ({
    openDrawer,
    closeDrawer
  }));

  const handleFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!formData.id) {
      // Create
      if (formData.mode === "onetime") {
        addReportMutation.mutate({
          title: formData.title,
          filters: formData.filters,
          duration: formData.duration,
          type: formData.type as any,
          emails: formData.emails,
          userDashboardId: formData.userDashboardId
        });
        return;
      }
      if (formData.mode === "schedule") {
        addReportMutation.mutate({
          title: formData.title,
          filters: formData.filters,
          schedule: formData.schedule,
          duration: formData.duration,
          type: formData.type as any,
          emails: formData.emails,
          userDashboardId: formData.userDashboardId
        });
        return;
      }
      return;
    }
    if (formData.mode === "onetime") {
      editReportMutation.mutate({
        id: formData.id,
        title: formData.title,
        filters: formData.filters,
        duration: formData.duration,
        emails: formData.emails
      });
      return;
    }
    if (formData.mode === "schedule") {
      editReportMutation.mutate({
        id: formData.id,
        title: formData.title,
        filters: formData.filters,
        duration: formData.duration,
        emails: formData.emails
      });
      return;
    }
  };

  const addEmail = (email: string) => {
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    if (formData.emails.includes(email.trim())) {
      setEmailError("This email is already added");
      return;
    }

    setFormData({
      ...formData,
      emails: [...formData.emails, email.trim()]
    });
    setEmailInput("");
    setEmailError("");
  };

  const handleAddEmail = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && emailInput.trim() !== "") {
      e.preventDefault();
      addEmail(emailInput);
    }
  };

  const handleRemoveEmail = (email: string) => {
    setFormData({
      ...formData,
      emails: formData.emails.filter((e) => e !== email)
    });
  };

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleAddEmailOnBlur = () => {
    if (emailInput.trim() !== "") {
      addEmail(emailInput);
    }
  };

  const updateReportFilters = (
    value: string[],
    filterToUpdate: DashboardGraphItem["filters"][number]
  ) => {
    if (filterToUpdate.new) {
      setFormData({
        ...formData,
        filters: [
          ...formData.filters,
          {
            ...filterToUpdate,
            value: value,
            new: false
          }
        ]
      });
      return;
    }
    setFormData({
      ...formData,
      filters: formData.filters.map((item) => {
        if (item.field === filterToUpdate.field) {
          return {
            ...item,
            value: value
          };
        }
        return item;
      })
    });
  };

  return (
    <FormDialog
      open={show}
      title={formData?.id ? "Edit Report" : "Add Report"}
      onClose={closeDrawer}
      footer={
        <div className="flex items-center gap-4 justify-end">
          <Button
            color="gray"
            type="button"
            small
            startIcon={<X size={BUTTON_ICON_SIZE} />}
            onClick={closeDrawer}
          >
            Close
          </Button>
          <Button
            type="submit"
            small
            loading={addReportMutation.isPending || editReportMutation.isPending}
            startIcon={
              formData?.id ? <Check size={BUTTON_ICON_SIZE} /> : <Plus size={BUTTON_ICON_SIZE} />
            }
            form="add-report-form"
          >
            {formData.id ? "Update" : "Add"} Report
          </Button>
        </div>
      }
    >
      <form onSubmit={handleFormSubmit} className=" space-y-4" id="add-report-form">
        <Dropdown
          options={REPORT_OPTIONS}
          getOptionLabel="title"
          required
          label="Report Type"
          disabled={Boolean(formData.id)}
          value={REPORT_OPTIONS.find((item) => item.value === formData.type)}
          onChange={(value) => setFormData({ ...formData, type: value.value })}
          placeHolder="Select Report Type"
        />

        <Dropdown
          options={["onetime", "schedule"]}
          label="Mode"
          value={formData.mode}
          disabled={Boolean(formData.id)}
          onChange={(value) => setFormData({ ...formData, mode: value })}
          placeHolder="Select Mode"
        />

        {formData.mode === "onetime" && (
          <UpdateOneTimeReportForm
            formData={formData}
            setFormData={setFormData}
            handleRemoveEmail={handleRemoveEmail}
            handleAddEmail={handleAddEmail}
            handleAddEmailOnBlur={handleAddEmailOnBlur}
            emailInput={emailInput}
            setEmailInput={setEmailInput}
            emailError={emailError}
          />
        )}
        {formData.mode === "schedule" && (
          <UpdateScheduleReportForm
            formData={formData}
            setFormData={setFormData}
            handleRemoveEmail={handleRemoveEmail}
            handleAddEmail={handleAddEmail}
            handleAddEmailOnBlur={handleAddEmailOnBlur}
            emailInput={emailInput}
            setEmailInput={setEmailInput}
            emailError={emailError}
          />
        )}

        {formData.type === "dashboard" ? (
          <Dropdown
            options={dashboardList || []}
            value={dashboardList?.find((item) => item.id === formData.userDashboardId)}
            getOptionLabel="title"
            optionsLoading={dashboardListLoading}
            placeHolder="Select dashboard"
            label="Select Dashboard"
            onChange={(value) => {
              setFormData({
                ...formData,
                userDashboardId: value.id
              });
            }}
          />
        ) : (
          <>
            {formData.type !== "event" && (
              <Dropdown
                value={productFilters.value}
                options={productList?.productList.map((item) => item.productName) || []}
                optionsLoading={productLisLoading}
                isMulti
                required={formData.type === "table"}
                placeHolder="Select product"
                label="Filter by product name"
                onChange={(value) => {
                  if (formData.type === "table") {
                    if (value.length > 1) {
                      showErrorToast("Multi product selection is not allowed for table report.");
                      return;
                    }
                  }
                  updateReportFilters(value, productFilters);
                }}
                isSearchable
                deepSearch={(value) => setSearchProduct(value)}
              />
            )}

            <Dropdown
              value={thingFilters.value}
              options={thingList?.things.map((item) => item.thingName) || []}
              optionsLoading={thingLoading}
              isMulti
              isSearchable
              deepSearch={(value) => setSearchedThingName(value)}
              placeHolder="Select thing"
              label="Filter by thing name"
              onChange={(value) => {
                updateReportFilters(value, thingFilters);
              }}
            />

            {formData.type === "event" && (
              <Dropdown
                value={eventFilters.value}
                options={[
                  "geofenceEnter",
                  "geofenceExit",
                  "overSpeed",
                  "deviceOnline",
                  "deviceOffline",
                  "deviceUnknown"
                ]}
                isMulti
                placeHolder="Select Event"
                label="Filter by Event"
                onChange={(value) => {
                  updateReportFilters(value, eventFilters);
                }}
              />
            )}
          </>
        )}
      </form>
    </FormDialog>
  );
};

export default forwardRef(AddReportRequestSidebar);
