import clsx from "clsx";
interface Props extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  data?: React.ReactNode; // Now supports JSX elements
  icon?: React.ReactElement;
  className?: string;
  dataClassName?: string;
  flexDirectionRow?: boolean;
}
export default function DetailsCell({
  title,
  data,
  className,
  dataClassName,
  icon,
  flexDirectionRow
}: Props) {
  return (
    <div className={clsx("flex items-start gap-2", className)}>
      {icon && <div className="text-muted-foreground mt-0.5 flex-shrink-0 ">{icon}</div>}

      <div className="">
        <p className="text-sm text-muted-foreground">{title}</p>
        <p
          className={clsx("font-medium text-base", flexDirectionRow && "flex gap-2", dataClassName)}
        >
          {" "}
          {data || (data === 0 ? 0 : "N/A")}
        </p>
      </div>
    </div>
  );
}
