import BrandLogo from "@components/BrandLogo";
import { UserDropdownContent } from "@components/Header";
import { Avatar, AvatarImage } from "@components/shadcn/components/avatar";
import { Button } from "@components/shadcn/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@components/shadcn/components/command";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger
} from "@components/shadcn/components/dropdown-menu";
import { cn } from "@components/shadcn/utils";
import { userActions } from "@frontend/shared/store/userSlice";
import useTenantList from "@hooks/tenant/useTenantsList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { BUTTON_ICON_SIZE, OPERATOR_ROLES } from "@utils/utilities";
import clsx from "clsx";
import { ArrowLef<PERSON>, Check, ChevronsUpDown } from "lucide-react";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../store";

function TenantSwitcher() {
  const [searchQuery, setSearchQuery] = useState("");
  const { partnerName } = useParams();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: tenants } = useTenantList({
    search: debouncedSearchQuery,
    limit: 50
  });
  const defaultNavigate = useNavigate();

  const [value, setValue] = useState(partnerName);
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between bg-secondary card-border py-6 px-2 gap-2 items-center"
        >
          <Avatar className="size-8">
            <AvatarImage src={`https://api.dicebear.com/8.x/initials/svg?seed=${value}`} />
          </Avatar>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold text-foreground">
              {value
                ? tenants?.allTenants.find((tenant) => tenant.name === value)?.name
                : "Select Tenant..."}
            </span>

            <span className="truncate text-xs text-muted-foreground">
              {" "}
              {value
                ? tenants?.allTenants.find((tenant) => tenant.name === value)?.email
                : "Select Tenant..."}
            </span>
          </div>
          <ChevronsUpDown size={BUTTON_ICON_SIZE} className="ml-auto text-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg !z-[1000]   card-border "
        align="start"
        side="bottom"
        // side={isMobile ? "bottom" : "right"}
        sideOffset={2}
      >
        <Command>
          <CommandInput placeholder="Search Tenant..." />
          <CommandEmpty>No Tenant found.</CommandEmpty>
          <CommandGroup>
            <CommandList>
              {tenants?.allTenants
                .slice()
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((tenant) => (
                  <CommandItem
                    key={tenant.name}
                    value={tenant.name}
                    className={clsx(value === tenant.name && "bg-brandColor text-black")}
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? "" : currentValue);
                      defaultNavigate(`/TM/tenants/${tenant.featureType}/${tenant.name}`);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === tenant.name ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {tenant.name}
                  </CommandItem>
                ))}
            </CommandList>
          </CommandGroup>
        </Command>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

const UserDetailsDropdown = () => {
  const user = useAppSelector(({ user }) => user.user);

  if (!user) return <div />;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="my-2 mt-auto border-t ">
        <Button
          variant="outline"
          className="w-full text-foreground justify-between border-x-0 border-b-0 rounded-t-none py-6 px-2 gap-2 items-center"
        >
          <Avatar className="size-8">
            <AvatarImage src={`https://api.dicebear.com/8.x/initials/svg?seed=${user.name}`} />
          </Avatar>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">{user.name}</span>
            <span className="truncate text-xs">{user.email}</span>
          </div>
          <ChevronsUpDown className="ml-auto size-4" />
        </Button>
      </DropdownMenuTrigger>
      <UserDropdownContent />
    </DropdownMenu>
  );
};

function Sidebar({ children, isTenantView }) {
  const isCollapsed = useAppSelector(({ sidebarSlice }) => sidebarSlice.isCollapsed);
  const navigate = useCustomNavigate();

  const dispatch = useAppDispatch();
  const userRole = useAppSelector(({ user }) => user.user?.role);

  return (
    <div className={clsx("sidebar bg-card", isCollapsed && "close !w-16")}>
      <div>
        <div
          className={clsx(
            `sidebar-brand-section duration-300 ease-in-out
            flex items-center justify-center h-16 px-4 border-b border-border
            `,
            isCollapsed && "justify-center"
          )}
        >
          <BrandLogo
            onClick={() => {
              navigate("/");
            }}
            // mode="dark"
            className={
              isCollapsed
                ? "max-w-[3rem] cursor-pointer"
                : "inline-block cursor-pointer max-w-[5rem] max-h-[3rem] ml-2 "
            }
          />
        </div>
      </div>
      {isTenantView && (
        <div className="mt-2">
          <TenantSwitcher />

          <Button
            variant="ghost"
            className="text-foreground hover:scale-105 duration-1000"
            onClick={() => {
              navigate("TM/tenants");
              dispatch(userActions.addTenantDetails(null));
            }}
          >
            <ArrowLeft size={BUTTON_ICON_SIZE} className="mr-2" />
            {!isCollapsed &&
              `Back to ${OPERATOR_ROLES.includes(userRole) ? "Operator" : "MSP"} View`}
          </Button>
          <hr className="hr " />
        </div>
      )}
      <ul className={clsx("nav-links space-y-1 py-2")}>{children}</ul>
      <UserDetailsDropdown />
    </div>
  );
}

export default Sidebar;
