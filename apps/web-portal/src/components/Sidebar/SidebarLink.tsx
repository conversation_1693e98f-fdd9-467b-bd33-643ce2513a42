import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger
} from "@components/shadcn/components/hover-card";
// import { Tooltip } from "@mui/material";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { ChevronRight } from "lucide-react";
import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { useAppSelector } from "../../store";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@components/shadcn/components/tooltip";

function SidebarLink({ icon, text, children, end, isChild, to, ...props }) {
  const [dropdown, setDropDown] = useState(false);
  const isCollapsed = useAppSelector(({ sidebarSlice }) => sidebarSlice.isCollapsed);
  const location = useLocation();

  const showSubnav = (e) => {
    e.preventDefault();
    setDropDown(!dropdown);
  };

  return (
    <HoverCard openDelay={100} closeDelay={200}>
      <li className={dropdown ? "showMenu" : ""}>
        <div className="iocn-link">
          <div className={clsx("w-full", isChild && "border-l border-gray-500 ml-2")}>
            <NavLink
              className={({ isActive }) => {
                const isChildActive = children?.some((child) =>
                  location.pathname.includes(child.props?.to)
                );

                return clsx(
                  "sidebar-link group ",
                  "flex items-center",
                  isActive && !children && "sidebar-link-active ",
                  isChildActive && "text-foreground",
                  isCollapsed && "hover:px-2 w-auto ",
                  isCollapsed && !isChild && "justify-center  "
                );
              }}
              data-testid={`sidebar-${text}`}
              to={to}
              end={end}
              onClick={children && showSubnav}
              {...props}
            >
              {icon && (
                <TooltipProvider>
                  <Tooltip delayDuration={50}>
                    <TooltipTrigger>
                      <HoverCardTrigger asChild>
                        <span
                          className={isCollapsed ? "group-hover:rotate-2" : " group-hover:rotate-2"}
                        >
                          {icon}
                        </span>
                      </HoverCardTrigger>
                    </TooltipTrigger>
                    {!children && isCollapsed && (
                      <TooltipContent side="right">{text}</TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              )}
              {(!isCollapsed || isChild) && (
                <span className="ml-3 content-bold truncate">{text}</span>
              )}
              {children && !isCollapsed && (
                <ChevronRight
                  size={BUTTON_ICON_SIZE}
                  className={clsx("ml-auto transition-transform", dropdown && "!rotate-90")}
                />
              )}
            </NavLink>
          </div>
        </div>
        {children &&
          (isCollapsed ? (
            <HoverCardContent side="right">
              {/* <ul className={clsx("sub-menu", isCollapsed && " card-border")}> */}
              <li className="flex items-center gap-2 mb-1">
                <div>
                  <span className={isCollapsed ? "group-hover:rotate-2" : " group-hover:rotate-2"}>
                    {icon}
                  </span>
                </div>

                <div className="link_name heading-3 truncate">{text}</div>
              </li>
              {children}
              {/* </ul> */}
            </HoverCardContent>
          ) : (
            <ul className={clsx("sub-menu", isCollapsed && " card-border")}>{children}</ul>
          ))}
      </li>
    </HoverCard>
  );
}

export default SidebarLink;
