import {
  Tooltip,
  Toolt<PERSON>Content,
  Too<PERSON><PERSON>Provider,
  TooltipTrigger
} from "./shadcn/components/tooltip";

export default function CustomTooltip({
  title,
  children
}: {
  title: string;
  children: React.ReactNode;
}) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className="" asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent className="z-[10000]">
          <span>{title}</span>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
