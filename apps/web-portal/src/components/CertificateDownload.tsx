import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from "@components/shadcn/components/dialog";
import { THING_VAR } from "@utils/featureLabels";
import { BUTTON_ICON_SIZE, certDownload } from "@utils/utilities";
import { Download, Fingerprint, Hash, KeySquare, Link, Terminal } from "lucide-react";
import Button from "./Button";
import Alert from "./Alert";

const CertificateDownload = ({ show, setShow, certDetails }) => {
  const certificateDetails = certDetails || show;
  return (
    <Dialog open={show} onOpenChange={setShow}>
      <DialogContent className="!max-w-[45rem] w-[45rem]">
        <DialogHeader>
          <DialogTitle>Download {THING_VAR} Certificate</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2 flex gap-6 items-center justify-between rounded-lg border p-4 dark:border-gray-700 ">
            <div className="flex gap-4 items-center">
              <div className="border p-4 rounded-lg bg-secondary">
                <Hash size="1.5rem" />
              </div>
              <div className="space-y-0.5">
                <h3 className="font-medium text-base">Certificate</h3>
                <p className="text-[0.8rem] text-muted-foreground">
                  Download the certificate and key files needed to install on your {THING_VAR} for
                  secure connection to AWS.
                </p>
              </div>
            </div>
            <Button
              color="gray"
              onClick={() => certDownload("deviceCert", certificateDetails)}
              startIcon={<Download size={BUTTON_ICON_SIZE} />}
              small
            >
              Download
            </Button>
          </div>
          <div className="space-y-2 flex gap-6 items-center justify-between rounded-lg border p-4 dark:border-gray-700 ">
            <div className="flex gap-4 items-center">
              <div className="border p-4 rounded-lg bg-secondary">
                <KeySquare size="1.5rem" />
              </div>
              <div className="space-y-0.5">
                <h3 className="font-medium text-base">Private Key</h3>
                <p className="text-[0.8rem] text-muted-foreground">
                  This key is unique to your certificate and cannot be retrieved after leaving this
                  page. Download it now and save it securely.
                </p>
              </div>
            </div>
            <Button
              color="gray"
              onClick={() => certDownload("privateKey", certificateDetails)}
              startIcon={<Download size={BUTTON_ICON_SIZE} />}
              small
            >
              Download
            </Button>
          </div>
          <Alert
            title="Warning!"
            description="This is the only time you can download the key files for this certificate.."
            Icon={Terminal}
            type="warning"
          />

          <div className="space-y-2 flex gap-6 items-center justify-between rounded-lg border p-4 dark:border-gray-700 ">
            <div className="flex gap-4 items-center">
              <div className="border p-4 rounded-lg bg-secondary">
                <Link size="1.5rem" />
              </div>
              <div className="space-y-0.5">
                <h3 className="font-medium text-base">CA Chain</h3>
                <p className="text-[0.8rem] text-muted-foreground">
                  Includes your server certificate and intermediate certificates, connecting your
                  certificate to a root CA. This ensures trust between your server and clients.
                </p>
              </div>
            </div>
            <Button
              onClick={() => certDownload("caChain", certificateDetails)}
              color="gray"
              startIcon={<Download size={BUTTON_ICON_SIZE} />}
              small
            >
              Download
            </Button>
          </div>
          <div className="space-y-2 flex gap-6 items-center justify-between rounded-lg border p-4 dark:border-gray-700 ">
            <div className="flex gap-4 items-center">
              <div className="border p-4 rounded-lg bg-secondary">
                <Fingerprint size="1.5rem" />
              </div>
              <div className="space-y-0.5">
                <h3 className="font-medium text-base">CA Certificate</h3>
                <p className="text-[0.8rem] text-muted-foreground">
                  Download the root CA certificate for your data endpoint and cipher suite. You can
                  also retrieve it later if needed.
                </p>
              </div>
            </div>
            <Button
              onClick={() => certDownload("ca", certificateDetails)}
              color="gray"
              startIcon={<Download size={BUTTON_ICON_SIZE} />}
              small
            >
              Download
            </Button>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={setShow}>Done</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CertificateDownload;
