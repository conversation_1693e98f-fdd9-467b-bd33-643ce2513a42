/* eslint-disable max-len */
import { CircularProgress } from "@mui/material";
import { darkColors } from "@utils/color";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { motion } from "framer-motion";
import React, { useContext } from "react";
import colors from "tailwindcss/colors";
import { DarkModeContext } from "../hooks/useDarkMode";

const variants = {
  click: {
    scale: 0.98,
    transition: {
      duration: 0.1,
      type: "spring"
    }
  },
  hover: {
    y: -1,
    transition: {
      duration: 0.1
    }
  }
};

type Props = import("framer-motion").HTMLMotionProps<"button"> & {
  outlined?: boolean;
  iconOnly?: boolean;
  link?: boolean;
  small?: boolean;
  extraSmall?: boolean;
  loading?: boolean;
  loadingText?: string;
  startIcon?: React.ReactElement;
  endIcon?: React.ReactElement;
  color?: import("../..").LooseAutoComplete<keyof typeof colors>;
  noAccess?: boolean;
};

const Button: import("react").ForwardRefRenderFunction<HTMLButtonElement, Props> = (
  {
    outlined,
    iconOnly,
    link,
    small,
    extraSmall,
    startIcon,
    endIcon,
    children,
    className,
    loading,
    loadingText = "",
    color = darkColors.brandColors,
    noAccess = false,
    disabled: _disabled,
    ...props
  },
  ref
) => {
  const [darkMode, _] = useContext(DarkModeContext);
  const disabled = _disabled || noAccess;

  const buttonStyle: import("framer-motion").MotionProps["style"] = {
    color: colors[color]
      ? colors[color][darkMode && outlined ? 400 : 700]
      : outlined
        ? color
        : "hsl(var(--button-text))",
    backgroundColor: outlined ? "" : `${colors[color] ? colors[color][200] : color}`,
    // border: `1.8px solid ${colors[color][200]}`
    border: outlined
      ? `1px solid ${colors[color]?.[darkMode ? 400 : 500] || color}`
      : `1px solid ${colors[color] ? colors[color][200] : color}`
    // "&:hover": {
    //   backgroundColor: outlined ? colors[color][50] : null,
    //   color: link ? colors[color][600] : null,
    // },
  };

  const loadingButtonStyle = {
    background: outlined
      ? "transparent"
      : colors[color]?.[100] ||
        (color === darkColors.brandColors ? darkColors.brandLightColors : color),
    color: colors[color]?.[500] || color,
    opacity: 0.8,
    border: outlined ? `1px solid ${colors[color]?.[darkMode ? 400 : 500] || color}` : ""
  };

  const loadingComputedText = loadingText
    ? loadingText
    : typeof children === "string"
      ? children
      : "";

  return (
    <motion.button
      variants={variants}
      ref={ref}
      whileTap="click"
      whileHover="hover"
      data-testid={`button-${children}`}
      className={clsx(
        small ? "button-sm" : extraSmall ? "button-xs" : "button-md",
        disabled && ["text-gray-400 cursor-not-allowed", !link && "bg-gray-400/30"],
        disabled || loading ? "center rounded-md" : ["button", link && "button-link"],
        className
      )}
      style={loading ? loadingButtonStyle : disabled ? {} : buttonStyle}
      {...props}
      disabled={disabled || loading}
    >
      {loading ? (
        <div className="flex items-center  text-black/50">
          <CircularProgress
            size={BUTTON_ICON_SIZE}
            sx={{ color: outlined ? colors[color]?.[500] || color : "inherit" }}
          />
          {loadingComputedText && <span className="ml-2">{loadingComputedText}</span>}
        </div>
      ) : (
        // <TooltipProvider>
        //   <Tooltip>
        //     <TooltipTrigger asChild>
        <>
          {startIcon && <span className={!iconOnly ? "mr-2" : ""}>{startIcon}</span>}
          {children}
          {endIcon && <span className="ml-2">{endIcon}</span>}
        </>
        //     </TooltipTrigger>
        //     <TooltipContent>
        //       <p>You don't have access to this feature</p>
        //     </TooltipContent>
        //   </Tooltip>
        // </TooltipProvider>
      )}
    </motion.button>
  );
};

export default React.forwardRef(Button);
