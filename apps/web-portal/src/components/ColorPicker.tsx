import { useEffect, useRef, useState } from "react";
import { Card } from "./ui";
const colorArray1 = [
  "#FFFFFF",
  "#F4F2F1",
  "#D7CEC9",
  "#B6ACA6",
  "#978F8A",
  "#564E4A",
  "#3C3836",
  "#000000"
];

const colorArray2 = [
  "#FF4136",
  "#FF851B",
  "#FFDC00",
  "#2ECC40",
  "#0074D9",
  "#B10DC9",
  "#FFFFFF",
  "#111111",
  "#7FDBFF",
  "#01FF70",
  "#F012BE",
  "#39CCCC",
  "#85144b",
  "#AAAAAA",
  "#DDDDDD",
  "#00e6c3"
];

const ColorPicker = ({
  defaultColor = "#00fad4",
  onChange,
  minimal = false,
  monoColor = false
}) => {
  const [color, setColor] = useState(defaultColor);
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [inputValue, setInputValue] = useState(defaultColor);
  const [hue, setHue] = useState(0);
  const [saturation, setSaturation] = useState(100);
  const [lightness, setLightness] = useState(50);
  const [pickerPosition, setPickerPosition] = useState({ x: 100, y: 0 });
  const [dropUp, setDropUp] = useState(false);
  const pickerRef = useRef(null);
  const gradientRef = useRef(null);

  // Convert hex to RGB
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        }
      : null;
  };

  // Convert RGB to hex
  const rgbToHex = (r, g, b) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
  };

  // Convert HSL to RGB
  const hslToRgb = (h, s, l) => {
    h /= 360;
    s /= 100;
    l /= 100;
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1 / 6) return p + (q - p) * 6 * t;
        if (t < 1 / 2) return q;
        if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1 / 3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1 / 3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  // Convert RGB to HSL
  const rgbToHsl = (r, g, b) => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h,
      s,
      l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
        default:
          h = 0;
      }

      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  };

  // Extract HSL values from the default color on initial render
  useEffect(() => {
    const rgb = hexToRgb(defaultColor);
    if (rgb) {
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      setHue(hsl.h);
      setSaturation(hsl.s);
      setLightness(hsl.l);

      // Set initial picker position based on HSL values
      setPickerPosition({
        x: hsl.s,
        y: 100 - hsl.l
      });
    }
  }, [defaultColor]);
  useEffect(() => {
    if (isPickerOpen && pickerRef.current) {
      const rect = pickerRef.current.getBoundingClientRect();
      const dropdownHeight = 300; // approximate height of the dropdown
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;

      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropUp(true);
      } else {
        setDropUp(false);
      }
    }
  }, [isPickerOpen]);
  // Handle color input change from the native color picker
  const handleColorChange = (e) => {
    const newColor = e.target.value;
    setColor(newColor);
    setInputValue(newColor);

    // Update HSL values and picker position
    const rgb = hexToRgb(newColor);
    if (rgb) {
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      setHue(hsl.h);
      setSaturation(hsl.s);
      setLightness(hsl.l);
      setPickerPosition({
        x: hsl.s,
        y: 100 - hsl.l
      });
    }

    onChange && onChange(newColor);
  };

  // Handle manual hex input
  const handleHexInput = (e) => {
    const value = e.target.value;
    setInputValue(value);

    // Only update the actual color if it's a valid hex
    if (/^#([A-Fa-f0-9]{6})$/.test(value)) {
      setColor(value);

      // Update HSL values and picker position
      const rgb = hexToRgb(value);
      if (rgb) {
        const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
        setHue(hsl.h);
        setSaturation(hsl.s);
        setLightness(hsl.l);
        setPickerPosition({
          x: hsl.s,
          y: 100 - hsl.l
        });
      }

      onChange && onChange(value);
    }
  };

  // Handle hue slider change
  const handleHueChange = (e) => {
    const newHue = parseInt(e.target.value, 10);
    setHue(newHue);

    // Keep saturation and lightness the same, just update the hue
    const rgbColor = hslToRgb(newHue, saturation, lightness);
    const hexColor = rgbToHex(rgbColor.r, rgbColor.g, rgbColor.b);

    setColor(hexColor);
    setInputValue(hexColor);
    onChange && onChange(hexColor);
  };

  // Handle click and drag on the color gradient
  const handleGradientInteraction = (e) => {
    if (!gradientRef.current) return;

    // Get gradient dimensions
    const rect = gradientRef.current.getBoundingClientRect();

    // Calculate position as percentages
    let x = ((e.clientX - rect.left) / rect.width) * 100;
    let y = ((e.clientY - rect.top) / rect.height) * 100;

    // Clamp values between 0 and 100
    x = Math.max(0, Math.min(100, x));
    y = Math.max(0, Math.min(100, y));

    // Update picker position
    setPickerPosition({ x, y });

    // Convert position to saturation and lightness
    // x: 0 = 0% saturation, x: 100 = 100% saturation
    // y: 0 = 100% lightness, y: 100 = 0% lightness
    const newSaturation = x;
    const newLightness = 100 - y;

    setSaturation(newSaturation);
    setLightness(newLightness);

    // Convert HSL to RGB to Hex
    const rgbColor = hslToRgb(hue, newSaturation, newLightness);
    const hexColor = rgbToHex(rgbColor.r, rgbColor.g, rgbColor.b);

    setColor(hexColor);
    setInputValue(hexColor);
    onChange && onChange(hexColor);
  };

  // Set up event listeners for drag operations on the gradient
  const handleMouseDown = (e) => {
    handleGradientInteraction(e);

    const handleMouseMove = (e) => {
      handleGradientInteraction(e);
    };

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target)) {
        setIsPickerOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const presentColors = monoColor ? colorArray1 : colorArray2;

  return (
    <div className="relative" ref={pickerRef}>
      {/* Color display and hex input */}
      <div className="flex items-center space-x-3">
        <button
          className="w-10 h-10 rounded-full border-2 border-white shadow-md flex items-center justify-center cursor-pointer hover:scale-105 transition-transform"
          style={{ backgroundColor: color }}
          onClick={() => setIsPickerOpen(!isPickerOpen)}
          type="button"
        ></button>
      </div>

      {/* Custom color picker dropdown */}
      {isPickerOpen && (
        <Card className={`absolute left-0 z-50 p-4 w-72 ${dropUp ? "bottom-12" : "top-12"}`}>
          <div className="space-y-4">
            {/* Main color gradient with picker handle */}
            {!minimal && (
              <>
                <div
                  ref={gradientRef}
                  className="relative h-40 w-full rounded-md cursor-crosshair card-border"
                  style={{
                    background: `linear-gradient(to bottom, rgba(0,0,0,0) 0%, #000 100%), 
                              linear-gradient(to right, #fff 0%, hsl(${hue}, 100%, 50%) 100%)`
                  }}
                  onMouseDown={handleMouseDown}
                >
                  {/* Picker dot indicator */}
                  <div
                    className="absolute w-4 h-4 rounded-full border-2 border-white shadow-md transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                    style={{
                      left: `${pickerPosition.x}%`,
                      top: `${pickerPosition.y}%`,
                      backgroundColor: color
                    }}
                  ></div>
                </div>

                {/* Color sliders */}
                <div className="space-y-3">
                  {/* Hue slider */}
                  <div className="relative">
                    <div
                      className="w-full h-6 rounded-md"
                      style={{
                        background:
                          "linear-gradient(to right, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000)"
                      }}
                    ></div>
                    <input
                      type="range"
                      min="0"
                      max="359"
                      value={hue}
                      onChange={handleHueChange}
                      className="absolute top-0 left-0 w-full h-6 opacity-0 cursor-pointer"
                    />
                    <div
                      className="absolute top-0 w-1 h-6 bg-white rounded pointer-events-none"
                      style={{ left: `calc(${(hue / 359) * 100}% - 2px)` }}
                    ></div>
                  </div>
                </div>
              </>
            )}
            <input
              type="text"
              value={inputValue}
              onChange={handleHexInput}
              className="h-10 px-3 w-full font-mono text-sm bg-gray-500/10 border card-border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              placeholder="#RRGGBB"
            />
            {/* Color presets */}
            <div>
              <label className="block text-gray-400 text-xs mb-2">Presets</label>
              <div className="grid grid-cols-8 gap-1">
                {presentColors.map((presetColor) => (
                  <button
                    key={presetColor}
                    className="w-7 h-7 rounded-md border border-gray-700 cursor-pointer hover:scale-110 transition-transform"
                    style={{ backgroundColor: presetColor }}
                    type="button"
                    onClick={() => {
                      setColor(presetColor);
                      setInputValue(presetColor);

                      // Update HSL values and picker position
                      const rgb = hexToRgb(presetColor);
                      if (rgb) {
                        const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                        setHue(hsl.h);
                        setSaturation(hsl.s);
                        setLightness(hsl.l);
                        setPickerPosition({
                          x: hsl.s,
                          y: 100 - hsl.l
                        });
                      }

                      onChange && onChange(presetColor);
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Native color input (hidden but accessible) */}
      <input
        type="color"
        value={color}
        onChange={handleColorChange}
        className="sr-only"
        aria-hidden="true"
      />
    </div>
  );
};

export default ColorPicker;
