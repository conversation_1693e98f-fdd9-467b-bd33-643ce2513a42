import { THING_VAR } from "@utils/featureLabels";
import { Terminal } from "lucide-react";
import Alert from "./Alert";
import Button from "./Button";
import Input from "./Input";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle
} from "./shadcn/components/dialog";

const ThingAuthInfo = ({ show, certificateDetails, setShow }) => {
  return (
    <Dialog
      open={Boolean(show)}
      onOpenChange={() => {
        setShow();
      }}
    >
      <DialogContent className="!max-w-[35rem] w-[35rem]">
        <DialogHeader>
          <DialogTitle>Thing Auth Details</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <Alert
            title="Success!"
            description={`${THING_VAR} Basic Authentication created Successfully.`}
            Icon={Terminal}
            type="success"
          />

          <div className="grid grid-cols-4 items-center gap-6">
            <p className="text-sm font-medium leading-none  text-right">Username</p>
            <Input value={certificateDetails?.username} className=" col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-6">
            <p className="text-sm font-medium leading-none  text-right">Password</p>
            <Input value={certificateDetails?.password} className=" col-span-3" />
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={() => {
              setShow();
            }}
          >
            Done
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ThingAuthInfo;
