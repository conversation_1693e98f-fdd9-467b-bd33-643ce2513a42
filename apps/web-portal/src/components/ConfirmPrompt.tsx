import { CircularProgress } from "@mui/material";
import { AlertTriangle } from "lucide-react";
import { useEffect, useState } from "react";
import Input from "./Input";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "./shadcn/components/alert-dialog";
import { Button } from "./shadcn/components/button";

type ConfirmPromptType = {
  show: boolean;
  title?: string;
  message?: string;
  onConfirm: () => any;
  onCancel: () => any;
  loading?: boolean;
  disabled?: boolean;
  validate?: boolean;
  item?: string | null;
};

export default function ConfirmPrompt({
  show,
  title = " Are you absolutely sure?",
  message = "This action cannot be undone. This will permanently delete and remove your data from our servers.",
  onConfirm,
  onCancel,
  loading,
  validate,
  item = "CONFIRM"
}: ConfirmPromptType) {
  const [confirmMessage, setConfirmMessage] = useState("");
  useEffect(() => {
    setConfirmMessage("");
  }, [show]);
  return (
    <AlertDialog
      open={Boolean(show)}
      onOpenChange={() => {
        setConfirmMessage("");
        onCancel();
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-amber-100">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {message}
            {/* {item && <span className="text-black dark:text-white uppercase">{`"${item}"? `}</span>} */}
          </AlertDialogDescription>
        </AlertDialogHeader>
        {validate && (
          <div className="space-y-1">
            <AlertDialogDescription>
              To confirm, type <span className="text-foreground font-semibold">"{item}"</span> in
              the box below
            </AlertDialogDescription>
            <Input
              small
              defaultValue={confirmMessage}
              onChange={(e) => {
                setConfirmMessage(e.target.value);
              }}
            />
          </div>
        )}
        <AlertDialogFooter className="gap-2 !space-x-0">
          <Button
            onClick={() => {
              onCancel();
              setConfirmMessage("");
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              onConfirm();
            }}
            className="bg-red-500 hover:bg-red-600"
            disabled={validate && confirmMessage !== item}
          >
            {loading ? <CircularProgress size={18} color="inherit" /> : "Confirm"}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
