import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import { logoutUser } from "@frontend/shared/store/userSlice";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { toggleCollapse } from "@src/features/sidebarSlice";
import { checkForHost, TENANT_ROLES, UserWithoutTenant } from "@utils/utilities";
import {
  Activity,
  Bell,
  HelpCircle,
  LogOut,
  PanelLeft,
  PanelRight,
  Settings,
  User
} from "lucide-react";
import { usePostHog } from "posthog-js/react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import routes from "../sidebarRoutes";
import { useAppDispatch, useAppSelector } from "../store";
import BrandLogo from "./BrandLogo";
import Breadcrumbs from "./Breadcrumbs";
import GlobalSearch from "./GlobalSearch";
import HeaderSettings from "./HeaderSettings";
import { Avatar, AvatarImage } from "./shadcn/components/avatar";
import { Button } from "./shadcn/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "./shadcn/components/dropdown-menu";
const IS_SASKEN = checkForHost();

export const UserDropdownContent = ({ side }: { side?: "left" | "right" | "top" | "bottom" }) => {
  const user = useAppSelector(({ user }) => user.user);
  const tenant = useAppSelector(({ user }) => user.tenant);

  const poasthog = usePostHog();
  const dispatch = useAppDispatch();

  const defaultNavigate = useNavigate();
  const navigate = useCustomNavigate();

  const handleLogout = async () => {
    try {
      await dispatch(logoutUser()); // wait for logout to complete
      poasthog.reset();
      navigate(0); // refresh the page
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };
  if (!user) return <div />;

  return (
    <DropdownMenuContent className="min-w-56 !z-[100000]" align="end" forceMount side={side}>
      <DropdownMenuLabel className="font-normal flex gap-2 items-center">
        <Avatar className="size-8">
          <AvatarImage src={`https://api.dicebear.com/8.x/initials/svg?seed=${user.name}`} />
        </Avatar>
        <div className="flex flex-col space-y-1">
          <p className="text-base leading-none font-medium">{user.name}</p>
          <p className="text-muted-foreground text-sm leading-none">{user.email}</p>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        {(tenant?.setupStatus === "COMPLETED" || UserWithoutTenant.includes(user?.role)) && (
          <>
            <DropdownMenuItem className="py-2.5" onClick={() => defaultNavigate("/profile")}>
              <User size={BUTTON_ICON_SIZE} className="mr-2" />
              Profile
            </DropdownMenuItem>
            {TENANT_ROLES.includes(user?.role) && (
              <DropdownMenuItem className="py-2.5" onClick={() => navigate("/organization")}>
                <Settings size={BUTTON_ICON_SIZE} className="mr-2" />
                Organization
              </DropdownMenuItem>
            )}
          </>
        )}
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem className="py-2.5" onClick={handleLogout}>
        <LogOut size={BUTTON_ICON_SIZE} className="mr-2" /> Log out
      </DropdownMenuItem>
    </DropdownMenuContent>
  );
};

export default function Header({ route, onShowNotificationPanel }) {
  const isCollapsed = useAppSelector(({ sidebarSlice }) => sidebarSlice.isCollapsed);

  const [visible, setVisible] = useState(true);
  const [prevScrollPos, setPrevScrollPos] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollPos = window.scrollY;

      // Show header when scrolling up or at the top
      const isScrollingUp = prevScrollPos > currentScrollPos;
      setVisible(isScrollingUp || currentScrollPos < 16);

      setPrevScrollPos(currentScrollPos);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [prevScrollPos]);

  const user = useAppSelector(({ user }) => user.user);
  const tenant = useAppSelector(({ user }) => user.tenant);
  const dispatch = useAppDispatch();

  const navigate = useCustomNavigate();

  return (
    <div id="navBar" className="sticky top-0 z-50 flex flex-col">
      <div
        // className="header flex items-center justify-between fixed  right-0 top-0 transition-all duration-500 h-[4.2rem]"

        className={`
     bg-background/50 flex  items-center gap-3 px-4 backdrop-blur-md pt-3 bg justify-between
        `}
        style={{
          zIndex: 100,
          left:
            tenant?.setupStatus !== "COMPLETED" && TENANT_ROLES.includes(user?.role)
              ? "0"
              : isCollapsed
                ? "6rem"
                : "18rem"
        }}
      >
        {tenant?.setupStatus === "COMPLETED" || UserWithoutTenant.includes(user?.role) ? (
          <div className="flex gap-2 items-center  ">
            {/* <IconButton
              className={` !p-2 !text-white `}
              onClick={() => {
                dispatch(toggleCollapse());
              }}
            >
              {isCollapsed ? <ChevronLast size="1.5rem" /> : <ChevronFirst size="1.5rem" />}
            </IconButton> */}
            <Button
              variant="secondary"
              size="icon"
              className="size-9 border bg-background"
              onClick={() => {
                // setIsCollapsed(prev=> !prev)
                dispatch(toggleCollapse());
              }}
            >
              {isCollapsed ? <PanelRight size="1.25rem" /> : <PanelLeft size="1.25rem" />}
            </Button>
            {(tenant?.setupStatus === "COMPLETED" || UserWithoutTenant.includes(user?.role)) && (
              <Breadcrumbs routes={routes.map((r) => ({ ...r, breadcrumb: r.title }))} />
            )}
          </div>
        ) : (
          <BrandLogo
            onClick={() => {
              navigate("/");
            }}
            className={"inline-block cursor-pointer max-w-[5rem] max-h-[3rem] ml-2 "}
          />
        )}
        <div className="flex gap-4 py-2 ">
          <div className="w-full flex items-center justify-center ">
            <div className="w-full max-w-2xl">
              <div className="   flex items-center gap-4">
                {(tenant?.setupStatus === "COMPLETED" ||
                  UserWithoutTenant.includes(user?.role)) && <GlobalSearch />}

                <div className="flex items-center gap-4">
                  <Button variant="secondary" size="icon" className="size-9 border bg-background">
                    <a
                      href={
                        "https://oneuptime.firewires.net/status-page/6d9409be-8bdd-44bc-a8cf-855e0b240db7"
                      }
                      target="_blank"
                    >
                      <Activity size="1.25rem" className="text-muted-foreground" />
                    </a>
                  </Button>
                  {route?.help && (
                    <Button variant="secondary" size="icon" className="size-9 border bg-background">
                      <a href={route.help} target="_blank">
                        <HelpCircle size="1.2rem" className="text-muted-foreground" />
                      </a>
                    </Button>
                  )}
                  <HeaderSettings />

                  {/* Notification with dot */}
                  {!IS_SASKEN && tenant && (
                    <div className="relative">
                      <Button
                        variant="secondary"
                        size="icon"
                        className="size-9 border shadow-sm bg-background"
                        onClick={onShowNotificationPanel}
                      >
                        <Bell size="1.25rem" className="text-muted-foreground" />
                      </Button>
                      <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-green-500" />
                    </div>
                  )}

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <div
                        data-testid="account-popup"
                        // onClick={() => setOpenUserMenu((prev) => !prev)}
                        className="center gap-4 mr-2"
                        // ref={userMenuRef}
                      >
                        {" "}
                        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                          <div className="p-1.5  bg-brandColor text-white rounded-full">
                            <User className="h-6 w-6" />
                          </div>
                        </Button>
                      </div>
                    </DropdownMenuTrigger>
                    <UserDropdownContent />
                  </DropdownMenu>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="header mb-9" /> */}
    </div>
  );
}
