"use client";

import type React from "react";

import useAddReportMutation from "@hooks/reports/useAddReportMutation";
import { availableOptions } from "@src/pages/MonitorPage/layout/SelectDuration";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BookPlus, X } from "lucide-react";
import { useState } from "react";
import CustomButton from "./Button";
import Dropdown from "./Dropdown";
import Input from "./Input";
import { Badge } from "./shadcn/components/badge";
import { Button } from "./shadcn/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "./shadcn/components/dialog";
import { Input as ShadcnInput } from "./shadcn/components/input";
import { Label } from "./shadcn/components/label";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "./shadcn/components/tabs";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { MODE } from "@api/index";

interface ReportFormData {
  title: string;
  schedule?: string;
  scheduleType?: "daily" | "weekly" | "monthly";
  scheduleDetails?: {
    hour?: string;
    minute?: string;
    daysOfWeek?: string[];
    daysOfMonth?: string[];
  };
  duration: string;
  userDashboardId?: number;
  emails: string[];
}

// Add this function to parse cron expressions and set form state
export const parseCronExpression = (cronExpression: string) => {
  if (!cronExpression) return null;

  const parts = cronExpression.split(" ");
  if (parts.length !== 5) return null;

  const [minute, hour, dayOfMonth, dayOfWeek] = parts;

  // Initialize schedule details
  const scheduleDetails: {
    hour?: string;
    minute?: string;
    daysOfWeek?: string[];
    daysOfMonth?: string[];
  } = {
    hour: hour === "*" ? "0" : hour,
    minute: minute === "*" ? "0" : minute
  };

  let scheduleType: "daily" | "weekly" | "monthly" = "daily";

  // Check if it's a weekly schedule
  if (dayOfWeek !== "*") {
    scheduleType = "weekly";
    scheduleDetails.daysOfWeek = dayOfWeek.split(",");
  }

  // Check if it's a monthly schedule
  if (dayOfMonth !== "*") {
    scheduleType = "monthly";
    scheduleDetails.daysOfMonth = dayOfMonth.split(",");
  }

  return {
    scheduleType,
    scheduleDetails
  };
};
export const getInitialState = () => {
  const savedCronExpression = "0 9 * * 1";

  const parsedSchedule = parseCronExpression(savedCronExpression);

  return {
    title: "",
    duration: "daily",
    emails: MODE === "development" ? ["<EMAIL>"] : [],
    scheduleType: parsedSchedule?.scheduleType || "daily",
    scheduleDetails: parsedSchedule?.scheduleDetails || {
      hour: "9",
      minute: "0",
      daysOfWeek: ["1"], // Monday
      daysOfMonth: ["1"] // 1st day
    },
    schedule: savedCronExpression
  } as {
    title: string;
    schedule?: string;
    scheduleType?: "daily" | "weekly" | "monthly";
    scheduleDetails?: {
      hour?: string;
      minute?: string;
      daysOfWeek?: string[];
      daysOfMonth?: string[];
    };
    duration: string;
    userDashboardId?: number;
    emails: string[];
  };
};

export default function GenerateReportModal({
  filters,
  reportType
}: {
  filters: any;
  reportType: "dashboard" | "trip" | "stop" | "event" | "table" | "tracking";
}) {
  const [open, setOpen] = useState(false);

  const [formData, setFormData] = useState<ReportFormData>(getInitialState());
  const [emailInput, setEmailInput] = useState("");
  const [emailError, setEmailError] = useState("");

  const resetFormState = () => {
    setFormData(getInitialState());
    setEmailInput("");
    setEmailError("");
  };

  // This simulates loading a report from the database

  const addReportMutation = useAddReportMutation({
    onSuccess: () => {
      showSuccessToast("Report request send successfully");
      resetFormState();
      setOpen(false);
    }
  });

  const handleAddEmailOnBlur = () => {
    if (emailInput.trim() !== "") {
      addEmail(emailInput);
    }
  };

  const addEmail = (email: string) => {
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    if (formData.emails.includes(email.trim())) {
      setEmailError("This email is already added");
      return;
    }

    setFormData({
      ...formData,
      emails: [...formData.emails, email.trim()]
    });
    setEmailInput("");
    setEmailError("");
  };

  const handleAddEmail = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && emailInput.trim() !== "") {
      e.preventDefault();
      addEmail(emailInput);
    }
  };

  const handleRemoveEmail = (email: string) => {
    setFormData({
      ...formData,
      emails: formData.emails.filter((e) => e !== email)
    });
  };

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>, type: "schedule" | "onetime") => {
    e.preventDefault();
    // Here you would typically send the data to your backend
    if (formData.emails.length === 0) {
      showErrorToast("Recipients are required for sending report.");
      return;
    }
    if (type === "onetime") {
      addReportMutation.mutate({
        title: formData.title,
        filters,
        duration: formData.duration,
        type: reportType,
        emails: formData.emails
      });
      return;
    } else {
      addReportMutation.mutate({
        title: formData.title,
        filters,
        schedule: formData.schedule,
        duration: formData.duration,
        type: reportType,
        emails: formData.emails
      });
      return;
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (state === false) {
          resetFormState();
        }
        setOpen(state);
      }}
    >
      <DialogTrigger asChild>
        <CustomButton color="gray" small startIcon={<BookPlus size={BUTTON_ICON_SIZE} />}>
          Create Report
        </CustomButton>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Report</DialogTitle>
          <DialogDescription>Configure your report settings and recipients.</DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="onetime" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="schedule">Schedule Report</TabsTrigger>
            <TabsTrigger value="onetime">One-time Report</TabsTrigger>
          </TabsList>
          <TabsContent value="schedule" className="space-y-4">
            <form onSubmit={(e) => handleSubmit(e, "schedule")}>
              <UpdateScheduleReportForm
                formData={formData}
                setFormData={setFormData}
                handleRemoveEmail={handleRemoveEmail}
                handleAddEmail={handleAddEmail}
                handleAddEmailOnBlur={handleAddEmailOnBlur}
                emailInput={emailInput}
                setEmailInput={setEmailInput}
                emailError={emailError}
              />

              <CustomButton
                type="submit"
                className="ml-auto mt-2"
                loading={addReportMutation.isPending}
              >
                Schedule Report
              </CustomButton>
            </form>
          </TabsContent>
          <TabsContent value="onetime" className="space-y-2">
            <form onSubmit={(e) => handleSubmit(e, "onetime")}>
              <UpdateOneTimeReportForm
                formData={formData}
                setFormData={setFormData}
                handleRemoveEmail={handleRemoveEmail}
                handleAddEmail={handleAddEmail}
                handleAddEmailOnBlur={handleAddEmailOnBlur}
                emailInput={emailInput}
                setEmailInput={setEmailInput}
                emailError={emailError}
              />

              <CustomButton
                loading={addReportMutation.isPending}
                type="submit"
                className="ml-auto mt-2"
              >
                Send Report Now
              </CustomButton>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

export const UpdateScheduleReportForm = ({
  formData,
  setFormData,
  handleRemoveEmail,
  handleAddEmail,
  handleAddEmailOnBlur,
  emailInput,
  setEmailInput,
  emailError
}: {
  formData: ReportFormData;
  setFormData: React.Dispatch<React.SetStateAction<ReportFormData>>;
  handleRemoveEmail: (email: string) => void;
  handleAddEmail: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  handleAddEmailOnBlur: () => void;
  emailInput: string;
  setEmailInput: React.Dispatch<React.SetStateAction<string>>;
  emailError: string;
}) => {
  const updateScheduleDetails = (key: string, value: string) => {
    const newDetails = {
      ...(formData.scheduleDetails || {}),
      [key]: value
    };

    const newSchedule = generateCronFromSchedule(formData.scheduleType || "daily", newDetails);

    setFormData({
      ...formData,
      scheduleDetails: newDetails,
      schedule: newSchedule
    });
  };

  const toggleDayOfWeek = (day: string) => {
    const currentDays = formData.scheduleDetails?.daysOfWeek || [];
    let newDays;

    if (currentDays.includes(day)) {
      newDays = currentDays.filter((d) => d !== day);
    } else {
      newDays = [...currentDays, day];
    }

    const newDetails = {
      ...(formData.scheduleDetails || {}),
      daysOfWeek: newDays
    };

    const newSchedule = generateCronFromSchedule(formData.scheduleType || "weekly", newDetails);

    setFormData({
      ...formData,
      scheduleDetails: newDetails,
      schedule: newSchedule
    });
  };

  const generateCronFromSchedule = (
    type: string,
    details: {
      hour?: string;
      minute?: string;
      daysOfWeek?: string[];
      daysOfMonth?: string[];
    }
  ) => {
    const minute = details.minute || "0";
    const hour = details.hour || "9";

    switch (type) {
      case "daily":
        return `${minute} ${hour} * * *`;
      case "weekly":
        if (!details.daysOfWeek?.length) return `${minute} ${hour} * * 1`; // Default to Monday
        return `${minute} ${hour} * * ${details.daysOfWeek.sort().join(",")}`;
      case "monthly":
        if (!details.daysOfMonth?.length) return `${minute} ${hour} 1 * *`; // Default to 1st day
        return `${minute} ${hour} ${details.daysOfMonth.sort((a, b) => Number.parseInt(a) - Number.parseInt(b)).join(",")} * *`;
      default:
        return "0 9 * * 1"; // Default
    }
  };

  return (
    <div className="grid gap-4">
      <div className="grid gap-2">
        <Input
          id="title"
          label="Report Title"
          value={formData.title}
          required
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          placeholder="Monthly Sales Report"
        />
      </div>
      <div className="grid gap-2">
        <div className="space-y-4">
          <Dropdown
            onChange={(option) => {
              setFormData({
                ...formData,
                scheduleType: option,
                schedule: generateCronFromSchedule(option, formData.scheduleDetails || {})
              });
            }}
            value={formData.scheduleType || "daily"}
            options={["daily", "weekly"]}
            label="Schedule Type"
            required
          />

          {formData.scheduleType === "daily" && (
            <div className="grid gap-2">
              <div className="flex gap-2">
                <Dropdown
                  onChange={(option) => updateScheduleDetails("hour", option)}
                  value={formData.scheduleDetails?.hour || "9"}
                  options={Array.from({ length: 24 }).map((_, i) => (i + 1).toString())}
                  label="Hour"
                  className="w-24"
                />

                <span className="flex items-center">:</span>

                <Dropdown
                  onChange={(option) => updateScheduleDetails("minute", option)}
                  value={formData.scheduleDetails?.minute || "0"}
                  options={Array.from({ length: 60 }).map((_, i) => (i + 1).toString())}
                  label="Minute"
                  className="w-24"
                />
              </div>
            </div>
          )}

          <div className="grid gap-2">
            <Dropdown
              onChange={(option) => setFormData({ ...formData, duration: option.value })}
              value={availableOptions.find((item) => item.value === formData.duration)}
              getOptionLabel="title"
              placeHolder="Select duration"
              options={availableOptions}
              label="Duration"
              required
            />
          </div>

          {formData.scheduleType === "weekly" && (
            <div className="grid gap-2">
              <Label>Days of Week</Label>
              <div className="flex flex-wrap gap-2">
                {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day, index) => (
                  <Button
                    key={day}
                    type="button"
                    variant={
                      formData.scheduleDetails?.daysOfWeek?.includes(index.toString())
                        ? "default"
                        : "outline"
                    }
                    className="w-12 h-10"
                    onClick={() => toggleDayOfWeek(index.toString())}
                  >
                    {day}
                  </Button>
                ))}
              </div>
              <div className="grid gap-2 mt-2">
                <Label htmlFor="time">Time</Label>
                <div className="flex gap-2">
                  <Dropdown
                    onChange={(option) => updateScheduleDetails("hour", option)}
                    value={formData.scheduleDetails?.hour || "9"}
                    options={Array.from({ length: 24 }).map((_, i) => (i + 1).toString())}
                    label="Hour"
                    required
                    className="w-24"
                  />
                  <span className="flex items-center">:</span>
                  <Dropdown
                    onChange={(option) => updateScheduleDetails("minute", option)}
                    value={formData.scheduleDetails?.minute || "0"}
                    options={Array.from({ length: 60 }).map((_, i) => (i + 1).toString())}
                    label="Minute"
                    required
                    className="w-24"
                  />
                </div>
              </div>
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            Cron expression:{" "}
            <code className="bg-muted px-1 py-0.5 rounded">{formData.schedule || "* * * * *"}</code>
          </div>
        </div>
      </div>
      <div className="grid gap-2">
        <Label htmlFor="emails">Recipients</Label>
        <div className="flex flex-wrap gap-2 mb-2 min-h-10 p-2 border rounded-md input-container ">
          {formData.emails.map((email) => (
            <Badge key={email} className="gap-1 py-1.5">
              {email}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-destructive/20 rounded-full"
                onClick={() => handleRemoveEmail(email)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove</span>
              </Button>
            </Badge>
          ))}
          <ShadcnInput
            id="emails"
            value={emailInput}
            onChange={(e) => setEmailInput(e.target.value)}
            onKeyDown={handleAddEmail}
            onBlur={handleAddEmailOnBlur}
            className="flex-1 min-w-[200px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-8 !outline-none !bg-transparent"
            placeholder={formData.emails.length ? "Add more..." : "Enter email addresses..."}
          />
        </div>

        {emailError && <p className="text-sm text-destructive">{emailError}</p>}
      </div>
    </div>
  );
};
export const UpdateOneTimeReportForm = ({
  formData,
  setFormData,
  handleRemoveEmail,
  handleAddEmail,
  handleAddEmailOnBlur,
  emailInput,
  setEmailInput,
  emailError
}: {
  formData: ReportFormData;
  setFormData: React.Dispatch<React.SetStateAction<ReportFormData>>;
  handleRemoveEmail: (email: string) => void;
  handleAddEmail: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  handleAddEmailOnBlur: () => void;
  emailInput: string;
  setEmailInput: React.Dispatch<React.SetStateAction<string>>;
  emailError: string;
}) => {
  return (
    <div className="grid gap-4">
      <div className="grid gap-2">
        <Input
          id="title-onetime"
          label="Report Title"
          required
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          placeholder="Sales Report"
        />
      </div>
      <div className="grid gap-2">
        <Dropdown
          onChange={(option) => setFormData({ ...formData, duration: option.value })}
          value={availableOptions.find((item) => item.value === formData.duration)}
          getOptionLabel="title"
          placeHolder="Select duration"
          options={availableOptions}
          label="Duration"
          required
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="emails-onetime">Recipients</Label>
        <div className="flex flex-wrap gap-2 mb-2 min-h-10 p-2 border rounded-md input-container ">
          {formData.emails.map((email) => (
            <Badge key={email} className="gap-1 py-1.5">
              {email}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-destructive/20 rounded-full"
                onClick={() => handleRemoveEmail(email)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove</span>
              </Button>
            </Badge>
          ))}
          <ShadcnInput
            id="emails-onetime"
            value={emailInput}
            onChange={(e) => setEmailInput(e.target.value)}
            onKeyDown={handleAddEmail}
            onBlur={handleAddEmailOnBlur}
            className="flex-1 min-w-[200px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-8 !outline-none !bg-transparent"
            placeholder={formData.emails.length ? "Add more..." : "Enter email addresses..."}
          />
        </div>
        {emailError && <p className="text-sm text-destructive">{emailError}</p>}
      </div>
    </div>
  );
};
