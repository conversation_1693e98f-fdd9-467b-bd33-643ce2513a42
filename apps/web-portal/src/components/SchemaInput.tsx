import { IconButton } from "@mui/material";
import { INPUT_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Plus } from "lucide-react";
import React from "react";
import ActionButton from "./ActionButton";
import Dropdown from "./Dropdown";
import Input from "./Input";

export const isObject = (data: unknown) => {
  return typeof data === "object" && data !== null && !Array.isArray(data);
};

const SchemaInput = ({
  onChangeKey,
  keyName,
  onChangeType,
  type,
  parentKey,
  className = "",
  index,
  onRemove
}: {
  onChangeKey: (e: React.ChangeEvent<HTMLInputElement>) => void;
  keyName: string;
  onChangeType: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type: string | object | Array<unknown>;
  className?: string;
  parentKey?: string;
  index?: number;
  onRemove: (pk?: string, key?: string) => void;
}) => {
  return (
    <>
      <div className={clsx("flex flex-row items-center gap-3", className)}>
        {index === 0 && (
          <IconButton
            className="!p-1 !bg-brandColor/20 !text-brandColor !absolute !left-[3.2rem]"
            onClick={() => {
              const newParent = JSON.parse(JSON.stringify(parentKey));
              const newKey = isObject(parentKey)
                ? `key${Object.keys(parentKey).length + 1}`
                : "key1";
              newParent[newKey] = "string";
              onChangeType(newParent);
            }}
          >
            <Plus size={INPUT_ICON_SIZE} />
          </IconButton>
        )}
        {keyName !== undefined && (
          <Input
            placeholder="key"
            className="flex-1"
            required
            value={keyName}
            onChange={onChangeKey}
          />
        )}
        <Dropdown
          placeHolder="value"
          required
          className="flex-1"
          options={["string", "number", "boolean", "object", "array"]}
          value={isObject(type) ? "object" : Array.isArray(type) ? "array" : `${type}`}
          onChange={onChangeType}
        />

        {(isObject(type) || Array.isArray(type)) && (
          <h2 className=" font-bold text-lg">{isObject(type) ? "{" : "["} </h2>
        )}
        <ActionButton type="delete" onClick={() => onRemove()} />
      </div>
      <div>
        {isObject(type) && (
          <div className="flex flex-col gap-1 pl-6 ">
            {Object.entries(type).map(([key, value], i) => (
              <SchemaInput
                keyName={key}
                type={value}
                onChangeKey={(e) => {}}
                onChangeType={(e) => {
                  // onChangeType(e);
                  console.log();
                }}
                parentKey={type}
                index={i}
                onRemove={() => {
                  // onRemove(isObject(type) ? undefined : parentKey, key);
                  console.log({ key, parentKey, type });
                }}
              />
            ))}
          </div>
        )}
        {Array.isArray(type) && (
          <div className="flex flex-col gap-1 pl-6 ">
            {type.map((item, i) => (
              <SchemaInput
                keyName={undefined}
                type={item}
                onChangeKey={(e) => {}}
                onChangeType={(e) => {}}
                parentKey={type}
                onRemove={() => {
                  // onRemove(isObject(type) ? undefined : parentKey, key);
                  console.log({ key, parentKey, type });
                }}
              />
            ))}
          </div>
        )}
      </div>

      {(isObject(type) || Array.isArray(type)) && (
        <h2 className=" font-bold text-lg">{isObject(type) ? "}" : "]"} </h2>
      )}
    </>
  );
};

export default SchemaInput;
