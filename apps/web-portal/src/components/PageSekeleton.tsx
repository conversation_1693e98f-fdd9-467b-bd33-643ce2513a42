import React from "react";
import { CardLoadingSkeleton } from "./Card/CardSkeleton";
import { Card } from "@components/ui";

const PageSekeleton = () => {
  return (
    <section className="  h-full m-2 opacity-50">
      <div className="space-y-6">
        <div className="grid grid-cols-4 gap-4">
          <Card>
            <CardLoadingSkeleton col={5} className="w-full" />
          </Card>
          <Card className="col-span-3">
            <CardLoadingSkeleton col={5} className="w-full" />
          </Card>
        </div>
        <div className="flex gap-6 child:w-full">
          <Card>
            <CardLoadingSkeleton col={4} className="w-full" />
          </Card>
          <Card>
            <CardLoadingSkeleton col={4} className="w-full" />
          </Card>
        </div>
      </div>
    </section>
  );
};

export default PageSekeleton;
