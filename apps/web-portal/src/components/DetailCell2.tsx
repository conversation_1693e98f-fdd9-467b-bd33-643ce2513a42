import clsx from "clsx";
import React from "react";

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  data?: React.ReactNode; // Now supports JSX elements
  icon?: React.ReactElement;
  flexDirectionRow?: boolean;
  titleWidth?: string;
  className?: string;
}

export default function DetailsCell2({
  title,
  data,
  icon,
  flexDirectionRow,
  titleWidth = "",
  className,
  ...props
}: Props) {
  return (
    <div {...props} className={clsx("flex gap-6 mb-3 items-center", className)}>
      <div className={clsx("flex gap-2 items-center min-w-[10rem]", titleWidth)}>
        {icon && <div className=" flex items-center justify-center w-8">{icon}</div>}
        <h4 className="heading-4 text-muted-foreground">{title}: </h4>
      </div>
      <div
        className={clsx(
          "heading-4 flex flex-col gap-2",
          flexDirectionRow && "!flex-row",
          title?.toLocaleLowerCase() !== "address" && " truncate"
        )}
      >
        {data || (data === 0 ? 0 : "N/A")}
      </div>
    </div>
  );
}
