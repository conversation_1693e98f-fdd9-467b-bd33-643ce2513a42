"use client";

import { useState, cloneElement, type ReactElement } from "react";
import { X } from "lucide-react";
import { Button } from "./shadcn/components/button";

interface ZoomableImageProps {
  children: ReactElement;
  zoomClassName?: string;
}

export default function ZoomableImage({
  children,
  zoomClassName = "max-w-lg "
}: ZoomableImageProps) {
  const [isZoomed, setIsZoomed] = useState(false);

  const openZoom = () => setIsZoomed(true);
  const closeZoom = () => setIsZoomed(false);

  // Clone the child element and add click handler and cursor pointer
  const clickableImage = cloneElement(children, {
    ...children.props,
    onClick: openZoom,
    className: `${children.props.className || ""} cursor-pointer transition-transform hover:scale-105`,
    style: {
      ...children.props.style
    }
  });

  return (
    <>
      {clickableImage}

      {/* Zoom Overlay */}
      {isZoomed && (
        <div
          className="fixed -inset-10 bg-black bg-opacity-80 flex items-center justify-center z-[100000]"
          onClick={closeZoom}
        >
          <div className="relative" onClick={(e) => e.stopPropagation()}>
            <Button
              variant="ghost"
              size="icon"
              className="absolute -top-12 right-0 text-white hover:bg-white/20 z-10"
              onClick={closeZoom}
            >
              <X className="w-6 h-6" />
            </Button>
            <div className="relative">
              <img
                src={children.props.src || "/placeholder.svg"}
                alt={children.props.alt}
                className={`rounded-lg shadow-2xl ${zoomClassName}`}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
