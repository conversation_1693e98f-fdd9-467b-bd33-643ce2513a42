import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger
} from "@components/shadcn/components/dialog";
import Button from "./Button";
import { Plus } from "lucide-react";
import React, { ChangeEventHandler, useState } from "react";
import Input from "./Input";
import { IconButton } from "@mui/material";
import { generateRandomString } from "@src/pages/MonitorPage/utils";
import { fetchApi } from "@api/_helpers";
import { SIMULATOR_URL } from "@api/index";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import SimulatorParameterItem from "@src/pages/ProductPage/SimulatorParameterItem";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";

// type Ref = {
//   openModal: () => void;
//   closeModal: () => void;
// };
type Props = unknown;
export const initialFormState = {
  name: "",
  productName: "",
  thingPrefix: "",
  thingCount: 1,
  description: ""
};

export type PropertiesItem =
  | {
      id: string;
      name: string;
      type: "number";
      min: number;
      max: number;
    }
  | {
      id: string;
      name: string;
      type: "string";
      values: string[];
    }
  | {
      id: string;
      name: string;
      type: "boolean";
    };

export const initialProperty: PropertiesItem = {
  name: "",
  type: "number",
  min: 0,
  max: 100,
  id: generateRandomString(8),
  // @ts-expect-error this is fine
  values: []
};

export type SimulatorItem = {
  id: string;
  name: string;
  description: string;
  parameters: PropertiesItem[];
  topic: string;
  status: "IN_PROGRESS" | "SUSPEND" | "RUNNING" | "PENDING";
  tenant: string;
  thingPrefix: string;
  thingCount: number;
  url: string;
  productName: string;
  createdAt: string;
  updatedAt: string;
};

const createSimulation = async ({
  data,
  properties
}: {
  data: typeof initialFormState;
  properties: PropertiesItem[];
}) => {
  const { name, ...rest } = data;

  const body = {
    parameters: properties,
    ...rest,
    simulatorName: name
  };

  const fetchResponse = await fetchApi(
    `/simulator`,
    {
      method: "POST",
      body
    },
    SIMULATOR_URL
  );
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
};

const AddSimulationModal: React.FC<Props> = () => {
  const [formData, setFormData] = React.useState(initialFormState);
  const [properties, setProperties] = useState<PropertiesItem[]>([initialProperty]);
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<1 | 2>(1);

  const createSimulatorMutation = useMutation({
    mutationFn: createSimulation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["simulators"] });
      setIsOpen(false);
      showSuccessToast("Simulation created successfully");
    },
    onError(error) {
      showErrorToast(error.message);
    }
  });

  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    if (e.target.id === "thingCount") {
      const newValue = e.target.value;
      if (newValue === "") {
        setFormData({
          ...formData,
          [e.target.id]: 0
        });
        return;
      }

      const numValue = parseFloat(newValue);

      // Check if it's a valid number and within range
      if (!Number.isNaN(numValue) && numValue >= 0 && numValue <= 50) {
        setFormData({
          ...formData,
          [e.target.id]: numValue
        });
      }
      return;
    }
    setFormData({
      ...formData,
      [e.target.id]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    createSimulatorMutation.mutate({ data: formData, properties });
  };

  const handleDialogChange = (_isOpen: boolean) => {
    if (!_isOpen) {
      setFormData(initialFormState);
      setStep(1);
      setProperties([initialProperty]);
    }
    setIsOpen(_isOpen);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogChange}>
      <DialogTrigger asChild>
        <Button startIcon={<Plus size={16} />}>Add Simulation</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Simulation</DialogTitle>
          <DialogDescription>
            Simulations will help you to view the data in a more interactive way.
          </DialogDescription>
        </DialogHeader>
        {step === 1 && (
          <form
            onSubmit={(e) => {
              e.preventDefault();
              setStep(2);
            }}
          >
            <div className="grid gap-4 pb-6">
              <Input
                id="name"
                required
                label="Simulator Name"
                value={formData.name}
                onChange={handleChange}
                className="col-span-4"
                placeholder="simulator name"
              />
              <Input
                id="productName"
                required
                label={`${PRODUCT_VAR} Name`}
                value={formData.productName}
                onChange={handleChange}
                className="col-span-4"
                placeholder={`${PRODUCT_VAR} name`}
              />
              <Input
                id="description"
                required
                label="Description"
                value={formData.description}
                onChange={handleChange}
                className="col-span-4"
                placeholder="description"
              />
              <Input
                id="thingPrefix"
                required
                label={`${THING_VAR} Prefix`}
                value={formData.thingPrefix}
                onChange={handleChange}
                className="col-span-4"
                placeholder={`${THING_VAR} prefix`}
              />

              <Input
                id="thingCount"
                required
                label="Thing Count (1-50)"
                value={formData.thingCount}
                className="col-span-4"
                placeholder="thing count"
                onChange={handleChange}
                type="number"
                max={50}
              />
            </div>

            <Button type="submit" className="ml-auto min-w-[160px]">
              Next
            </Button>
          </form>
        )}
        {step === 2 && (
          <form onSubmit={handleSubmit}>
            <div className="flex items-center justify-between mb-3">
              <p>Properties</p>
              <IconButton
                className="!p-1 !bg-brandColor !text-black"
                onClick={() => {
                  setProperties([
                    ...properties,
                    { ...initialProperty, id: generateRandomString(8) }
                  ]);
                }}
              >
                <Plus size={16} />
              </IconButton>
            </div>
            <div className="grid gap-3 pb-6">
              {properties.map((property) => (
                <SimulatorParameterItem
                  key={property.id}
                  property={property}
                  setProperties={setProperties}
                />
              ))}
            </div>
            <div className="flex items-center justify-between">
              <Button className="min-w-[160px]" onClick={() => setStep(1)} outlined>
                Back
              </Button>
              <Button loading={createSimulatorMutation.isPending} type="submit">
                Create Simulation
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddSimulationModal;
