import useCustomNavigate from "@hooks/useCustomNavigate";
import useDeleteNotificationHistory from "@hooks/useDeleteNotificationHistory";

import useReadNotificationHistory from "@hooks/useReadNotificationHistory";
import { Drawer, IconButton } from "@mui/material";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Archive, Bell, Cpu, MoreVertical, User } from "lucide-react";
import { useContext } from "react";
import { Info, X } from "lucide-react";
import { DarkModeContext } from "../hooks/useDarkMode";
import { darkColors } from "../utils/color";
import { CardLoadingSkeleton } from "./Card/CardSkeleton";
import DataNotFound from "./DataNotFound";
import HeadingIcon from "./HeadingIcon";
import { Button } from "./shadcn/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "./shadcn/components/dropdown-menu";
import { ScrollArea } from "./shadcn/components/scroll-area";
import { notification } from "@frontend/shared";
import SideSheet from "./SideSheet";
import { Card } from "./ui";

const TYPE_ICON_MAP = {
  product: Archive,
  thing: Cpu,
  user: User,
  default: Info
};

const ACTION_COLOR_MAP = {
  created: "text-green-500 bg-green-500/10",
  updated: "text-yellow-500 bg-yellow-500/10",
  deleted: "text-red-500 bg-red-500/10",
  default: "text-blue-500 bg-blue-500/10"
};

const notificationIconColor = (notificationType) => {
  const match = notificationType.match(/^(product|thing|user)(Created|Updated|Deleted)$/i);
  const prefix = match?.[1]?.toLowerCase() || "default";
  const action = match?.[2]?.toLowerCase() || "default";
  // Get icon and color based on prefix and action
  const Icon = TYPE_ICON_MAP[prefix] || TYPE_ICON_MAP.default;
  const color = ACTION_COLOR_MAP[action] || ACTION_COLOR_MAP.default;

  return (
    <div
      className={clsx(
        "p-2 center   duration-300 rounded-full group-hover:scale-105",
        "shadow-md  z-10",
        color
      )}
    >
      <Icon size="1.3rem" />
    </div>
  );
};

export default function NotificationDrawer({ open, onClose, ...props }) {
  const [darkMode, _] = useContext(DarkModeContext);
  const { data: notificationList, isLoading } = notification.useNotificationHistory({
    page: 1,
    limit: 10
  });

  const deleteNotification = useDeleteNotificationHistory();
  const readNotification = useReadNotificationHistory();
  const navigate = useCustomNavigate();
  return (
    <SideSheet
      title="Notifications"
      size="lg"
      showBackdrop={true}
      open={open}
      onClose={onClose}
      {...props}
      footer={
        notificationList &&
        notificationList.notificationHistoryList.length && (
          <Button
            onClick={() => {
              onClose();
              navigate("/notifications/history");
            }}
            variant="ghost"
            size="sm"
            className="w-full justify-center text-xs"
          >
            View all
          </Button>
        )
      }
    >
      {isLoading ? (
        <CardLoadingSkeleton className="w-full" />
      ) : !notificationList || !notificationList.notificationHistoryList.length ? (
        <DataNotFound title="No Notification Available" />
      ) : (
        <div className=" space-y-2">
          {notificationList.notificationHistoryList.map((notification) => (
            <Card
              variant={notification.read ? "second" : "third"}
              key={notification.id}
              className={`flex items-start `}
            >
              {notificationIconColor(notification.category)}

              <div className="flex-grow">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{notification.title}</p>
                  <div className="flex items-center justify-between gap-4">
                    <p className="text-xs ">
                      {convetUTCToLocal(notification.createdAt, "DD MMM YY HH:mm A")}
                    </p>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="z-[10000]">
                        {!notification.read && (
                          <DropdownMenuItem
                            onClick={() => readNotification.mutate([notification.id])}
                          >
                            Mark as read
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem
                          onClick={() => deleteNotification.mutate(notification.id)}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">{notification.body}</p>
              </div>
            </Card>
          ))}
        </div>
      )}
    </SideSheet>
  );
}
