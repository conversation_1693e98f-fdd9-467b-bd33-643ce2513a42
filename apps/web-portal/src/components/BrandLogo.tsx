import { memo, useContext } from "react";
import { DarkModeContext } from "@hooks/useDarkMode";
import WHITE_LABEL_CONFIG from "@hooks/whiteLabel/whiteLabelConfig";
import useCustomizations from "@hooks/classic/useCustomisetions";

const BrandLogo = ({
  className,
  mode,
  ...props
}: {
  mode?: "light" | "dark";
  className?: string;
}) => {
  const [darkMode] = useContext(DarkModeContext);
  const { customization } = useCustomizations();

  const logoMode = () => {
    if (mode === "light") {
      return WHITE_LABEL_CONFIG.logoLight;
    }

    return WHITE_LABEL_CONFIG.logoDark;
  };

  return (
    <img
      className={className}
      src={
        mode ? logoMode() : darkMode ? WHITE_LABEL_CONFIG.logoDark : WHITE_LABEL_CONFIG.logoLight
      }
      alt={WHITE_LABEL_CONFIG.logoAlt}
      {...props}
    />
  );
};

export default memo(BrandLogo);
