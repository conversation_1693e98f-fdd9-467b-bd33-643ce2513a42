import clsx from "clsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "./shadcn/components/selelct";

function SelectFilterData({
  options,
  selected,
  setSelected,
  className
}: {
  options: { label: string; value: string }[];
  selected: string;
  setSelected: (item: string) => void;
  className?: string;
}) {
  return (
    <Select value={selected} onValueChange={setSelected}>
      <SelectTrigger className={clsx("min-w-20  input-container", className)}>
        <SelectValue placeholder="Select Filter" />
      </SelectTrigger>
      <SelectContent>
        {options.map((item) => (
          <SelectItem value={item.value} key={item.value}>
            {item.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export default SelectFilterData;
