import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@components/shadcn/components/dialog";
import { Button } from "@components/shadcn/components/button";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import React from "react";
import { X } from "lucide-react";

const FormDialog = ({
  children,
  title,
  open,
  onClose,
  description,
  notDismissable,
  footer
}: {
  children: React.ReactNode;
  title?: string;
  open: boolean;
  onClose: () => void;
  description?: string;
  notDismissable?: boolean;
  footer?: React.ReactNode;
}) => {
  return (
    <Dialog
      open={open}
      onOpenChange={notDismissable ? undefined : (isOpen) => !isOpen && onClose()}
    >
      <DialogContent className="max-w-2xl max-h-[90vh] p-0 gap-0 [&>button]:hidden">
        <div className="flex flex-col max-h-[90vh]">
          {title && (
            <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b border-border">
              <div className="flex items-start justify-between">
                <div className="flex flex-col gap-2">
                  <DialogTitle className="text-left text-xl font-semibold">{title}</DialogTitle>
                  {description && (
                    <DialogDescription className="text-muted-foreground text-left">
                      {description}
                    </DialogDescription>
                  )}
                </div>

                <Button variant="ghost" size="icon" className="h-8 w-8 p-0 -mt-1" onClick={onClose}>
                  <X size={BUTTON_ICON_SIZE} />
                </Button>
              </div>
            </DialogHeader>
          )}
          <div className="flex-1 p-6 overflow-y-auto">{children}</div>
          {footer && (
            <DialogFooter className="flex-shrink-0 p-6 pt-4 border-t border-border">
              {footer}
            </DialogFooter>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FormDialog;
