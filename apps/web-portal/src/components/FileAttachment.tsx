import { X } from "lucide-react";
import { But<PERSON> } from "./shadcn/components/button";
import { <PERSON>Footer } from "./shadcn/components/card";
import clsx from "clsx";

interface FileAttachmentProps {
  file: string;
  isDownloadable?: boolean;
  handleRemove?: () => void;
  small?: boolean;
  ext?: string;
  fileName?: string;
}

export function FileAttachment({
  file,
  isDownloadable = false,
  handleRemove,
  small,
  ext,
  fileName
}: FileAttachmentProps) {
  const extension = ext?.toLowerCase() || file.split(".").pop()?.toLowerCase() || "";
  const isImage = /^(png|jpg|jpeg|gif|webp|svg)$/.test(extension);
  const getFileIcon = (extension: string) => {
    if (extension.includes("pdf")) {
      return (
        <div className="h-12 w-12 bg-red-500 rounded-md flex items-center justify-center">
          <span className="text-white font-bold text-sm">PDF</span>
        </div>
      );
    }

    if (extension.includes("doc") || extension.includes("word")) {
      return (
        <div className="h-12 w-12 bg-blue-500 rounded-md flex items-center justify-center">
          <span className="text-white font-bold text-sm">DOC</span>
        </div>
      );
    }

    if (extension.includes("xls") || extension.includes("sheet")) {
      return (
        <div className="h-12 w-12 bg-green-500 rounded-md flex items-center justify-center">
          <span className="text-white font-bold text-sm">XLS</span>
        </div>
      );
    }

    if (extension.includes("zip") || extension.includes("archive")) {
      return (
        <div className="h-12 w-12 bg-yellow-500 rounded-md flex items-center justify-center">
          <span className="text-white font-bold text-sm">ZIP</span>
        </div>
      );
    }

    return (
      <div className="h-12 w-12 bg-gray-500 rounded-md flex items-center justify-center">
        <span className="text-white font-bold text-sm">FILE</span>
      </div>
    );
  };

  return (
    <div className={clsx(small ? "w-32" : "w-40")}>
      <div
        className={clsx(" flex-shrink-0  overflow-hidden rounded-md card-border group relative ")}
      >
        {handleRemove && (
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity !p-1 h-8 w-8"
            onClick={handleRemove}
            type="button"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Remove</span>
          </Button>
        )}
        <div
          className={clsx(
            "bg-gray-500/10 h-28 w-full flex flex-col items-center justify-center relative"
          )}
        >
          {isImage ? (
            <img src={file} alt={file} className="h-full object-cover w-full" />
          ) : (
            <>
              <div className="mb-4">{getFileIcon(extension)}</div>
            </>
          )}
        </div>
        {isDownloadable && (
          <CardFooter className="p-0 ">
            <Button
              variant="secondary"
              className="w-full rounded-none "
              onClick={() => window.open(file, "_blank")}
            >
              Download
            </Button>
          </CardFooter>
        )}
      </div>
      {fileName && <p className="truncate text-muted-foreground">{fileName}</p>}
    </div>
  );
}
