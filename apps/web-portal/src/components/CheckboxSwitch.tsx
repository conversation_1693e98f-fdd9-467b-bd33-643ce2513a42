import { Checkbox } from "./shadcn/components/checkbox";
import { PropsWithChildren, useId } from "react";

type CheckboxSwitchProps = PropsWithChildren<{
  label: string;
  description?: string;
}>;

const CheckboxSwitch = ({ label, description, ...props }: CheckboxSwitchProps) => {
  const id = useId();

  return (
    <div className="flex items-start">
      <Checkbox {...props} id={`${id}-${label}-checkbox-switch`} />
      <div className="ml-2 space-y-0.5">
        <label
          htmlFor={`${id}-${label}-checkbox-switch`}
          className="flex items-center heading-3 leading-none	"
        >
          {label}
        </label>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
      </div>
    </div>
  );
};

export default CheckboxSwitch;
