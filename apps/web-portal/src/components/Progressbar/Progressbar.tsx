import clsx from "clsx"
import React from "react"
import colors from "tailwindcss/colors"

function Progressbar({ color, filled, max, text, className, ...props }) {
  return (
    <div
      style={{ color: colors[color][500] }}
      className={clsx("flex items-center space-x-2 font-bold", className)}
      {...props}
    >
      <span>{text}</span>
      <span
        style={{
          width: `${(filled / max) * 100}%`,
          backgroundColor: colors[color][500],
        }}
        className="h-2 rounded-full"
      />
      <span>{filled}</span>
    </div>
  )
}

export default Progressbar
