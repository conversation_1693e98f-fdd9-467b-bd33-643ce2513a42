import { LinearProgress, linearProgressClasses } from "@mui/material"
import React from "react"

function MaterialProgressbar({ value, limit, color }) {
  return (
    <div className="relative">
      <LinearProgress
        className="!h-2 !rounded-xl"
        sx={{ [`& .${linearProgressClasses.bar}`]: { borderRadius: 999 } }}
        variant="determinate"
        color={color || "inherit"}
        value={(value * 100) / limit}
      />
      <p className="text-xs font-medium text-gray-500 dark:text-gray-300 absolute -top-4 right-0">
        {`${value} / ${limit}`}
      </p>
    </div>
  )
}

export default MaterialProgressbar
