import {
  AlertDescription,
  AlertTitle,
  Alert as ShadcnAlert
} from "@components/shadcn/components/alert";
import clsx from "clsx";
import { Info, LucideIcon } from "lucide-react";

type AlertType = {
  title: React.ReactNode;
  description: React.ReactNode;
  Icon?: LucideIcon;
  type?: "info" | "warning" | "error" | "success";
  className?: string;
};

const variantStyleMap = {
  info: {
    container: "bg-blue-400/10 text-blue-600 ring-1 ring-blue-500",
    icon: "!text-blue-600"
  },
  warning: {
    container: "bg-yellow-400/10 text-yellow-600 ring-1 ring-yellow-500",
    icon: "!text-yellow-600"
  },
  error: {
    container: "bg-red-400/10 text-red-600  ring-1 ring-red-500",
    icon: "!text-red-600"
  },
  success: {
    container: "bg-green-400/10 text-green-600  ring-1 ring-green-500",
    icon: "!text-green-600"
  }
};

const Alert = ({ title, description, Icon = Info, type = "warning", className }: AlertType) => {
  const variant = variantStyleMap[type];

  return (
    <ShadcnAlert className={clsx("border-card-border", variant.container, className)}>
      <Icon className={clsx("h-4 w-4", variant.icon)} />
      <AlertTitle className="uppercase">{title}</AlertTitle>
      <AlertDescription>{description}</AlertDescription>
    </ShadcnAlert>
  );
};

export default Alert;
