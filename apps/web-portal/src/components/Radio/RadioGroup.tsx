import clsx from "clsx"
import React, { createContext } from "react"

export const RadioContext = createContext()

function RadioGroup({ col, value, onChange, children }) {
  return (
    <div className={clsx("flex gap-4 justify-center", col && "flex-col")}>
      <RadioContext.Provider value={{ selected: value, onChange }}>
        {children}
      </RadioContext.Provider>
    </div>
  )
}

export default RadioGroup
