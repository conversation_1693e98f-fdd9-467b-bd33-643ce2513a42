import clsx from "clsx"
import React, { useContext } from "react"
import { RadioContext } from "./RadioGroup"

function Radio({ children, className, ...props }) {
  const { selected, onChange } = useContext(RadioContext)
  return (
    <div
      className={clsx(
        "radio",
        selected === props.value && "radio-selected",
        className
      )}
    >
      <label className="center gap-2 w-full h-full cursor-pointer">
        {children}
        <input
          type="radio"
          className="hidden"
          checked={selected === props.value}
          onChange={onChange}
          {...props}
        />
      </label>
    </div>
  )
}

export default Radio
