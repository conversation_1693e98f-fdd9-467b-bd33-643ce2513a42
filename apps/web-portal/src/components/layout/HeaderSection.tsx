import React from "react";

type HeaderSectionProps = {
  title: React.ReactNode;
  description: string;
  actions?: React.ReactNode;
  startContent?: React.ReactNode;
};

const HeaderSection = ({ title, description, actions, startContent }: HeaderSectionProps) => {
  return (
    <div className="flex items-start md:items-center gap-4 justify-between flex-col md:flex-row">
      <div className="flex items-center gap-4">
        {startContent && startContent}
        <div className="">
          <h1 className="text-2xl font-bold text-foreground">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
      </div>
      <div className="shrink-0">{actions}</div>
    </div>
  );
};

export default HeaderSection;
