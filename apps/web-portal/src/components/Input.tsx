/* eslint-disable max-len */
import clsx from "clsx";
import React, { useId } from "react";

type Props = import("react").InputHTMLAttributes<HTMLInputElement> & {
  startIcon?: React.ReactElement;
  endIcon?: React.ReactElement;
  helperText?: string;
  small?: boolean;
  error?: string | boolean;
  innerClasses?: string;
  inputType?: "input" | "textarea";
  labelClassName?: string;
  label?: string;
  medium?: boolean;
  rows?: number;
  description?: string;
  noSpace?: boolean;
};

const Input: import("react").ForwardRefRenderFunction<HTMLInputElement, Props> = (
  {
    label,
    startIcon,
    endIcon,
    error,
    helperText,
    className,
    innerClasses,
    inputType = "input",
    labelClassName,
    rows = 4,
    medium = false,
    description,
    noSpace = false,
    ...props
  },
  forwardedRef
) => {
  const id = useId();

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (noSpace && e.key === " ") {
      e.preventDefault();
    }

    // If user provided onKeyDown, call it
    props.onKeyDown?.(e);
  };

  return (
    <div className={className}>
      {label && (
        <label
          htmlFor={`input-${id}`}
          className={clsx(
            "input-label-text",
            error && "!text-red-500 dark:!text-red-400",
            labelClassName && `${labelClassName} !ml-0`
          )}
        >
          {label}
          {props.required && <span className="ml-1 opacity-70">*</span>}
        </label>
      )}
      <div
        className={clsx(
          "input-container flex relative",
          innerClasses || "",
          props.disabled && "opacity-60",
          error ? "input-error" : "input-primary"
        )}
      >
        {startIcon && <div className="flex items-center pl-3 input-icon">{startIcon}</div>}
        {inputType === "textarea" ? (
          <textarea
            id={`input-${id}`}
            className={clsx(
              "input",
              props.disabled && "input-disabled  opacity-50 cursor-not-allowed"
            )}
            ref={forwardedRef}
            rows={rows}
            data-testid={`input${label || props.placeholder || "any"}`}
            {...props}
          />
        ) : (
          <input
            id={`input-${id}`}
            className={clsx(
              "input ",
              medium && "!py-2 !px-2 text-xs placeholder:!text-xs",
              props.disabled && "input-disabled opacity-70 cursor-not-allowed"
            )}
            disabled={props.disabled}
            ref={forwardedRef}
            data-testid={`input${label || props.placeholder || "any"}`}
            onKeyDown={handleKeyDown}
            {...props}
          />
        )}

        {endIcon && <div className="flex items-center pr-3 input-icon ">{endIcon}</div>}
      </div>
      {helperText && (
        <p className={clsx("helper-text", error && "input-helper-text-error")}>{helperText}</p>
      )}
      {description && <p className="helper-text">{description}</p>}
    </div>
  );
};
export default React.forwardRef(Input);
