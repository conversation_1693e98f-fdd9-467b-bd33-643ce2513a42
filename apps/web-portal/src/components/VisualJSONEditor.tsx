import { I<PERSON><PERSON>utton } from "@mui/material"
import clsx from "clsx"
import { AnimatePresence, motion } from "framer-motion"
import { useEffect, useState } from "react"
import { Box, ChevronRight } from "lucide-react"
import useDebounce from "../hooks/useDebounce"
import Input from "./Input"

const variants = {
  initial: {
    height: 0,
    transition: { duration: 0.2, ease: "easeOut" },
  },
  expanded: {
    height: "auto",
    transition: { duration: 0.2, ease: "easeOut" },
  },
}

export default function VisualJSONEditor({ className, json, onChange }) {
  const [jsonState, setJsonState] = useState(json)
  const [expanded, setExpanded] = useState(false)

  const debouncedJsonState = useDebounce(jsonState, 300)

  useEffect(() => {
    onChange(debouncedJsonState)
  }, [debouncedJsonState])

  return Object.entries(jsonState).map(([key, value]) => {
    // const dataType = getDataType(value)
    const dataType = typeof value

    return (
      <div key={key}
        className={clsx(
          "px-5 py-2 rounded-lg bg-gray-400/20 my-2",
          dataType !== "object" && "flex gap-4 justify-between",
          className
        )}
      >
        <div className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            {dataType === "object" && (
              <div className="-ml-2 p-2 bg-blue-500 rounded-full">
                <Box size={30} className="text-white" />
              </div>
            )}
            <label>
              <h4 className="text-lg font-medium">{key}</h4>
              {/* <span className="text-gray-400">{getDataType(value)}</span> */}
            </label>
          </div>
          {dataType === "object" && (
            <IconButton onClick={() => setExpanded((prev) => !prev)}>
              <ChevronRight
                size={30}
                className={clsx("transition", expanded && "rotate-90")}
              />
            </IconButton>
          )}
        </div>
        <div>
          {/* recurse for objects */}
          {dataType === "object" ? (
            <AnimatePresence>
              {expanded && (
                <motion.div
                  variants={variants}
                  initial="initial"
                  animate="expanded"
                  exit="initial"
                  className="overflow-hidden"
                >
                  <VisualJSONEditor
                    json={value}
                    onChange={(json) =>
                      setJsonState({ ...jsonState, [key]: json })
                    }
                  />
                </motion.div>
              )}
            </AnimatePresence>
          ) : (
            <Input
              defaultValue={value}
              type={dataType === "number" ? "number" : "text"}
              className="bg-transparent"
              onChange={(e) =>
                setJsonState({
                  ...jsonState,
                  [key]:
                    dataType === "number"
                      ? Number(e.target.value)
                      : e.target.value,
                })
              }
            />
          )}
        </div>
      </div>
    )
  })
}

const getDataType = (data) => (Array.isArray(data) ? "array" : typeof data)
