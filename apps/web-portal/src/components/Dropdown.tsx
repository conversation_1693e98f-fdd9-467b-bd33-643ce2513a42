import clsx from "clsx";
import React, { useEffect, useId, useRef, useState } from "react";
import Button from "./Button";
import { CircularProgress } from "@mui/material";
import Input from "./Input";
import { Plus, X, Check, ChevronsUpDown } from "lucide-react";
import { cn } from "./shadcn/utils";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { INPUT_ICON_SIZE } from "@utils/utilities";
import GenerateInputValue from "./GenerateInputValue";
import { generateRandomString } from "@src/pages/MonitorPage/utils";
import { DropdownProps } from "@/index";
import { Popover, PopoverContent, PopoverTrigger } from "./shadcn/components/popover";

const EmptyOption = <div className="dropdown-item center">No Option Available</div>;

const Dropdown = ({
  placeHolder,
  options,
  disabled,
  isSearchable,
  onChange,
  getOptionLabel,
  label,
  required,
  newOption,
  className,
  defaultValue,
  deepSearch,
  error,
  helperText,
  value,
  startIcon,
  getOptionDisabled,
  isMulti = false,
  noTags,
  isAddField,
  onAddField,
  autoFocus = false,
  labelClassName = "",
  optionsLoading = false,
  optionItemClassName = "",
  inputClassName,
  iconClassName,
  menuClassName,
  emptyOption = EmptyOption,
  generateRandomWithPrefix,
  fixedSuffix,
  direction,
  description
}: DropdownProps): React.ReactElement => {
  const [showMenu, setShowMenu] = useState(autoFocus);
  const [selectedValue, setSelectedValue] = useState(
    isMulti ? value || defaultValue || [] : value || defaultValue || null
  );
  const [inputWidth, setInputWidth] = useState<number | null>(null);
  const [searchValue, setSearchValue] = useState("");
  /**
   * @type {React.MutableRefObject<HTMLInputElement | null>}
   */
  const searchRef = useRef(null);
  /**
   * @type {React.MutableRefObject<HTMLDivElement | null>}
   */
  const inputRef = useRef(null);
  const addItemRef = useRef();
  const navigate = useCustomNavigate();
  const id = useId();
  const isRequiredCheck = isMulti ? selectedValue?.length === 0 : !selectedValue;

  useEffect(() => {
    if (showMenu && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setInputWidth(rect.width);
    }
  }, [showMenu]);
  useEffect(() => {
    if (!value || value.length === 0) {
      setSelectedValue(isMulti ? [] : null);
    }
  }, [value]);

  useEffect(() => {
    if (
      value ||
      typeof value === "string" ||
      typeof value === "number" ||
      typeof value === "undefined"
    ) {
      if (typeof value === "undefined") {
        setSelectedValue(value || defaultValue || "");
        return;
      }
      setSelectedValue(value);
    }
  }, [value]);

  useEffect(() => {
    setSearchValue("");
    if (deepSearch) {
      deepSearch("");
    }

    if (showMenu && searchRef.current) {
      searchRef.current.focus();
    }
  }, [showMenu]);

  const onSearch = (e) => {
    if (deepSearch) {
      deepSearch(e.target.value);
      setSearchValue(e.target.value);
    } else setSearchValue(e.target.value);
  };
  const getOptions = () => {
    if (!searchValue) {
      return options;
    }
    return getOptionLabel
      ? options.filter(
          (option) => option[getOptionLabel]?.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0
        )
      : options.filter((option) => option.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0);
  };

  useEffect(() => {
    const handler = (e: any) => {
      if (inputRef.current && !inputRef.current.contains(e.target)) {
        setShowMenu(false);
      }
    };
    setTimeout(() => {
      window.addEventListener("click", handler);
    }, 1000);
    return () => {
      window.removeEventListener("click", handler);
    };
  }, []);

  // Position dropdown when it opens
  useEffect(() => {
    const positionDropdown = (e?: Event) => {
      // Don't reposition if scroll is happening inside the dropdown
      if (e && e.target && (e.target as Element).closest(".overflow-y-auto")) {
        return;
      }

      if (showMenu && inputRef.current) {
        const rect = (inputRef.current as HTMLElement)?.getBoundingClientRect();
        // Find the specific dropdown menu by ID
        const dropdown = document.getElementById(`dropdown-menu-${id}`) as HTMLElement;
        if (rect && dropdown) {
          // dropdown.style.setProperty("top", `${rect.bottom + window.scrollY - 36}px`, "important");
          // dropdown.style.setProperty("left", `${rect.left + window.scrollX}px`, "important");
          dropdown.style.setProperty("width", `${rect.width}px`, "important");
          dropdown.style.setProperty("min-width", `${rect.width}px`, "important");
        } else if (showMenu) {
          // Retry after a short delay if dropdown not found
          setTimeout(() => positionDropdown(), 10);
        }
      }
    };

    if (showMenu) {
      positionDropdown();
      // setTimeout(positionDropdown, 0);
      window.addEventListener("scroll", positionDropdown, true);
      window.addEventListener("resize", positionDropdown);
    }

    return () => {
      window.removeEventListener("scroll", positionDropdown, true);
      window.removeEventListener("resize", positionDropdown);
    };
  }, [showMenu]);

  const handleInputClick = (e: any) => {
    setShowMenu(!showMenu);
  };

  const getDisplay = () => {
    if (noTags || !selectedValue || selectedValue.length === 0) {
      return <span className="text-gray-500 dark:text-gray-400 text-xs">{placeHolder}</span>;
    }
    if (isMulti) {
      return (
        <div className="flex  gap-1 flex-shrink-0 overflow-hidden overflow-x-scroll dropdown-scroll">
          {selectedValue.map((option) => (
            <div
              key={getOptionLabel ? option[getOptionLabel] : option}
              className="dropdown-tag-item text-white whitespace-nowrap"
            >
              {getOptionLabel ? option[getOptionLabel] : option}
              <span
                onClick={(e) => onTagRemove(e, option)}
                className={clsx(
                  "dropdown-tag-close bg-transparent rounded-full ml-1 hover:bg-gray-800/20",
                  !disabled && "cursor-pointer"
                )}
                style={{ padding: "1px" }}
              >
                <X size=".75rem" color="black" />
              </span>
            </div>
          ))}
        </div>
      );
    }
    return getOptionLabel ? selectedValue[getOptionLabel] : selectedValue;
  };

  const removeOption = (option) => {
    return selectedValue.filter((o) => {
      return getOptionLabel ? o[getOptionLabel] !== option[getOptionLabel] : o !== option;
    });
  };
  const onTagRemove = (e, option) => {
    e.stopPropagation();
    if (disabled) return;
    const newValue = removeOption(option);
    setSelectedValue(newValue);
    onChange(newValue);
  };

  const onItemClick = (option) => {
    let newValue;
    if (isMulti) {
      if (
        selectedValue.findIndex((o) => {
          return getOptionLabel ? o[getOptionLabel] === option[getOptionLabel] : o === option;
        }) >= 0
      ) {
        newValue = removeOption(option);
      } else {
        newValue = [...selectedValue, option];
      }
    } else {
      newValue = option;
      setShowMenu(false);
    }
    setSelectedValue(newValue);
    onChange(newValue);
  };

  const isSelected = (option) => {
    if (isMulti) {
      const data =
        selectedValue.filter((o) => {
          return getOptionLabel ? o[getOptionLabel] === option[getOptionLabel] : o === option;
        }).length > 0;
      return data;
    }
    if (!selectedValue) {
      return false;
    }
    return getOptionLabel
      ? selectedValue[getOptionLabel] === option[getOptionLabel]
      : selectedValue === option;
  };

  return (
    <div className={className} data-testid={`dropdown-${label || placeHolder}`}>
      {label && (
        <label
          htmlFor={`input-${id}`}
          className={clsx(
            "input-label-text",
            error && "!text-red-500 dark:!text-red-400",
            labelClassName && `${labelClassName} !ml-0`
          )}
        >
          {label}
          {required && <span className="ml-1 opacity-70">*</span>}
        </label>
      )}
      <div className={clsx("dropdown-container relative")}>
        <input
          name="validCheck"
          required={required && isRequiredCheck}
          className="absolute h-full w-full bg-transparent outline-none left-0 border-0 pointer-events-none caret-transparent text-transparent"
        />
        <Popover open={showMenu} onOpenChange={setShowMenu}>
          <PopoverTrigger asChild>
            <div
              ref={inputRef}
              onClick={disabled ? null : handleInputClick}
              data-testid="dropdown-opener"
              className={clsx(
                "flex gap-3 items-center justify-between input-container",
                showMenu && " ring-1 ring-brandColor",
                error && "input-error !ring-0",
                inputClassName
              )}
            >
              <div
                id={`input-${id}`}
                className={clsx(
                  "input gap-3 items-center",
                  disabled && "opacity-60 cursor-not-allowed"
                )}
              >
                {startIcon && startIcon}
                <div
                  className={clsx("dropdown-selected-value flex-1 truncate", optionItemClassName)}
                >
                  {getDisplay()}
                </div>
                <ChevronsUpDown size={INPUT_ICON_SIZE} className={iconClassName} />
              </div>
            </div>
          </PopoverTrigger>

          <PopoverContent
            align="start"
            side={direction || "bottom"}
            collisionPadding={8}
            style={{
              width: inputWidth || "auto",
              maxHeight: "240px"
            }}
            className={clsx("dropdown-menu !p-1 !overflow-hidden", menuClassName)}
          >
            {isSearchable && (
              <div className="search-box">
                <Input
                  className="w-full"
                  placeholder="Search..."
                  onChange={onSearch}
                  value={searchValue}
                  ref={searchRef}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            )}
            {isAddField && (
              <div className="search-box flex gap-2">
                <Input
                  placeholder="Enter new value"
                  ref={addItemRef}
                  small
                  className="w-full"
                  onChange={onSearch}
                  onClick={(e) => e.stopPropagation()}
                  value={searchValue}
                  endIcon={
                    generateRandomWithPrefix && (
                      <GenerateInputValue
                        title={label || "Generate"}
                        onClick={() => {
                          const randomNewOption = `${generateRandomWithPrefix}/${
                            fixedSuffix || generateRandomString(12)
                          }`;
                          onSearch({ target: { value: randomNewOption } });
                        }}
                      />
                    )
                  }
                />
                <Button
                  color="gray"
                  onClick={() => {
                    if (onAddField) {
                      addItemRef?.current?.value && onAddField(addItemRef?.current?.value);
                    }
                  }}
                >
                  Add
                </Button>
              </div>
            )}

            {newOption && (
              <>
                <div
                  onClick={() =>
                    navigate(newOption.target, {
                      state: {
                        openCreateModal: newOption.openCreateModal,
                        from: newOption.from
                      }
                    })
                  }
                  data-testid="new-dropdown-option"
                  className={`dropdown-item flex gap-2 items-center`}
                >
                  <Plus size={INPUT_ICON_SIZE} /> {newOption.placeHolder}
                </div>
                <hr className="h-[2px] border-gray-400" />
              </>
            )}

            <div
              className="overflow-y-auto max-h-[180px] dropdown-scroll-container"
              style={{
                touchAction: "pan-y",
                WebkitOverflowScrolling: "touch",
                overscrollBehavior: "contain"
              }}
              onWheel={(e) => {
                e.stopPropagation();
              }}
              onTouchStart={(e) => {
                e.stopPropagation();
              }}
              onTouchMove={(e) => {
                e.stopPropagation();
              }}
            >
              {optionsLoading ? (
                <div className="dropdown-item center">
                  <CircularProgress size={20} />
                </div>
              ) : getOptions().length === 0 ? (
                typeof emptyOption === "string" ? (
                  <div className="dropdown-item center">{emptyOption}</div>
                ) : (
                  emptyOption
                )
              ) : (
                getOptions().map((option) => {
                  const key = option.value || option[getOptionLabel] || option;

                  return (
                    <div
                      data-testid={`${getOptionLabel ? option[getOptionLabel] : option}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (getOptionDisabled && getOptionDisabled(option)) return;
                        onItemClick(option);
                      }}
                      key={key}
                      className={clsx(
                        "dropdown-item z-30 my-0.5 ",
                        getOptionDisabled &&
                          getOptionDisabled(option) &&
                          "!text-gray-400 hover:!bg-transparent",
                        isSelected(option) && "bg-brandColor text-black",
                        optionItemClassName
                      )}
                    >
                      {getOptionLabel ? option[getOptionLabel] : option}
                      <Check
                        size={INPUT_ICON_SIZE}
                        className={cn(
                          "ml-4 h-4 w-4",
                          isSelected(option) ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </div>
                  );
                })
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
      {helperText && (
        <p className={clsx("helper-text", error && "input-helper-text-error")}>{helperText}</p>
      )}
      {description && <p className="helper-text">{description}</p>}
    </div>
  );
};

export default Dropdown;
