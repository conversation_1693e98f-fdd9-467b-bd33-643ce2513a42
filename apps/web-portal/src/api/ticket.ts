import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { NEW_INCIDENT_URL } from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi, fetchImage } from "./_helpers";

export async function incidentCategoryList({page = 1,limit = DEFAULT_PAGE_COUNT ,search = ""}) {
  const resp = await fetchApi(`/incident-categories/list?${getUrlParams({page,limit,search})}`, {}, NEW_INCIDENT_URL);
  return resp.json();
}

export async function createIncidentCategory(body) {
  const fetchResponse = await fetchApi(
    "/incident-categories",
    {
      method: "POST",
      body
    },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
}

export async function deleteIncidentCategory({ id }) {
  const fetchResponse = await fetchApi(
    `/incident-categories/${id}`,
    {
      method: "DELETE"
    },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
}
export async function updateIncidentCategory({ id, name, description }) {
  const fetchResponse = await fetchApi(
    `/incident-categories/${id}`,
    {
      method: "PUT",
      body: { name, description }
    },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
}

export async function fetchTicketList({ page = 1, limit = DEFAULT_PAGE_COUNT, search = "" }) {
  const resp = await fetchApi(
    `/incidents/list?${getUrlParams({ page, limit, search })}`,
    {},
    NEW_INCIDENT_URL
  );
  return resp.json();
}

export async function fetchTicketDetail(ticketId) {
  const resp = await fetchApi(`/incidents/${ticketId}`, {}, NEW_INCIDENT_URL);
  return resp.json();
}

export async function createIncidentTicket(body) {
  const fetchResponse = await fetchApi("/incidents", { method: "POST", body }, NEW_INCIDENT_URL);
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res;
  }

  throw new Error(res.message);
}

export async function updateIncidentTicket({ id, payload }) {
  const fetchResponse = await fetchApi(
    `/incidents/${id}`,
    { method: "PUT", body: payload },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res;
  }

  throw new Error(res.message);
}

export async function deleteIncidentTicket({ id }) {
  const fetchResponse = await fetchApi(
    `/incidents/${id}`,
    {
      method: "DELETE"
    },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
}

export async function uploadTicketAttachment(body) {
  const fetchResponse = await fetchImage(
    `/file-upload`,
    { method: "POST", body },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res;
  }

  throw new Error(res.message);
}

export async function getTicketComment({ ticketId }) {
  const resp = await fetchApi(`/incident-comments/${ticketId}`, {}, NEW_INCIDENT_URL);
  return resp.json();
}

export async function addTicketComment(body) {
  const fetchResponse = await fetchApi(
    `/incident-comments`,
    {
      method: "POST",
      body
    },
    NEW_INCIDENT_URL
  );
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res;
  }

  throw new Error(res.message);
}
