import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { IOT_MESSAGE, IOT_URL, OTA_URL, STATS_URL, THING_URL } from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi, fetchImage, getTokens } from "./_helpers";

export async function fetchThingsCount({ productName }) {
  const resp = await fetchApi(
    `/stats/count/status?${getUrlParams({ productName })}`,
    {},
    STATS_URL
  );
  return resp.json();
}

export async function fetchThings({
  filter,
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
  productName,
  searchQuery = "",
  thingGroup,
  gatewayName
}) {
  // const resp = await fetchApi(
  //   `/things/all?page=${page}&limit=${limit}${
  //     searchQuery && "&search=" + searchQuery
  //   }${filter ? "&status=" + filter : ""}`,
  //   {},
  //   THING_URL
  // );
  const resp = await fetchApi(
    `/things/all?${getUrlParams({
      page,
      limit,
      search: searchQuery,
      status: filter,
      productName,
      gatewayName,
      thingGroup
    })}`,
    {},
    THING_URL
  );
  return resp.json();
}

export async function fetchThingShadow(thingName) {
  const resp = await fetchApi(`/thing-ops/shadow?deviceIds[0]=${thingName}`, {}, IOT_MESSAGE);
  return resp.json();
}

export async function fetchAvailableOtas({
  page = 1,
  searchQuery = "",
  limit = DEFAULT_PAGE_COUNT,
  productName = ""
}) {
  const resp = await fetchApi(
    `/ota/doc?${getUrlParams({
      page,
      limit,
      search: searchQuery,
      productName
    })}`,

    {},
    OTA_URL
  );
  return resp.json();
}

export async function fetchThingAuth({ page = 1, limit = DEFAULT_PAGE_COUNT, search }) {
  const resp = await fetchApi(
    `/thingsAuth/list?${getUrlParams({ page, limit, search })}`,
    {},
    IOT_URL
  );
  return resp.json();
}

export async function fetchInventoryThings({
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
  searchQuery = "",
  productName,
  filter
}) {
  let isRegistered = null;
  if (filter) {
    isRegistered = filter === "registered";
  }
  const resp = await fetchApi(
    `/inventory?${getUrlParams({
      page,
      limit,
      search: searchQuery,
      productName,
      registered: isRegistered
    })}`,
    {},
    THING_URL
  );
  return resp.json();
}

export async function fetchInventoryThingDetail(thingName) {
  const resp = await fetchApi(`/inventory/${thingName}`, {}, THING_URL);
  return resp.json();
}

export async function fetchThingListeners() {
  const resp = await fetchApi(`/listners`, {}, IOT_URL);
  return resp.json();
}

// =========================== NON-FETCH REQUESTS =========================== //

export async function createInventory(body) {
  const resp = await fetchApi("/inventory", { method: "POST", body }, THING_URL);
  return resp.json();
}

export async function createGatewayCertificate(body) {
  const resp = await fetchApi(
    "/tls/gateway/certificate/issuecert",
    { method: "POST", body },
    IOT_URL
  );
  return resp.json();
}

export async function createThingAuth(body, type) {
  const resp = await fetchApi(
    `/thingsAuth?${getUrlParams({
      type
    })}`,
    { method: "POST", body },
    IOT_URL
  );
  return resp.json();
}

export async function deleteThingAuth({ thingName }) {
  const resp = await fetchApi(
    `/thingsAuth?thingName=${thingName}`,
    {
      method: "DELETE"
    },
    IOT_URL
  );
  const data = await resp.json();
  if (!resp.ok) {
    throw new Error(data.message);
  }
  return data;
}

export async function createDefaultThing(body, type, isGeo = false) {
  const resp = await fetchApi(
    `/things?type=${type}&geo=${isGeo}`,
    { method: "POST", body },
    THING_URL
  );
  return resp.json();
}

export async function uploadBulkThings({ file }) {
  const resp = await fetchApi(
    `/things/bulk`,
    {
      method: "POST",
      body: file
    },
    THING_URL
  );
  return resp.json();
}

export async function createNewDevice(body) {
  const resp = await fetchApi("/add/inventory/register", { method: "POST", body }, THING_URL);
  return resp.json();
}

export async function updateStandardThing({ attributes, thingName, type }) {
  const resp = await fetchApi(
    `/things/${thingName}?type=${type}`,
    {
      method: "PUT",
      body: attributes
    },
    THING_URL
  );
  return resp.json();
}

export async function executeOtaUpdate({ thingName, version }) {
  const resp = await fetchApi(
    `/ota/execute`,
    { method: "POST", body: { thingName, version } },
    IOT_URL
  );
  return resp.json();
}
export async function disableOtaDoc({ uniqueId, payload }) {
  const resp = await fetchApi(
    `/ota/doc/update-ota/${uniqueId}`,
    { method: "POST", body: payload },
    OTA_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function deleteStandardThing({ thingName, deleteType, deleteReason, type }) {
  const resp = await fetchApi(
    `/things/${thingName}?${getUrlParams({ deleteType, deleteReason, type })}`,
    { method: "DELETE" },
    THING_URL
  );
  return resp.json();
}

export async function deleteMultiThing(body) {
  const resp = await fetchApi(`/things/bulk`, { method: "DELETE", body }, THING_URL);
  return resp.json();
}

export async function updateThingListeners({ type, status }) {
  const resp = await fetchApi(
    `/listners?type=${type}&status=${status}`,
    { method: "PUT" },
    IOT_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateThingShadow(body) {
  const { thingName, payload } = body;
  const resp = await fetchApi(
    `/thing-ops/shadow/${thingName}`,
    {
      method: "PUT",
      body: payload
    },
    IOT_MESSAGE
  );
  return resp.json();
}

export async function deleteThingShadow(thing_name) {
  const resp = await fetchApi(
    "/iot/delete/thingShadow",
    {
      method: "DELETE",
      body: { thing_name }
    },
    IOT_URL
  );
  return resp.json();
}

export async function deleteOtaJob(uniqueId) {
  const resp = await fetchApi(`/ota/doc/${uniqueId}`, { method: "DELETE" }, OTA_URL);
  return resp.json();
}

export async function rebootDevice(deviceId) {
  const resp = await fetchApi(
    `/thing-ops/command/reboot`,
    {
      method: "POST",
      body: { thingName: deviceId }
    },
    IOT_MESSAGE
  );
  return resp.json();
}

export async function updateInventoryThing({ body, thingName }) {
  const resp = await fetchApi(
    `/inventory/${thingName}`,
    {
      method: "PUT",
      body
    },
    THING_URL
  );
  return resp.json();
}

export async function uploadThingImage(thingName, body) {
  const resp = await fetchImage(
    `/things/upload-image?thingName=${thingName}`,
    { method: "POST", body },
    THING_URL
  );
  return resp.json();
}
