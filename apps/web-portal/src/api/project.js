import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { PRODUCT_URL } from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi } from "./_helpers";

export async function fetchProjects({ page = 1, limit = DEFAULT_PAGE_COUNT, search = "" }) {
  const resp = await fetchApi(
    `/products?${getUrlParams({ page, limit, search })}`,
    {},
    PRODUCT_URL
  );
  return resp.json();
}

// =========================== NON-FETCH REQUESTS =========================== //

export async function createProject(body) {
  const resp = await fetchApi(
    "/product-templates",
    {
      method: "POST",
      body
    },
    PRODUCT_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateProject(body) {
  const resp = await fetchApi(
    `/product-templates`,
    {
      method: "PATCH",
      body
    },
    PRODUCT_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function deleteProject(projectName) {
  const resp = await fetchApi(
    `/product-templates/${projectName}`,
    { method: "DELETE" },
    PRODUCT_URL
  );

  return resp.json();
}

export async function createRule({ select }) {
  const resp = await fetchApi("/users/create-rule", {
    method: "POST",
    body: { select }
  });
  return resp.json();
}

export async function deleteRule({ select }) {
  const resp = await fetchApi("/users/delete-rule", {
    method: "DELETE",
    body: { select }
  });
  return resp.json();
}
