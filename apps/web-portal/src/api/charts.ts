import { STATS_URL } from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi } from "./_helpers";

export async function fetchCharts({
  type = "messages",
  duration
}: {
  type: string;
  duration: string;
}): Promise<any> {
  const resp = await fetchApi(
    `/stats/graph/thing/all/${type}?${getUrlParams({
      duration
    })}`,
    {},
    STATS_URL
  );
  return resp.json();
}

export async function fetchMessageCharts({
  duration = "",
  tenant
}: {
  duration: string;
  tenant?: string;
}): Promise<any> {
  const resp = await fetchApi(
    `/stats/metric/broker-messages?${getUrlParams({
      duration,
      tenant
    })}`,
    {},
    STATS_URL
  );
  return resp.json();
}
