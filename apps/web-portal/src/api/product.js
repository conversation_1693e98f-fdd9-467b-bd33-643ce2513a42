import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import {
  APP_URL,
  IOT_URL,
  PRODUCT_URL,
  SIMULATOR_URL,
  STATS_URL,
  THING_URL,
} from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi, fetchImage, getTokens } from "./_helpers";

export const fetchProducts = async ({ next = 0 }) => {
  const resp = await fetchApi(
    `/projects?${next ? "next=" + next : ""}`,
    {},
    PRODUCT_URL,
  );
  return resp.json();
};

export const fetchSimulatorTemplate = async () => {
  const resp = await fetchApi(
    `/simulator/availableSimulators`,
    {},
    SIMULATOR_URL,
  );
  return resp.json();
};

export const fetchSimulatorTemplateDetails = async ({ queryKey }) => {
  const [_, simulatorId] = queryKey;
  
  const resp = await fetchApi(
    `/simulator/availableSimulators/${simulatorId}`,
    {},
    SIMULATOR_URL,
  );
  return resp.json();
};

export const fetchProductDetials = async (productName) => {
  const resp = await fetchApi(`/products/${productName}`, {}, PRODUCT_URL);
  return resp.json();
};

export const fetchCustomVariables = async (productName) => {
  const resp = await fetchApi(
    `/products/settings/${productName}`,
    {},
    PRODUCT_URL,
  );
  return resp.json();
};

export const fetchProductVariables = async ({ id }) => {
  const resp = await fetchApi(`/product-templates/${id} `, {}, PRODUCT_URL);

  return resp.json();
};

export const fetchProductDetails = async ({ productName }) => {
  const resp = await fetchApi(
    `/deviceManagement?deviceName=${productName}`,
    {},
    IOT_URL,
  );
  return resp.json();
};

export const fetchProductDevices = async ({ partner, productName }) => {
  const resp = await fetchApi(
    `/deviceInventory/mongo/?tenant=${partner}&productName=${productName}`,
    {},
    IOT_URL,
  );
  return resp.json();
};

export const fetchProductThings = async ({
  filter,
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
  productName,
  searchQuery = "",
}) => {
  const resp = await fetchApi(
    `/things/${productName}?${getUrlParams({
      page,
      limit,
      status: filter,
      search: searchQuery,
    })}`,
    {},
    THING_URL,
  );
  return resp.json();
};

export const fetchProductDeviceCount = async ({ productName }) => {
  const resp = await fetchApi(`/count/things/${productName}`, {}, STATS_URL);
  return resp.json();
};

export const fetchAppMembers = async () => {
  const resp = await fetchApi(`/members/app`, {}, APP_URL);
  return resp.json();
};
// =========================== NON-FETCH REQUESTS =========================== //

export const createGateway = async (body) => {
  const resp = await fetchApi(
    "/gateways",
    { method: "POST", body },
    PRODUCT_URL,
  );

  return resp.json();
};

export const updateGateway = async ({ gatewayName, body }) => {
  const resp = await fetchApi(
    `/gateways/${gatewayName}`,
    { method: "PUT", body },
    PRODUCT_URL,
  );

  return resp.json();
};

export const deleteGateway = async (gatewayName) => {
  const resp = await fetchApi(
    `/gateways/${gatewayName}`,
    { method: "DELETE" },
    PRODUCT_URL,
  );

  return resp.json();
};

export const createTemplate = async (body) => {
  const resp = await fetchApi(
    "/policy/template",
    { method: "POST", body },
    IOT_URL,
  );

  return resp.json();
};

export const deleteTemplate = async (templateName) => {
  const resp = await fetchApi(
    `/policy/template/${templateName}`,
    { method: "DELETE" },
    IOT_URL,
  );

  return resp.json();
};

export const updateTemplate = async ({ templateName, template }) => {
  const resp = await fetchApi(
    `/policy/template/${templateName}`,
    { method: "PUT", body: { template } },
    IOT_URL,
  );

  return resp.json();
};

export async function createSimulatorTemplate(body) {
  const resp = await fetchApi(
    "/simulator/msp",
    {
      method: "POST",
      body,
    },
    SIMULATOR_URL,
  );

  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export const createProduct = async (body, isGeo = false) => {
  const resp = await fetchApi(
    `/products?geo=${isGeo}`,
    { method: "POST", body },
    PRODUCT_URL,
  );
  return resp.json();
};

export const createSimulatedProduct = async (body) => {
  const { defaultTemplateId, featureType, ...payload } = body;
  const resp = await fetchApi(
    `/simulator?${getUrlParams({
      defaultTemplateId,
      featureType,
    })}`,
    { method: "POST", body: payload },
    SIMULATOR_URL,
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};
export const createProductTopic = async (productName, body) => {
  const resp = await fetchApi(
    `/products/topic?productName=${productName}`,
    { method: "POST", body },
    PRODUCT_URL,
  );
  return resp.json();
};

export const updateProductTopic = async (productName, body) => {
  const resp = await fetchApi(
    `/products/topic/${productName}`,
    { method: "PATCH", body },
    PRODUCT_URL,
  );
  return resp.json();
};

export const updateProductData = async ({ productName, body }) => {
  const resp = await fetchApi(
    `/products/${productName}`,
    { method: "PATCH", body },
    PRODUCT_URL,
  );
  if (resp.ok) {
    return resp.json();
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export async function uploadProductImage(productName, body) {
  const resp = await fetchImage(
    `/products/upload-image?productName=${productName}`,
    { method: "POST", body },
    PRODUCT_URL,
  );
  return resp.json();
}

export const createProductFields = async ({
  appStyle,
  thingType,
  productName,
  capabilities,
  category,
  managedBy,
}) => {
  const resp = await fetchApi(
    "/deviceManagement/devices ",
    {
      method: "POST",
      body: {
        appStyle,
        thingType,
        productName,
        capabilities,
        category,
        managedBy,
      },
    },
    IOT_URL,
  );
  return resp.json();
};

export const updateProductSettings = async ({
  projectId,
  productName,
  partner,
  productSettings,
}) => {
  const resp = await fetchApi(
    `/products/settings/${productName}`,
    { method: "PATCH", body: { projectId, partner, productSettings } },
    PRODUCT_URL,
  );
  return resp.json();
};

export const createProductSettings = async ({
  projectId,
  productName,
  custom_settings,
}) => {
  const resp = await fetchApi(
    "/products/settings/",
    { method: "POST", body: { projectId, productName, custom_settings } },
    PRODUCT_URL,
  );
  return resp.json();
};

export const deleteProduct = async (productName, productType) => {
  const resp = await fetchApi(
    `/products/${productName}?productType=${productType}`,
    { method: "DELETE" },
    PRODUCT_URL,
  );
  return resp.json();
};

export const deleteProductSettings = async (productName) => {
  const resp = await fetchApi(
    `/products/settings/${productName}`,
    { method: "DELETE" },
    PRODUCT_URL,
  );
  return resp.json();
};

export const createProductBuilds = async (body) => {
  const resp = await fetchApi(
    "/products/builds",
    { method: "POST", body },
    PRODUCT_URL,
  );
  return resp.json();
};

export const uploadDeviceInfo = async ({ productName, file }) => {
  const resp = await fetch(`${THING_URL}/inventory/bulk/${productName}`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${getTokens()?.accessToken}`,
    },
    body: file,
  });
  return resp;
};
export const uploadDeviceInfoPartner = async ({
  productName,
  licenceType,
  file,
}) => {
  const resp = await fetch(
    `${IOT_URL}/deviceManagement/partner/${productName}?licenseType=${licenceType}`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${getTokens()?.accessToken}`,
      },
      body: file,
    },
  );
  return resp;
};
