import { ThingReplayResp } from "@onetypes/ontrack";
import { STATS_URL } from ".";
import { DashboardGraphItem, SavedDashboards } from "../..";
import { updateDashboardGraph } from "../features/dashboardBuilderSlice";
import { updateExpandedGraph } from "../features/expandedGraphSlice";
import { store } from "../store";
import { fetchApi } from "./_helpers";
import { queryClient } from "@utils/queryClient";

/**
 * @param {string} filterBy
 * @param {string} dataIndex
 * @param {string} searchText
 * @returns {Promise<object[]>}
 */
export const fetchFilterValues = async (
  filterBy: string,
  dataIndex: string,
  searchText: string,
  extra?: Record<string, string>
): Promise<object[]> => {
  try {
    let url =
      filterBy === "thingName" ||
      filterBy === "productName" ||
      dataIndex.includes("connection-status")
        ? `/dashboard/distinct/${filterBy}`
        : `/dashboard/elastic/distinct/${filterBy}/${dataIndex}`;

    if (searchText) {
      url = url.concat(`?search=${searchText}`);
    }
    if (extra?.productName) {
      if (searchText) {
        url = url.concat(`&productName=${extra.productName}`);
      } else {
        url = url.concat(`?productName=${extra.productName}`);
      }
    }

    const fetchResponse = await fetchApi(url, {}, STATS_URL);

    const res = await fetchResponse.json();
    if (fetchResponse.ok) {
      return res.data.uniqueValues;
    }
    throw new Error(res.message);
  } catch (error) {
    console.log("failed to fetch filter values", error.response?.data || error?.message);
    return [];
  }
};

/**
 * @param {string | string[]} productName
 * @param {string | string[]} mode
 * @param {string} duration
 * @param {string[] | string} [thingName]
 * @returns {Promise<object[]>}
 */
export const fetchMapRoute = async (
  productName: string | string[],
  mode: string | string[],
  duration: string,
  thingName: string[] | string
): Promise<object[]> => {
  let url = `/stats/map/route/${productName}?duration=${duration}`;
  if (thingName) {
    url = url.concat(`&${JSON.stringify(thingName)}`);
  }

  url = url.concat(`&mode=${mode || "Position"}`);

  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.location;
  }
  throw new Error(res.message);
};

export const addDummyDataToDashboard = async () => {
  const fetchResponse = await fetchApi(`/dashboard/add-data`, {}, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.devices;
  }
  throw new Error(res.message);
};

/**
 * @param {string} filterBy
 * @param {boolean} timeseries
 * @returns {Promise<object[]>}
 */
export const fetchAllEventTypes = async (
  filterBy: string,
  timeseries: boolean
): Promise<object[]> => {
  let url = `/dashboard/elastic/distinct/${filterBy}`;
  if (timeseries) {
    url = url.concat("?type=timeseries");
  }
  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.events;
  }
  throw new Error(res.message);
};

/**
 * @param {string[]} productName
 * @returns {Promise<object[]>}
 */
export const fetchAllDevicesByProductName = async (productName: string[]): Promise<object[]> => {
  const fetchResponse = await fetchApi(
    `/dashboard/productName/devices?productName=${JSON.stringify(productName)}`,
    {},
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.devices;
  }
  throw new Error(res.message);
};

export const fetchGaugeChartData = async (
  gaugeMapping: { productName: string; thingName: string[]; field: string; operations: string },
  dataIndex: string,
  duration: string,
  allowZeros: boolean
): Promise<Record<string, number>> => {
  const filterBy = JSON.stringify(gaugeMapping);
  const fetchResponse = await fetchApi(
    `/dashboard/gauge?filterBy=${filterBy}&index=${dataIndex}&duration=${duration}&ignoreZeroes=${!allowZeros}`,
    {},
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data;
  }
  throw new Error(res.message);
};

export const fetchPositionDataParsedAsTimeSeries = async ({
  thingName,
  startDate,
  endDate,
  key
}: {
  thingName: string;
  startDate: string;
  endDate: string;
  key: string;
}) => {
  try {
    const fetchResponse = await fetchApi(
      `/stats/ts/position/${key}?from=${startDate}&to=${endDate}&thingName=${thingName}`,
      {},
      STATS_URL
    );
    const data = await fetchResponse.json();
    if (!fetchResponse.ok) {
      throw new Error(data.message);
    }

    return data.data as {
      labels: string[];
      positionData: number[];
      aggregations: any;
    };
  } catch (error) {
    return {
      labels: [],
      positionData: [],
      aggregations: {}
    };
  }
};

/**
 * @deprecated war returning in correct data This method will be removed in future releases.
 */
export const fetchGeoPositionWidgetData = async ({
  thingName,
  productName,
  duration
}: {
  thingName: string;
  productName: string;
  duration: string;
}) => {
  const fetchResponse = await fetchApi(
    `/dashboard/position?productName=${productName}&thingName=${thingName}&duration=${duration}`,
    {},
    STATS_URL
  );
  const res: ThingReplayResp = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const fetchAllShadowAvailableDevices = async ({
  productName,
  required
}: {
  productName?: string;
  required?: string;
}): Promise<Record<string, string[]>> => {
  let url = `/dashboard/productName/distinct/${productName}`;

  if (required) {
    url = url.concat(`&required=${required}`);
  }

  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.productNames;
  }

  return {};
};

/**
 * @returns {Promise<object>}
 */
export const fetchAvailableIndexes = async (): Promise<Record<string, Record<string, string>>> => {
  try {
    const fetchResponse = await fetchApi(`/dashboard/available-indexes`, {}, STATS_URL);
    const res = await fetchResponse.json();

    return res.data.availableDataPoints;
  } catch (error) {
    console.log(error.response?.data || error.message);
    return [];
  }
};

export const fetchUserDashboardList = async ({ signal }: { signal: any }) => {
  try {
    const fetchResponse = await fetchApi(`/dashboard/list`, { signal }, STATS_URL);
    const res = await fetchResponse.json();
    const dashboardViewId = store.getState().user.user?.dashboardViewId;
    const payload = { dashboardList: res.data.usersDashboardList, dashboardViewId };
    store.dispatch({
      type: "dashboardBuilder/setUserDashboardList",
      payload
    });
    return res.data.usersDashboardList as {
      id: number;
      title: number;
      description: string;
    }[];
  } catch (error) {
    console.log(error);
    return [];
  }
};

export const fetchUserDashboardById = async (id: string, signal: any): Promise<SavedDashboards> => {
  const dashboardData = queryClient.getQueryData(["dashboard", id]);
  const areaDashboardData = queryClient.getQueryData(["area-dashboard", id]);

  if (dashboardData) {
    store.dispatch({
      type: "dashboardBuilder/setCurrentDashboard",
      payload: dashboardData
    });
  } else if (areaDashboardData) {
    store.dispatch({
      type: "dashboardBuilder/setCurrentDashboard",
      payload: areaDashboardData
    });
  }

  const fetchResponse = await fetchApi(`/dashboard/${id}`, { signal }, STATS_URL);

  const res = await fetchResponse.json();

  if (!fetchResponse.ok || !res.data.usersDashboard) {
    store.dispatch({
      type: "dashboardBuilder/setPrimaryDashboardId",
      payload: ""
    });
    throw new Error("dashboard does not exists");
  }

  const payload = res.data.usersDashboard;

  store.dispatch({
    type: "dashboardBuilder/setCurrentDashboard",
    payload
  });

  return payload;
};

/**
 *
 * @param {string} id
 * @returns {Promise<Response>}
 */
export const deleteUserDashboard = async (id: string): Promise<Response> => {
  const fetchResponse = await fetchApi(`/dashboard/${id}`, { method: "DELETE" }, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res;
  }

  throw new Error(res.message);
};

/**
 * @param {object} body
 * @returns {Promise<Response>}
 */
export const cloneUserDashboard = async (body: object): Promise<Response> => {
  const fetchResponse = await fetchApi(`/dashboard/clone`, { method: "PUT", body }, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.clonedDashboard.id;
  }

  throw new Error(res.message);
};

/**
 * @param {import("../..").SavedDashboards} body
 * @returns {Promise<object>}
 */
export const createUserDashboard = async (
  body: import("../..").SavedDashboards
): Promise<object> => {
  const fetchResponse = await fetchApi(`/dashboard`, { method: "POST", body }, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
};

/**
 * @param {string} dashboardId
 * @param {string} graphId
 * @param {Partial<import("../..").DashboardGraphItem>} body
 * @returns {Promise<object>}
 */
export const updateUserDashboardGraph = async (
  dashboardId: string,
  graphId: string,
  body: Partial<import("../..").DashboardGraphItem>
): Promise<object> => {
  const fetchResponse = await fetchApi(
    `/dashboard/${dashboardId}/${graphId}`,
    { method: "POST", body },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data;
  }

  throw new Error(res.message);
};

/**
 * @param {object} id
 * @returns {Promise<object>}
 */

export const updateUserDashboard = async ({
  dashboardId,
  newDashboard
}: object): Promise<object> => {
  const fetchResponse = await fetchApi(
    `/dashboard/edit/${dashboardId}`,
    { method: "PUT", body: newDashboard },
    STATS_URL
  );

  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res;
  }

  throw new Error(res.message);
};

/**
 * @param {string} url
 * @param {import("../..").DashboardGraphItem['filters']} _filters
 * @param {string} dataKey
 * @returns {string}
 */
const getDeepFilterUrl = (
  url: string,
  _filters: import("../..").DashboardGraphItem["filters"],
  dataKey: string
): string => {
  const filters =
    dataKey === "payload" ? _filters : _filters.filter((item) => !item.ignoreIfNotPayload);

  if (!filters) {
    return url;
  }
  const appliedFilters = filters
    .filter((filter) => filter.enabled)
    .map((item) => ({ field: item.field, value: item.value, operation: item.operation }));

  url = url.concat(`&filterBy=${JSON.stringify(appliedFilters)}`);

  return url;
};

export const fetchTableData = async (
  indexName: string,
  duration: string,
  filters: import("../..").DashboardGraphItem["filters"] = []
): Promise<{
  data: object[];
  total: number;
}> => {
  let url = `/dashboard/table/${indexName}?duration=${duration}`;

  url = getDeepFilterUrl(url, filters, "");

  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  if (!fetchResponse.ok) {
    return {};
  }
  const res = await fetchResponse.json();
  return res.data;
};

/**
 * @param {string} tenant
 * @param {string} productName
 * @param {string} thingName
 * @param {string} duration
 * @param {string} field
 * @param {boolean} allowZeros
 * @returns {Promise<object>}
 */
export const getThingWidgetData = async (
  tenant: string,
  productName: string,
  thingName: string,
  duration: string,
  field: string,
  allowZeros: boolean
): Promise<object> => {
  /**
   * @type {import("../..").DashboardGraphItem['filters']}
   */
  const filters: import("../..").DashboardGraphItem["filters"] = [
    { value: [productName], field: "productName", operation: "is", enabled: true, id: "1" },
    { value: [thingName], field: "thingName", operation: "is", enabled: true, id: "2" }
  ];
  let url = `/dashboard/${tenant}-time-series/payload?duration=${duration}&field=${field}&ignoreZeroes=${!allowZeros}`;

  url = getDeepFilterUrl(url, filters, "");
  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  if (!fetchResponse.ok) {
    return {
      labels: [],
      datapoints: [],
      count: 0
    };
  }
  const res = await fetchResponse.json();
  const response = res.data;
  return response;
};

export const fetchGraphPlotData = async (
  indexName: string,
  dataKey: string,
  duration: string,
  isPieChart: boolean,
  // eslint-disable-next-line default-param-last
  filters: DashboardGraphItem["filters"] = [],
  stackedOrExpanded?: { stacked?: boolean; expanded?: boolean; isBar: boolean; id: string },
  modal?: boolean,
  allowZeros?: boolean
): Promise<{
  labels: string[];
  count: number;
  elaboratedDataKeys: string[];
  elaboratedDataPoint: Record<string, number>[];
  aggregations: Record<string, number>;
  datapoints: number[];
}> => {
  try {
    let url = isPieChart
      ? `/dashboard/pie/${indexName}/${dataKey}?duration=${duration}`
      : `/dashboard/${indexName}/${dataKey}?duration=${duration}&ignoreZeroes=${!allowZeros}`;

    const thingFilter = filters.find((filter) => filter.field === "thingName");

    if (thingFilter && thingFilter.value?.length > 1 && !isPieChart) {
      const otherFilters = filters.filter((filter) => filter.field !== "thingName");

      const promises = thingFilter.value.map((name) => {
        const newDeepFilter: DashboardGraphItem["filters"] = [
          ...otherFilters,
          { value: [name], field: "thingName", operation: "is", enabled: true, id: "2" }
        ];

        return fetchGraphPlotData(indexName, dataKey, duration, isPieChart, newDeepFilter);
      });

      const responses = await Promise.all(promises);

      const labels = responses[0].labels;
      const datapoints: number[] = [];
      const elaboratedDataKeys = [];
      let count = 0;
      const aggregations = {};
      const elaboratedDataPoint = Array(responses[0].elaboratedDataPoint.length).fill({});

      responses.forEach((res, i) => {
        const thing = thingFilter.value[i];

        count += res.count;
        const aggregationArray = Object.keys(res.aggregations || {});
        aggregationArray.forEach((key) => {
          aggregations[`${thing}-${key}`] = res.aggregations[key];
        });
        res.elaboratedDataPoint.forEach((dataPoint, index) => {
          const items = Object.keys(dataPoint).reduce((acc, key) => {
            acc[`${thing}-${key}`] = dataPoint[key];
            return acc;
          }, {});
          elaboratedDataPoint[index] = { ...elaboratedDataPoint[index], ...items };
        });
        res.elaboratedDataKeys.forEach((item) => elaboratedDataKeys.push(`${thing}-${item}`));
      });

      return { labels, count, elaboratedDataKeys, elaboratedDataPoint, aggregations, datapoints };
    }

    url = getDeepFilterUrl(url, filters, dataKey);

    const fetchResponse = await fetchApi(url, {}, STATS_URL);

    if (!fetchResponse.ok) {
      return {
        labels: [] as string[],
        datapoints: [] as number[],
        count: 0,
        elaboratedDataKeys: [] as string[],
        elaboratedDataPoint: [] as Record<string, number>[],
        aggregations: {} as Record<string, number>
      };
    }
    const res = await fetchResponse.json();

    const response = res.data;

    if (stackedOrExpanded) {
      const { stacked, expanded, isBar, id } = stackedOrExpanded;
      /**
       * @type {Record<string,boolean| undefined>}
       */
      const config: Record<string, boolean | undefined> = {
        stacked: undefined,
        expanded: undefined
      };
      if (response.elaboratedDataKeys?.length) {
        const barCount = response.labels.length * response.elaboratedDataKeys.length;

        if (isBar && barCount > 36 && stacked !== false) {
          config.stacked = typeof stacked === "boolean" ? stacked : true;
        } else {
          config.expanded = typeof expanded === "boolean" ? expanded : true;
        }
      }

      if (response.aggregations) {
        const payload = {
          aggregation: response.aggregations,
          ...config
        };

        store.dispatch(
          modal ? updateExpandedGraph(payload) : updateDashboardGraph({ id, ...payload })
        );
      } else {
        const payload = {
          id,
          ...config
        };
        store.dispatch(modal ? updateExpandedGraph(config) : updateDashboardGraph(payload));
      }
    }
    return response;
  } catch (error) {
    console.log(error.message);
    throw new Error(error.message);
  }
};
