import { fetchApi } from "./_helpers";
import { STATS_URL } from ".";
import { colorAtI } from "../pages/MonitorPage/utils";

const range = [
  { id: "8233", start: "0", color: "#36a3ea" },
  { id: "1301", start: "100", color: "#fe6282" }
];

/**
 * @param {string} thingName
 * @param {string} productName
 * @returns {Promise<import("../..").ThingWidget[]>}
 */
export const fetchThingWidgets = async (thingName, productName) => {
  const fetchResponse = await fetchApi(
    `/stats/thing/widgets/${thingName}/${productName}`,
    {},
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return (
      res.data.map((item, i) => {
        const resItem = { ...item };
        if (!["min", "max", "avg", "sum"].includes(item.operation)) {
          resItem.operation = "latest";
        }
        if (!resItem.color) {
          resItem.color = colorAtI(i);
        }
        if (!resItem.range) {
          resItem.range = range;
        }

        return resItem;
      }) || []
    );
  }
  return [];
};

/**
 * @param {{thingName:string;productName:string;widgets:import("../..").ThingWidget[]}} thingName
 * @returns {Promise<object[]>}
 */
export const updateThingWidgets = async ({ thingName, widgets, productName }) => {
  const body = { widgets: widgets.filter((item) => !item.product) };

  if (productName) {
    body.productName = productName;
  }
  const fetchResponse = await fetchApi(
    `/stats/thing/widgets/${thingName}`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data.widgets || [];
  }
  throw new Error(res.message);
};
