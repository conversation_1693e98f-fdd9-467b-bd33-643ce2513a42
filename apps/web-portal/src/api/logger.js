import { fetchApi } from "./_helpers";
import { STATS_URL } from ".";

// const STATS_URL = "http://localhost:8019/api/stats";

export const getUniqueFilterOptions = async (field, search) => {
  let url = `/dashboard/elastic/distinct/${field}/-logs`;
  if (search) {
    url = url.concat(`?search=${search}`);
  }

  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res.data.uniqueValues || [];
  }
  throw new Error(res.message);
};

export const getLoggerGraph = async (duration, filter, type) => {
  let url = `/stats/logger/graph/?duration=${duration}&type=${type}`;

  if (filter) {
    url = url.concat(`&filter=${JSON.stringify(filter)}`);
  }

  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  const res = await fetchResponse.json();

  if (fetchResponse.ok) {
    return res.data;
  }
  throw new Error(res.message);
};

export const getLoggerTable = async (duration, filter, page, type) => {
  let url = `/stats/logger/table/?duration=${duration}&page=${page}&type=${type}`;

  if (filter) {
    url = url.concat(`&filter=${JSON.stringify(filter)}`);
  }

  const fetchResponse = await fetchApi(url, {}, STATS_URL);
  const res = await fetchResponse.json();
  if (fetchResponse.ok) {
    return res.data?.data;
  }
  throw new Error(res.message);
};
