import { getUrlParams } from "@utils/url";
import { PROCESSING_URL } from ".";
import { fetchApi } from "./_helpers";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";

// <-----NOTIFICATION ROUTES ------>

/**
 * @param {object} body
 * @returns {Promise<any>}
 */

export async function createNotificationTemplate(body) {
  const resp = await fetchApi(
    `/notification/templates`,
    {
      method: "POST",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}
/**
 * @param {string} productName
 * @param {object} body
 * @returns {Promise<any>}
 */

export async function updateNotificationTemplate(templateName, body) {
  const resp = await fetchApi(
    `/notification/templates/${templateName}`,
    {
      method: "PUT",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}
/**
 * @param {string} productName
 * @returns {Promise<any>}
 */

export async function deleteNotificationTemplate(templateName) {
  const resp = await fetchApi(
    `/notification/templates/${templateName}`,
    {
      method: "DELETE"
    },
    PROCESSING_URL
  );
  return resp.json();
}
// <-----CONTACT ROUTES ------>

/**
 * @param {object} body
 * @returns {Promise<any>}
 */

export async function createContact(body) {
  const resp = await fetchApi(
    `/contacts`,
    {
      method: "POST",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}

/**
 * @param {string} contactId
 * @param {object} body
 * @returns {Promise<any>}
 */

export async function updateContact(contactId, body) {
  const resp = await fetchApi(
    `/contacts/${contactId}`,
    {
      method: "PUT",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}

/**
 * @param {string} contactId
 * @returns {Promise<any>}
 */

export async function deleteContact(contactId) {
  const resp = await fetchApi(
    `/contacts/${contactId}`,
    {
      method: "DELETE"
    },
    PROCESSING_URL
  );
  return resp.json();
}

// <-----ASSETS ROUTES ------>

/**
 * @param {object} body
 * @returns {Promise<any>}
 */

export async function createEscalation(body) {
  const resp = await fetchApi(
    `/escalation-groups`,
    {
      method: "POST",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}
export async function updateEscalation(groupName, body) {
  const resp = await fetchApi(
    `/escalation-groups/${groupName}`,
    {
      method: "PUT",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}
/**
 * @param {object} body
 * @returns {Promise<any>}
 */

/**
 * @param {object} body
 * @returns {Promise<any>}
 */

export async function deleteEscalation(groupName) {
  const resp = await fetchApi(
    `/escalation-groups/${groupName}`,
    {
      method: "DELETE"
    },
    PROCESSING_URL
  );
  return resp.json();
}
