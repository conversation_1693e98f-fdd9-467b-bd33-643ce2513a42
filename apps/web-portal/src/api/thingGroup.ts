import { IOT_URL } from ".";
import { fetchApi } from "./_helpers";

// =========================== NON-FETCH REQUESTS =========================== //

export async function addStaticThingGroup(body) {
  const resp = await fetchApi(
    "/thingGroup/static",
    {
      method: "POST",
      body
    },
    IOT_URL
  );
  return resp.json();
}

export async function addStaticMembers({ things, thingGroupName }) {
  const resp = await fetchApi(
    `/thingGroup/static/addMember/${thingGroupName}`,
    {
      method: "POST",
      body: {
        things
      }
    },
    IOT_URL
  );
  return resp.json();
}

export async function updateStaticGroups({ body, thingGroupName }) {
  const resp = await fetchApi(
    `/thingGroup/static/${thingGroupName}`,
    {
      method: "PUT",
      body
    },
    IOT_URL
  );
  return resp.json();
}

export async function deleteStaticMembers({ deleteThings, thingGroupName }) {
  const resp = await fetchApi(
    `/thingGroup/static/removeMember/${thingGroupName}`,
    {
      method: "POST",
      body: {
        deleteThings
      }
    },
    IOT_URL
  );
  return resp.json();
}

export async function deleteStaticThingGroup(groupName) {
  const resp = await fetchApi(`/thingGroup/static/${groupName}`, { method: "DELETE" }, IOT_URL);
  return resp.json();
}

export async function addDynamicThingGroup(body) {
  const resp = await fetchApi(
    "/thingGroup/dynamic",
    {
      method: "POST",
      body
    },
    IOT_URL
  );
  return resp.json();
}

export async function deleteDynamicThingGroup(groupName) {
  const resp = await fetchApi(`/thingGroup/dynamic/${groupName}`, { method: "DELETE" }, IOT_URL);
  return resp.json();
}

export async function addDynamicMembers({ things, thingGroupName }) {
  const resp = await fetchApi(
    `/thingGroup/dynamic/addMember/${thingGroupName}`,
    {
      method: "POST",
      body: {
        things
      }
    },
    IOT_URL
  );
  return resp.json();
}

export async function deleteDynamicMembers({ deleteThings, thingGroupName }) {
  const resp = await fetchApi(
    `/thingGroup/dynamic/removeMember/${thingGroupName}`,
    {
      method: "POST",
      body: {
        deleteThings
      }
    },
    IOT_URL
  );
  return resp.json();
}

export async function updateDynamicGroups({ body, thingGroupName }) {
  const resp = await fetchApi(
    `/thingGroup/dynamic/${thingGroupName}`,
    {
      method: "PUT",
      body
    },
    IOT_URL
  );
  return resp.json();
}
