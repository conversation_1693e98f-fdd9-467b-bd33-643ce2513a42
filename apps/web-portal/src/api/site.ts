import { fetchApi } from "@frontend/shared/utils/httpUtils";
import { STATS_URL } from ".";
import { handleUploadVideo } from "@src/pages/DigitalTwin/Layout/CreateDigitalTwin";

export const deleteSite = async (id: number) => {
  const fetchResponse = await fetchApi(
    `/site/delete/${id}`,
    {
      method: "DELETE"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const createSite = async (body: {
  name: string;
  description: string;
  siteAreas: Record<string, any>[];
}) => {
  const imagePromises = body.siteAreas.map((area: any) =>
    fetch(area.imgURL)
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], `image.${blob.type.split("/")[1]}`, { type: blob.type });
        return file;
      })
  );

  const imageFiles = await Promise.all(imagePromises);

  const imageUrls = await Promise.all(imageFiles.map((file) => handleUploadVideo(file)));
  const fetchResponse = await fetchApi(
    `/site/create`,
    {
      method: "POST",
      body: {
        ...body,
        siteAreas: body.siteAreas.map((area, index) => ({
          ...area,
          imageUrl: imageUrls[index]?.data.fileUrl
        }))
      }
    },
    STATS_URL
  );

  const res = await fetchResponse.json();

  if (res.status === "Success") {
    return res.data.site;
  }
  throw new Error(res.message);
};

export const updateSite = async (body: {
  name: string;
  description: string;
  areaOrder?: number[];
  hideConnector?: boolean;
  orientation?: "grid" | "inline";
}) => {
  const fetchResponse = await fetchApi(
    `/site/update/${body.name}`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );

  const res = await fetchResponse.json();

  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const addNewAreaToSite = async (body: { area: Record<string, any>; siteId: number }) => {
  let imageUrl = body.area.imgURL;

  if (!imageUrl.startsWith("https://")) {
    const imageFile = await fetch(body.area.imgURL)
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], `image.${blob.type.split("/")[1]}`, { type: blob.type });
        return file;
      });

    imageUrl = await handleUploadVideo(imageFile);
    body.area.imageUrl = imageUrl.data.fileUrl;
  } else {
    body.area.imageUrl = imageUrl;
  }

  const fetchResponse = await fetchApi(
    `/site/area/new`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const removeAreaFromSite = async (body: { areaId: number }) => {
  const fetchResponse = await fetchApi(
    `/site/area/remove/${body.areaId}`,
    {
      method: "DELETE"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const updateSiteArea = async (body: { area: Record<string, any>; areaId: number }) => {
  let imageUrl = body.area.imgURL;

  if (!imageUrl.startsWith("https://")) {
    const imageFile = await fetch(body.area.imgURL)
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], `image.${blob.type.split("/")[1]}`, { type: blob.type });
        return file;
      });

    imageUrl = await handleUploadVideo(imageFile);
    body.area.imageUrl = imageUrl.data.fileUrl;
  } else {
    body.area.imageUrl = imageUrl;
  }

  const fetchResponse = await fetchApi(
    `/site/area/update/${body.areaId}`,
    {
      method: "PUT",
      body: body.area
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

// ASSETS
export const createAsset = async (body: {
  name: string;
  description: string;
  fields: Record<string, any>[];
  imgURL?: string;
}) => {
  const fetchResponse = await fetchApi(
    `/site/asset/create`,
    {
      method: "POST",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const updateAsset = async (body: {
  name: string;
  description: string;
  id: number;
  imgURL: string;
}) => {
  const imageUrl = body.imgURL;

  if (!imageUrl.startsWith("https://")) {
    const imageFile = await fetch(body.imgURL)
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], `image.${blob.type.split("/")[1]}`, { type: blob.type });
        return file;
      });

    const uploadedImage = await handleUploadVideo(imageFile);
    if (uploadedImage) {
      body.imgURL = uploadedImage?.data?.fileUrl;
    }
  }

  const fetchResponse = await fetchApi(
    `/site/asset/update/${body.id}`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const deleteAsset = async (id: number) => {
  const fetchResponse = await fetchApi(
    `/site/asset/delete/${id}`,
    {
      method: "DELETE"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const addAssetField = async (body: { assetId: number; fields: Record<string, any>[] }) => {
  const fetchResponse = await fetchApi(
    `/site/asset/field/new`,
    {
      method: "POST",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const removeAssetField = async (fieldId: number) => {
  const fetchResponse = await fetchApi(
    `/site/asset/field/remove/${fieldId}`,
    {
      method: "DELETE"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const updateAssetField = async (body: {
  field: string;
  fieldId: number;
  thingName: string;
  productName: string;
  color: string;
  unit: string;
  decimal: number;
  displayName: string;
  type?: "ts" | "shadow";
}) => {
  const fetchResponse = await fetchApi(
    `/site/asset/field/update/${body.fieldId}`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const createSiteDigitalTwin = async (body: {
  name: string;
  description: string;
  videoUrl: string;
  assetIds: number[];
  is3D: boolean;
}) => {
  const fetchResponse = await fetchApi(
    `/site/digital-twin/create`,
    {
      method: "POST",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();

  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const addDigitalTwinAsset = async (body: { assetId: number; digitalTwinId: number }) => {
  const fetchResponse = await fetchApi(
    `/site/digital-twin/asset/new`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const removeDigitalTwinAsset = async (body: { assetId: number; digitalTwinId: number }) => {
  const fetchResponse = await fetchApi(
    `/site/digital-twin/asset/remove/${body.digitalTwinId}/${body.assetId}`,
    {
      method: "DELETE"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const addWidgetToSite = async (body: {
  widget: {
    name: string;
    description: string;
    type: string;
    digitalTwinId?: number;
  };
  siteId: number;
}) => {
  const fetchResponse = await fetchApi(
    `/site/widget/new`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const removeWidgetFromSite = async (widgetId: number) => {
  const fetchResponse = await fetchApi(
    `/site/widget/remove/${widgetId}`,
    {
      method: "DELETE"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const updateSiteWidget = async (
  body: Partial<{
    name: string;
    description: string;
    type: string;
    digitalTwinId: number;
  }> & { id: number }
) => {
  const fetchResponse = await fetchApi(
    `/site/widget/update/${body.id}`,
    {
      method: "PUT",
      body
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

export const cloneSiteApi = async ({
  title,
  description,
  siteName
}: {
  title: string;
  description: string;
  siteName: string;
}) => {
  const fetchResponse = await fetchApi(
    `/site/clone/${siteName}`,
    {
      method: "POST",
      body: {
        name: title,
        description
      }
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};
export const cloneSiteArea = async (areaId: number) => {
  const fetchResponse = await fetchApi(
    `/site/area/clone/${areaId}`,
    {
      method: "POST"
    },
    STATS_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};
