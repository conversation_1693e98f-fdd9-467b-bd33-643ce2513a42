export const SECRET_KEY = import.meta.env.VITE_APP_SECRET_KEY;

/**
 * @type {'development'|'production'|'dev'|'alpha'}
 */
export const MODE = import.meta.env.VITE_APP_ENV;
// NEW URLS

export const BASE = import.meta.env.VITE_APP_API_URL;
export const API_URL = `${BASE}/em/api`;
// export const API_URL = `http://localhost:8001/api`;
export const IOT_URL = `${BASE}/fm/api`;
// export const IOT_URL = "http://localhost:8007/api";
export const PRODUCT_URL = `${BASE}/pm/api`;
// export const PRODUCT_URL = "http://localhost:8018/api";
export const THING_URL = `${BASE}/tm/api`;
// export const THING_URL = "http://localhost:8020/api";
export const OTA_URL = `${BASE}/ota/api`;
export const PKI_URL = `${BASE}/ocm/api`;
export const INCIDENT_URL = `${BASE}/inc/api`;
export const NEW_INCIDENT_URL = `${BASE}/inm/api`;
export const STATS_URL = `${BASE}/anl/api`;
// export const STATS_URL = "http://localhost:8019/api/stats";
export const COMM_URL = `${BASE}/int/api`;
// export const COMM_URL = "http://localhost:8003/api";
export const PROCESSING_URL = `${BASE}/nm/api`;
export const IOT_MESSAGE = `${BASE}/ahe/api`;
export const GEO_URL = `${BASE}/geo/api`;
// export const GEO_URL = `http://localhost:8026/api`;
export const SIMULATOR_URL = `${BASE}/sim/api`;
export const APP_URL = `${BASE}/ab/api`;
// export const APP_URL = `http://localhost:8022/api`;
// export const GEO_URL_WS = "http://localhost:8026";
// services-ind-01.oneiot.io
export const RAG_API = `${BASE}/ora`;
