import { COMM_URL, IOT_URL, PROCESSING_URL } from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi } from "./_helpers";

export async function createIotRule(args) {
  const resp = await fetchApi(
    "/brk/rules",
    {
      method: "POST",
      body: args
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function createN8nRule(args) {
  const resp = await fetchApi(
    "/brk/automation/workflow",
    {
      method: "POST",
      body: args
    },
    COMM_URL
  );

  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateN8nRule({ ruleId, body }: { ruleId: string; body: any }) {
  const resp = await fetchApi(
    `/brk/automation/workflow/${ruleId}`,
    {
      method: "PUT",
      body
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

// export async function createIotRule({ rawsql, actions, description }) {
//   const resp = await fetchApi(
//     "/rule",
//     {
//       method: "POST",
//       body: { rawsql, actions, description },
//     },
//     IOT_URL
//   )
//   return  resp.json()
// }

export async function updateIotRule({ ruleId, body }: { ruleId: string; body: any }) {
  const resp = await fetchApi(
    `/brk/rules/${ruleId}`,
    {
      method: "PUT",
      body
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateNotificationRule(ruleId, body) {
  const resp = await fetchApi(
    `/notification-rule/${ruleId}`,
    {
      method: "PUT",
      body
    },
    PROCESSING_URL
  );
  return resp.json();
}

export async function deleteIotRule(ruleId: string) {
  const resp = await fetchApi(`/brk/rules/${ruleId}`, { method: "DELETE" }, COMM_URL);
  return resp.json();
}

export async function deleteWorkflow(workflowId: string) {
  const resp = await fetchApi(
    `/brk/automation/workflow/${workflowId}`,
    {
      method: "DELETE"
    },
    COMM_URL
  );
  return resp.json();
}

export async function createIotResource(body) {
  const resp = await fetchApi("/brk/destinations", { method: "POST", body }, COMM_URL);
  return resp.json();
}

export async function updateDestination(destinationId, body) {
  const resp = await fetchApi(
    `/brk/destinations/${destinationId}`,
    {
      method: "PUT",
      body
    },
    COMM_URL
  );
  return resp.json();
}

export async function enableIntegration(body) {
  const resp = await fetchApi(
    `/brk/destinations/int`,
    {
      method: "POST",
      body
    },
    COMM_URL
  );
  return resp.json();
}

export async function deleteDestination({ destinationId, type, integration }) {
  const resp = await fetchApi(
    `/brk/destinations/${destinationId}?${getUrlParams({
      integration,
      type
    })}`,
    { method: "DELETE" },
    COMM_URL
  );
  return resp.json();
}

export async function disableIntegration({ integrationId, type, integration }) {
  const resp = await fetchApi(
    `/brk/destinations/int/${integrationId}?${getUrlParams({
      integration,
      type
    })}`,
    { method: "DELETE" },
    COMM_URL
  );
  return resp.json();
}

export async function updateIotEndPoint({ deviceId, endPoint }) {
  const resp = await fetchApi(
    `/iot/command/update-endpoint`,
    {
      method: "POST",
      body: { deviceId, endPoint }
    },
    IOT_URL
  );
  return resp.json();
}

export async function upateIotDeviceCertificate({ deviceId, action = "rotate" }) {
  const resp = await fetchApi(`/devicemanagement/certificate/updatecert`, {
    method: "POST",
    body: { thingName: deviceId, action }
  });
  return resp.json();
}
