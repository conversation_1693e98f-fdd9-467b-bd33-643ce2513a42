import { IOT_URL, PRODUCT_URL } from ".";
import { fetchApi } from "./_helpers";

export const fetchCount = {
  devices: async () => {
    // const resp = await fetchApi("/dynamodb/inventory/graph")
    const resp = await fetchApi("/dstock/super/inventory/graph", {}, IOT_URL);
    const data = await resp.json();
    return {
      ...data,
      message: {
        registered: data.message.registeredCount,
        unregistered: data.message.unregisteredCount
      }
    };
  },
  users: async () => {
    const resp = await fetchApi("/profiles", {}, PRODUCT_URL);
    const data = await resp.json();
    return {
      ...data,
      total: data.message.profiles.length
    };
  }
};

/**
 * @returns {Promise<any>}
 */
export async function fetchGroupCount() {
  const resp = await fetchApi("/esupply/group/count", {}, IOT_URL);
  return resp.json();
}

export async function upgradeLimit({
  alexa,
  googleHome,
  tickets,
  device_limit,
  increase_device,
  licenseType
}) {
  const resp = await fetchApi("/payments/request-limit", {
    method: "POST",
    body: {
      alexa,
      googleHome,
      tickets,
      device_limit,
      increase_device,
      licenseType
    }
  });
  return resp.json();
}

export async function postRazorpayResponse({ order_id, response_razorpay }) {
  const resp = await fetchApi("/in/payments/post-payment", {
    method: "POST",
    body: { order_id, response_razorpay }
  });
  return resp.json();
}
