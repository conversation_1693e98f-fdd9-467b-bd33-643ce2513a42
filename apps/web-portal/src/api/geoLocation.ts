import { getUrlParams } from "@frontend/shared/utils/httpUtils";
import { GEO_URL, PRODUCT_URL, THING_URL } from ".";
import { SharedLinkItem } from "../..";
import { fetchApi } from "./_helpers";

export const createShareAbleLink = async (body: Record<string, string>) => {
  const resp = await fetchApi(
    `/thing/generate-link/new`,
    {
      method: "POST",
      body
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};
export const updateShareAbleLink = async ({
  id,
  body
}: {
  id: number;
  body: Partial<SharedLinkItem>;
}) => {
  const resp = await fetchApi(
    `/thing/generate-link/${id}`,
    {
      method: "PUT",
      body
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const deleteShareAbleLink = async (id: number) => {
  const resp = await fetchApi(
    `/thing/generate-link/${id}`,
    {
      method: "DELETE"
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const createGeoProduct = async (body) => {
  const resp = await fetchApi(
    `/products?geo=true`,
    {
      method: "POST",
      body
    },
    PRODUCT_URL
  );
  return resp.json();
};

export const updateGeoProduct = async (body) => {
  const { productName, ...payload } = body;
  const resp = await fetchApi(
    `/products/${productName}?geo=true`,
    {
      method: "PATCH",
      body: payload
    },
    PRODUCT_URL
  );
  if (resp.ok) {
    return resp.json();
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const deleteGeoProduct = async ({
  productName,
  productType
}: {
  productName: string;
  productType: "mqtt" | "gps";
}) => {
  const resp = await fetchApi(
    `/products/${productName}?${getUrlParams({ productType, geo: true })}`,
    {
      method: "DELETE"
    },
    PRODUCT_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};
export const createGeoThing = async (body) => {
  const resp = await fetchApi(
    `/things?${getUrlParams({ type: "standard", geo: true })}`,
    {
      method: "POST",
      body
    },
    THING_URL
  );
  return resp.json();
  // if (resp.ok) {
  //   return true;
  // }
  // const errorMsg = await resp.json();
  // throw new Error(errorMsg.message);
};

export const updateGeoThing = async (body) => {
  const { thingName, ...payload } = body;
  const resp = await fetchApi(
    `/things/${thingName}?geo=true`,
    {
      method: "PUT",
      body: payload
    },
    THING_URL
  );
  return resp.json();

  // if (resp.ok) {
  //   return true;
  // }
  // const errorMsg = await resp.json();
  // throw new Error(errorMsg.message);
};

export const createGeoFence = async (body) => {
  const resp = await fetchApi(
    `/geofence`,
    {
      method: "POST",
      body
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};
export const updateGeoFence = async (body) => {
  const { id, ...payload } = body;
  const resp = await fetchApi(
    `/geofence/${id}`,
    {
      method: "PUT",
      body: payload
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};
export const deleteGeoFence = async (geoId) => {
  const resp = await fetchApi(
    `/geofence/${geoId}`,
    {
      method: "DELETE"
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const deleteGeoThing = async (thingName: string) => {
  const resp = await fetchApi(
    `/things/${thingName}?${getUrlParams({ type: "standard", geo: true })}`,
    {
      method: "DELETE"
    },
    THING_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const linkDeviceGeofence = async (body) => {
  const resp = await fetchApi(
    `/geofence/link`,
    {
      method: "POST",
      body
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const unlinkDeviceGeofence = async ({
  thingName,
  geofenceId
}: {
  thingName: string;
  geofenceId: number;
}) => {
  const resp = await fetchApi(
    `/geofence/unlink?thingName=${thingName}&geofenceId=${geofenceId}`,
    {
      method: "DELETE"
    },
    GEO_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};
