import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { COMM_URL, IOT_URL } from ".";
import { getUrlParams } from "../utils/url";
import { fetchApi } from "./_helpers";

export async function fetchPartners({ page = 1, limit = DEFAULT_PAGE_COUNT, searchQuery = "" }) {
  const resp = await fetchApi(
    `/msp/tenants/list?page=${page}&limit=${limit}&search=${searchQuery}`
  );
  return resp.json();
}

export async function fetchPartnersHealth({ page = 1, limit = DEFAULT_PAGE_COUNT, search }) {
  const resp = await fetchApi(
    `/operator/tenants/health/list?${getUrlParams({ page, limit })}&search=${search}`
  );
  return resp.json();
}

export async function fetchPartnersVersions() {
  const resp = await fetchApi(`/operator/tenants/health/availableVersions`);
  return resp.json();
}

export async function fetchPartnerHealthDetail(partnerName) {
  const resp = await fetchApi(`/operator/tenants/health?tenant=${partnerName}`);
  return resp.json();
}

export async function fetchPartner({ queryKey }) {
  const [_, tenant] = queryKey;
  const resp = await fetchApi(`/msp/tenants/acc/${tenant}`);

  return resp.json();
}

export async function fetchAuthorizationList({
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
  search = "",
}) {
  const resp = await fetchApi(
    `/authentication/list?${getUrlParams({ page, limit, search})}`,
    {},
    IOT_URL
  );
  return resp.json();
}

export async function fetchAuthorizationDetails({ authName, authType }) {
  const resp = await fetchApi(
    `/authentication/${authName}?${getUrlParams({ authType })}`,
    {},
    IOT_URL
  );
  return resp.json();
}

export async function fetchTenantLimitList({
  type = "msp",
  searchQuery = "",
  page = 1,
  limit = DEFAULT_PAGE_COUNT
}) {
  const resp =
    type === "operator"
      ? await fetchApi(
          `/operator/tenants/limits/list?${getUrlParams({ page, limit, search: searchQuery })}`
        )
      : await fetchApi(
          `/msp/tenants/limits/list?${getUrlParams({ page, limit, search: searchQuery })}`
        );

  const data = await resp.json();

  return data.data as {
    pages: number;
    tenantLimitsData: {
      credentialsExpiration: string;
      email: string;
      expiration: string;
      id: string;
      msp: string;
      phone: string;
      productsCount: number;
      productsLimit: number;
      tenant: string;
      thingsCount: number;
      thingsLimit: number;
    }[];
  };
}

export async function fetchTenantAccessList({
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
  msp,
  view = "msp"
}) {
  const resp = await fetchApi(
    `/msp/access-templates/list?${getUrlParams({ page, limit, msp, view })}`
  );
  const data = await resp.json();
  return data.data;
}

export async function fetchTenantAccessDetails(templateId) {
  const resp = await fetchApi(`/msp/access-templates/${templateId}`);
  const data = await resp.json();
  return data.data;
}

export async function fetchTenantLimit({ queryKey }) {
  const [_, tenantName, type] = queryKey;
  let resp;

  if (type === "operator") {
    resp = await fetchApi(`/operator/tenants/limits/${tenantName}`);
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/tenants/limits/${tenantName}`);
  }

  return resp.json();
}
export async function fetchTenantHookData(tenantName: string) {
  const resp = await fetchApi(`/hook-data/?tenant=${tenantName}`, {}, COMM_URL);
  return resp.json();
}
export async function fetchTenantBackupList(tenantName: string) {
  const resp = await fetchApi(`/operator/tenant-backup/list?tenant=${tenantName}`);
  return resp.json();
}
export async function fetchFeatureFlagList({
  tenant,
  page = 1,
  limit = DEFAULT_PAGE_COUNT
}: {
  tenant: string;
  page?: number;
  limit?: number;
}) {
  const resp = await fetchApi(
    `/feature-flags?${getUrlParams({ tenant, page, limit })}`,
    {},
    COMM_URL
  );
  return resp.json();
}

// =========================== NON-FETCH REQUESTS =========================== //

export async function createMongoUser(body) {
  const resp = await fetchApi(
    "/userAuth",
    {
      method: "POST",
      body
    },
    IOT_URL
  );
  return resp.json();
}

export async function createAuthorization(body) {
  const resp = await fetchApi(
    "/authentication",
    {
      method: "POST",
      body
    },
    IOT_URL
  );
  return resp.json();
}
export async function updateAuthorization(body) {
  const { username, ...payload } = body;
  const resp = await fetchApi(
    `/authentication/${username}`,
    {
      method: "PATCH",
      body: payload
    },
    IOT_URL
  );
  return resp.json();
}

export async function updateMongoListners(type, value) {
  const resp = await fetchApi(
    `/brk/listners/${type}?status=${value ? "start" : "stop"}`,
    { method: "POST" },
    COMM_URL
  );
  return resp.json();
}

export async function deleteMongoUser({ username }) {
  const resp = await fetchApi(
    `/userAuth?username=${username}`,
    {
      method: "DELETE"
    },
    IOT_URL
  );
  const data = await resp.json();
  if (resp.ok) {
    return data;
  }

  throw new Error(data.message);
}

export async function deleteAuthorization({ username, authType }) {
  const resp = await fetchApi(
    `/authentication/${username}?${getUrlParams({ authType })}`,
    {
      method: "DELETE"
    },
    IOT_URL
  );
  const data = await resp.json();
  if (resp.ok) {
    return data;
  }

  throw new Error(data.message);
}

export async function updatePartnerHealthVersions(body) {
  const resp = await fetchApi("/operator/tenants/health/addVersions", {
    method: "POST",
    body
  });
  return resp.json();
}
export async function updatePartnerResource(body) {
  const resp = await fetchApi("/operator/tenants/health/updateSetup", {
    method: "PATCH",
    body
  });
  return resp.json();
}

export async function updateTenantHookData(body) {
  const { tenantName, ...payload } = body;
  const resp = await fetchApi(
    `/hook-data/?tenant=${tenantName}`,
    {
      method: "PUT",
      body: payload
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}
export async function updateTenantLimits(body) {
  const { tenantName, type, ...payload } = body;
  let resp;
  if (type === "operator") {
    resp = await fetchApi(`/operator/tenants/limits/${tenantName}`, {
      method: "PATCH",
      body: payload
    });
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/tenants/limits/${tenantName}`, {
      method: "PATCH",
      body: payload
    });
  }

  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}
export async function restoreTenantBackup({ tenantName, body }) {
  const resp = await fetchApi(`/operator/tenant-backup/import?tenant=${tenantName}`, {
    method: "POST",
    body
  });
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function createFeatureFlag({ tenant, body }) {
  const resp = await fetchApi(
    `/feature-flags?tenant=${tenant}`,
    {
      method: "POST",
      body
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function updateFeatureFlag({ tenant, body, serviceName }) {
  const resp = await fetchApi(
    `/feature-flags/${serviceName}?tenant=${tenant}`,
    {
      method: "PUT",
      body
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function deleteFeatureFlag({ tenant, serviceName }) {
  const resp = await fetchApi(
    `/feature-flags/${serviceName}?tenant=${tenant}`,
    {
      method: "DELETE"
    },
    COMM_URL
  );
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
}

export async function registerPartner(body) {
  const { platformType, ...payload } = body;

  let resp;
  if (platformType === "operator") {
    resp = await fetchApi("/operator/tenants", {
      method: "POST",
      body: payload
    });
  }
  if (platformType === "msp") {
    resp = await fetchApi("/msp/tenants/acc", {
      method: "POST",
      body: payload
    });
  }

  return resp.json();
}

export const createTenantAccessTemplate = async (body) => {
  const resp = await fetchApi("/msp/access-templates", {
    method: "POST",
    body
  });
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const deleteTenantAccessTemplate = async (templateId) => {
  const resp = await fetchApi(`/msp/access-templates/${templateId}`, {
    method: "DELETE"
  });
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export const editTenantAccessTemplate = async ({ templateId, payload }) => {
  const resp = await fetchApi(`/msp/access-templates/${templateId}`, {
    method: "PUT",
    body: payload
  });
  if (resp.ok) {
    return true;
  }
  const errorMsg = await resp.json();
  throw new Error(errorMsg.message);
};

export async function updatePartner({ userType, ...payload }) {
  let resp;
  if (userType === "operator") {
    resp = await fetchApi("/operator/tenants", {
      method: "PATCH",
      body: payload
    });
  } else if (userType === "msp") {
    resp = await fetchApi("/msp/tenants/acc", {
      method: "PATCH",
      body: payload
    });
  } else {
    resp = await fetchApi("/tnt/acc", {
      method: "PATCH",
      body: payload
    });
  }

  return resp.json();
}

export async function deletePartner({ type, name }) {
  let resp;
  if (type === "operator") {
    resp = await fetchApi(`/operator/tenants/${name}`, {
      method: "DELETE"
    });
  }
  if (type === "msp") {
    resp = await fetchApi(`/msp/tenants/acc/${name}`, {
      method: "DELETE"
    });
  }
  return resp.json();
}
