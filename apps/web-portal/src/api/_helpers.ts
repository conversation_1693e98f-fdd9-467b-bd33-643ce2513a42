import { getPartnerAndFeature } from "@utils/url";
import { API_URL } from ".";
import { refreshUser } from "@frontend/shared/api/user";
import { storage } from "@frontend/shared";

/**
 * @param {string} tokens
 */
export const setTokens = (tokens: string) => {
  localStorage.setItem("tokens", JSON.stringify(tokens));
};
export const getTokens = () => {
  const tokens = localStorage.getItem("tokens");
  return tokens ? (JSON.parse(tokens) as { accessToken: string; refreshToken: string }) : null;
};

const checkForOperatorRoutes = (url: string) => {
  if (
    url.includes("/auth/self") ||
    url.includes("/auth/refresh") ||
    url.includes("/msp/tenants/list")
  ) {
    return false;
  }
  return true;
};

export const isInPublicSharedLink = () => {
  return window.location.pathname.includes("/shared/");
};

let isRefreshing = false;
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token!);
    }
  });

  failedQueue = [];
};

export async function fetchApi(
  relURL: string,
  { method = "GET", headers = {}, body, ...options }: any = {},
  baseURL: string = API_URL
): Promise<Response> {
  const token = getTokens()?.accessToken;
  if (isInPublicSharedLink()) {
    // discuss with supriyo once before you make changes here
    const pathArray = window.location.pathname.split("/");

    if (pathArray[2] && pathArray[3]) {
      const key = `${pathArray[2]}:${pathArray[3]}`;
      headers["x-shared-resource-id"] = key;

      if (relURL.includes("/ts/")) {
        relURL = relURL?.replace("/ts/", "/publicLink/");
      } else if (relURL.includes("/site/")) {
        relURL = relURL?.replace("/site/", "/stats/publicLink/");
      } else if (relURL.includes("/dashboard/")) {
        relURL = relURL?.replace("/dashboard/", "/stats/publicLink/");
      }
    }
  } else if (token) {
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }
  const isFormData = body instanceof FormData;
  if (!isFormData) {
    headers["Content-Type"] = "application/json";
  }

  const urlDetails = getPartnerAndFeature();
  const isTenant = Boolean(urlDetails.featureType) && Boolean(urlDetails.partnerName);
  if (isTenant && checkForOperatorRoutes(relURL) && !relURL.includes("tenant=")) {
    // Check if relURL already contains query parameters
    if (baseURL.includes("?") || relURL.includes("?")) {
      relURL += `&tenant=${urlDetails.partnerName}`;
    } else {
      relURL += `?tenant=${urlDetails.partnerName}`;
    }
  }

  const response = await fetch(baseURL + relURL, {
    method,
    headers,
    body: isFormData ? body : body ? JSON.stringify(body) : null,
    ...options
  });

  // We are doing this to call refresh api for access token when it expires and to prevent multiple call if there any multiple request in single page we are using queue
  if (response.status === 401) {
    if (isRefreshing) {
      // If already refreshing, queue this request
      return new Promise((resolve, reject) => {
        failedQueue.push({
          resolve: (token: string) => {
            headers.Authorization = `Bearer ${token}`;
            resolve(
              fetch(baseURL + relURL, {
                method,
                headers,
                body: isFormData ? body : body ? JSON.stringify(body) : null,
                ...options
              })
            );
          },
          reject: (error: any) => {
            reject(error);
          }
        });
      });
    }

    isRefreshing = true;

    try {
      const refreshResult = await refreshUser();
      if (refreshResult.status === "Success") {
        const newToken = getTokens()?.accessToken;
        if (newToken) {
          processQueue(null, newToken);
          isRefreshing = false;

          // Retry the original request
          headers.Authorization = `Bearer ${newToken}`;
          return fetch(baseURL + relURL, {
            method,
            headers,
            body: isFormData ? body : body ? JSON.stringify(body) : null,
            ...options
          });
        }
      }

      // Refresh failed
      processQueue(new Error("Token refresh failed"), null);
      isRefreshing = false;

      storage.clear();
      if (!window.location.pathname?.includes("/shared")) {
        window.location.href = "/login";
      }
    } catch (error) {
      processQueue(error, null);
      isRefreshing = false;

      storage.clear();
      if (!window.location.pathname?.includes("/shared")) {
        window.location.href = "/login";
      }
    }
  }

  const clonedResponse = response.clone();
  try {
    const data = await clonedResponse.json();
    if (
      data.status === "Failure" &&
      (data.message === "Invalid token!" || data.message.includes("The token is expired"))
    ) {
      const isLoginPage = window.location.pathname === "/login";
      // if on a shared route dont navigate to login
      if (!isLoginPage && !window.location.pathname?.includes("/shared")) {
        window.location.href = "/login";
      }
    }
  } catch (error) {
    console.error("Error parsing response:", error);
  }
  return response;
}
/**
 * @param {string} relURL
 * @param {any} param1
 * @param {string} [baseURL]
 * @returns {Promise<Response>}
 */
export const fetchImage = (
  relURL: string,
  { method = "GET", headers = {}, body, ...options }: any = {},
  baseURL: string = API_URL
): Promise<Response> => {
  const token = getTokens()?.accessToken;
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return fetch(baseURL + relURL, {
    method,
    headers,
    body,
    ...options
  });
};
