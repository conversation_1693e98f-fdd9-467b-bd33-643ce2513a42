import { fetchApi } from "@api/_helpers";
import { IOT_URL } from "@api/index";
import { getUrlParams } from "@frontend/shared/utils/httpUtils";
import { AuthenticationDetails } from "@src/features/features";
import { useQuery } from "@tanstack/react-query";

const useUserDetails = (authName:string,authType : "thing" | "custom" ) => {
  return useQuery({
   queryKey: ["user-auth-details", authType,authName],
    queryFn: async () => {
      const resp = await fetchApi(
          `/authentication/${authName}?${getUrlParams({ authType:authType })}`,
          {},
          IOT_URL
        );
         const data = await resp.json();
     
      if (data.status === "Success") {
        return data.data as AuthenticationDetails;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error(data.message);
    },
    enabled : <PERSON><PERSON><PERSON>(authName && authType)
  });
};

export default useUserDetails;
