import { fetchApi } from "@api/_helpers";
import { IOT_URL } from "@api/index";
import { DEFAULT_PAGE_COUNT } from "@frontend/shared/config/defaults";
import { getUrlParams } from "@frontend/shared/utils/httpUtils";
import { AuthenticationList} from "@src/features/features";
import { useQuery } from "@tanstack/react-query";

const useAuthenticationList = ({ search = "", page = 1, limit = DEFAULT_PAGE_COUNT }) => {
  return useQuery({
   queryKey: ["authorization-list", page, limit, search],
    queryFn: async () => {
      const resp = await fetchApi(
          `/authentication/list?${getUrlParams({ page, limit, search})}`,
          {},
          IOT_URL
        );
      const data = await resp.json();
      if (data.status === "Success") {
        return data.data as AuthenticationList;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error(data.message);
    }
  });
};

export default useAuthenticationList;
