import { ClassicThingListItem } from "@/index";
import { fetchApi } from "@api/_helpers";
import { THING_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";

const useThingList = ({
  enabled = true,
  limit = DEFAULT_PAGE_COUNT,
  search = "",
  page = 1,
  status = "",
  productName = "",
  gatewayName = "",
  thingGroup = ""
}) => {
  return useQuery({
    queryKey: ["things-list", page, limit, search, status, productName, gatewayName, thingGroup],
    enabled,
    queryFn: async () => {
      const resp = await fetchApi(
        `/things/list?${getUrlParams({
          page,
          limit,
          search,
          status,
          productName,
          gatewayName,
          thingGroup
        })}`,
        {},
        THING_URL
      );

      const data = await resp.json();

      if (!resp.ok) {
        throw new Error(data.message);
      }

      return data.data as {
        things: ClassicThingListItem[];
        page: number;
      };
    }
  });
};

export default useThingList;
