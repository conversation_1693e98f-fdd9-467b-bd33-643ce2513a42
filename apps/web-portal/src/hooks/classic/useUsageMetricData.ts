import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import { getDateFromDurationString } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { getPartnerAndFeature } from "@utils/url";

const useUsageMetricData = ({
  duration,
  enabled = true,
  tenant
}: {
  duration: string;
  enabled?: boolean;
  tenant?: string;
}) => {
  const { partnerName } = getPartnerAndFeature();

  return useQuery({
    queryKey: ["usage-metric-data", duration, partnerName],
    enabled,
    queryFn: async () => {
      const { startDate, endDate } = getDateFromDurationString(duration);
      const url = new URL(`${STATS_URL}/stats/metric/usage`);

      url.searchParams.append("start", startDate);
      url.searchParams.append("end", endDate);

      if (partnerN<PERSON>) {
        url.searchParams.append("tenant", partnerName);
      }

      const resp = await fetchApi(url.toString(), {}, "");

      const data = await resp.json();

      if (resp.ok) {
        return data.data as {
          event:
            | "notification.failure"
            | "notification.success"
            | "ota-releases.created"
            | "ota-releases.deleted"
            | "ota-jobs.success"
            | "ota-jobs.failure"
            | "thing.deleted"
            | "product.created"
            | "product.deleted"
            | "sms.success"
            | "sms.failure"
            | "rule_action.success"
            | "rule_action.failed"
            | "rule_exec_success"
            | "rule_exec_failed"
            | "rule_action.total"
            | "message_publish"
            | "message_delivered"
            | "message_dropped"
            | "message_sent"
            | "message_bytes_received"
            | "message_bytes_sent";
          count: number;
        }[];
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useUsageMetricData;
