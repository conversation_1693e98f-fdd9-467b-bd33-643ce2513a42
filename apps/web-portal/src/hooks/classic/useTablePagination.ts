import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { useCallback } from "react";
import { useSearchParams } from "react-router-dom";

const useTablePagination = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const limit = Number(searchParams.get("limit")) || DEFAULT_PAGE_COUNT;

  const page = Number(searchParams.get("page")) || 1;

  const searchQuery = searchParams.get("searchQuery") || "";

  const setSearchQuery = useCallback((value: string) => {
    setSearchParams((prev) => {
      if (!value) {
        prev.delete("searchQuery");
      } else {
        prev.set("searchQuery", value);
      }
      return prev;
    });
  }, []);

  const setPage = useCallback((value: number) => {
    setSearchParams((prev) => {
      prev.set("page", String(value));
      return prev;
    });
  }, []);

  const setLimit = useCallback((value: number) => {
    setSearchParams((prev) => {
      prev.set("limit", String(value));
      return prev;
    });
  }, []);

  const addSearchParams = useCallback((params: Record<string, string>) => {
    setSearchParams((prev) => {
      Object.keys(params).forEach((key) => {
        prev.set(key, params[key]);
      });
      return prev;
    });
  }, []);

  const removeSearchParams = useCallback((params: string[]) => {
    setSearchParams((prev) => {
      params.forEach((key) => {
        prev.delete(key);
      });
      return prev;
    });
  }, []);

  return {
    page,
    limit,
    setPage,
    setLimit,
    searchQuery,
    setSearchQuery,
    addSearchParams,
    removeSearchParams,
    searchParams
  };
};

export default useTablePagination;
