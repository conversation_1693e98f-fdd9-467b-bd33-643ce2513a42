import { availableOptions } from "@src/pages/MonitorPage/layout/SelectDuration";
import { create } from "zustand";

type UseWidgetConfig = {
  duration: string;
  setDuration: (duration: string) => void;
  editing: boolean;
  setEditing: (editing: boolean) => void;
  liveMode: boolean;
  toggleLiveMode: () => void;
  stopLiveMode: () => void;
};

const useWidgetConfig = create<UseWidgetConfig>((set) => ({
  duration:
    availableOptions.find((item) => item.value === localStorage.getItem("@widget-duration"))
      ?.value || "today",
  setDuration: (duration: string) => {
    localStorage.setItem("@widget-duration", duration);
    set({ duration });
  },
  editing: false,
  setEditing: (editing: boolean) => {
    set({ editing });
  },
  liveMode: false,
  toggleLiveMode() {
    set((state) => ({ liveMode: !state.liveMode }));
  },
  stopLiveMode() {
    set({ liveMode: false });
  }
}));

export default useWidgetConfig;
