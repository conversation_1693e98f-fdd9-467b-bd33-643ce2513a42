import { DashboardGraphItem } from "@/index";
import { fetchGraphPlotData } from "@api/dashboardBuilder";
import {
  isChartCircular,
  isChartEnabled,
  isConnectionStatusOrShadow,
  SENSOR_KEY
} from "@src/pages/MonitorPage/utils";
import { useAppSelector } from "@src/store";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

const useChartData = (graphItem: DashboardGraphItem) => {
  const isCircularChart = isChartCircular(graphItem.chartType);
  const { id, dataIndex, dataKey, chartType } = graphItem;
  const filters = graphItem.filters;
  const allowZeros = graphItem.allowZeros;
  const stacked = graphItem.stacked;
  const expanded = graphItem.expanded;
  const modal = graphItem.modal;

  const graphDuration = useAppSelector(
    ({ dashboardBuilder }) => dashboardBuilder.graphDuration.value
  );
  const expandedGraphDuration = useAppSelector(({ expandedGraph }) => expandedGraph.duration.value);

  const filterCount = useMemo(() => {
    return filters?.filter((item) => item.enabled)?.length || 0;
  }, [filters]);

  const filterString = useMemo(() => {
    return (
      filters
        ?.filter((item) => item.enabled)
        ?.map((item) => `${item.field}:${item.value}:${item.operation}`)
        .join(",") || ""
    );
  }, [filterCount]);

  return useQuery({
    queryKey: [
      `graph-plot-${isCircularChart ? "circular" : "normal"}`,
      dataIndex,
      dataKey,
      filterCount,
      modal ? expandedGraphDuration : graphDuration,
      allowZeros,
      filterString
    ],
    enabled: dataIndex !== SENSOR_KEY && isChartEnabled(dataIndex, dataKey, filters || []),
    queryFn: () =>
      fetchGraphPlotData(
        dataIndex,
        dataKey,
        modal ? expandedGraphDuration : graphDuration,
        isCircularChart || isConnectionStatusOrShadow(dataIndex),
        filters,
        { stacked, expanded, id, isBar: ["Bar", "Bar-H"].includes(chartType) },
        modal,
        allowZeros
      )
  });
};

export default useChartData;
