import { AppUserItem } from "@/index";
import { fetchApi } from "@api/_helpers";
import { APP_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";

const useAppUserList = ({
  enabled = true,
  searchQuery = ""
}: {
  enabled?: boolean;
  searchQuery?: string;
}) => {
  const queryKey = ["app-user-list", searchQuery];

  return useQuery({
    queryKey,
    enabled,
    queryFn: async () => {
      const fetchResponse = await fetchApi(
        `/app-things/user-list?${getUrlParams({
          searchQuery
        })}`,
        {},
        APP_URL
      );

      const res = await fetchResponse.json();

      if (res.status === "Success") {
        return res.data as {
          users: AppUserItem[];
          page: number;
          limit: number;
          userCount: number;
        };
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
    }
  });
};

export default useAppUserList;
