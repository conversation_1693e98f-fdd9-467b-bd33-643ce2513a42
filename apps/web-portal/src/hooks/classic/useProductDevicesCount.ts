import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";

import { useQuery } from "@tanstack/react-query";

const useProductDevicesCount = ({
  enabled = true,
  productName
}: {
  enabled?: boolean;
  productName?: string;
}) => {
  return useQuery({
    queryKey: ["productDevicesCount", productName],
    enabled,
    initialData: [],
    queryFn: async () => {
      const resp = await fetchApi(`/stats/count/product-devices`, {}, STATS_URL);
      const data = await resp.json();
      if (!resp.ok) {
        throw new Error(data.message);
      }
      return data.data as {
        productName: string;
        devices: number;
      }[];
    }
  });
};

export default useProductDevicesCount;
