import { fetchApi } from "@api/_helpers";
import { RAG_API } from "@api/index";
import { useQuery } from "@tanstack/react-query";

const useLlmHistory = () => {
  return useQuery({
    queryKey: ["llm-history"],
    queryFn: async () => {
      const fetchResponse = await fetchApi("/history", {}, RAG_API);
      const res = await fetchResponse.json();

      if (res.status === "Success") {
        return res.data;
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useLlmHistory;
