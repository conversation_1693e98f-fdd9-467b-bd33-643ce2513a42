import { AppUserItem } from "@/index";
import { fetchApi } from "@api/_helpers";
import { APP_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";

const useAppUserDetails = ({ enabled = true, email }: { enabled?: boolean; email: string }) => {
  const queryKey = ["app-user-details", email];

  return useQuery({
    queryKey,
    enabled,
    queryFn: async () => {
      const fetchResponse = await fetchApi(`/app-things/user-detail/${email}`, {}, APP_URL);

      const res = await fetchResponse.json();

      if (res.status === "Success") {
        return res.data as {
          userDetails: AppUserItem;
        };
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
    }
  });
};

export default useAppUserDetails;
