import { ScheduleWithShifts } from "@hooks/notifications/useScheduleDetail";
import { generateRandomId } from "@src/pages/MonitorPage/utils";
import { create } from "zustand";

export interface Shift {
  id: string;
  name: string;
  startTime: Date | undefined;
  endTime: Date | undefined;
  assignees: string[]; // or change to specific type if assignees are objects
}

export interface SchedulesConfigState {
  name: string;
  timezone: string;
  pattern: "daily" | "weekly" | "custom";
  workingDays: string[];
  nonWorkingDates: string[];
  status: boolean | null;
  startDate: Date;
  endDate: Date | null;
  shifts: Shift[];
  editDetails: (key: keyof SchedulesConfigState, value: any) => void;
}
const initialScheduleConfig = {
  name: "",
  timezone: "UTC",
  pattern: "daily" as "daily",
  workingDays: [],
  nonWorkingDates: [],
  status: null,
  startDate: new Date(),
  endDate: null
};

const STATUS_OPTIONS = [
  { label: "Active", value: true },
  { label: "Inactive", value: false }
];

function timeStringToTodayDate(time: string): Date {
  // Handle ISO date strings (convert UTC to local time)
  if (time.includes("T")) {
    const utcDate = new Date(time);
    const now = new Date();
    now.setHours(utcDate.getHours(), utcDate.getMinutes(), utcDate.getSeconds(), 0);
    return now;
  }

  // Handle time-only strings like "02:30:00"
  const [hours, minutes, seconds] = time.split(":").map(Number);
  const now = new Date();
  now.setHours(hours, minutes, seconds || 0, 0);
  return now;
}
const initialShiftsConfig = {
  shifts: [
    {
      id: generateRandomId(16),
      name: "",
      startTime: undefined,
      endTime: undefined,
      assignees: [],
      targetType: "Contacts"
    }
  ]
};

const convertedShift = (shifts) => {
  const updatedShifts = shifts.map((shift) => ({
    id: shift.id,
    name: shift.name,
    startTime: timeStringToTodayDate(shift.startTime),
    endTime: timeStringToTodayDate(shift.endTime),
    targetType: shift.assignees.type === "user" ? "Contacts" : "Group",
    assignees:
      shift.assignees.type === "user"
        ? shift.assignees.members
        : {
            id: shift.assignees.groupId,
            name: shift.assignees.groupName
          }
  }));

  return updatedShifts;
};

const useSchedulesConfig = create<SchedulesConfigState>((set) => ({
  ...initialScheduleConfig,
  ...initialShiftsConfig,
  resetConfig: () => set({ ...initialScheduleConfig, ...initialShiftsConfig }),
  updateScheduleConfig: (data: ScheduleWithShifts) =>
    set(() => {
      const updatedShifts = convertedShift(data.shifts);
      const matchedStatus = STATUS_OPTIONS.find((option) => option.value === data.status);
      return {
        name: data.name,
        timezone: data.timezone,
        pattern: data.pattern,
        workingDays: data.workingDays,
        nonWorkingDates: data.nonWorkingDates,
        status: matchedStatus,
        startDate: new Date(data.startDate),
        endDate: data.endDate ? new Date(data.endDate) : null,
        shifts: updatedShifts
      };
    }),
  editDetails: (key, value) =>
    set(() => ({
      [key]: value
    })) // make this dumb function
}));

export default useSchedulesConfig;
