import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";

const useUserCountStats = ({ enabled = true }) => {
  return useQuery({
    queryKey: ["user-count-stats"],
    enabled,
    queryFn: async () => {
      const resp = await fetchApi("/stats/metric/users-stats", {}, STATS_URL);

      const data = await resp.json();
      if (resp.ok) {
        return data.data.count as {
          user: number;
          msp: number;
          operator: number;
          tenant: number;
        };
      }

      throw new Error(data.message);
    }
  });
};

export default useUserCountStats;
