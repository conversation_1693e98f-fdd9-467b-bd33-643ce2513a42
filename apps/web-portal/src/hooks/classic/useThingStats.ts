import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";

const useThingsStats = ({
  productName,
  enabled = true
}: {
  productName?: string;
  enabled?: boolean;
}) => {
  return useQuery({
    queryKey: ["thing-stats", productName],
    enabled,
    initialData: {
      connected: 0,
      disconnected: 0,
      total: 0,
      inactive: 0
    },
    queryFn: async () => {
      const resp = await fetchApi(
        `/stats/count/status?${getUrlParams({ productName })}`,
        {},
        STATS_URL
      );
      const data = await resp.json();
      if (!resp.ok) throw new Error(data.message);
      const { connected, disconnected, inactive } = data.data as {
        connected: number;
        disconnected: number;
        inactive: number;
      };

      return {
        connected,
        disconnected,
        inactive,
        total: connected + disconnected + inactive
      };
    }
  });
};

export default useThingsStats;
