import { generateRandomId } from "@src/pages/MonitorPage/utils";
import { create } from "zustand";
export const SMS_TEMPLATE = [
  {
    label: "Format 1",
    msg91TemplateId: "668e24bfd6fc056e272cf2a2",
    format:
      "Device ${thingName} has detected an issue with ${level} severity. The detected issue is ${issue}. Please check the device ${payload} and contact support if needed. Click here to ack message read status: https://${host}.1iot.io/ack/${smsId}. - ONEIOT"
  },
  {
    label: "Format 2",
    msg91TemplateId: "668e167cd6fc0511bd112ce2",
    format:
      "${deviceName} detected issue with ${level} severity - ${payload}. Please check ${details} and Contact support if needed. Click here to acknowledge https://${host}.1iot.io/ack/${smsId} - ONEIOT"
  },
  {
    label: "Format 3",
    msg91TemplateId: "668d3647d6fc055bde533432",
    format:
      "${deviceName} detected issue with ${level} severity - ${payload}. Please check ${details} for more details. Contact support if needed. - ONEIOT"
  }
];
type CommonTemplateFields = {
  conditions: string[];
  feId: string;
};

export type PushTemplate = CommonTemplateFields & {
  title: string;
  body: string;
};

export type EmailTemplate = CommonTemplateFields & {
  subject: string;
  body: string;
  HTMLbody: string;
  attachments: any[]; // Define more strictly if needed
};

export type SmsTemplate = CommonTemplateFields & {
  body: string;
  msg91TemplateId: string;
};

export type TeamsTemplate = CommonTemplateFields & {
  title: string;
  body: string;
  themeColor: string;
};
export type ChannelType = "pushTemplates" | "emailTemplates" | "smsTemplates" | "teamsTemplates";
type TemplateType = "pushTemplates" | "emailTemplates" | "smsTemplates" | "teamsTemplates";
export type NotificationConfigStore = {
  details: {
    templateName: string;
    description: string;
  };
  channels: Record<ChannelType, boolean>;
  pushTemplates: PushTemplate[];
  emailTemplates: EmailTemplate[];
  smsTemplates: SmsTemplate[];
  teamsTemplates: TeamsTemplate[];
  resetConfig: () => void;
  editDetails: (key: keyof NotificationConfigStore["details"], value: string) => void;
  toggleChannel: (channelName: ChannelType) => void;

  editTemplate: (
    id: string,
    templateType: TemplateType,
    key: keyof PushTemplate | keyof EmailTemplate | keyof SmsTemplate | keyof TeamsTemplate,
    value: string | string[]
  ) => void;

  addTemplate: (templateType: ChannelType) => void;
  removeTemplate: (templateType: TemplateType, feId: string) => void;
  updateNotificationConfig: (data: {
    templateName: string;
    description: string;
    pushTemplates?: PushTemplate[];
    emailTemplates?: EmailTemplate[];
    smsTemplates?: SmsTemplate[];
    teamsTemplates?: TeamsTemplate[];
  }) => void;
};

const initialConfig = {
  details: { templateName: "", description: "" },
  channels: {
    pushTemplates: true,
    emailTemplates: false,
    smsTemplates: false,
    teamsTemplates: false
  },
  pushTemplates: [
    {
      title: "",
      body: "",
      conditions: [],
      feId: generateRandomId(16)
    }
  ],
  emailTemplates: [
    {
      subject: "",
      body: "",
      conditions: [],
      feId: generateRandomId(16),
      HTMLbody: "",
      attachments: []
    }
  ],
  smsTemplates: [
    {
      body: SMS_TEMPLATE[0]?.format,
      msg91TemplateId: SMS_TEMPLATE[0]?.msg91TemplateId,
      conditions: [],
      feId: generateRandomId(16)
    }
  ],
  teamsTemplates: [
    {
      title: "",
      body: "",
      themeColor: "#00e6c3",
      conditions: [],
      feId: generateRandomId(16)
    }
  ]
};

const useNotificationConfig = create<NotificationConfigStore>((set) => ({
  ...initialConfig,
  resetConfig: () => set(initialConfig),

  updateNotificationConfig: (data: {
    templateName: string;
    description: string;
    pushTemplates?: PushTemplate[];
    emailTemplates?: EmailTemplate[];
    smsTemplates?: SmsTemplate[];
    teamsTemplates?: TeamsTemplate[];
  }) =>
    set(() => {
      const {
        templateName,
        description,
        pushTemplates = [],
        emailTemplates = [],
        smsTemplates = [],
        teamsTemplates = []
      } = data;

      return {
        details: {
          templateName,
          description
        },
        channels: {
          pushTemplates: pushTemplates.length > 0,
          emailTemplates: emailTemplates.length > 0,
          smsTemplates: smsTemplates.length > 0,
          teamsTemplates: teamsTemplates.length > 0
        },
        pushTemplates,
        emailTemplates,
        smsTemplates,
        teamsTemplates
      };
    }),
  editDetails: (key: string, value: string) =>
    set((state) => ({
      details: { ...state.details, [key]: value }
    })),
  toggleChannel: (channelName: string) =>
    set((state) => ({
      channels: { ...state.channels, [channelName]: !state.channels[channelName] }
    })),
  editTemplate: (id, templateType, key, value) =>
    set((state) => ({
      [templateType]: state[templateType].map((item) =>
        item.feId === id ? { ...item, [key]: value } : item
      )
    })),

  addTemplate: (templateType) =>
    set((state) => {
      const newTemplate = (() => {
        switch (templateType) {
          case "pushTemplates":
            return {
              title: "",
              body: "",
              conditions: [],
              feId: generateRandomId(16)
            };
          case "emailTemplates":
            return {
              subject: "",
              body: "",
              conditions: [],
              feId: generateRandomId(16),
              HTMLbody: "",
              attachments: []
            };
          case "smsTemplates":
            return {
              body: SMS_TEMPLATE[0]?.format,
              msg91TemplateId: SMS_TEMPLATE[0]?.msg91TemplateId,
              conditions: [],
              feId: generateRandomId(16)
            };
          case "teamsTemplates":
            return {
              title: "",
              body: "",
              themeColor: "#00e6c3",
              conditions: [],
              feId: generateRandomId(16)
            };
        }
      })();

      return {
        [templateType]: [...state[templateType], newTemplate]
      } as Pick<NotificationConfigStore, typeof templateType>;
    }),
  removeTemplate: (templateName, feId) =>
    set((state) => {
      const newTemplates = state[templateName].filter((item) => item.feId !== feId);
      return {
        ...state,
        [templateName]: newTemplates
      };
    })
}));

export default useNotificationConfig;
