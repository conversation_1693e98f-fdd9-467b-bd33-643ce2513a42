import { fetchApi } from "@api/_helpers";
import { APP_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";

const useAppUserThings = ({
  enabled = true,
  email,
  searchQuery = "",
  limit = DEFAULT_PAGE_COUNT,
  page = 1
}: {
  enabled?: boolean;
  email: string;
  searchQuery?: string;
  limit: number;
  page: number;
}) => {
  const queryKey = ["app-user-things", email, page, limit, searchQuery];

  return useQuery({
    queryKey,
    enabled,
    queryFn: async () => {
      const fetchResponse = await fetchApi(
        `/app-things/user-things/${email}?${getUrlParams({ page, limit, searchQuery })}`,
        {},
        APP_URL
      );

      const res = await fetchResponse.json();

      if (res.status === "Success") {
        return res.data as {
          things: {
            thingName: string;
            productName: string;
            displayName: string;
            createdAt: string;
            role : string;
          }[];
          page: number;
          limit: number;
          totalCount: number;
        };
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
    }
  });
};

export default useAppUserThings;
