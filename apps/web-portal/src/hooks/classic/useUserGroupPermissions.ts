import { MODE } from "@api/index";
import { fetchUserGroupDetail } from "@api/user";
import { useAppSelector } from "@src/store";
import { useQuery } from "@tanstack/react-query";

import { useMemo } from "react";

export const getUserType = (role: string) => {
  switch (role) {
    case "OperatorAdmin":
    case "OperatorSupport":
    case "OperatorSuperUser":
      return "operator";
    case "MSPSuperUser":
    case "MSPAdmin":
    case "MSPSupport":
      return "msp";
    case "TenantAdmin":
    case "TenantSupport":
    case "TenantSuperUser":
      return "tenant";
    default:
      return "tenant";
  }
};

type Permission = "read" | "write" | "none";

export type UserGroupPermissions = {
  thing: Permission;
  inventory: Permission;
  product: Permission;
  ota: Permission;
  destinations: Permission;
  rules: Permission;
  thingGroup: Permission;
  policy: Permission;
  tls: Permission;
  incidents: Permission;
  mspAcc: Permission;
  mspKey: Permission;
  mspUSers: Permission;
  tntAcc: Permission;
  tntKey: Permission;
  tntUsers: Permission;
  authentication: Permission;
  geofence: Permission;
  route: Permission;
  driver: Permission;
  geoHistory: Permission;
  geoThing: Permission;
  geoProduct: Permission;
  listners: Permission;
  escalations: Permission;
  notificationTemplates: Permission;
  notificationHistory: Permission;
  contacts: Permission;
  whiteLabel: Permission;
  stats: Permission;
  appUser: Permission;
  site: Permission;
  dashboard: Permission;
  reports: Permission;
  sites: Permission;
};

const DEFAULT_PERMISSIONS: UserGroupPermissions = {
  thing: "write",
  inventory: "write",
  product: "write",
  ota: "write",
  destinations: "write",
  rules: "write",
  thingGroup: "write",
  policy: "write",
  tls: "write",
  incidents: "write",
  mspAcc: "write",
  mspKey: "write",
  mspUSers: "write",
  tntAcc: "write",
  tntKey: "write",
  tntUsers: "write",
  authentication: "write",
  geofence: "write",
  route: "write",
  driver: "write",
  geoHistory: "write",
  geoThing: "write",
  geoProduct: "write",
  listners: "write",
  escalations: "write",
  notificationTemplates: "write",
  notificationHistory: "write",
  contacts: "write",
  whiteLabel: "write",
  stats: "read",
  appUser: "write",
  sites: "write",
  dashboard: "write",
  reports: "write"
};

const useUserGroupPermissions = () => {
  const groupId = useAppSelector(({ user }) => user.user?.userGroupId || "");
  const role = useAppSelector(({ user }) => user.user?.role || "");

  const userType = useMemo(() => getUserType(role), [role]);

  return useQuery({
    queryKey: ["user-group-permissions", userType, groupId],
    initialData: DEFAULT_PERMISSIONS,
    enabled: Boolean(groupId),
    queryFn: async () => {
      try {
        const res = await fetchUserGroupDetail({
          queryKey: ["user-group-details", userType, groupId]
        });

        const { authorizationData } = res.data.groupData;

        const { things, inventory } = authorizationData["thing-manager-api"] || {};
        const { products } = authorizationData["product-manager-api"] || {};
        const { ota } = authorizationData["ota-api"] || {};
        const {
          notification: { templates = "none", history = "none" } = {},
          contacts = "none",
          "escalation-groups": escalations = "none"
        } = authorizationData["notification-manager-api"] || {};
        const { destinations = "none", rules = "none" } =
          authorizationData["integrations-api"]?.brk || {};
        const { stats, dashboard, site, reports } = authorizationData["analytics-api"] || {};
        const {
          tenants: mspAcc = "none",
          key: mspKey = "none",
          users: mspUSers = "none"
        } = authorizationData["enterprise-manager-api"]?.msp || {};

        const {
          acc: tntAcc = "none",
          key: tntKey = "none",
          users: tntUsers = "none"
        } = authorizationData["enterprise-manager-api"]?.tnt || {};
        const { "white-label": whiteLabel } = authorizationData["enterprise-manager-api"] || {};
        const { portal: incidents } = authorizationData["incidents-api"] || {};
        const { tls, authentication, thingGroup, policy, listners } =
          authorizationData["fleet-manager-api"] || {};
        const {
          geofence,
          route,
          driver,
          history: geoHistory
        } = authorizationData["one-track-api"] || {};

        const result: UserGroupPermissions = {
          thing: things || "none",
          inventory: inventory || "none",
          product: products || "none",
          ota: ota || "none",
          contacts: contacts || "none",
          whiteLabel: whiteLabel || "none",
          stats: stats || "none",
          escalations: escalations || "none",
          notificationTemplates: templates || "none",
          notificationHistory: history || "none",
          destinations: destinations || "none",
          rules: rules || "none",
          thingGroup: thingGroup || "none",
          policy: policy || "none",
          tls: tls || "none",
          incidents: incidents || "none",
          mspAcc: mspAcc || "none",
          mspKey: mspKey || "none",
          mspUSers: mspUSers || "none",
          tntAcc: tntAcc || "none",
          tntKey: tntKey || "none",
          tntUsers: tntUsers || "none",
          authentication: authentication || "none",
          geofence: geofence || "none",
          geoThing: things || "none", // we have migrated geoThing to TM
          geoProduct: products || "none", // we have migrated geoProduct to PM
          listners: listners || "none",
          sites: site || "write",
          route: route || "none",
          geoHistory: geoHistory || "none",
          driver: driver || "none",
          dashboard: dashboard || "write",
          reports: reports || "write"
        };

        if (MODE === "dev" || MODE === "development") {
          result.appUser = "write";
        }
        return result;
      } catch (error) {
        console.log(error);
        return DEFAULT_PERMISSIONS;
      }
    },
    staleTime(query) {
      const status = query.state.status;
      const lastFetched = query.state.dataUpdatedAt;
      const fetchCount = query.state.dataUpdateCount;

      if (fetchCount === 0) {
        return undefined;
      }

      if (status !== "success") {
        return undefined;
      }

      const timePasses = Date.now() - lastFetched;

      if (timePasses > 1000 * 60 * 10) {
        return undefined;
      }

      return 1000 * 60 * 10;
    }
  });
};

export default useUserGroupPermissions;
