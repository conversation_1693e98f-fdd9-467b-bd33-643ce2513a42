import { ClassicProductListItem } from "@/index";
import { PRODUCT_URL } from "@api/index";
import { fetchApi, getUrlParams } from "@frontend/shared/utils/httpUtils";
import { useQuery } from "@tanstack/react-query";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";

const useProductList = ({
  enabled = true,
  limit = DEFAULT_PAGE_COUNT,
  search = "",
  page = 1,
  templateName = "",
  inventoryEnabled,
  productType
}: {
  enabled?: boolean;
  limit?: number;
  search?: string;
  page?: number;
  templateName?: string;
  inventoryEnabled?: boolean;
  productType?: string;
}) => {
  return useQuery({
    queryKey: ["product-list", page, limit, search, templateName, inventoryEnabled,productType],
    enabled,
    queryFn: async () => {
      // const resp = await fetchProjects({ page, limit, search });
      const resp = await fetchApi(
        `/products?${getUrlParams({ page, limit, search, templateName, inventoryEnabled, productType })}`,
        {},
        PRODUCT_URL
      );

      const data = await resp.json();

      return data.data as {
        productList: ClassicProductListItem[];
        pages: number;
      };
    }
  });
};

export default useProductList;
