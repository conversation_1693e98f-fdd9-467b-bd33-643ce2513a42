import { create } from "zustand";

interface Customization {
  brandColor: string;
  darkBg: string;
  accentColor: string;
  buttonText: string;
  logoLight: string | null;
  logoDark: string | null;
  host: string;
  description: string;
  id?: number;
  title: string;
  product_alias: string;
  thing_alias: string;
  verificationCode?: string;
}

type UseCustomizations = {
  customization: Customization;
  setCustomization: (customization: Partial<Customization>) => void;
  status: "pending" | "approved" | "demo" | undefined;
  setStatus: (status: "pending" | "approved" | "demo") => void;
};

export const DEFAULT_WHITE_LABEL_CUSTOMIZATION: Customization = {
  brandColor: "#00e6c3",
  darkBg: "#1c2e45",
  accentColor: "#68d8d6",
  buttonText: "#000000",
  logoDark: null,
  logoLight: null,
  host: "",
  description: "example description",
  title: "OneIoT Platform",
  product_alias: "Product",
  thing_alias: "Thing"
};

const useCustomizations = create<UseCustomizations>((set) => ({
  customization: DEFAULT_WHITE_LABEL_CUSTOMIZATION,
  setCustomization: (customization: Partial<Customization>) => {
    set((state) => ({ customization: { ...state.customization, ...customization } }));
  },
  status: undefined,
  setStatus: (status: "pending" | "approved" | "demo") => {
    set({ status });
  }
}));

export default useCustomizations;
