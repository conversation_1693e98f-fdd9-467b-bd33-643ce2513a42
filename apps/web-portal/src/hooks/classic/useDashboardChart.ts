import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";

const useDashboardChart = ({
  duration,
  enabled = true
}: {
  duration: string;
  enabled?: boolean;
}) => {
  return useQuery({
    queryKey: ["home-dashboard-chart", duration],
    enabled,
    queryFn: async () => {
      const resp = await fetchApi(
        `/stats/metric/broker-messages?${getUrlParams({
          duration
        })}`,
        {},
        STATS_URL
      );
      const data = await resp.json();
      const payload = data.data;
      const keysToSum = ["delivered_resp", "dropped_resp", "publish_resp", "sent_resp"];
      const seriesData = payload.processedResults;
      const totalSum = keysToSum.reduce((acc, key) => {
        if (seriesData[key] && seriesData[key].values) {
          return (
            acc +
            seriesData[key].values.reduce((innerAcc: number, val: number) => innerAcc + val, 0)
          );
        }
        return acc;
      }, 0);

      if (resp.ok) {
        return {
          timestamp: payload.timestamp.map((item) => item * 1000),
          total: totalSum,
          processedResults: payload.processedResults
        } as {
          timestamp: number[];
          total: number;
          processedResults: Record<
            string,
            {
              values: number[];
              metric: Record<string, string>;
            }
          >;
        };
      }
      throw new Error(data.message);
    }
  });
};
export default useDashboardChart;
