import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import {
  convetUTCToLocal,
  getDateFromDurationString
} from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { getPartnerAndFeature } from "@utils/url";

type keyUnions =
  | "publish_resp"
  | "sent_resp"
  | "delivered_resp"
  | "dropped_resp"
  | "bytes_received_resp"
  | "bytes_sent_resp"
  | "rules_success_resp"
  | "rules_failed_resp"
  | "rules_total_resp"
  | "rules_exec_success_resp"
  | "rules_exec_failed_resp"
  | "rules_exec_total_resp"
  | "rules_discarded_resp";

const useUsageChartData = ({
  duration,
  enabled = true
}: {
  duration: string;
  enabled?: boolean;
}) => {
  const { partnerName } = getPartnerAndFeature();

  return useQuery({
    queryKey: ["usage-chart-data", duration, partnerName],
    enabled,
    queryFn: async () => {
      const { startDate, endDate } = getDateFromDurationString(duration);

      const url = new URL(`${STATS_URL}/stats/metric/broker-messages`);

      url.searchParams.append("start", startDate);
      url.searchParams.append("end", endDate);

      if (partnerName) {
        url.searchParams.append("tenant", partnerName);
      }

      const resp = await fetchApi(url.toString(), {}, "");

      const data = await resp.json();

      if (resp.ok) {
        const keysToSum: keyUnions[] = [
          "delivered_resp",
          "dropped_resp",
          "publish_resp",
          "sent_resp"
        ];
        const seriesData = data.data.processedResults as Record<
          keyUnions,
          { metric: unknown; values: number[] }
        >;
        const totalSum = keysToSum.reduce((acc, key) => {
          if (seriesData[key] && seriesData[key].values) {
            return acc + seriesData[key].values.reduce((innerAcc, val) => innerAcc + val, 0);
          }
          return acc;
        }, 0);
        return {
          series: seriesData,
          loading: false,
          labels: data.data.timestamp.map((item: any) => convetUTCToLocal(item * 1000)),
          total: totalSum
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useUsageChartData;
