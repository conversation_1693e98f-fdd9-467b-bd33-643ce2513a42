import { fetchApi } from "@api/_helpers";
import { IOT_MESSAGE } from "@api/index";
import { useQuery } from "@tanstack/react-query";

const useThingShadow = ({
  thingName,
  enabled = true,
  retryCount = 3,
  nested = true
}: {
  thingName: string;
  enabled?: boolean;
  retryCount?: number;
  nested?: boolean;
}) => {
  return useQuery({
    queryKey: ["thing-shadow", thingName, nested],
    enabled,
    queryFn: async () => {
      const resp = await fetchApi(
        `/thing-ops/shadow?deviceIds[0]=${thingName}&nested=${nested}`,
        {},
        IOT_MESSAGE
      );

      const res = await resp.json();

      if (res.status === "Success") {
        return res.data as Record<
          string,
          {
            reported: Record<string, any>;
            desired: Record<string, any>;
          }
        >;
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
      throw new Error("Something went wrong");
    },
    retry(failureCount) {
      if (failureCount >= retryCount) {
        return false;
      }
      return true;
    }
  });
};

export default useThingShadow;
