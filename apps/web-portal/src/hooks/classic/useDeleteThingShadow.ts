import { IOT_MESSAGE } from "@api/index";
import { fetchApi } from "@frontend/shared/utils/httpUtils";
import { useMutation, UseMutationOptions } from "@tanstack/react-query";
type Body = {
  thingName: string;
};
const useDeleteThingShadow = ({
  ...options
}: Omit<UseMutationOptions<void, Error, Body>, "mutationFn">) => {
  return useMutation<void, Error, Body>({
    mutationFn: async ({ thingName }) => {
      const fetchResponse = await fetchApi(
        `/thing-ops/shadow/${thingName}`,
        { method: "DELETE" },
        IOT_MESSAGE
      );
      const res = await fetchResponse.json();
      if (res.status === "Success") {
        return;
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
      throw new Error(res.message);
    },
    ...options
  });
};

export default useDeleteThingShadow;
