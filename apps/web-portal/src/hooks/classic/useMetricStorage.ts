import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import { getUrlParams } from "@frontend/shared/utils/httpUtils";
import { useQuery } from "@tanstack/react-query";

const useMetricStorage = ({ tenant }: { tenant?: string }) => {
  return useQuery({
    queryKey: ["metric-storage", tenant],
    queryFn: async () => {
      const resp = await fetchApi(
        `/stats/metric/storage?${getUrlParams({ tenant })}`,
        {},
        STATS_URL
      );
      const data = await resp.json();
      if (data.status === "Success") {
        return data.data as {
          logsIndexSizeInMB: string;
          timeSeriesTableStats: number;
          timeSeriesTableRowCount: string;
          logsRetentionPeriod: string;
          timeSeriesRetentionPeriod: string;
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error(data.message);
    }
  });
};

export default useMetricStorage;
