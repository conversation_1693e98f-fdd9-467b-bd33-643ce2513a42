import { generateRandomId } from "@src/pages/MonitorPage/utils";
import { create } from "zustand";

export type NotificationType = {
  notificationType: "alert" | "system_generated";
  push: boolean;
  email: boolean;
  sms: boolean;
  teams: boolean;
};

type DoNotDisturbSlot = {
  startTime: Date | null;
  endTime: Date | null;
  id: string;
};

type DoNotDisturbStatus = {
  enabled: boolean;
  slots: DoNotDisturbSlot[];
};

export type UpdateContactConfigInput = {
  name: string;
  email: string;
  address: string;
  country: string;
  pincode: number | string | null;
  phone: number | string | null;
  preferences: NotificationType[];
  doNotDisturbStatus?: DoNotDisturbStatus;
};

export type ContactConfigState = {
  name: string;
  email: string;
  address: string;
  country: string;
  pincode: number | string | null;
  phone: number | string | null;
  alertNotificationTypes: NotificationType;
  systemNotificationTypes: NotificationType;
  doNotDisturbStatus: DoNotDisturbStatus;
  resetConfig: () => void;
  updateContactConfig: (data: UpdateContactConfigInput) => void;
  editDetails: (
    key: keyof Omit<ContactConfigState, "resetConfig" | "updateContactConfig" | "editDetails">,
    value: any
  ) => void;
};

// Input type for updateContactConfig

const initialContactConfig = {
  name: "",
  email: "",
  address: "",
  country: "",
  pincode: null,
  phone: null
};

const initialAlertNotificationTypes: NotificationType = {
  notificationType: "alert",
  push: false,
  email: false,
  sms: false,
  teams: false
};

const initialSystemNotificationTypes: NotificationType = {
  notificationType: "system_generated",
  push: false,
  email: false,
  sms: false,
  teams: false
};

const initialDoNotDisturbStatus: DoNotDisturbStatus = {
  enabled: false,
  slots: [{ startTime: null, endTime: null, id: generateRandomId(16) }]
};

const useContactConfig = create<ContactConfigState>((set) => ({
  ...initialContactConfig,
  alertNotificationTypes: initialAlertNotificationTypes,
  systemNotificationTypes: initialSystemNotificationTypes,
  doNotDisturbStatus: initialDoNotDisturbStatus,
  resetConfig: () =>
    set({
      ...initialContactConfig,
      alertNotificationTypes: initialAlertNotificationTypes,
      systemNotificationTypes: initialSystemNotificationTypes,
      doNotDisturbStatus: initialDoNotDisturbStatus
    }),
  updateContactConfig: (data) =>
    set(() => {
      const formattedDoNotDisturbStatus = {
        enabled: data.doNotDisturbStatus?.enabled ?? false,
        slots: data.doNotDisturbStatus?.enabled
          ? data.doNotDisturbStatus?.slots?.map((slot) => ({
              startTime: slot.startTime ? new Date(slot.startTime) : null,
              endTime: slot.endTime ? new Date(slot.endTime) : null,
              id: slot.id
            }))
          : []
      };

      return {
        name: data.name,
        email: data.email,
        address: data.address,
        country: data.country,
        pincode: data.pincode,
        phone: data.phone,
        alertNotificationTypes: data.preferences.find((item) => item.notificationType === "alert"),
        systemNotificationTypes: data.preferences.find(
          (item) => item.notificationType === "system_generated"
        ),
        doNotDisturbStatus: formattedDoNotDisturbStatus
      };
    }),
  editDetails: (key, value) =>
    set(() => ({
      [key]: value
    })) // make this dumb function
}));

export default useContactConfig;
