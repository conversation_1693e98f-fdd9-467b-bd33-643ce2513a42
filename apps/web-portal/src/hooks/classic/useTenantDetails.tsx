import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

const useTenantDetails = () => {
  const { partnerName, featureType } = useParams();
  const isTenantView = Boolean(partnerName) && Boolean(featureType);

  return useQuery({
    queryKey: ["tenant-details", partnerName],
    enabled: isTenantView,
    queryFn: async () => {
      const resp = await fetchApi(`/msp/tenants/acc/${partnerName}`, {});

      const data = await resp.json();
      if (resp.ok) {
        return data.data as {
          mspAccess: string;
          operatorAccess: string;
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useTenantDetails;
