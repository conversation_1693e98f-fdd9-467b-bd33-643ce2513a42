import { fetchApi } from "@api/_helpers";
import { PRODUCT_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";

const useProductTemplateDevices = ({
  searchQuery,
  enabled = true
}: {
  searchQuery: string;
  enabled?: boolean;
}) => {
  return useQuery({
    queryKey: ["product-template-devices", searchQuery],
    enabled,
    queryFn: async () => {
      const fetchResponse = await fetchApi(
        `/product-templates/gitlab?search=${searchQuery}`,
        {},
        PRODUCT_URL
      );
      const data = await fetchResponse.json();

      if (data.status !== "Success") {
        throw new Error(data.message);
      }

      if (!fetchResponse.ok) {
        throw new Error(data.message);
      }

      return data.data as {
        projects: { name: string; id: number }[];
        next: number;
      };
    }
  });
};

export default useProductTemplateDevices;
