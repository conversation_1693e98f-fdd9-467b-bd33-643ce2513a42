import { fetchApi } from "@api/_helpers";
import { THING_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";

const useThingInventory = ({
  enabled = true,
  limit = DEFAULT_PAGE_COUNT,
  searchQuery = "",
  page = 1,
  productName,
  filter
}: {
  enabled?: boolean;
  limit?: number;
  searchQuery?: string;
  page?: number;
  productName?: string;
  filter?: string;
}) => {
  return useQuery({
    queryKey: ["inventory-thing-list", page, limit, searchQuery, productName, filter],
    enabled,
    queryFn: async () => {
      const fetchResponse = await fetchApi(
        `/inventory?${getUrlParams({
          page,
          limit,
          search: searchQuery,
          productName,
          status: filter
        })}`,
        {},
        THING_URL
      );
      const data = await fetchResponse.json();

      if (data.status !== "Success") {
        throw new Error(data.message);
      }

      if (!fetchResponse.ok) {
        throw new Error(data.message);
      }

      return data.data as {
        things: {
          thingName: string;
          productName: string;
          registered: boolean;
          createdAt: string;
          status: "active" | "pending_activation" | "expired";
          mfgDate: string;
        }[];
        pages: number;
        counts: {
          registered: number;
          notRegistered: number;
          total: number;
        };
      };
    }
  });
};

export default useThingInventory;
