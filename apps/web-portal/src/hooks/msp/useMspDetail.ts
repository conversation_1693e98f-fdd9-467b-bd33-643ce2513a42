import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";

export type MspDetailsType = {
  address: string;
  canCreateTenants: "true" | "false"; // Can be boolean or string
  createdAt: string;
  email: string;
  mspFullName: string;
  mspId: string;
  phone: string;
  pincode: string;
  tenants: string[];
  updatedAt: string;
  website: string;
  access?: string;
};

export type TenantDetailsType = {
  createdAt: string;
  email: string;
  name: string;
  updatedAt: string;
};

const useMspDetail = (mspId: string) => {
  return useQuery({
    queryKey: ["msp-details", mspId],
    queryFn: async () => {
      const resp = await fetchApi(`/operator/msp/${mspId}`);

      const data = await resp.json();
      if (data.status == "Success") {
        return data.data as {
          mspDetails: MspDetailsType;
          tenantDetails: TenantDetailsType[];
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    },
    enabled: !!mspId
  });
};

export default useMspDetail;
