import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";
import {Users } from "@src/features/features";


const useMspUserDetail= (userEmail:string) => {
  return useQuery({
     queryKey: ["msp-details", userEmail, "msp"],
    queryFn: async () => {
      const  resp = await fetchApi(`/msp/users/user?email=${userEmail}`);

      const data = await resp?.json();
      if (data.status == "Success") {
        return data.data as Users;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useMspUserDetail;
