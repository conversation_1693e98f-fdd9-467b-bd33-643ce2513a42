import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";
import {MspFormData } from "@src/features/features";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { getUrlParams } from "@utils/url";

const useMspList = ({ page = 1, limit= DEFAULT_PAGE_COUNT,search}:{search:string,page : number,limit:number}) => {
  return useQuery({
    queryKey: ["msp-list", page, limit, search],
    queryFn: async () => {
       const resp = await fetchApi(
          `/operator/msp/list?${getUrlParams({
            page,
            limit
          })}&search=${search}`
        );
      
        const data = await resp.json();
      if (data.status == "Success") {
        return data.data as {
          mspsList: MspFormData[];
          pages: number;
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useMspList;
