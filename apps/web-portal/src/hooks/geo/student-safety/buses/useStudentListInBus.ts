import { fetchApi } from "@api/_helpers";
import { GEO_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";

const useStudentListInBus = ({
  busName,
  enabled = true,
  search
}: {
  busName: string;
  enabled?: boolean;
  search?: string;
}) => {
  return useQuery({
    queryKey: ["student-list-in-bus", busName, search],
    enabled: Boolean(enabled && busName),
    queryFn: async () => {
      const url = new URL(`${GEO_URL}/student-safety/bus/student/${busName}`);
      if (search) url.searchParams.set("search", search);

      const fetchResponse = await fetchApi(url.toString(), {}, "");

      const res = await fetchResponse.json();
      if (res.status === "Success") {
        return res.data as {
          students: {
            studentId: string;
            studentName: string;
            imgURL: string;
            parentContact: string;
            gender: string;
            studentClass: string;
            stop: string | null;
            routeId: number | null;
            departure: string;
          }[];
        };
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useStudentListInBus;
