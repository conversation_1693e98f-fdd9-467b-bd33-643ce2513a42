import { fetchApi } from "@api/_helpers";
import { GEO_URL, THING_URL } from "@api/index";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";

const useDeleteBulkCreateEntry = (callback = () => {}) => {
  return useMutation({
    mutationFn: async (docId: string) => {
      const resp = await fetchApi(
        `/things/bulk/delete/${docId}`,
        {
          method: "DELETE"
        },
        THING_URL
      );

      if (resp.ok) {
        return true;
      }
      const errorMsg = await resp.json();
      throw new Error(errorMsg.message);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bulk-stats"] });
      queryClient.invalidateQueries({ queryKey: ["bulk-update-going-on"] });
      showSuccessToast("Bulk entry deleted successfully");
      callback();
      return true;
    },
    onError: (error) => {
      showErrorToast(error.message);
      return false;
    }
  });
};

export default useDeleteBulkCreateEntry;
