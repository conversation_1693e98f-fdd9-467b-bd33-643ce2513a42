import { fetchApi } from "@api/_helpers";
import { THING_URL } from "@api/index";

import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";

const createBulkThing = async (file: FormData) => {
  const resp = await fetchApi(
    `/things/bulk?geo=true`,
    {
      method: "POST",

      body: file
    },
    THING_URL
  );

  const data = await resp.json();
  if (!resp.ok) {
    throw new Error(data.message);
  }

  return data as { message: string; data: { docName: string }[] };
};

const useGeoBulkCreate = ({
  onSuccess = () => {},
  onError = () => {}
}: {
  onSuccess?: () => void;
  onError?: () => void;
}) => {
  return useMutation({
    mutationFn: createBulkThing,
    onSuccess: (data) => {
      // refetchGeoFence();
      showSuccessToast(data.message);
      onSuccess(data);
    },
    onError: (error) => {
      showErrorToast(error.message);
      onError(error);
    }
  });
};

export default useGeoBulkCreate;
