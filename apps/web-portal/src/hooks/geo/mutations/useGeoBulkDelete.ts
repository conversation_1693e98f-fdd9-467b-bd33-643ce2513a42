import { fetchApi } from "@api/_helpers";
import { GEO_URL, THING_URL } from "@api/index";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";

const useGeoBulkDelete = ({
  onSuccess,
  onError
}: {
  onSuccess?: (value: any) => void;
  onError?: () => void;
}) => {
  return useMutation({
    mutationFn: async (data: { thingName: string; productName: string }[]) => {
      const url = new URL(`${THING_URL}/things/bulk?geo=true`);

      // if (!Array.isArray(data)) {
      //   throw new Error("Data should be an array");
      // }

      // for (const thingName of data) {
      //   url.searchParams.append("thingNames", thingName);
      // }

      const fetchResponse = await fetchApi(
        url.toString(),
        {
          method: "DELETE",
          body: { things: data }
        },
        ""
      );

      const res = await fetchResponse.json();
      if (fetchResponse.ok) {
        return res.data;
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
      throw new Error("Something went wrong");
    },
    onSuccess: (val) => {
      showSuccessToast("Things deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["geo-thing-list"] });
      onSuccess?.(val);
    },
    onError: (error) => {
      showErrorToast(error.message);
      onError?.();
    }
  });
};

export default useGeoBulkDelete;
