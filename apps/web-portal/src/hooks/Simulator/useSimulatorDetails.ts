import { fetchApi } from "@api/_helpers";
import { SIMULATOR_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";

export type ParametersType = {
  name: string;
    id : string;
    max: number;
    min:number;
    type:string;
    values:string[]; 
}

type SimulatorDetails = {
    name : string;
    description:string;
    parameters:ParametersType[];

}
const useSimulatorDetails = ({templateName}:{templateName:string|undefined}) => {
  return useQuery({
     queryKey: ["simulator-template-detial", templateName],
      queryFn:  async () => {
        const response = await fetchApi(
          `/simulator/availableSimulators/${templateName}`,
          {},
          SIMULATOR_URL,
        ); 
           const data  = await response.json();
            if (data.status === "Success") {
               return data.data as SimulatorDetails;
                }
            if (data.status === "Failure") {
                throw new Error(data.message);
                }
                throw new Error(data.message);  
            }
    })

};
export default useSimulatorDetails;