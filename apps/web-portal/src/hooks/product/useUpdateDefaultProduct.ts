import { PRODUCT_URL } from "@api/index";
import { fetchApi } from "@frontend/shared/utils/httpUtils";
import { useMutation, UseMutationOptions } from "@tanstack/react-query";
import { showErrorToast } from "@utils/index";

type Body = {
  productName: string;
  body: any;
};

const useUpdateDefaultProduct = ({
  ...options
}: Omit<UseMutationOptions<void, Error, Body>, "mutationFn">) => {
  return useMutation<void, Error, Body>({
    mutationFn: async ({ productName, body }) => {
      const resp = await fetchApi(
        `/products/${productName}`,
        { method: "PATCH", body },
        PRODUCT_URL
      );
      const data = await resp.json();
      if (data.status === "Success") {
        return;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    },
    onError: (error) => {
      showErrorToast(error.message);
    },
    ...options
  });
};

export default useUpdateDefaultProduct;
