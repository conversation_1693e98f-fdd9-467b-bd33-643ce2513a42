import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";

const useUserApiList = ({
  page = 1,
  limit = 50,
  search,
  type = "tnt"
}: {
  page?: number;
  limit?: number;
  search?: string;
  type?: "tnt" | "msp";
}) => {
  return useQuery({
    queryKey: ["user-api-list", page, limit, search, type],
    queryFn: async () => {
      const resp = await fetchApi(`/${type}/key/list?${getUrlParams({ page, limit, search })}`);
      const data = await resp.json();

      if (data.status == "Success") {
        return data.data as {
          apiKeys: {
            keyId: string;
            createdAt: string;
            role: string;
            description: string;
            email: string;
            encryptedKey:string;
            tokenName:string;
            keyAuthorization: {
              updatedAuthData: {
                [key: string]: string;
              };
            };
          }[];
          pages: number;
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useUserApiList;
