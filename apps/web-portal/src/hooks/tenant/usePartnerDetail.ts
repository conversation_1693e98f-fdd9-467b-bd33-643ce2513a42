import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";

export type TenantDetails = {
  accountType: string;
  address: string;
  createdAt: string;
  defaultDashboardViewId: number | null;
  defaultSiteViewId: number | null;
  email: string;
  featureType: string;
  homePageView: {
    id: number;
    viewType: string;
  };
  invoiceControl: string;
  mspAccess: string;
  mspId: string;
  name: string;
  operatorAccess: string;
  phone: string;
  pincode: string;
  setupStatus: string;
  stripeCustomerId: string;
  subFeature: string;
  subsIdArr: string[];
  subscriptionType: string;
  tenantId: number;
  tenantName: string;
  updatedAt: string; // ISO date string
  website: string;
};

const usePartnerDetail = (partnerName:string) => {
  return useQuery({
    queryKey: [`tenant-details-${partnerName}`, partnerName],
    queryFn: async () => {
        const resp = await fetchApi(`/msp/tenants/acc/${partnerName}`);
      
        const data = await resp.json();
      if (data.status == "Success") {
        return data.data as TenantDetails;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default usePartnerDetail;
