import { fetchApi } from "@api/_helpers";
import { Users } from "@src/features/features";
import { useQuery } from "@tanstack/react-query";

const useAllUserDetails = (email:string, type:string) => {
  return useQuery({
    queryKey: ["msp-details", email, type],
    queryFn: async () => {
      let resp;
      if (type === "operator") {
        resp = await fetchApi(`/operator/users?email=${email}`);
      }
      if (type === "msp") {
        resp = await fetchApi(`/msp/users/user?email=${email}`);
      }
      if (type === "tenant") {
        resp = await fetchApi(`/tnt/users/user?email=${email}`);
      }
      
      const data = await resp?.json();


      if (data.status == "Success") {
        return data.data as Users;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    },
    enabled: !!email && !!type,
  });
};

export default useAllUserDetails;