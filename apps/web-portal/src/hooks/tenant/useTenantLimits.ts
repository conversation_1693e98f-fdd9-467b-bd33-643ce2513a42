import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";

export type TenantDetails = {
  id: string;
  msp: string;
  tenant: string;
  email: string;
  phone: string;
  credentialsExpiration: string | undefined;
  expiration: Date;
  productsCount: number;
  productsLimit: number;
  thingsCount: number;
  thingsLimit: number;
};

const useTenantLimits = (partnerName: string, type: string) => {
  return useQuery({
    queryKey: [`tenantLimits`, partnerName, type],
    queryFn: async () => {
      let resp;

      if (type === "operator") {
        resp = await fetchApi(`/operator/tenants/limits/${partnerName}`);
      }
      if (type === "msp") {
        resp = await fetchApi(`/msp/tenants/limits/${partnerName}`);
      }

      const data = await resp?.json();
      if (data.status == "Success") {
        return data.data as TenantDetails;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useTenantLimits;
