import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";
import { FeatureItem, Type } from "@src/features/features";


const useAccessTemplateList = ({ type = "tenant" }: { type:Type }) => {
  return useQuery({
    queryKey: ["access-template-list", type],
    queryFn: async () => {
      const endUrl = type === "tenant" ? "/tnt/access-templates" : "/access-template/list";
      const resp = await fetchApi(endUrl);
      const data = await resp.json();
      if (data.status == "Success") {
        return data as {
          data: FeatureItem[];
          pages: number;
        };
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useAccessTemplateList;
