import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";

const useTenantAccessList = ({page = 1, limit = DEFAULT_PAGE_COUNT, view = "msp"}) => {
  return useQuery({
   queryKey: [`tenant-access-list`, page, limit, view],
    queryFn: async () => {
    
          const resp = await fetchApi(
            `/msp/access-templates/list?${getUrlParams({ page, limit,view })}`
          );
          const data = await resp.json();
        
      if (data.status == "Success") {
        return data.data;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
    }
  });
};

export default useTenantAccessList;