import { fetchApi } from "@api/_helpers";
import { useQuery } from "@tanstack/react-query"


const useTenantAccessDetails = (templateId :string) => {
    return useQuery({
        queryKey: ["tenant-access-details",templateId],
        queryFn: async () => {
            const response = await fetchApi(`/msp/access-templates/${templateId}`);

            const data =  await response.json();
            
         if (data.status == "Success") {
        return data.data;
      }
      if (data.status === "Failure") {
        throw new Error(data.message);
      }
      throw new Error("Something went wrong");
        }
    })
}
export default useTenantAccessDetails;