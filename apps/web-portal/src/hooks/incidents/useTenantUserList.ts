import { fetchApi } from "@api/_helpers";
import { DEFAULT_PAGE_COUNT } from "@frontend/shared/config/defaults";
import { getUrlParams } from "@frontend/shared/utils/httpUtils";
import { useQuery } from "@tanstack/react-query";
import { Users } from "@src/features/features";

export type TenantUserList = {
  pages : number;
  users : Users[];
}

const UseTenantUserList = ({page=1,limit= DEFAULT_PAGE_COUNT,search=""}) => {
  return useQuery({
    queryKey: ["tenant-users-list", 1, 25, search],
    queryFn: async () => {
            const response = await fetchApi(
                `/tnt/users/list?search=${search}&${getUrlParams({
                  page,
                  limit,
                  onlyTenantUsers: true
                })}`
            )
            const data  = await response.json();
            if (data.status === "Success") {
               return data.data as TenantUserList;
                }
            if (data.status === "Failure") {
                throw new Error(data.message);
                }
                throw new Error(data.message);
        },  
    })
};
export default UseTenantUserList;