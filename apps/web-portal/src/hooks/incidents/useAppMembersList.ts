import { fetchApi } from "@api/_helpers";
import { APP_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query";


type AppMembersList = {
  appUserList : {
  id: string;
  email: string;
  name: string;
  pincode: string;
  address: string;
  contactNumber: string;
  msp: string;
  tenant: string;
  identityID: string | null;
  createdAt: string; 
  updatedAt: string;
}[]
}

const useAppMembersList = () => {
  return useQuery({
    queryKey: ["app-members-list"],
      queryFn:  async () => {
            const response = await fetchApi(`/members/app`, {}, APP_URL);
            
            const data  = await response.json();
            if (data.status === "Success") {
               return data.data as AppMembersList;
                }
            if (data.status === "Failure") {
                throw new Error(data.message);
                }
                throw new Error(data.message);
        },  
    })
};
export default useAppMembersList;