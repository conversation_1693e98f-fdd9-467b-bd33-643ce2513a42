import { fetchApi } from "@api/_helpers";
import { NEW_INCIDENT_URL } from "@api/index";
import { useQuery } from "@tanstack/react-query"

 export type ChecklistItem = {
  id: string;
  item: string;
  order: number;
  isDone: boolean;
  notes: string;
};
export type Summary = {
  timestamp: string;
  key: string;
  value: string;
};

export type TicketData = {
  id: string;
  type: string;
  title: string;
  priority: string;
  status: string;
  thingName: string;
  category: string;
  description: string;
  tenant: string;
  msp: string;
  triggerRuleId: string | null;
  triggerCount: number;
  createdAt: string;
  updatedAt: string;
  assignee: string;
  attachments: string[];
  tags: string[];
  checklistPattern: string; 
  checklist: ChecklistItem[];
  recurring: string; 
  recurringPattern: string;
  scheduledStartDate: string;
  scheduledEndDate: string;
    reportedDate?: string;
  lastUpdatedDate?: string;
  summary?: Summary;
  snapshots?: Record<string, string>;
};



const useIncidentTicketDetails = (ticketId:string)=>{
    return useQuery ({
        queryKey : ["incident-ticket", ticketId],
        queryFn : async()=>{
            const response = await fetchApi(`/incidents/${ticketId}`,
                 {},
                 NEW_INCIDENT_URL);
        
      const data  = await response.json();
            if (data.status === "Success") {
               return data.data as TicketData;
                }
            if (data.status === "Failure") {
                throw new Error(data.message);
                }
                throw new Error(data.message);
            }
    })
};

export default  useIncidentTicketDetails;