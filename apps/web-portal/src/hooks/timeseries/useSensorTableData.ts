import { DashboardGraphItem } from "@/index";
import { fetchApi } from "@api/_helpers";
import { STATS_URL } from "@api/index";
import {
  concatUrlWithTenantQueryParam,
  getDateFromDurationString
} from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useInfiniteQuery } from "@tanstack/react-query";
import { ColumnSort } from "@tanstack/react-table";

const useSensorTableData = ({
  filters,
  duration,
  enabled = true,
  sort,
  isGeoTenant,
  assetFields,
  limit
}: {
  filters?: DashboardGraphItem["filters"];
  duration: string;
  enabled?: boolean;
  limit?: number;
  sort?: ColumnSort[];
  isGeoTenant?: boolean;
  assetFields?: number[];
}) => {
  const queryKeys = ["sensor-table-infinite", duration];

  const enabledFilters =
    filters?.filter(
      (item) => item.enabled && item.field !== "operations" && item.field !== "payload.field"
    ) || [];

  const filterString = enabledFilters
    .map((item) => {
      return item.field.concat(item.operation).concat(item.value.join());
    })
    .join("");

  queryKeys.push(filterString);

  if (sort) {
    queryKeys.push(JSON.stringify(sort));
  }

  if (assetFields && assetFields.length > 0) {
    queryKeys.push(assetFields.toSorted().join(","));
  }

  return useInfiniteQuery({
    queryKey: queryKeys,
    enabled,
    queryFn: async ({ pageParam }) => {
      const url = new URL(`${STATS_URL}/stats/ts/table`);

      const { startDate, endDate } = getDateFromDurationString(duration);
      if (enabledFilters?.length > 0) {
        url.searchParams.append("filters", JSON.stringify(enabledFilters));
      }

      concatUrlWithTenantQueryParam(url);

      url.searchParams.append("from", startDate);
      url.searchParams.append("to", endDate);

      if (assetFields) {
        assetFields.forEach((item) => {
          url.searchParams.append("assetField", item.toString());
        });
      }

      if (isGeoTenant) {
        url.searchParams.append("includeLatLong", "true");
      }
      if (limit) {
        url.searchParams.append("limit", limit.toString());
      }

      if (sort) {
        url.searchParams.append("sort", JSON.stringify(sort));
      }

      if (pageParam) {
        url.searchParams.append("page", pageParam.toString());
      }

      const fetchResponse = await fetchApi(url.toString(), {}, "");
      const data = await fetchResponse.json();
      if (!fetchResponse.ok) {
        throw new Error(data.message);
      }
      return data.data as {
        data: {
          uniqueId: number;
          timestamp: string;
          value: string;
          key: string;
          thingName: string;
          productName: string;
          payload: Record<string, string>;
        }[];
        page: number;
        limit: number;
      };
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => (lastPage?.page || 0) + 1
  });
};

export default useSensorTableData;
