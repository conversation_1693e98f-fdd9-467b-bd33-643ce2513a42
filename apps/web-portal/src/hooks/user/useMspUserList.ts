import { fetchApi } from "@api/_helpers";
import { DEFAULT_PAGE_COUNT } from "@frontend/shared/config/defaults";
import { getUrlParams } from "@frontend/shared/utils/httpUtils";
import { Contact } from "@src/features/features";
import { useQuery } from "@tanstack/react-query";

type User = {
  name: string;
  email: string;
  role: "TenantAdmin" | "TenantSuperUser" | "TenantSupport" | "MSPAdmin";
  address: string;
  phone: string;
  pincode: string;
  userGroup: string;
  monitoringGroups: string[];
  disabled: boolean;
  msp: string;
  tenant: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  userGroupId: string;
  contactId:string;
};

const useMspUserList = ({
  enabled = true,
  search = "",
  page = 1,
  limit = DEFAULT_PAGE_COUNT,
  msp = true
}) => {
  return useQuery({
    queryKey: ["msp-user-list", search, page, limit],
    enabled,
    queryFn: async () => {
      const resp = await fetchApi(
        `/msp/users/list?${getUrlParams({
          search,
          page,
          msp,
          limit
        })}`
      );

      const data = await resp.json();

      if (!resp.ok) {
        throw new Error(data.message);
      }

      return data.data as {
        pages: number;
        users: User[];
      };
    }
  });
};
export default useMspUserList;
