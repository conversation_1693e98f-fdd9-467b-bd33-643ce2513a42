import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import ReactGridLayout from "react-grid-layout";
import { DashboardGraphItem, SavedDashboards } from "../..";
import {
  SIZING_OPTIONS,
  generateRandomString,
  getDateRangeFormatter,
  isChartCircular
} from "../pages/MonitorPage/utils";
import { queryClient } from "../utils/queryClient";
import { store } from "@src/store";

const ALLOW_ZEROS = "@ALLOW_ZEROS";

type GraphDuration = {
  title: string;
  value: string;
};

type InitialState = {
  loading: boolean;
  previousDurations: GraphDuration[];
  graphDuration: GraphDuration;
  dashboardGraphList: DashboardGraphItem[];
  nextLayoutPosition: Record<string, ReactGridLayout.Layout>;
  userDashboards: SavedDashboards[];
  primaryDashboardId: number;
  editingDashboardGraph: string;
  allowZeros: boolean;
};

const getDuration = () => {
  try {
    const saved = JSON.parse(localStorage.getItem("@duration")!);
    if (saved.title === "Today") {
      return {
        title: "Today",
        value: "today"
      };
    }
    return saved;
  } catch (error) {
    return {
      title: "Today",
      value: "today"
    };
  }
};

const getPreviousDurations = () => {
  try {
    const prev = localStorage.getItem("@prevDuration");
    if (prev) {
      return JSON.parse(prev);
    }
    return [];
  } catch (error) {
    console.log(error);

    return [];
  }
};

const initialState: InitialState = {
  loading: true,
  previousDurations: getPreviousDurations(),
  graphDuration: getDuration(),
  dashboardGraphList: [],
  nextLayoutPosition: {},
  userDashboards: [],
  primaryDashboardId: 0,
  editingDashboardGraph: "",
  allowZeros: localStorage.getItem(ALLOW_ZEROS) === "true"
};

/**
 * Redux slice for managing the counter.
 * @type {import('@reduxjs/toolkit').Slice<DashboardBuilderState>}
 */

export const dashboardBuilderSlice = createSlice({
  name: "dashboardBuilder",
  initialState,
  reducers: {
    updateGraphDuration: (state, action: PayloadAction<GraphDuration>) => {
      localStorage.setItem("@duration", JSON.stringify(action.payload));
      const preDurationValue = state.graphDuration.value;
      const prevTitle = state.graphDuration.title;
      state.graphDuration = action.payload;

      const newDuration = state.previousDurations.filter((item) => {
        if (item.title === "custom") {
          return (
            getDateRangeFormatter(item) !==
            getDateRangeFormatter({ title: "", value: preDurationValue })
          );
        }
        return item.value !== preDurationValue;
      });

      if (newDuration.length >= 5) {
        newDuration.pop();
      }

      newDuration.unshift({ title: prevTitle, value: preDurationValue });

      state.previousDurations = newDuration;
      localStorage.setItem("@prevDuration", JSON.stringify(newDuration));
    },
    cloneDashboardWidget: (state, action: PayloadAction<string>) => {
      const widget = state.dashboardGraphList.find((item) => item.id === action.payload);
      if (widget) {
        const nweWidget: DashboardGraphItem = {
          ...widget,
          id: generateRandomString(),
          isOverview: false
        };
        state.dashboardGraphList.push(nweWidget);
      }
    },
    addDashboardGraph: (
      state,
      action: PayloadAction<{ id: string } & Partial<DashboardGraphItem>>
    ) => {
      const nweWidget = {
        ...action.payload
      };

      state.dashboardGraphList.push(nweWidget);
      state.editingDashboardGraph = action.payload.id;
    },

    updateLayout: (state, action) => {
      const positionObject = {};
      action.payload.forEach((item) => {
        positionObject[item.i] = {
          ...item
        };
      });

      state.nextLayoutPosition = positionObject;
    },

    updateDashboardGraph: (
      state,
      action: PayloadAction<{ id: string } & Partial<DashboardGraphItem>>
    ) => {
      state.dashboardGraphList = state.dashboardGraphList.map((item) => {
        if (item.id === action.payload.id) {
          // detect change in size
          if (action.payload.size && item.size !== action.payload.size) {
            const { size } = action.payload;
            const { w: width, h: height } = SIZING_OPTIONS[size];
            const h =
              item.size === "4X" && !["Table", "Map"].includes(item.chartType) ? 10 : height;
            const w = isChartCircular(item.chartType) ? 2 : width;
            state.nextLayoutPosition[item.id] = {
              ...state.nextLayoutPosition[item.id],
              w,
              h
            };
          }
          return {
            ...item,
            expanded: action.payload.filters ? true : item.expanded,
            ...action.payload
          };
        }
        return item;
      });
    },
    removeDashboardGraph: (state, action: PayloadAction<string>) => {
      state.dashboardGraphList = state.dashboardGraphList.filter(
        (item) => item.id !== action.payload
      );
      delete state.nextLayoutPosition[action.payload];
    },
    startBuildDashboard: (state) => {
      queryClient.cancelQueries({ queryKey: [state.primaryDashboardId] });
      state.dashboardGraphList = [];
      state.nextLayoutPosition = {};
    },

    setCurrentDashboard: (state, action) => {
      const dashboardItem = action.payload;
      Object.keys(dashboardItem.layout).forEach((key) => {
        const item = dashboardItem.layout[key];
        if (item.type === "Table") {
          item.minW = 4;
          item.minH = 5;
          return;
        }
        if (!item.minW) {
          item.minW = 1;
        }
        if (!item.minH) {
          item.minH = 4;
        }
      });
      state.nextLayoutPosition = dashboardItem.layout;
      state.dashboardGraphList = dashboardItem.dashboardList;
    },

    setUserDashboardList: (
      state,
      action: PayloadAction<{
        dashboardList: SavedDashboards[];
        dashboardViewId: number | null;
      }>
    ) => {
      state.userDashboards = action.payload.dashboardList;

      if (!state.primaryDashboardId) {
        state.primaryDashboardId =
          action.payload.dashboardViewId || action.payload.dashboardList?.[0]?.id || 0;
      }
    },

    setPrimaryDashboardId(state, action: PayloadAction<number>) {
      state.primaryDashboardId = action.payload;
    },

    removeDashboardFromState(state, action: PayloadAction<number>) {
      state.userDashboards = state.userDashboards.filter((item) => item.id !== action.payload);
      state.primaryDashboardId = 0;
    },
    openDashboardDrawer(state, action) {
      state.editingDashboardGraph = action.payload;
    },
    closeDashboardDrawer(state) {
      state.editingDashboardGraph = "";
    },
    toggleAllowZeros: (state) => {
      localStorage.setItem(ALLOW_ZEROS, `${!state.allowZeros}`);
      state.allowZeros = !state.allowZeros;
    }
  },
  extraReducers: {}
});

export const {
  updateGraphDuration,
  addDashboardGraph,
  removeDashboardGraph,
  updateDashboardGraph,
  startBuildDashboard,
  updateLayout,
  setCurrentDashboard,
  setPrimaryDashboardId,
  removeDashboardFromState,
  openDashboardDrawer,
  closeDashboardDrawer,
  cloneDashboardWidget,
  toggleAllowZeros
} = dashboardBuilderSlice.actions;

export default dashboardBuilderSlice.reducer;
