import { createSlice } from "@reduxjs/toolkit";

const DURATION = "@logger-duration";

interface LoggerState {
  duration: { title: string; value: string };
  selectedItem: { clientId: string[]; event: string[] } | null;
  filter: object;
  selectedId: string;
  type: "mqtt" | "platform";
}

const getDuration = () => {
  try {
    const duration = localStorage.getItem(DURATION);
    if (!duration)
      return {
        title: "Today",
        value: "today"
      };
    const saved = JSON.parse(duration);

    if (saved.title === "Today") {
      return {
        title: "Today",
        value: "today"
      };
    }
    return saved;
  } catch (error) {
    console.error(error);
    return {
      title: "Today",
      value: "today"
    };
  }
};

const initialState: LoggerState = {
  duration: getDuration(),
  type: (localStorage.getItem("@logger-type") as any) || "mqtt",
  filter: {
    clientId: [] as string[],
    event: [] as string[]
  },
  selectedItem: null,
  selectedId: ""
};

export const loggerSlice = createSlice({
  name: "logger",
  initialState,
  reducers: {
    updateLoggerDuration: (state, action) => {
      localStorage.setItem(DURATION, JSON.stringify(action.payload));
      state.duration = action.payload;
    },
    setSelectedLog: (state, action) => {
      state.selectedId = action.payload.id;
      state.selectedItem = action.payload.data;
    },
    setLoggerFilter: (state, action) => {
      state.filter = {
        ...state.filter,
        ...action.payload
      };
    },
    updateLoggerType: (state, action) => {
      state.type = action.payload;
      state.filter = {
        clientId: [],
        event: []
      };
      localStorage.setItem("@logger-type", action.payload);
    }
  }
});

export const { updateLoggerDuration, setSelectedLog, setLoggerFilter, updateLoggerType } =
  loggerSlice.actions;

export default loggerSlice.reducer;
