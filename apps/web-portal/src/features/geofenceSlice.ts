import { PathDetailsResp } from "@onetypes/ontrack";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ExtractSuccessResp, GeofenceListItem } from "../..";

const getTypes = () => {
  try {
    const types = localStorage.getItem("history-types");
    if (types) {
      return JSON.parse(types);
    }
    return ["event", "trip", "stop"];
  } catch (error) {
    return ["event", "trip", "stop"];
  }
};

const initialState = {
  replayState: "pause",
  isGeofenceEdit: null as GeofenceListItem | null,
  selectedGeofence: undefined as GeofenceListItem | undefined,
  mapCurrentPoint: {
    points: [0, 0] as [number, number],
    identifier: "",
    path: "",
    extraInfo: null as unknown as ExtractSuccessResp<PathDetailsResp>["data"][number]
  },
  createGeofence: false,
  clickedMarker: 0,
  geofenceShape: { type: null, coordinates: null },
  historyData: {
    selectedAsset: [],
    types: getTypes()
  },
  mapConfig: {
    center: { lat: 17.40264, lng: 78.40303 },
    zoom: 12
  },
  replayAttributes: [] as string[],
  selectedProduct: null,
  historyTripData: {
    selectedTrip: undefined as { thingName: string; id: string } | undefined,
    replayState: "pause",
    clickedMarker: 0,
    replayAttributes: []
  },
  followedDevice: undefined as
    | {
        thingName: string;
        productName: string;
        lat: number;
        long: number;
        attributes: Record<string, number | boolean | string>;
        course: number;
        altitude: number;
        thingFriendlyName: string;
      }
    | undefined,
  ignoreTrackThreshold: false,
  routeOriginAndDestination: {
    origin: null,
    destination: null
  }
};

const geofenceReducer = createSlice({
  name: "geofence",
  initialState,
  reducers: {
    setReplayState: (state, action) => {
      state.replayState = action.payload;
    },
    setIsGeofenceEdit: (state, action) => {
      if (action.payload === null) {
        state.isGeofenceEdit = action.payload;
      } else {
        state.isGeofenceEdit = { ...(state.isGeofenceEdit || {}), ...action.payload };
      }
    },

    setSelectedGeofence: (state, action) => {
      state.selectedGeofence = action.payload;
    },
    setMapCurrentPoint: (
      state,
      action: PayloadAction<Partial<(typeof initialState)["mapCurrentPoint"]>>
    ) => {
      state.mapCurrentPoint = {
        ...state.mapCurrentPoint,
        ...action.payload,
        identifier: action.payload.identifier || "",
        extraInfo: action.payload.extraInfo || (null as any)
      };
    },
    setMapConfig: (state, action) => {
      state.mapConfig = action.payload;
    },
    setCreateGeofence: (state, action) => {
      state.createGeofence = action.payload;
    },
    setHistoryData: (state, action) => {
      const { types } = action.payload;
      if (types) {
        localStorage.setItem("history-types", JSON.stringify(types));
      }
      state.historyData = { ...state.historyData, ...action.payload };
    },
    setGeofenceShape: (state, action) => {
      state.geofenceShape = action.payload;
    },

    setClickedMarker: (state, action) => {
      state.clickedMarker = action.payload;
    },
    setSelectedProduct: (state, action) => {
      state.selectedProduct = action.payload;
    },
    setHistoryTripData: (state, action) => {
      state.historyTripData = { ...state.historyTripData, ...action.payload };
    },
    updateFollowedDevice: (
      state,
      action: PayloadAction<(typeof initialState)["followedDevice"]>
    ) => {
      state.followedDevice = state.followedDevice
        ? { ...state.followedDevice, ...action.payload }
        : action.payload;
    },
    stopFollowDevice: (state) => {
      state.followedDevice = undefined;
    },
    setReplayAttributes: (state, action) => {
      state.replayAttributes = action.payload;
    },
    resetReplayState: (state) => {
      state.replayState = "pause";
      state.clickedMarker = 0;
    },
    toggleTrackThresholdState: (state) => {
      state.ignoreTrackThreshold = !state.ignoreTrackThreshold;
    },
    setRouteOriginAndDestination: (state, action) => {
      state.routeOriginAndDestination = action.payload;
    }
  }
});

export const {
  setReplayState,
  setIsGeofenceEdit,
  setSelectedGeofence,
  setMapCurrentPoint,
  setMapConfig,
  setCreateGeofence,
  setHistoryData,
  setGeofenceShape,
  setClickedMarker,
  setSelectedProduct,
  setHistoryTripData,
  stopFollowDevice,
  updateFollowedDevice,
  setReplayAttributes,
  resetReplayState,
  toggleTrackThresholdState,
  setRouteOriginAndDestination,
  setAvailableRoutes,
  setSelectedRoute
} = geofenceReducer.actions;

export default geofenceReducer.reducer;
