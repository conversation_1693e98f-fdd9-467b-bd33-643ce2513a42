import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  duration: localStorage.getItem("deviceDetailsDate-duration") || "today",
  dateTitle: localStorage.getItem("deviceDetailsDate-title") || "This Week",
  mapCurrentPoint: { identifier: null, points: [] as [number, number][], extraInfo: null },
  tripReplayData: {
    selectedTrip: undefined as any as
      | {
          id: string;
          thingName: string;
          startTime: string;
          endTime: string;
          endAddress: string;
          startAddress: string;
          distance: number;
          duration: number;
          avgSpeed: number;
          maxSpeed: number;
          startId: number;
          endId: number;
        }
      | undefined,
    replayState: "pause",
    clickedMarker: 0,
    replayAttributes: []
  },
  deviceFullScreenMap: false,
  deviceDetailMinimize: false
};

const geoDeviceReducer = createSlice({
  name: "geoDevice",
  initialState,
  reducers: {
    setDuration: (state, action: { payload: { value: string; title: string } }) => {
      localStorage.setItem(`deviceDetailsDate-duration`, action.payload.value);
      localStorage.setItem(`deviceDetailsDate-title`, action.payload.title);
      state.duration = action.payload.value;
      state.dateTitle = action.payload.title;
    },
    setDateTitle: (state, action) => {
      localStorage.setItem(`deviceDetailsDate-title`, action.payload);
      state.dateTitle = action.payload;
    },
    setMapCurrentPoint: (state, action) => {
      state.mapCurrentPoint = {
        ...state.mapCurrentPoint,
        ...action.payload,
        points: action.payload.points || [],
        identifier: action.payload.identifier || "",
        extraInfo: action.payload.extraInfo || (null as any)
      };
    },
    setTripReplayData: (state, action) => {
      state.tripReplayData = { ...state.tripReplayData, ...action.payload };
    },
    setDeviceFullScreenMap: (state, action) => {
      state.deviceFullScreenMap = action.payload;
    },
    setDeviceDetailMinimize: (state, action) => {
      state.deviceDetailMinimize = action.payload || !state.deviceDetailMinimize;
    }
  }
});

export const {
  setMapCurrentPoint,
  setTripReplayData,
  setDateTitle,
  setDeviceFullScreenMap,
  setDeviceDetailMinimize,
  setDuration
} = geoDeviceReducer.actions;

export default geoDeviceReducer.reducer;
