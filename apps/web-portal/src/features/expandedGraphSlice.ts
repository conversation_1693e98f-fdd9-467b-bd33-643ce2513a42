import { DashboardGraphItem } from "@/index";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type LoggerState = {
  duration: Duration;
  expandedGraph: DashboardGraphItem | null;
};

const initialState: LoggerState = {
  duration: localStorage.getItem("@duration")
    ? JSON.parse(localStorage.getItem("@duration") as any)
    : {
        title: "Last 30 days",
        value: "last_30_days"
      },
  expandedGraph: null
};

export const expandedGraphSlice = createSlice({
  name: "expandedGraph",
  initialState,
  reducers: {
    updateExpandedGraph: (state, action) => {
      if (!state.expandedGraph) {
        state.expandedGraph = action.payload;
        return;
      }
      state.expandedGraph = {
        ...state.expandedGraph,
        ...action.payload
      };
    },
    updateExpandedDuration: (state, action) => {
      state.duration = action.payload;
    },
    setExpandedGraph(state, action: PayloadAction<DashboardGraphItem>) {
      state.expandedGraph = action.payload;
    }
  }
});

export const { updateExpandedGraph, setExpandedGraph, updateExpandedDuration } =
  expandedGraphSlice.actions;

export default expandedGraphSlice.reducer;
