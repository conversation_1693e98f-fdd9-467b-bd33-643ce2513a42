import { createSlice } from "@reduxjs/toolkit";

const KEY = "@sidebar-state";

const initialState = {
  isCollapsed: localStorage.getItem(KEY)
    ? JSON.parse(localStorage.getItem(KEY))
    : false,
  deviceSidebar: false,
};

const sidebarSlice = createSlice({
  name: "sidebar",
  initialState,
  reducers: {
    toggleCollapse(state) {
      localStorage.setItem(KEY, JSON.stringify(!state.isCollapsed));

      if (state.isCollapsed && !state.deviceSidebar) {
        state.deviceSidebar = true;
      }
      state.isCollapsed = !state.isCollapsed;
    },
    colseSidebar(state) {
      state.isCollapsed = true;
    },
    toggleDeviceSiderbar(state) {
      if (!state.isCollapsed && state.deviceSidebar) {
        state.isCollapsed = true;
      }
      state.deviceSidebar = !state.deviceSidebar;
    },
  },
});

export default sidebarSlice.reducer;
export const { toggleCollapse, colseSidebar, toggleDeviceSiderbar } =
  sidebarSlice.actions;
