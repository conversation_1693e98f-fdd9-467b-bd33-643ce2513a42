import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { fetchCustomVariables, fetchProductVariables } from "../api/product";

const initialState = {
  customVariables: {},
  productVars: undefined // old variables
};

/**
 * @type {import(".").TypedAsyncThunk<typeof fetchProductVariables>}
 */
export const getProductVars = createAsyncThunk("product/productVars", async (args) => {
  const data = await fetchProductVariables(args);
  return data;
});

/**
 * @type {import(".").TypedAsyncThunk<typeof fetchCustomVariables>}
 */
export const getCustomVariables = createAsyncThunk("product/fetchCustomVariables", async (args) => {
  const data = await fetchCustomVariables(args);
  return data;
});

export const productSlice = createSlice({
  name: "product",
  initialState,
  reducers: {
    resetProductVars: (state) => {
      state.productVars = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProductVars.pending, (state) => {
        state.productVars = undefined;
      })
      .addCase(getProductVars.fulfilled, (state, action) => {
        if (action.payload.status !== "Success") return;
        state.productVars = action.payload.data;
      });

    builder
      .addCase(getCustomVariables.pending, (state) => {
        state.customVariables = { data: {}, loading: true };
      })
      .addCase(getCustomVariables.fulfilled, (state, action) => {
        const {
          payload: { data, status }
        } = action;
        if (status !== "Success") return;

        state.customVariables.data = data;
        state.customVariables.loading = false;
      });
  }
});

export const { resetProductVars } = productSlice.actions;

export default productSlice.reducer;
