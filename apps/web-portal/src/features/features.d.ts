export interface User {
  role: string;
  email: string;
  phone: string;
  name: string;
  pincode: string;
  address: string;
  tenant: string;
  avatar: string;
  userGroup: string;
  accountType: string;
  createdAt: string;
  updatedAt: string;
}

export interface Tenant {
  pincode: string;
  setupStatus: string;
  mcCount: number;
  website: string;
  access: number;
  address: string;
  notificationCount: number;
  smsCount: number;
  companyName: string;
  accountType: string;
  callCount: number;
  subscription: string;
  phone: string;
  name: string;
  email: string;
  createdAt: number;
  updatedAt: number;
  avatar: string;
  featureType: string;
  subFeature?: "basicAssetTracking" | "studentSafety" | "basic" | "fleetManagement";
}

export interface UserUsage {
  data: Daum[];
  loading: boolean;
}

export interface Daum {
  event: string;
  count: number;
}

export interface UsageChartData {
  series: Series;
  labels: string[];
  total: number;
  loading: boolean;
}

export interface Series {
  publish_resp: { values: number[] };
  delivered_resp: { values: number[] };
  dropped_resp: { values: number[] };
  sent_resp: { values: number[] };
  bytes_received_resp: { values: number[] };
  bytes_sent_resp: { values: number[] };
  rules_success_resp: { values: number[] };
  rules_failed_resp: { values: number[] };
  rules_total_resp: { values: number[] };
  rules_exec_success_resp: { values: number[] };
  rules_exec_failed_resp: { values: number[] };
  rules_exec_total_resp: { values: number[] };
}

export type UserSlice = {
  user: User | null;
  tenant: Tenant | null;
  loading: boolean;
  userUsage: UserUsage;
  usageChartData: UsageChartData;
};

export type ThingMetaList = Record<
  string,
  {
    thingName: string;
    productName: string;
    meta: Meta;
    thingFriendlyName: string;
  }
>;
export type ProductMetaAndTopicList = {
  productName: string;
  metadata: { key: string; required: boolean }[];
  topic: {
    topic: string;
    payload: {
      key: string;
      dataType: string;
    }[];
  }[];
}[];

export type ThingMetaAndTopics = {
  thingMetaList: ThingMetaList;
  productMetaAndTopicList: ProductMetaAndTopicList;
};

export interface ThingSlice {
  availableOtas: AvailableOtas;
  certificates: {
    data: Certificate[];
    loading: boolean;
    pages: boolean;
  };
  certificateDetail: {
    data:
      | {
          caName: string;
          thingName: string;
          status: string;
          createdAt: string;
          expiration: string;
          serial_number: string;
          certificate: string;
        }
      | {};
    loading: boolean;
  };
  policies: {
    data: Policy[];
    loading: boolean;
    pages: boolean;
  };
  policyDetail: {
    data: Policy | {};
    loading: boolean;
  };
  otaDocDetail: {
    data: Ota | {};
    loading: boolean;
  };
  otaJobList: {
    data: OtaJobDoc[];
    loading: boolean;
  };
}

type InventoryItem = {
  name: string;
  value: number;
};

export type InventoryData = {
  pending_activation: InventoryItem[];
  active: InventoryItem[];
  expired: InventoryItem[];
  total: InventoryItem[];
  pending_activationCount: number;
  activeCount: number;
  expiredCount: number;
  totalCount: number;
};

export type InventoryThingDetail = {
  id: number;
  serialNumber: string;
  thingName: string;
  productName: string;
  mfgDate: string;
  createdAt: string;
  updatedAt: string;
  status: "pending_activation" | "active" | "expired";
  statusReason: string | null;
};

export type OtaJobDoc = {
  id: string;
  thingName: string;
  timestamp: number;
  otaType: string;
  uniqueId: string;
  status: "COMPLETED" | "FAILED" | "DOWNLOADING";
  percentage: number;
  updatedAt: Date;
  createdAt: Date;
};

export interface OtaJobDetail {
  jobId: string;
  status: string;
  queuedAt: number;
  lasUpdatedAt: number;
  versionNumber: number;
  executionNumber: number;
  jobDocument: JobDocument;
  statusDetails: StatusDetails;
  execution: {
    status: "DOWNLOADING" | "IN_PROGRESS" | "QUEUED" | "FAILED" | "COMPLETED";
    queuedAt: string;
    jobId: string;
    lasUpdatedAt: string;
  };
  percentage: number;
  uniqueId: string;
  thingName: string;
  otaType: string;
  createdAt: string;
}

export interface JobDocument {
  afr_ota: AfrOta;
}

export interface AfrOta {
  protocols: string[];
  streamname: string;
  files: File[];
}

export interface File {
  filepath: string;
  filesize: number;
  fileid: number;
  certfile: string;
  "sig-sha256-ecdsa": string;
}

export interface StatusDetails {
  self_test: string;
  updatedBy: string;
}

export interface Thing {
  _id: string;
  thingName: string;
  productName: string;
  status: "connected" | "disconnected";
  partner: string;
  meta: Meta;
  version: string;
  createdAt: number;
}

export type Meta = {
  owner: string;
  thingFriendlyName: string;
  users: string;
  [val: string]: string;
};

export type ThingDetailsReducer = {
  loading: boolean;
  shadow: {
    reported: Record<string, number | string>;
    desired: Record<string, number | string>;
  } | null;
  policy: Policy | null;
  thingLogMetric: {
    labels: string[];
    series: Record<string, number[]>;
    total: number;
  };
};

export type ThingDetails = Thing & {
  thingGroups: ThingGroup[];
};

export type ThingGroup = {
  thingGroupName: string;
  email: string;
  contact: string;
  createdAt: string;
  thingCount: number;
};

export type Policy = {
  thingName: string;
  clientId: string;
  topics: {
    topic: string;
    permission: "allow" | "deny";
    action: "publish" | "subscribe" | "all";
  }[];
  type: string;
  createdAt: date;
  updatedAt: date;
};
export type ThingOta = {
  id: string;
  thingName: string;
  timestamp: number;
  otaType: string;
  uniqueId: string;
  status: string;
  percentage: number;
  createdAt: string;
};

export type AvailableOtas = {
  data: Ota[];
  loading: boolean;
  pages: number;
};

export type Ota = {
  otaType: string;
  jobId: string;
  targetType: string;
  targetName: string;
  target: string;
  closed: boolean;
  version: string;
  fileSize: number;
  file_url: string;
  sig: string;
  productName: string;
  partnerName: string;
  uniqueId: string;
  timestamp: number;
  createdAt: date;
  updatedAt: date;
};

export type Certificate = {
  id: string;
  thingName: string;
  status: "inactive" | "active";
  createdAt: string;
  updatedAt: string;
  expiration: string;
  caName: string;
};

export type CertificateDetails = {
  data: {
    caName: string;
    thingName: string;
    status: string;
    createdAt: string;
    id: string;
    expiration: string;
    serial_number: string;
    certificate: string;
  };
  message: string;
  status: "Success" | "Failure";
};
export type CertificateAuthList = {
  data: {
    pages: number;
    list: {
      caName: string;
      createdAt: date;
      updatedAt: date;
      expiration: date;
      serial_number: string;
      type: string;
    }[];
  };
  message: string;
  status: "Success" | "Failure";
};
export type CertificateAuthDetails = {
  data: {
    caName: string;
    certificate: string;
    commonName: string;
    createdAt: date;
    updatedAt: date;
    expiration: date;
    serialNumber: string;
    type: string;
  };

  message: string;
  status: "Success" | "Failure";
};

export type Template = {
  id?: number;
  feId: string;
  conditions: string[];
};

export type PushTemplate = Template & {
  title: string;
  body: string;
};

export type EmailTemplate = Template & {
  subject: string;
  body: string;
  HTMLbody: string;
  attachments: string[];
};

export type SmsTemplate = Template & {
  msg91TemplateId: string;
  body: string;
};
export type TeamsTemplate = Template & {
  title: string;
  body: string;
  themeColor: string;
};

export type NotificationTemplateDetail = {
  templateName: string;
  description: string;
  tenant: string;
  msp: string;
  createdAt: string;
  updatedAt: string;
  pushTemplates: PushTemplate[];
  emailTemplates: EmailTemplate[];
  smsTemplates: SmsTemplate[];
  teamsTemplates: teamsTemplates[];
};

export type Contact = {
  contactId: string;
  name: string;
  email: string;
  phone: string;
  userType: "platform" | "external";
  createdAt: string;
  updatedAt: string;
};

export type ContactTeamsList = {
  id: string;
  name: string;
  description: string;
  createdAt: string;
};

interface TeamMember {
  contactId: string;
  name: string;
  email: string;
  phone: string;
}

export interface TeamGroupDetails {
  id: string;
  name: string;
  description: string;
  tenant: string;
  members: TeamMember[];
  createdAt: string; // or Date if you're parsing to Date object
  updatedAt: string; // or Date
}

type NotificationPreference = {
  notificationType: "alert" | "system_generated"; // Use union type for strict typing
  push: boolean;
  email: boolean;
  sms: boolean;
  teams: boolean;
  updatedAt?: string;
};

type TimeSlot = {
  startTime: Date;
  endTime: Date;
  id: string;
};

type DoNotDisturb = {
  enabled: boolean;
  slots: TimeSlot[];
};

type OnCallSchedule = {
  enabled: boolean;
  slots: TimeSlot[];
};

interface Shift {
  id: number;
  name: string;
  enabled: boolean;
  shifts: { name: string; startTime: string; endTime: string }[];
}
export type ContactDetail = {
  contactId: string;
  name: string;
  email: string;
  phone: string;
  whatsapp: string;
  address: string;
  country: string;
  pincode: string;
  userType: "platform" | "external"; // Union type for consistency
  msp: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
  preferences: NotificationPreference[];
  doNotDisturb: DoNotDisturb;
  onCallSchedule: OnCallSchedule;
  shifts: Shift[];
};

export type EscalationMember = {
  id ?: string;
  contactId: string;
  name: string;
  escalateAfter: number; // Time in seconds or minutes (based on your context)
};

export type EscalationGroup = {
  id: string;
  groupName: string;
  description: string;
  notificationExecution: "parallel" | "sequential";
  tenant: string;
  msp: string;
  createdAt: string;
  updatedAt: string;
  members: EscalationMember[];
};

export type PolicyTemplate = {
  createdAt: string;
  productCount: number;
  templateName: string;
};
export type Topic = {
  topic: string;
  action: "publish" | "subscribe" | "all";
  permission: "allow" | "deny";
};
export type PolicyTemplateDetails = {
  id: string;
  templateName: string;
  thingName: string;
  clientId: string;
  type: string;
  topics: Topic[];
  tenant: string;
  createdAt: string;
  updatedAt: string;
};

type ProductTopic = {
  topic: string;
  action: "publish" | "subscribe" | "all";
  permission: "allow" | "deny";
  payload?: {
    key: string;
    dataType: string;
  }[];
  shortName?: string;
};

type PolicyTemplate = {
  template: {
    topics: Topic[];
  };
  templateName: string;
};

type Authorization = {
  "policy-template": PolicyTemplate;
};

type Authentication = {
  type: "tls" | "basic";
  caName?: string;
};

type Widget = {
  id: string;
  type: string;
  field: string;
  title: string;
  source: string;
  operation: string;
};

type Metadata = {
  key: string;
  required: boolean;
};

type AppRequirement = {
  alexa?: boolean;
  googleHome?: boolean;
  category?: string;
  components?: {
    features: string[];
    switches: string[];
  };
};

type ProductSetting = {
  key: string;
  name: string;
  error: string;
  default: string;
  depends: string;
  required: boolean;
  validation: string;
  description: string;
};

export type BaseProductTemplate = {
  name: string;
  productTemplateType: "standard" | "managed";
  imgURL: string;
  description: string;
  authentication: Authentication;
  authorization: Authorization;
  dataManagement: string[];
  widgets: Widget[];
  metadata: Metadata[];
  otaType: "mqtt" | "http";
  createdBy: string | null;
  allowedMsps: string[];
  appRequirement: AppRequirement | null;
  projectId: string | null;
  version: string;
  capabilities: string[] | null;
  category: string | null;
  productSettings: ProductSetting[] | null;
  enabled: boolean;
  documentLink: string;
  topics: ProductTopic[];
};

export type ProductTemplates = {
  mspTemplates: BaseProductTemplate[];
  opTemplates: BaseProductTemplate[];
};

export type ProductDocInfo = {
  info: string | number;
  description: string;
};

export type GatewayListItem = {
  gatewayName: string;
  vendorName: string;
  createdAt: string;
};

export type GatewayDetails = {
  id: string;
  gatewayName: string;
  vendorName: string;
  tenant: string;
  authentication: {
    type: string;
  };
  meta: Record<string, string>; // Can contain dynamic key-value pairs
  connectedTimeStamp: string | null;
  disconnectedTimeStamp: string | null;
  disconnectedReason: string | null;
  createdAt: string;
  updatedAt: string;
};

export type NotificationHistory = {
  id: number;
  contactId: string;
  email: string;
  channel: string;
  notificationType: string;
  category: string;
  title: string;
  body: string;
  thingName: string;
  productName: string;
  ackLink: string | null;
  read: boolean;
  msp: string;
  priority: string;
  metadata: Record<string, unknown>;
  createdAt: string;
};

export type notificationStatusLog = {
  id: number;
  contactName: string;
  notificationType: string;
  category: string;
  title: string;
  body: string;
  priority: string;
  status: string;
  type: string;
  email: string;
  scheduledAt: string;
  createdAt: string;
  ackAt: string | null;
  failureReason: string | null;
};

export type NotificationLogsDetail = {
  id: number;
  contactName: string;
  contactId: string;
  notificationType: "transactional" | "alert" | "system_generated";
  notificationExecution: "parallel" | "escalation";
  category: string;
  title?: string;
  body: string;
  subject?: string;
  HTMLbody?: string;
  attachments?: Record<string, any>[];
  retries: number;
  priority: "low" | "medium" | "high";
  metadata:
    | {
        thingName: string;
        productName: string;
      }
    | Record<string, any>;
  status: "pending" | "processing" | "sent" | "failed" | "escalation_exceed" | "acknowledged";
  ackAt: string | null;
  ackBy: string | null;
  failureReason: string | null;
  type: "app" | "portal";
  email: string;
  phoneNo: string | null;
  scheduledAt: string;
  createdAt: string;
};

export type IncidentCategory = {
  categoryList: {
    id: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  }[];
  pages: number;
};
export type AuthUser = {
    username: string,
    authType: string,
    isSuperuser:boolean,
    createdAt:string,
    updatedAt:string
};

export type AuthenticationList = {
  
  authList:AuthUser[];
  pages : number
};

export type AuthDetails = {
    authName: string;
    authType: "thing" | "custom" 
  } | undefined;

export type AuthenticationDetails = {
        createdAt: string;
        email: string;
        username: string;
        updatedAt: string;
        thingName: string;
        authName: string;
        authType: "thing" | "custom" ;
      };

export type TopicItem = {
  topic: string;
  action: "publish"|"subscribe"| "all"|null;
  permission: "allow"| "deny"|null;
  id : number;
}

export type MetaDataInputs = {
  key : string;
  value:string;
  id : string;
}

type Actions = {
  actionLabel: string;
  desc: string;
  disabled: boolean;
  isChecked: boolean;
 
}
type SubFeatures = {
  desc: string;
  disabled:boolean;
  group: string;
  isChecked: boolean;
  name : string;
  subFeature: string;
  actions: Actions[];
  enabled?: boolean;
}
type SubServices = {
   desc: string;
   feature: string;
   name : string;
   subFeatures : SubFeatures[]
   actions?: Actions[];
    group?: string;
    enabled?: boolean;

}
 export type FeatureItem = {
  desc: string;
  isChecked : boolean;
  service : string;
  subServices: SubServices[];
}

export type AuthorizationData = {
    [service: string]: {
      [feature: string]: string | { [subFeature: string]: string };
    };
  };

  export type Users = {
    address: string;
    createdAt: string;
    disabled: boolean;
    email: string;
    monitoringGroups: string[];
    msp: string;
    name: string;
    phone: string;
    pincode: string;
    role: string;
    tenant: string;
    updatedAt: string;
    userGroup: string;
    userGroupId: string;
  };

  export type Type = "tenant" | "operator" | "msp" |"platform-operator"| "MSP" | "Tenant" | "Operator"|undefined;

  export type MspFormData = {
    address: string;
    canCreateTenants: "true" | "false"|undefined;
    confirmPassword?: string;
    email: string;
    mspFullName: string;
    mspId: string;
    password?: string;
    phone: RPNInput.Value; 
    pincode: string;
    userName?: string;
    website: string;
    tenant?: string[];
    createdAt?: string;
    updatedAt?: string;
    access?: string;
  };