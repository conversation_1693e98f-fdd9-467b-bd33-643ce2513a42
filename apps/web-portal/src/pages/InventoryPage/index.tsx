import DataNotFound from "@components/DataNotFound";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useThingInventory from "@hooks/thing/useThingInventory";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { generateColor } from "@utils/color";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE, separateCamelCase } from "@utils/utilities";
import { Plus, Search } from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import Button from "../../components/Button";
import Input from "../../components/Input";
import Label from "../../components/Label";
import { TableHead, TableRow } from "../../components/Table";
import Table from "../../components/Table/Table";
import TableRowsSkeleton from "../../components/Table/TableRowsSkeleton";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import CreateInventorySidebar from "./CreateInventory/CreateInventorySidebar";
import InventoryCardSection from "./InventoryCardSection";
import SelectFilterData from "@components/SelectFilterData";

const THINGS_FILTERS = [
  { label: "All", value: "all" },
  { label: "Active", value: "active" },
  { label: "Pending", value: "pending_activation" },
  { label: "Expired", value: "expired" }
];

const InventoryPage = () => {
  const [filter, setFilter] = useState("all");

  const { page, setPage, limit, setLimit, searchQuery, setSearchQuery } = useTablePagination();

  const navigate = useCustomNavigate();

  const createInventoryRef = useRef<ElementRef<typeof CreateInventorySidebar>>(null);

  const [sortFn, sort] = useTableSort();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: permissions } = useUserGroupPermissions();
  const { data: inventoryThings, isLoading: inventoryThingsLoading } = useThingInventory({
    page,
    limit,
    searchQuery: debouncedSearchQuery,
    filter: filter === "all" ? "" : filter
  });

  return (
    <main className="space-y-4">
      <HeaderSection
        title={`${THING_VAR} Inventory`}
        description="Manage your things"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => createInventoryRef.current?.openCreateInventory()}
            noAccess={permissions.inventory !== "write"}
            type="submit"
          >
            Add {THING_VAR}
          </Button>
        }
      />
      <InventoryCardSection />
      <Card className="space-y-4">
        <h3 className="heading-2">{THING_VAR}s List</h3>
        <div className="flex gap-4 items-center">
          <Input
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setPage(1);
            }}
            className="flex-1"
            placeholder="Search"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />

          <SelectFilterData
            options={THINGS_FILTERS}
            selected={filter}
            setSelected={(value) => {
              console.log(value);
              // setSelectedFilter(value);
              setFilter(value);
              setPage(1);
            }}
            className="w-40"
          />
        </div>

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
              <TableHead onSort={(order) => sort("productName", order)}>
                {PRODUCT_VAR} Name
              </TableHead>
              <TableHead onSort={(order) => sort("status", order)}>Status</TableHead>
              <TableHead onSort={(order) => sort("mfgDate", order)}>Manufacture Date</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created Date</TableHead>
            </>
          }
          body={
            inventoryThingsLoading ? (
              <TableRowsSkeleton />
            ) : !inventoryThings || !inventoryThings?.things.length ? (
              <DataNotFound
                title={!inventoryThings ? "Something Went Wrong" : "No Things Available"}
                isTable
              />
            ) : (
              inventoryThings.things.toSorted(sortFn).map((thing, i) => (
                <tr
                  key={thing.thingName}
                  className=" cursor-pointer"
                  onClick={() => navigate(`${thing.thingName}`)}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{thing.thingName}</TableRow>
                  <TableRow>{thing.productName}</TableRow>
                  <TableRow>
                    <Label
                      color={generateColor(thing.status)}
                      text={separateCamelCase(thing.status)}
                    />
                  </TableRow>
                  <TableRow>{convetUTCToLocal(thing.mfgDate)}</TableRow>
                  <TableRow>{convetUTCToLocal(thing.createdAt)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: inventoryThings?.pages || 0
          }}
        />
      </Card>
      <CreateInventorySidebar ref={createInventoryRef} />
    </main>
  );
};

export default InventoryPage;
