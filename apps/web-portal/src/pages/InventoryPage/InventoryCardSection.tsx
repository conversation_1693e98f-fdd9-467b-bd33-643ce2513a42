import { Skeleton } from "@components/shadcn/components/skeleton";
import { Card } from "@components/ui";
import useInventoryStats from "@hooks/thing/useInventoryStats";
import PieChart from "@src/charts/PieChart";

const InventoryCardSection = () => {
  const { data: inventoryStats, isLoading: inventoryStatsLoading } = useInventoryStats();
  return (
    <section className="">
      {!inventoryStats || inventoryStatsLoading ? (
        <StatCardSkeleton />
      ) : (
        <section className="flex w-full  gap-4">
          <InventoryChartCard
            title="Active"
            value={inventoryStats.active.total}
            percentage={((inventoryStats?.active.total / inventoryStats.total.total) * 100).toFixed(
              1
            )}
          >
            <PieChart
              type="donut"
              palette={1}
              height="auto"
              series={inventoryStats.active.series}
              labels={inventoryStats.active.labels}
            />
          </InventoryChartCard>
          <InventoryChartCard
            title="Pending"
            value={inventoryStats?.pending.total}
            percentage={(
              (inventoryStats?.pending.total / inventoryStats.total.total) *
              100
            ).toFixed(1)}
          >
            <PieChart
              type="donut"
              palette={2}
              height="auto"
              series={inventoryStats?.pending.series}
              labels={inventoryStats.pending.labels}
            />
          </InventoryChartCard>
          <InventoryChartCard
            title="Expired"
            value={inventoryStats.expired.total}
            percentage={(
              (inventoryStats?.expired.total / inventoryStats.total.total) *
              100
            ).toFixed(1)}
          >
            <PieChart
              type="donut"
              palette={3}
              series={inventoryStats?.expired.series}
              labels={inventoryStats.expired.labels}
              height="auto"
            />
          </InventoryChartCard>
          <InventoryChartCard title="Total" value={inventoryStats.total.total}>
            <PieChart
              type="donut"
              palette={4}
              series={inventoryStats.total.series}
              labels={inventoryStats.total.labels}
              height="auto"
            />
          </InventoryChartCard>
        </section>
      )}
    </section>
  );
};

export default InventoryCardSection;
export const StatCardSkeleton = ({ count = 4 }: { count?: number }) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {[...Array(count)].map(() => (
        <Card className="relative overflow-hidden">
          <div className="pt-6">
            <div className="flex items-start justify-between">
              <div className="space-y-3">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-9 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
              <div className="relative">
                <Skeleton className="w-20 h-20 rounded-full" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Skeleton className="w-10 h-10 rounded-full" />
                </div>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

const InventoryChartCard = ({ title, value, percentage, children }: any) => {
  return (
    <Card className="flex justify-between items-center w-full">
      <div className="flex flex-col items-center justify-between h-full">
        <h3 className="text-muted-foreground text-sm font-medium">{title}</h3>
        <div>
          <div className="text-2xl font-bold text-foreground mt-1">{value}</div>
          {percentage && <div className="text-xs text-muted-foreground mt-1">{percentage}%</div>}
        </div>
      </div>
      {children}
    </Card>
  );
};
