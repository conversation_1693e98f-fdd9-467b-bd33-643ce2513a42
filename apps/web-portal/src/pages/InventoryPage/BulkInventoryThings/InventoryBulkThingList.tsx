import DataNotFound from "@components/DataNotFound";
import Label from "@components/Label";
import HeaderSection from "@components/layout/HeaderSection";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import useTablePagination from "@hooks/classic/useTablePagination";
import useBulkInventoryThingList from "@hooks/thing/useBulkInventoryThingList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { THING_VAR } from "@utils/featureLabels";

const InventoryBulkThingList = () => {
  const { page, setPage, limit, setLimit } = useTablePagination();
  const { data: inventoryBulkThingList, isLoading: inventoryBulkThingListLoading } =
    useBulkInventoryThingList({ page, limit });
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  return (
    <main className=" space-y-4">
       <HeaderSection
              title={`Inventory Bulk Creates`}
              description="Manage your Inventory Bulk Creates"
            />
      {/* <section className="flex w-full flex-wrap gap-4">
        <InfoCard4
          title={`Successful ${THING_VAR}s`}
          data={stats.success}
          color="green"
          icon={<CheckCircle size="1.625rem" />}
          footer={`${((stats.success / stats.total) * 100).toFixed(1)}%`}
        />
        <InfoCard4
          title={`Failed ${THING_VAR}s`}
          data={stats.failed}
          color="red"
          icon={<XCircle size="1.625rem" />}
          footer={`${((stats.failed / stats.total) * 100).toFixed(1)}%`}
        />
        <InfoCard4
          title={`Pending ${THING_VAR}s`}
          data={stats.pending}
          color="gray"
          icon={<Clock size="1.625rem" />}
          footer={`${((stats.pending / stats.total) * 100).toFixed(1)}%`}
        />
        <InfoCard4
          title={`Total ${THING_VAR}s`}
          data={stats.total}
          color="blue"
          icon={<Cpu size="1.625rem" />}
        />
      </section> */}
      <Card className="space-y-4">
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead>Job Name</TableHead>
              <TableHead onSort={(order) => sort("targetType", order)}>Completed</TableHead>
              <TableHead onSort={(order) => sort("version", order)}>Total {THING_VAR}s</TableHead>
              <TableHead onSort={(order) => sort("version", order)}>Status</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Timestamp</TableHead>
            </>
          }
          body={
            inventoryBulkThingListLoading ? (
              <TableRowsSkeleton />
            ) : !inventoryBulkThingList?.data.length ? (
              <DataNotFound title="No Bulk Create Found" isTable />
            ) : (
              <>
                {inventoryBulkThingList.data.sort(sortFn).map((bulkJob, i) => (
                  <tr
                    key={bulkJob.id}
                    onClick={() => navigate(`${bulkJob.id}`)}
                    className="cursor-pointer"
                  >
                    <TableRow>{i + 1}</TableRow>

                    <TableRow title>{bulkJob.fileName || "N/A"}</TableRow>

                    <TableRow>
                      <span className=" font-semibold mr-1">{bulkJob.count}</span> Completed
                    </TableRow>

                    <TableRow>
                      <span className="font-semibold mr-1">{bulkJob.expectedCount}</span>
                      Expected
                    </TableRow>

                    <TableRow>
                      <Label
                        text={bulkJob.status}
                        color={bulkJob.status === "COMPLETED" ? "green" : "yellow"}
                      />
                    </TableRow>
                    <TableRow>{convetUTCToLocal(bulkJob.createdAt)}</TableRow>
                  </tr>
                ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: inventoryBulkThingList?.pages || 1
          }}
        />
      </Card>
    </main>
  );
};

export default InventoryBulkThingList;
