import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import { Card } from "@components/ui";
import PageSekeleton from "@components/PageSekeleton";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useBulkInventoryThingStatus from "@hooks/thing/useBulkInventoryThingStatus";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { Cpu } from "lucide-react";
import { useParams } from "react-router-dom";

const InventoryBulkThingStatus = () => {
  const docId = useParams().docId!;
  const [sortFn, sort] = useTableSort();

  const { data: bulKThingsStatus, isLoading: isBulkThingsStatusLoading } =
    useBulkInventoryThingStatus({ docId });
  console.log(bulKThingsStatus);
  if (isBulkThingsStatusLoading) {
    return <PageSekeleton />;
  }

  if (isBulkThingsStatusLoading && !bulKThingsStatus) {
    return (
      <main className="flex-1 flex flex-col gap-4 rounded-md  h-full items-center justify-center">
        <Card>
          <DataNotFound
            title="Bulk status not found"
            content="Bulk status was never created or report is already downloaded. "
          />
        </Card>
      </main>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between ">
        <HeadingIcon Icon={Cpu} title="Bulk Create Items" />
        <div className="flex gap-4 items-center">
          {/* <Input
        value={searchQuery}
        onChange={(e) => {
          setSearchQuery(e.target.value);
        }}
        className="w-[25rem]"
        placeholder="Search"
        endIcon={<Search size={INPUT_ICON_SIZE} />}
      /> */}
          {/* <Button
        onClick={() => {
          setShowWarningPrompt(true);
        }}
        startIcon={<DownloadCloud size={BUTTON_ICON_SIZE} />}
      >
        Download Report
      </Button> */}
        </div>
      </div>

      <Table  
        head={
          <>
            <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
            <TableHead onSort={(order) => sort("productName", order)}>{PRODUCT_VAR} Name</TableHead>
            <TableHead onSort={(order) => sort("type", order)}>Status</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("completedAt", order)}>Updated At</TableHead>
            <TableHead onSort={(order) => sort("type", order)}>Error</TableHead>
          </>
        }
        body={
          isBulkThingsStatusLoading || !bulKThingsStatus ? (
            <TableRowsSkeleton />
          ) : (
            bulKThingsStatus.toSorted(sortFn).map((thing) => (
              <tr className="bg-white  " key={thing.id}>
                <TableRow title>
                  <div className="flex items-center gap-2">{thing.id}</div>
                </TableRow>

                <TableRow>{thing.productName}</TableRow>
                <TableRow>
                  <Label
                    text={thing.status}
                    className=" !capitalize"
                    color={
                      thing.status === "failure"
                        ? "red"
                        : thing.status === "success"
                          ? "green"
                          : "blue"
                    }
                  />
                </TableRow>
                <TableRow>{convetUTCToLocal(thing.createdAt)}</TableRow>

                <TableRow>{thing.deQueuedAt ? convetUTCToLocal(thing.deQueuedAt) : "N/A"}</TableRow>
                <TableRow>{thing.error?.message || "N/A"}</TableRow>
              </tr>
            ))
          )
        }
      />
    </div>
  );
};

export default InventoryBulkThingStatus;
