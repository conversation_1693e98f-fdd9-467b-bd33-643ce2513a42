import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useThingInventoryDetail from "@hooks/thing/useThingInventoryDetail";
import { generateColor } from "@utils/color";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE, separateCamelCase } from "@utils/utilities";
import {
  Archive,
  Calendar,
  Clock,
  Cpu,
  Edit,
  Edit2,
  FolderKanban,
  NotebookPen,
  NotepadText,
  ScanBarcode
} from "lucide-react";
import { ElementRef, useRef } from "react";
import { useParams } from "react-router-dom";
import Button from "../../components/Button";
import Label from "../../components/Label";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import UpdateInventory from "./UpdateInventory";

const InventoryDetailPage = () => {
  const thingName = useParams().thingName!;

  const updateInventoryRef = useRef<ElementRef<typeof UpdateInventory>>(null);

  const { data: thingInventoryDetails, isLoading: thingInventoryLoading } = useThingInventoryDetail(
    { thingName: thingName }
  );

  const { data: permissions } = useUserGroupPermissions();

  return (
    <main>
      <Card className=" space-y-6">
        <div className="flex justify-between gap-2 items-center ">
          <HeadingIcon Icon={FolderKanban} title={`Inventory ${THING_VAR} Detail`} />

          <div className="flex gap-4">
            <Button
              onClick={() => {
                updateInventoryRef.current?.openUpdateModal();
              }}
              color="gray"
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
              noAccess={permissions.inventory !== "write"}
            >
              Edit
            </Button>
          </div>
        </div>

        {thingInventoryLoading ? (
          <CardLoadingSkeleton />
        ) : !thingInventoryDetails ? (
          <DataNotFound title="No Inventory Found" />
        ) : (
          <section className="grid grid-cols-3 gap-4">
            <DetailsCell
              title={`${THING_VAR} Name`}
              data={thingName}
              icon={<Cpu size={DATACELL_ICON_SIZE} />}
            />
            <DetailsCell
              title={`${PRODUCT_VAR} Name `}
              data={thingInventoryDetails.productName}
              icon={<Archive size={DATACELL_ICON_SIZE} />}
            />
            <DetailsCell
              title={`Serial Number`}
              data={thingInventoryDetails.serialNumber}
              icon={<ScanBarcode size={DATACELL_ICON_SIZE} />}
            />
            <DetailsCell
              title="Status"
              data={
                <Label
                  color={generateColor(thingInventoryDetails.status)}
                  text={separateCamelCase(thingInventoryDetails.status)}
                />
              }
              icon={<NotebookPen size={DATACELL_ICON_SIZE} />}
            />

            <DetailsCell
              title={`Status Reason`}
              data={thingInventoryDetails.statusReason}
              icon={<NotepadText size={DATACELL_ICON_SIZE} />}
            />
            <DetailsCell
              title="Manufacturer Date"
              data={
                thingInventoryDetails.mfgDate
                  ? convetUTCToLocal(thingInventoryDetails.mfgDate)
                  : "N/A"
              }
              icon={<Clock size={DATACELL_ICON_SIZE} />}
            />

            <DetailsCell
              title="Created At"
              data={convetUTCToLocal(thingInventoryDetails.createdAt)}
              icon={<Calendar size={DATACELL_ICON_SIZE} />}
            />
            <DetailsCell
              title="Updated At"
              data={convetUTCToLocal(thingInventoryDetails.updatedAt)}
              icon={<Edit size={DATACELL_ICON_SIZE} />}
            />
          </section>
        )}
      </Card>
      <UpdateInventory ref={updateInventoryRef} />
    </main>
  );
};

export default InventoryDetailPage;
