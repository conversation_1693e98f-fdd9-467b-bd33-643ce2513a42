import { deleteSite, updateSite } from "@api/site";
import Button from "@components/Button";
import ConfirmPrompt from "@components/ConfirmPrompt";
import Dropdown from "@components/Dropdown";
import {
  <PERSON><PERSON>r,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import { setSelectedSite } from "@frontend/shared/store/siteSlice";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useSiteDetails from "@hooks/site/useSiteDetails";
import useSiteList from "@hooks/site/useSiteList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { IconButton } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import { useAppDispatch, useAppSelector } from "@src/store";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, TENANT_ROLES } from "@utils/utilities";
import {
  ChevronsLeftRightEllipsisIcon,
  Cog,
  Copy,
  EllipsisVertical,
  Plus,
  RefreshCcw,
  Rss,
  Edit,
  Trash,
  Share2,
  LayoutGrid,
  Rows3
} from "lucide-react";
import { ElementRef, forwardRef, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import SelectDuration from "../MonitorPage/layout/SelectDuration";
import CloneSiteModal from "./CloneSiteModal";
import AddAreaDrawer from "./layouts/AddAreaDrawer";
import AddSiteWidget from "./layouts/AddSiteWidget";
import CreateSiteCard from "./layouts/CreateSiteCard";
import SaveSiteModal from "./layouts/SaveSiteModal";
import SiteAutomationFlow from "./layouts/SiteAutomationFlow";
import SiteDetails from "./layouts/SiteDetails";
import SiteTriggersSideBar from "./layouts/SiteTriggersSideBar";
import ShareDashboardLink from "../MonitorPage/layout/ShareDashboardLink";
import useUpdateUserView from "@hooks/user/useUpdateUserView";
import { getUserData } from "@frontend/shared/store/userSlice";

const Site = () => {
  const tenantName = useAppSelector((state) => state.user?.tenant?.name || "");

  const { data: siteList, isLoading } = useSiteList({ enabled: true, tenantName });
  const { data: permissions } = useUserGroupPermissions();

  const { siteName } = useParams();
  const addAreaDrawerRef = useRef<ElementRef<typeof AddAreaDrawer>>(null);
  const saveSiteModalRef = useRef<ElementRef<typeof SaveSiteModal>>(null);
  const areaOrderRef = useRef<ElementRef<typeof SiteDetails>>(null);
  const siteViewId = useAppSelector((state) => state.user.user?.siteViewId);

  const selectedSiteName = useAppSelector(({ site }) => site.selectedSiteName!);
  const { data: siteDetails } = useSiteDetails({ siteId: siteName ?? selectedSiteName });

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (!siteList?.length) return;

    if (siteViewId) {
      const selectedSiteInSiteList = siteList?.find((item) => item.siteId === siteViewId);
      if (!selectedSiteInSiteList) {
        dispatch(setSelectedSite(siteList[0].name));
        return;
      }
      const selectedSiteViewName = selectedSiteInSiteList?.name;
      dispatch(setSelectedSite(selectedSiteViewName));
      return;
    }

    dispatch(setSelectedSite(siteList[0].name));
  }, [siteList]);

  const navigate = useCustomNavigate();

  const handleDropDownChange = (option: string) => {
    dispatch(setSelectedSite(option));
  };

  if (isLoading || !siteList?.length) {
    return (
      <main className="flex justify-center">
        <div className=" max-w-lg" style={{ marginTop: "25vh" }}>
          {isLoading ? (
            <CircularProgress />
          ) : (
            <CreateSiteCard title="Seamlessly collect data from all your assets and transform it into powerful, real-time visual insights. Process IQ gives you a complete, live overview of your factory floor—enabling faster decisions, increased efficiency, and smarter operations." />
          )}
        </div>
      </main>
    );
  }

  return (
    <main className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className=" text-lg capitalize font-medium">{siteDetails?.name}</h1>
          <p className=" text-gray-500">{siteDetails?.description}</p>
        </div>

        <div className="flex items-end gap-4">
          {siteName && (
            <Button
              onClick={() => {
                addAreaDrawerRef.current?.openDrawer();
              }}
              color="emerald"
              noAccess={permissions.sites !== "write"}
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            >
              Add Area
            </Button>
          )}

          {!siteName && (
            <Dropdown
              placeHolder="Select dashboard"
              value={selectedSiteName || siteList?.[0]?.name}
              className="min-w-[200px]"
              onChange={handleDropDownChange}
              options={siteList?.map((item) => item.name) || []}
            />
          )}
          <SelectDuration type="dashboard" />

          {siteName ? (
            <Button
              onClick={() => {
                if (!siteDetails) return;
                const siteAreas = areaOrderRef.current?.reorderedAreas();
                const areaOrder = siteAreas
                  ? {
                      areaOrder: siteAreas
                    }
                  : {};

                saveSiteModalRef.current?.showModal({
                  title: siteDetails?.name,
                  description: siteDetails?.description,
                  ...areaOrder
                });
              }}
              startIcon={<Edit size={BUTTON_ICON_SIZE} className="mr-1" />}
              noAccess={permissions.sites !== "write"}
            >
              Save Site
            </Button>
          ) : (
            <Button
              color="orange"
              onClick={() => {
                navigate(`/processIQ/sites/${selectedSiteName}`);
              }}
              startIcon={<Edit size={BUTTON_ICON_SIZE} className="mr-1" />}
              noAccess={permissions.sites !== "write"}
            >
              Edit
            </Button>
          )}

          <SiteMenuOptions />
        </div>
      </div>

      <SiteDetails ref={areaOrderRef} />
      <SaveSiteModal ref={saveSiteModalRef} />

      {siteDetails && (
        <>
          <AddAreaDrawer
            ref={addAreaDrawerRef}
            siteId={siteDetails?.siteId}
            onAddNewArea={() => {}}
            onUpdateArea={() => {}}
            areas={siteDetails?.areas || []}
          />
        </>
      )}
    </main>
  );
};

const SiteMenuOptions = forwardRef(() => {
  const siteName = useParams().siteName!;
  const tenantName = useAppSelector((state) => state.user?.tenant?.name || "");

  const cloneSiteRef = useRef<ElementRef<typeof CloneSiteModal>>(null);
  const addSiteWidgetRef = useRef<ElementRef<typeof AddSiteWidget>>(null);
  const selectedSiteName = useAppSelector(({ site }) => site.selectedSiteName!);
  const siteTriggersRef = useRef<ElementRef<typeof SiteTriggersSideBar>>(null);
  const siteAutomationFlowRef = useRef<ElementRef<typeof SiteAutomationFlow>>(null);
  const siteViewId = useAppSelector((state) => state.user.user?.siteViewId);
  const { data: siteList } = useSiteList({ enabled: true, tenantName });
  const userRole = useAppSelector((state) => state.user.user?.role);

  const shareDashboardRef = useRef<ElementRef<typeof ShareDashboardLink>>(null);
  const { data: permissions } = useUserGroupPermissions();
  const dispatch = useAppDispatch();
  const updateUserView = useUpdateUserView({
    onSuccess: () => {
      showSuccessToast("Dashboard marked as default");
      dispatch(getUserData());
    }
  });
  const updateSiteHideConnector = async ({
    hideConnector,
    orientation
  }: {
    hideConnector?: boolean;
    orientation?: "grid" | "inline";
  }) => {
    if (!siteDetails) return;

    try {
      // Update cache optimistically
      queryClient.setQueriesData({ queryKey: ["site", siteName] }, (prev) => {
        if (!prev || typeof prev !== "object") return prev;

        return {
          ...prev,
          ...(hideConnector !== undefined && { hideConnector }),
          ...(orientation !== undefined && { orientation })
        };
      });

      // Prepare updated values
      const payload = {
        name: siteName,
        description: siteDetails.description,
        hideConnector: hideConnector ?? siteDetails.hideConnector,
        orientation: orientation ?? siteDetails.orientation
      };

      await updateSite(payload);
    } catch (error) {
      showErrorToast(error instanceof Error ? error.message : "Something went wrong");
    }
  };

  const { data: siteDetails } = useSiteDetails({ siteId: siteName ?? selectedSiteName });
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);

  const navigate = useCustomNavigate();

  const deleteSiteMutation = useMutation({
    mutationFn: deleteSite,
    onSuccess: () => {
      dispatch(setSelectedSite(""));
      setShowDeletePrompt(false);
      showSuccessToast("Site deleted successfully");
      navigate(-1);
      queryClient.invalidateQueries({ queryKey: ["site"] });
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });
  const selectedSiteViewId = siteList?.find((item) => item.name === siteName)?.siteId;
  return (
    <>
      <Menubar className="!bg-transparent border-none p-0 ">
        <MenubarMenu>
          <MenubarTrigger className="rounded-full">
            <IconButton className="!bg-accent-foreground/10">
              <EllipsisVertical size={BUTTON_ICON_SIZE} />
            </IconButton>
          </MenubarTrigger>
          <MenubarContent>
            {siteName ? (
              <>
                {TENANT_ROLES.includes(userRole || "") && (
                  <MenubarItem
                    onClick={() => {
                      updateUserView.mutate({ siteViewId: selectedSiteViewId });
                    }}
                    disabled={siteViewId === selectedSiteViewId}
                  >
                    <Cog size="1.2rem" className="mr-2" />
                    Mark as Default
                  </MenubarItem>
                )}
                <MenubarItem
                  onClick={() => {
                    setShowDeletePrompt(true);
                  }}
                >
                  <Trash size={BUTTON_ICON_SIZE} className="mr-2" />
                  Delete
                </MenubarItem>
                <MenubarItem
                  onClick={() => {
                    cloneSiteRef.current?.showModal(siteName);
                  }}
                >
                  <Copy size={BUTTON_ICON_SIZE} className="mr-2" />
                  Clone Site
                </MenubarItem>

                <MenubarItem
                  onClick={() => {
                    shareDashboardRef.current?.openModal();
                  }}
                  disabled={permissions.sites !== "write"}
                >
                  <Share2 size={BUTTON_ICON_SIZE} className="mr-2" />
                  Shared Links
                </MenubarItem>

                <MenubarItem
                  onClick={() => {
                    addSiteWidgetRef.current?.openDrawer();
                  }}
                >
                  <Plus size={BUTTON_ICON_SIZE} className="mr-2" />
                  Add View
                </MenubarItem>
                <MenubarItem
                  onClick={() => {
                    updateSiteHideConnector({ hideConnector: !siteDetails?.hideConnector });
                  }}
                >
                  <ChevronsLeftRightEllipsisIcon size={BUTTON_ICON_SIZE} className="mr-2" />
                  {siteDetails?.hideConnector ? "Show Connector" : "Hide Connector"}
                </MenubarItem>
                <MenubarItem
                  onClick={() => {
                    updateSiteHideConnector({
                      orientation: siteDetails?.orientation === "grid" ? "inline" : "grid"
                    });
                  }}
                >
                  {siteDetails?.orientation === "grid" ? (
                    <Rows3 size={BUTTON_ICON_SIZE} className="mr-2" />
                  ) : (
                    <LayoutGrid size={BUTTON_ICON_SIZE} className="mr-2" />
                  )}
                  {siteDetails?.orientation === "grid" ? "Inline Layout" : "Grid Layout"}
                </MenubarItem>
              </>
            ) : (
              <>
                <MenubarItem
                  onClick={() => {
                    navigate(`/processIQ/sites/create`);
                  }}
                >
                  <Plus size={BUTTON_ICON_SIZE} className="mr-2" />
                  Create Site
                </MenubarItem>
                <MenubarItem
                  onClick={() => {
                    siteTriggersRef.current?.openDrawer();
                  }}
                >
                  <Rss size={BUTTON_ICON_SIZE} className="mr-2" />
                  Triggers
                </MenubarItem>
                <MenubarItem
                  onClick={() => {
                    siteAutomationFlowRef.current?.openDrawer();
                  }}
                >
                  <RefreshCcw size={BUTTON_ICON_SIZE} className="mr-2" />
                  Automation
                </MenubarItem>
              </>
            )}
          </MenubarContent>
        </MenubarMenu>
      </Menubar>
      <CloneSiteModal ref={cloneSiteRef} />
      {siteDetails && <AddSiteWidget ref={addSiteWidgetRef} siteId={siteDetails?.siteId} />}
      {siteDetails && <SiteTriggersSideBar ref={siteTriggersRef} />}
      {siteDetails && <SiteAutomationFlow ref={siteAutomationFlowRef} />}
      {siteDetails && (
        <ShareDashboardLink ref={shareDashboardRef} type="site" id={siteDetails.siteId} />
      )}

      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={siteName}
        onConfirm={() => {
          if (!siteDetails) return;
          deleteSiteMutation.mutate(siteDetails?.siteId);
        }}
        loading={deleteSiteMutation.isPending}
        onCancel={() => setShowDeletePrompt(false)}
      />
    </>
  );
});

export default Site;
