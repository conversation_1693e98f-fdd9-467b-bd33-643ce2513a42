import useAssetDigitalTwinList from "@hooks/site/useAssetDigitalTwinList";
import Table, { TableHead, TableRow } from "@components/Table";
import useTableSort from "@hooks/useTableSort";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import DataNotFound from "@components/DataNotFound";
import { getTableIndex } from "@utils/tableUtils";
import useTablePagination from "@hooks/classic/useTablePagination";
import useDebounce from "@hooks/useDebounce";
import { List, Plus, Search } from "lucide-react";
import Input from "@components/Input";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import Button from "@components/Button";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import HeadingIcon from "@components/HeadingIcon";
import useCustomNavigate from "@hooks/useCustomNavigate";

const SiteDigitalTwin = () => {
  const [sortFn, sort] = useTableSort();
  const { limit, page, setPage, setLimit, searchQuery, setSearchQuery } = useTablePagination();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: digitalTwinList, isLoading } = useAssetDigitalTwinList({
    limit,
    page,
    search: debouncedSearchQuery
  });
  const navigate = useCustomNavigate();

  return (
    <main className="space-y-6">
      <div className="flex items-center justify-between">
        <HeadingIcon title="Digital Twin List" Icon={List} />

        <div className="flex items-center gap-3">
          <Input
            startIcon={<Search size={BUTTON_ICON_SIZE} />}
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
            }}
            className="w-[25rem]"
            placeholder="Search Digital Twin"
          />
          <Button
            onClick={() => {
              navigate(`/advance/digital-twin/new`);
            }}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          >
            Digital Twin
          </Button>
        </div>
      </div>

      <Table
        head={
          <>
            <TableHead>No .</TableHead>
            <TableHead onSort={(order) => sort("assetName", order)}>Digital Twin Name</TableHead>
            <TableHead onSort={(order) => sort("description", order)}>Description</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading ? (
            <TableRowsSkeleton />
          ) : !digitalTwinList?.digitalTwinList.length ? (
            <DataNotFound title={`No Digital Twin Available`} isTable />
          ) : (
            <>
              {digitalTwinList?.digitalTwinList.toSorted(sortFn).map((digitalTwin, i) => (
                <tr
                  className="cursor-pointer"
                  onClick={() => {
                    navigate(`${digitalTwin.name}`);
                  }}
                  key={digitalTwin.name}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{digitalTwin.name}</TableRow>

                  <TableRow className="max-w-[4rem] truncate">{digitalTwin.description}</TableRow>

                  <TableRow>{convetUTCToLocal(digitalTwin.createdAt)}</TableRow>

                  <TableRow>{convetUTCToLocal(digitalTwin.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: digitalTwinList?.totalPages || 0
        }}
      />
    </main>
  );
};

export default SiteDigitalTwin;
