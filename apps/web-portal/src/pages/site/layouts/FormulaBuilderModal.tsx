import ActionButton from "@components/ActionButton";
import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import { Button as ShadButton } from "@components/shadcn/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogOverlay,
  DialogTitle,
  DialogTrigger
} from "@components/shadcn/components/dialog";
import { SEMI_LARGE_ICON } from "@frontend/shared/config/defaults";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import {
  Calculator,
  Clock,
  Divide,
  Minus,
  Plus,
  RefreshCcw,
  Sigma,
  Square,
  Trash2,
  X
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";

type OperatorType = {
  symbol: string;
  icon: any;
  description: string;
};

type FormulaItem =
  | string
  | { type: "operator"; symbol: string }
  | { type: "function"; name: string; field: string }
  | { type: "number"; value: string };

type FunctionType = {
  name: string;
  description: string;
};

const FUNCTIONS: FunctionType[] = [
  { name: "avg", description: "Calculate average value" },
  { name: "min", description: "Find minimum value" },
  { name: "max", description: "Find maximum value" },
  { name: "sum", description: "Calculate sum of values" },
  { name: "abs", description: "Absolute value" }
];

const OPERATORS: OperatorType[] = [
  { symbol: "+", icon: Plus, description: "Add values" },
  { symbol: "-", icon: Minus, description: "Subtract values" },
  { symbol: "*", icon: X, description: "Multiply values" },
  { symbol: "/", icon: Divide, description: "Divide values" }
  // { symbol: "%", icon: Percent, description: "Modulo operation" }
];

export type FieldOperation = "single" | "sum" | "average";

export interface Field {
  id: string;
  name: string;
}

export type Operator = "+" | "-" | "*" | "/";

type Props = {
  options: string[];
  onFormulaChange: (formula: string) => void;
  initialFormula: string;
  onSave: (formula: string) => void;
};

const parseFormula = (formulaString: string): FormulaItem[] => {
  if (!formulaString.trim()) return [];
  const tokens = formulaString.split(" ").filter(Boolean);
  const result: FormulaItem[] = [];

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    // Check for function
    const functionMatch = token.match(/^(\w+)\((\w+)\)$/);
    if (functionMatch) {
      const [_, funcName, field] = functionMatch;
      result.push({ type: "function", name: funcName, field });
      continue;
    }

    if (/^\d+$/.test(token)) {
      result.push({ type: "number", value: token });
      continue;
    }

    // Check for operator
    if (OPERATORS.some((op) => op.symbol === token)) {
      result.push({ type: "operator", symbol: token });
      continue;
    }

    // Must be a field
    result.push(token);
  }

  return result;
};

const FormulaBuilderModal = ({ options, onFormulaChange, initialFormula, onSave }: Props) => {
  const [modalVisibility, setModalVisibility] = useState(false);
  const [formula, setFormula] = useState<FormulaItem[]>(parseFormula(initialFormula || ""));
  const [error, setError] = useState<string>("");
  const [activeFunction, setActiveFunction] = useState<string | null>(null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  const [fieldToAdd, setFieldToAdd] = useState<string>("");
  const [numberToAdd, setNumberToAdd] = useState<string>("");

  const getFunctionIcon = (funcName: string) => {
    switch (funcName) {
      case "pow2":
        return Square;
      case "sum":
      case "stddev":
        return Sigma;
      case "first":
      case "last":
        return Clock;
      default:
        return Calculator;
    }
  };

  useEffect(() => {
    if (initialFormula) {
      const parsedFormula = parseFormula(initialFormula);

      setFormula(parsedFormula);
    }
  }, [initialFormula]);

  const addToFormula = useCallback(
    (item: string, type: "field" | "operator") => {
      console.log(item, type);

      if (type === "field") {
        if (activeFunction) {
          setFormula([...formula, { type: "function", name: activeFunction, field: item }]);
          setActiveFunction(null);
          return;
        }
        if (
          formula.length > 0 &&
          (typeof formula[formula.length - 1] === "string" ||
            formula[formula.length - 1].type === "function")
        ) {
          setError("Cannot add two fields or functions consecutively");
          return;
        }
      }

      const lastItem = formula[formula.length - 1];

      if (type === "operator") {
        if (!lastItem) {
          setError("Cannot add operator at this position");
          return;
        }

        if (lastItem.type === "operator") {
          setError("Cannot add operator at this position");
          return;
        }
      }

      if (lastItem?.type === "number" && type === "field") {
        setError("Can not add field at this position , add a operator after number");
        return;
      }

      const newFormula: FormulaItem[] = [
        ...formula,
        type === "field" ? item : { type: "operator", symbol: item }
      ];
      setFormula(newFormula);
      setError("");
      setFieldToAdd("");

      // Notify parent of formula change
      onFormulaChange(getFormulaString(newFormula));
    },
    [formula, activeFunction, onFormulaChange]
  );

  const addNumber = useCallback(
    (num: string) => {
      if (
        formula.length > 0 &&
        (typeof formula[formula.length - 1] === "string" ||
          formula[formula.length - 1].type === "function")
      ) {
        setError("Cannot add number after field, function, or another number");
        return;
      }
      // if (
      //   typeof formula[formula.length - 1] === "object" &&
      //   formula[formula.length - 1].type === "number"
      // ) {
      //   setFormula((prev) =>
      //     prev.map((item, index) => {
      //       if (index === formula.length - 1) {
      //         return { type: "number", value: `${item.value}${num}` };
      //       }
      //       return item;
      //     })
      //   );
      //   return;
      // }

      const newFormula: FormulaItem[] = [...formula, { type: "number", value: num }];
      setFormula(newFormula);
      setError("");
      setNumberToAdd("");
      onFormulaChange(getFormulaString(newFormula));
    },
    [formula, onFormulaChange]
  );

  const startFunction = useCallback(
    (funcName: string) => {
      if (
        formula.length > 0 &&
        (typeof formula[formula.length - 1] === "string" ||
          formula[formula.length - 1].type === "function")
      ) {
        setError("Cannot add function after field or another function");
        return;
      }
      setActiveFunction(funcName);
      setError("");
    },
    [formula]
  );

  const removeLast = useCallback(() => {
    const newFormula = formula.slice(0, -1);
    setFormula(newFormula);
    setError("");
    setActiveFunction(null);
    onFormulaChange(getFormulaString(newFormula));
  }, [formula, onFormulaChange]);

  const clearFormula = useCallback(() => {
    setFormula([]);
    setError("");
    setActiveFunction(null);
    onFormulaChange("");
    setFieldToAdd("");
    setNumberToAdd("");
  }, [onFormulaChange]);

  const getDisplayFormula = useCallback(() => {
    return formula.map((item, index) => {
      if (typeof item === "string") {
        return (
          <span key={index} className="bg-card px-2 py-1 rounded mx-1">
            {item}
          </span>
        );
      }
      if (item.type === "function") {
        return (
          <span key={index} className="bg-card px-2 py-1 rounded mx-1">
            {item.name}({item.field})
          </span>
        );
      }

      if (item.type === "number") {
        return (
          <span key={index} className="bg-card px-2 py-1 rounded mx-1">
            {item.value}
          </span>
        );
      }

      const OperatorIcon = OPERATORS.find((op) => op.symbol === item.symbol)?.icon;
      return <OperatorIcon key={index} className="mx-2 inline" size={SEMI_LARGE_ICON} />;
    });
  }, [formula]);

  const getFormulaString = (currentFormula: FormulaItem[]): string => {
    return currentFormula
      .map((item) => {
        if (typeof item === "string") return item;
        if (item.type === "function") return `${item.name}(${item.field})`;
        if (item.type === "number") return item.value;
        return item.symbol;
      })
      .join(" ");
  };

  return (
    <Dialog open={modalVisibility} onOpenChange={setModalVisibility}>
      <DialogOverlay className="z-[10000]" />

      <DialogTrigger asChild>
        <ActionButton type="edit" iconSize=".85rem" />
      </DialogTrigger>

      <DialogContent className="sm:max-w-[425px] !z-[10000]">
        <DialogHeader>
          <DialogTitle>Build Comparer</DialogTitle>
          <DialogDescription>Build a formula to compare the values with range.</DialogDescription>
        </DialogHeader>

        {activeFunction && (
          <div className="text-purple-600 text-sm">
            Select a field to apply {activeFunction}() to...
          </div>
        )}

        {/* Formula Display */}
        <div className="min-h-1 p-2 border rounded-lg bg-secondary flex items-center flex-wrap">
          {formula.length > 0 ? (
            getDisplayFormula()
          ) : (
            <span className="text-muted-foreground">Start building your formula...</span>
          )}
        </div>

        {/* Error Message */}
        {error && <div className="text-red-500 text-sm">{error}</div>}

        {/* Functions */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Functions</h3>
          <div className="flex flex-wrap gap-2">
            {FUNCTIONS.map((func) => {
              const IconComponent = getFunctionIcon(func.name);
              return (
                <button
                  key={func.name}
                  onClick={() => startFunction(func.name)}
                  onMouseEnter={() => setHoveredItem(func.name)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={`px-3 py-2 ${
                    activeFunction === func.name ? "bg-secondary" : "bg-secondary"
                  } rounded-md text-sm flex items-center gap-1 relative`}
                >
                  <IconComponent size={16} />
                  {func.name}
                  {hoveredItem === func.name && (
                    <div className="absolute bottom-full left-0 mb-2 px-2 py-1 bg-foreground text-white text-xs rounded whitespace-nowrap">
                      {func.description}
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium">Numbers</h3>
          <div className="flex gap-3 items-center">
            <Input
              type="number"
              value={numberToAdd}
              className="flex-1"
              placeholder="Enter number"
              onChange={(e) => setNumberToAdd(e.target.value)}
            />
            <ShadButton
              disabled={!numberToAdd}
              onClick={() => addNumber(numberToAdd)}
              type="button"
              size="icon"
            >
              <Plus className="h-4 w-4" />
              <span className="sr-only">Add</span>
            </ShadButton>
          </div>
        </div>

        {/* Fields */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Fields</h3>

          <div className="flex gap-3 items-center">
            <Dropdown
              options={options}
              onChange={(option) => setFieldToAdd(option)}
              className="flex-1"
              value={fieldToAdd}
              placeHolder="Select field"
              emptyOption="No fields available in selected assets."
              isSearchable
              menuClassName="!z-[10000]"
            />

            <ShadButton
              disabled={!fieldToAdd}
              onClick={() => addToFormula(fieldToAdd, "field")}
              type="button"
              size="icon"
            >
              <Plus className="h-4 w-4" />
              <span className="sr-only">Add</span>
            </ShadButton>
          </div>
        </div>

        {/* Operators */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Operators</h3>
          <div className="flex flex-wrap gap-2">
            {OPERATORS.map(({ symbol, icon: Icon, description }) => (
              <button
                key={symbol}
                onClick={() => addToFormula(symbol, "operator")}
                onMouseEnter={() => setHoveredItem(symbol)}
                onMouseLeave={() => setHoveredItem(null)}
                className="p-2 bg-secondary border-card-border rounded-md relative"
              >
                <Icon size={SEMI_LARGE_ICON} />
                {hoveredItem === symbol && (
                  <div className="absolute bottom-full left-0 mb-2 px-2 py-1 bg-background  text-xs rounded whitespace-nowrap">
                    {description}
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Controls */}
        <div className="flex gap-2">
          <Button
            onClick={removeLast}
            disabled={formula.length === 0}
            color="orange"
            className="px-4 py-2  rounded-md flex items-center gap-2"
          >
            <Trash2 size={16} />
            Remove Last
          </Button>
          <Button
            onClick={clearFormula}
            disabled={formula.length === 0}
            color="red"
            className="px-4 py-2  rounded-md flex items-center gap-2 "
          >
            <RefreshCcw size={16} />
            Clear All
          </Button>
        </div>

        {/* Formula String Preview */}

        <div className="pt-4 border-t">
          <h3 className="text-sm font-medium mb-2">Formula String for Backend:</h3>
          <code className="bg-secondary p-2 rounded-md block">
            {formula.length > 0 ? getFormulaString(formula) : "Empty"}
          </code>
        </div>

        <div className="flex justify-end items-center gap-4">
          <Button
            disabled={formula.length === 0}
            onClick={() => {
              onSave(getFormulaString(formula));
              setModalVisibility(false);
            }}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          >
            Save
          </Button>

          <Button
            onClick={() => {
              setModalVisibility(false);
              setNumberToAdd("");
              setFieldToAdd("");
            }}
            color="gray"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FormulaBuilderModal;
