import { MetricStatus, SiteArea } from "@/index";
import useAreaMetricResult from "@hooks/site/useAreaMetricResult";
import { ChevronDown, ChevronUp } from "lucide-react";
import { alpha } from "@mui/material";
import { formattedNumber } from "@src/pages/MonitorPage/utils";
import { useAppSelector } from "@src/store";
import clsx from "clsx";
import { memo, useMemo } from "react";
import { useParams } from "react-router-dom";
import AreaOverviewLoader from "../components/AreaOverviewLoader";
import SiteListItemOptions from "./SiteListItemOptions";
import { INPUT_ICON_SIZE } from "@utils/utilities";

const SiteOverViewItem = ({
  name,
  description,
  imageUrl,
  areaId,
  metrics,
  onClick,
  onEdit,
  onClone,
  StatusBadgeComponent,
  showStatusBadge = true
}: {
  name: string;
  description: string;
  onEdit?: () => void;
  imageUrl: string;
  areaId: number;
  metrics: SiteArea["metrics"];
  onClick?: () => void;
  onClone?: () => void;
  StatusBadgeComponent?: React.ReactNode;
  showStatusBadge?: boolean;
}) => {
  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);

  const metricIds = useMemo(() => {
    return metrics.map((metric) => metric.id).join(",");
  }, [metrics]);

  const { data: areaMetricResult, isLoading } = useAreaMetricResult({
    areaId,
    duration,
    metricIds
  });
  const isEditView = useParams().siteName;

  const metricValues = useMemo(() => {
    if (!areaMetricResult || !Array.isArray(areaMetricResult)) return [];
    return areaMetricResult.map((item) => ({
      ...item[item.length - 1],
      previousValue: item[item.length - 2]?.value
    }));
  }, [areaMetricResult]);

  return (
    <div
      className={clsx(
        " flex flex-col w-full relative    cursor-pointer ",
        isEditView && "!cursor-grab"
      )}
      id="site-area-overview"
    >
      {showStatusBadge && (
        <div className="flex items-start justify-between relative mb-2 ">
          <div className="overflow-hidden h-[2.5rem]">
            <h2 className="heading-3 capitalize truncate">{name}</h2>
            <p className=" absolute content-1 -bottom-2 text-muted-foreground mb-2 truncate whitespace-nowrap right-4 left-0  ">
              {description}
            </p>
          </div>
          <div className="flex items-start gap-1">
            {showStatusBadge && StatusBadgeComponent}
            {Boolean(isEditView) && <SiteListItemOptions onEdit={onEdit} onClone={onClone} />}
          </div>
        </div>
      )}

      <div className="card p-3 h-full cursor-pointer min-h-[18.25rem]" onClick={onClick}>
        <img
          src={imageUrl}
          alt="area-image "
          className="w-full h-40 object-cover rounded-md overflow-hidden "
          draggable={false}
        />
        {isLoading || !areaMetricResult ? (
          <AreaOverviewLoader metricsLength={metrics.length} />
        ) : (
          <div className="mt-4 space-y-1">
            {metricValues.map((item) => (
              <div key={item.name} className="flex items-center justify-between">
                <span className="text-sm font-medium text-slate-400 truncate whitespace-nowrap">
                  {item.name}
                </span>
                <div className="flex items-center gap-1 ml-1">
                  <TrendIndicator currenValue={item.value} previousValue={item.previousValue} />
                  <span className="font-mono text-sm font-semibold min-w-[3.15rem] text-end">
                    {formattedNumber(item.value, 1)}
                    {item.suffix}
                  </span>
                  <div
                    className="flex rounded-full"
                    style={{ backgroundColor: alpha(item.color, 0.2) }}
                  >
                    <div
                      className={`h-3 w-3 rounded-full m-1`}
                      style={{ backgroundColor: item.color }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const TrendIndicator = ({
  currenValue,
  previousValue
}: {
  currenValue: number;
  previousValue: number;
}) => {
  const change = currenValue - previousValue;
  const percentChange = (change / currenValue) * 100;
  const isPositive = change >= 0;

  return (
    <div className={`flex text-xs items-center ${isPositive ? "text-green-500" : "text-red-500"}`}>
      <span className=" font-semibold">{formattedNumber(percentChange || 0, 1)}%</span>
      {isPositive ? <ChevronUp size={INPUT_ICON_SIZE} /> : <ChevronDown size={INPUT_ICON_SIZE} />}
    </div>
  );
};

export default memo(SiteOverViewItem);
