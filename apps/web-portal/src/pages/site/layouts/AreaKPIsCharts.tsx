import { SiteArea } from "@/index";
import NoDataFound from "@components/NoDataFound";
import useAreaMetricResult from "@hooks/site/useAreaMetricResult";
import { alpha } from "@mui/material";
import ChartLoader from "@src/pages/MonitorPage/Charts/ChartLoader";
import SummaryCharts from "@src/pages/MonitorPage/Charts/SummaryCharts";
import { formattedNumber } from "@src/pages/MonitorPage/utils";
import { formatDistanceToNowLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useAppDispatch, useAppSelector } from "@src/store";
import { ChartPie } from "lucide-react";
import { memo, useMemo } from "react";
import AreaOverviewGuage from "./AreaOverviewGuage";
import { updateGraphDuration } from "@src/features/dashboardBuilderSlice";
import clsx from "clsx";
import SummaryCharts2 from "@src/pages/MonitorPage/Charts/SummaryCharts2";

const AreaKPIsCharts = ({ area }: { area: SiteArea }) => {
  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);

  const dispatch = useAppDispatch();

  const handleDurationChange = (dateString = "") => {
    const res = { title: "custom", value: dateString };
    dispatch(updateGraphDuration(res));
  };

  const metricIds = useMemo(() => {
    return area.metrics.map((metric) => metric.id).join(",");
  }, [area.metrics]);

  const { data: areaMetricResult, isLoading } = useAreaMetricResult({
    areaId: area.areaId,
    duration,
    metricIds
  });

  if (isLoading) {
    return (
      <div
        className={clsx(
          "grid  gap-3",
          `grid-cols-${Math.min(Math.max(area.metrics.length, 2), 5)}`
        )}
      >
        {area.metrics.map((item) => (
          <div key={item.id} className="card dark:!bg-transparent p-">
            <p className=" capitalize">{item.name}</p>
            <div className="h-48 items-center justify-center flex">
              <ChartLoader />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!areaMetricResult || !areaMetricResult.length) {
    return (
      <div
        className={clsx(
          "grid  gap-3",
          `grid-cols-${Math.min(Math.max(area.metrics.length, 2), 4)}`
        )}
      >
        {area.metrics.map((item) => (
          <div key={item.id} className="card dark:!bg-transparent p-">
            <p className=" capitalize">{item.name}</p>
            <div className="h-48 items-center justify-center flex">
              <NoDataFound />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div
      className={clsx(
        "grid  gap-4",
        `grid-cols-${Math.min(Math.max(areaMetricResult.length, 2), 4)}`
      )}
    >
      {areaMetricResult.map((item) => {
        const lastItem = item[item.length - 1];
        const areaName = area.name;
        const chartType =
          area.metrics.find((metricItem) => metricItem.name === lastItem.name)?.chartType || "Area";

        const distribution = calculateColorDistribution(item);

        return (
          <div
            key={item?.[0].name}
            className="card dark:!bg-transparent p-2  flex flex-col relative  h-[18.25rem] "
          >
            <div className="flex items-center justify-between">
              <div className="flex gap-1 items-center">
                <div className={"p-1 rounded-md my-1 bg-brandColor/20"}>
                  <ChartPie size={10} className=" text-brandColor" />
                </div>

                <p className=" capitalize text-sm ml-1">{`${areaName} - ${lastItem.name}`}</p>
              </div>
            </div>

            <div className=" flex items-end mt-1 gap-1 ">
              <p className=" text-2xl">
                {formattedNumber(lastItem.value, 2)} {lastItem.suffix}
              </p>
              <p className=" text-sm mb-[2px] text-muted-foreground">
                {formatDistanceToNowLocal(lastItem.timestamp)}
              </p>
              <div className="absolute top-1 right-0 ">
                <AreaOverviewGuage
                  ranges={
                    area.metrics.find((metricItem) => metricItem.name === lastItem.name)?.metrics ||
                    []
                  }
                  value={lastItem.value}
                />
              </div>
              <div className="flex gap-2 ml-auto mb-1">
                {distribution.map((percentageItem) => (
                  <div className=" flex items-center gap-1" key={percentageItem.color}>
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: percentageItem.color }}
                    />
                    <p className=" text-xs">{percentageItem.percentage.toFixed(1)}%</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex-1">
              <SummaryCharts2
                type={chartType === "Bar" ? "Bar" : "Line"}
                className="h-full"
                showAxis={false}
                handleDurationChange={handleDurationChange}
                data={{
                  labels: item.map((item) => item.timestamp),
                  datasets: [
                    {
                      label: lastItem.name,
                      data: item.map((item) => ({
                        x: item.timestamp,
                        y: item.value
                      })),
                      type: chartType === "Bar" ? "bar" : "line",
                      fill: true,
                      borderColor: item.map((item) => item.color),
                      backgroundColor: item.map((item) =>
                        chartType === "Bar"
                          ? item.color
                          : chartType === "Area"
                            ? alpha(item.color, 0.3)
                            : "transparent"
                      )
                    }
                  ]
                }}
                timeSeries
              />
            </div>
          </div>
        );
      })}
    </div>
  );
};

const calculateColorDistribution = (
  data: {
    name: string;
    value: number;
    color: string;
    timestamp: string;
  }[]
) => {
  try {
    const colorCounts = data.reduce<Record<string, number>>((acc, item) => {
      acc[item.color] = (acc[item.color] || 0) + 1;
      return acc;
    }, {});

    const total = data.length;

    return Object.entries(colorCounts).map(([color, count]) => ({
      color,
      count,
      percentage: Number(((count / total) * 100).toFixed(2))
    }));
  } catch (error) {
    console.log(error);

    return [];
  }
};

export default memo(AreaKPIsCharts);
