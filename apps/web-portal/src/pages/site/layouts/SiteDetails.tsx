import { MetricStatus, SiteArea } from "@/index";
import { isInPublicSharedLink } from "@api/_helpers";
import { cloneSiteArea } from "@api/site";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useSelectedSiteName from "@hooks/site/useSelectedSiteName";
import useSiteDetails from "@hooks/site/useSiteDetails";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { CircularProgress } from "@mui/material";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import clsx from "clsx";
import { ChartColumnBig, LayoutGridIcon, MoveRight } from "lucide-react";
import {
  ElementRef,
  ForwardedRef,
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from "react";
import { useParams } from "react-router-dom";
import SiteWidget from "../components/SiteWidget";
import AddAreaDrawer from "./AddAreaDrawer";
import AreaKPIsCharts from "./AreaKPIsCharts";
import AreaWidgets from "./AreaWidgets";
import SiteAreaStatus from "./SiteAreaStatus";
import SiteListItemOptions from "./SiteListItemOptions";
import SiteOverViewItem from "./SiteOverViewItem";

export const STATUS_TYPES: Record<MetricStatus, { label: string; color: string }> = {
  OPTIMAL: { label: "Optimal", color: "#01bc7c" },
  NORMAL: { label: "Normal", color: "#00c851" },
  CAUTION: { label: "Caution", color: "#ff9901" },
  WARNING: { label: "Warning", color: "#fe6901" },
  CRITICAL: { label: "Critical", color: "#ff1f57" }
};
export interface SiteDetailsRef {
  reorderedAreas: () => number[] | null;
}
const SiteDetails = (
  { homeSiteView }: { homeSiteView?: string },
  ref: ForwardedRef<SiteDetailsRef>
) => {
  const fallbackSiteName = useSelectedSiteName();
  const selectedSiteName = homeSiteView || fallbackSiteName;
  const isEditView = useParams().siteName;
  const { data: siteDetails, isLoading: siteLoading } = useSiteDetails({
    siteId: selectedSiteName!,
    enabled: Boolean(selectedSiteName)
  });

  const addAreaDrawerRef = useRef<ElementRef<typeof AddAreaDrawer>>(null);

  const [draggedItem, setDraggedItem] = useState<number>();
  const [items, setItems] = useState<SiteArea[]>([]);

  const navigate = useCustomNavigate();

  useEffect(() => {
    if (siteDetails?.areas) {
      setItems(siteDetails.areas);
    }
  }, [siteDetails]);

  const reorderedAreas = useCallback(() => {
    if (!siteDetails?.areas) {
      return null;
    }

    // Extract areaIds from both arrays
    const initialAreas = siteDetails.areas.map((area) => area.areaId);
    const updatedAreas = items.map((area) => area.areaId);

    // Check if arrays are identical
    const hasChanged = initialAreas.some((id, index) => id !== updatedAreas[index]);

    // If no change, return null
    if (!hasChanged) {
      return null;
    }

    // If changed, return array of areaIds in new order
    return updatedAreas;
  }, [siteDetails, items]);

  useImperativeHandle(ref, () => ({ reorderedAreas }), [reorderedAreas]);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    setDraggedItem(index);
    e.dataTransfer.effectAllowed = "move";
    // Required for Firefox
    e.dataTransfer.setData("text/html", "");
  };
  // console.log(draggedItem, items);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();

    if (typeof draggedItem !== "number") return;
    // Don't replace items with themselves
    if (draggedItem === index) {
      return;
    }

    // Update the items array
    const itemsCopy = [...items];
    const draggedItemContent = itemsCopy[draggedItem];

    itemsCopy.splice(draggedItem, 1);
    itemsCopy.splice(index, 0, draggedItemContent);

    setItems(itemsCopy);
    setDraggedItem(index);
  };

  const handleAreaOverViewClick = (areaName: string) => {
    if (isInPublicSharedLink()) {
      navigate(`${siteDetails?.name}/${areaName}`);
      return;
    }
    navigate(`/processIQ/sites/${siteDetails?.name}/${areaName}`);
  };

  const handleDragEnd = () => {
    setDraggedItem(undefined);
  };

  const handleClone = async (area: SiteArea) => {
    try {
      if (!siteDetails) return;
      await cloneSiteArea(area.areaId);
      showSuccessToast("Area cloned successfully");
      queryClient.invalidateQueries({ queryKey: ["site", siteDetails?.name] });
    } catch (error) {
      showErrorToast(error instanceof Error ? error.message : "Something went wrong");
    }
  };

  if (!selectedSiteName) return null;

  if (siteLoading) {
    return (
      <div className="  flex items-center  justify-center w-full" style={{ marginTop: "25vh" }}>
        <CircularProgress />
      </div>
    );
  }

  if (!siteDetails?.areas)
    return (
      <div>
        <DataNotFound title="Site not found" />
      </div>
    );

  const GridLayout = () => (
    <>
      <div
        className={clsx(
          "grid grid-cols-6 sm:grid-cols-2 gap-4 md:grid-cols-4 pt-0 pr-0 ",
          items.length === 6 ? "lg:grid-cols-6" : "lg:grid-cols-5"
        )}
      >
        {items.map((area, index) => (
          <div key={area.areaId} className="flex items-center w-full">
            <div
              draggable={isEditView ? true : false}
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDragEnd={handleDragEnd}
              onDrop={handleDragEnd}
              className={`h-full w-full ${draggedItem === index ? "opacity-70" : "opacity-100"}`}
            >
              <SiteOverViewItem
                StatusBadgeComponent={<SiteAreaStatus area={area} />}
                showStatusBadge={true}
                name={area.name}
                description={area.description}
                imageUrl={area.imgURL}
                areaId={area.areaId}
                metrics={area.metrics}
                onEdit={() => {
                  addAreaDrawerRef.current?.openDrawer({
                    name: area.name,
                    description: area.description,
                    assets: area.assets,
                    image: area.imgURL,
                    metrics: area.metrics,
                    areaId: area.areaId,
                    fromDb: true
                  });
                }}
                onClick={() => handleAreaOverViewClick(area.name)}
                onClone={() => handleClone(area)}
              />
            </div>

            {!siteDetails?.hideConnector && (
              <MoveRight
                className={clsx("text-muted-foreground", index === items.length - 1 && "invisible")}
                size={20}
              />
            )}
          </div>
        ))}
      </div>

      <HeadingIcon title="KPIs" Icon={ChartColumnBig} />
      <div className="flex flex-col gap-y-3">
        {siteDetails.areas.map((item) => (
          <AreaKPIsCharts area={item} key={item.areaId} />
        ))}
      </div>
    </>
  );
  const InlineLayout = () => (
    <div className="space-y-8">
      {items.map((area, index) => (
        <div key={area.areaId} className="space-y-4">
          {/* Area Header */}
          <div className="flex items-center justify-between pb-2 border-b border-card-border">
            <div className="flex gap-2 items-start">
              <div className="">
                <h3 className="heading-3">{area.name}</h3>
                <p className="text-sm text-muted-foreground">{area.description}</p>
              </div>
              <SiteAreaStatus area={area} />
            </div>

            <div className="flex items-start gap-1">
              {Boolean(isEditView) && (
                <SiteListItemOptions
                  onEdit={() => {
                    addAreaDrawerRef.current?.openDrawer({
                      name: area.name,
                      description: area.description,
                      assets: area.assets,
                      image: area.imgURL,
                      metrics: area.metrics,
                      areaId: area.areaId,
                      fromDb: true
                    });
                  }}
                  onClone={() => handleClone(area)}
                />
              )}
            </div>
          </div>

          {/* Area and KPIs in same row */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            {/* Area Overview - Takes 1 column */}
            <div className="lg:col-span-1">
              <div
                draggable={isEditView ? true : false}
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={(e) => handleDragOver(e, index)}
                onDragEnd={handleDragEnd}
                onDrop={handleDragEnd}
                className={`h-full w-full ${draggedItem === index ? "opacity-50" : "opacity-100"}`}
              >
                <SiteOverViewItem
                  name={area.name}
                  description={area.description}
                  imageUrl={area.imgURL}
                  areaId={area.areaId}
                  metrics={area.metrics}
                  onEdit={() => {
                    addAreaDrawerRef.current?.openDrawer({
                      name: area.name,
                      description: area.description,
                      assets: area.assets,
                      image: area.imgURL,
                      metrics: area.metrics,
                      areaId: area.areaId,
                      fromDb: true
                    });
                  }}
                  onClick={() => handleAreaOverViewClick(area.name)}
                  onClone={() => handleClone(area)}
                  showStatusBadge={false}
                />
              </div>
            </div>

            {/* KPIs - Takes 4 columns */}
            <div className="lg:col-span-4">
              <AreaKPIsCharts area={area} />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
  return (
    <div className="space-y-5 flex-1">
      {/* <LayoutToggleButton /> */}

      {siteDetails.orientation === "grid" ? <GridLayout /> : <InlineLayout />}

      <HeadingIcon title="All Widgets" Icon={LayoutGridIcon} />

      <div className="grid grid-cols-2 gap-5">
        {siteDetails.areas.map((item) => (
          <AreaWidgets area={item} key={item.areaId} siteName={selectedSiteName} />
        ))}
        {siteDetails.widgets.map((item) => (
          <SiteWidget key={item.widgetId} widget={item} />
        ))}
      </div>

      <AddAreaDrawer
        ref={addAreaDrawerRef}
        siteId={siteDetails.siteId}
        onAddNewArea={() => {}}
        onUpdateArea={() => {}}
        areas={siteDetails.areas}
        mode="edit"
      />
    </div>
  );
};

export default memo(forwardRef(SiteDetails));
