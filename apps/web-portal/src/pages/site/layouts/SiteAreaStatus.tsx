import { MetricStatus, SiteArea } from "@/index";
import useAreaMetricResult from "@hooks/site/useAreaMetricResult";
import { alpha } from "@mui/system";
import { useAppSelector } from "@src/store";
import React, { useMemo } from "react";
import { STATUS_TYPES } from "./SiteDetails";

const SiteAreaStatus = ({ area }: { area: SiteArea }) => {
  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);

  const metricIds = useMemo(() => {
    return area.metrics.map((metric) => metric.id).join(",");
  }, [area.metrics]);

  const { data: areaMetricResult } = useAreaMetricResult({
    areaId: area.areaId,
    duration,
    metricIds
  });

  const status = useMemo(() => {
    if (!areaMetricResult || !Array.isArray(areaMetricResult)) {
      return "OPTIMAL" as MetricStatus;
    }

    const metricValues = areaMetricResult.map((item) => ({
      ...item[item.length - 1]
    }));

    return getClusterStatus(metricValues.map((item) => item.status));
  }, [areaMetricResult]);

  return <StatusBadge status={status} />;
};

export default SiteAreaStatus;

function StatusBadge({ status }: { status: MetricStatus }) {
  const { color, label } = useMemo(() => {
    const statusDetails = STATUS_TYPES[status];
    if (!statusDetails) {
      return {
        label: "Unknown",
        color: "#cecece"
      };
    }
    return statusDetails;
  }, [status]);

  return (
    <span
      className={`rounded-full border px-2 py-0.5 text-xs font-medium `}
      style={{ backgroundColor: alpha(color, 0.2), color, borderColor: color }}
    >
      {label}
    </span>
  );
}

function getClusterStatus(serviceStatuses: MetricStatus[]) {
  const statusPriority = {
    OPTIMAL: 0,
    NORMAL: 1,
    CAUTION: 2,
    WARNING: 3,
    CRITICAL: 4
  };

  let highestPriorityFound = -1;
  let clusterStatus = "OPTIMAL";

  for (const status of serviceStatuses) {
    const priority = statusPriority[status];

    if (priority > highestPriorityFound) {
      highestPriorityFound = priority;
      clusterStatus = status;
    }
  }

  return clusterStatus as MetricStatus;
}
