import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useTablePagination from "@hooks/classic/useTablePagination";
import useSiteAssetList from "@hooks/site/useSiteAssetList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import HeadingIcon from "@components/HeadingIcon";
import { List, Search, Plus } from "lucide-react";
import Input from "@components/Input";
import { ElementRef, useRef } from "react";
import AddAssetDrawer from "./layouts/AddAssetDrawer";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

const SiteAssets = () => {
  const { limit, page, setPage, setLimit, searchQuery, setSearchQuery } = useTablePagination();
  const addAssetDrawerRef = useRef<ElementRef<typeof AddAssetDrawer>>(null);

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const [sortFn, sort] = useTableSort();
  const { data: assetList, isLoading: isLoading } = useSiteAssetList({
    enabled: true,
    search: debouncedSearchQuery,
    limit,
    page
  });

  const navigate = useCustomNavigate();

  return (
    <main className="space-y-4">
       <HeaderSection
              title="Assets List"
              description="Manage your Assets"
              actions={
                <Button
                   onClick={() => {
              addAssetDrawerRef.current?.openDrawer();
            }}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                >
                  Create Asset
                </Button>
              }
            />
        <Card className="space-y-4">
          <Input
            startIcon={<Search size={BUTTON_ICON_SIZE} />}
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
            }}
            className="flex-1"
            placeholder="Search asset"
          />

      <Table
        head={
          <>
            <TableHead>No .</TableHead>
            <TableHead onSort={(order) => sort("assetName", order)}>Asset Name</TableHead>

            <TableHead onSort={(order) => sort("description", order)}>Description</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading ? (
            <TableRowsSkeleton />
          ) : !assetList?.assetList.length ? (
            <DataNotFound title={`No Assets Available`} isTable />
          ) : (
            <>
              {assetList?.assetList.toSorted(sortFn).map((asset, i) => (
                <tr
                  className="cursor-pointer"
                  onClick={() => navigate(`${asset.name}`)}
                  key={asset.name}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>
                    <div className="flex items-center gap-2">
                      <img
                        className="object-cover w-[2.5rem] rounded-lg"
                        alt="thing"
                        src={asset.imgURL}
                      />

                      <p>{asset.name}</p>
                    </div>
                  </TableRow>

                  <TableRow className="max-w-[4rem] truncate">{asset.description}</TableRow>

                  <TableRow>{convetUTCToLocal(asset.createdAt)}</TableRow>

                  <TableRow>{convetUTCToLocal(asset.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: assetList?.totalPages || 0
        }}
      />
      <AddAssetDrawer ref={addAssetDrawerRef} />
      </Card>
    </main>
  );
};

export default SiteAssets;
