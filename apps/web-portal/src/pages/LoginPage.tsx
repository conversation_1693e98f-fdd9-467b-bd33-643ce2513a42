import BrandLogo from "@components/BrandLogo";
import { Button as Shadcn<PERSON><PERSON>on } from "@components/shadcn/components/button";
import { Card } from "@components/ui";
import { login, resetPassword, sendResetCode } from "@frontend/shared/api/user";
import { getUserData } from "@frontend/shared/store/userSlice";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { Alert, Checkbox, FormControlLabel, IconButton, Tooltip } from "@mui/material";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Eye, EyeOff, Hash, Lock, LogIn, Mail, RefreshCw, Send } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useSearchParams } from "react-router-dom";
import { MODE } from "../api";
import Button from "../components/Button";
import Input from "../components/Input";
import PasswordInput from "../components/PasswordInput";
import { useAppDispatch, useAppSelector } from "../store";

function LoginPage() {
  const [searchParam] = useSearchParams();
  const user = useAppSelector((state) => state.user.user);
  const [rememberMeChecked, setRememberMeChecked] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState(null);
  const [currentScreen, setCurrentScreen] = useState(searchParam.get("tab") || "login");
  const [resetCodeSent, setResetCodeSent] = useState(!!searchParam.get("email"));
  const [loadingBtn, setLoadingBtn] = useState({
    login: false,
    restCode: false,
    resetPassword: false
  });
  const navigate = useCustomNavigate();

  const loginForm = useForm({
    defaultValues: {
      password: MODE === "development" ? "Qwertyuiop@123" : ""
    }
  });
  const dispatch = useAppDispatch();

  const resetPasswordForm = useForm({
    defaultValues: {
      verificationCode: searchParam.get("code") || "",
      email: searchParam.get("email") || ""
    }
  });

  useEffect(() => {
    if (user) navigate("/");
  }, [user]);

  const onLoginSubmit = async (values) => {
    try {
      setError(null);
      setLoadingBtn((prev) => {
        return { ...prev, login: true };
      });

      const resp = await login({ ...values, rememberMe: rememberMeChecked });
      if (resp.status === "Failure") {
        if (resp.errorType === "NewPasswordRequired") {
          setCurrentScreen("set-new-password");
        } else {
          setError(resp.message);
        }
      }
      if (resp.status === "Success") {
        dispatch(getUserData());
      }
      setLoadingBtn((prev) => {
        return { ...prev, login: false };
      });
    } catch (error) {
      let message = error.message;
      if (message === "Failed to fetch") {
        message = "Something went wrong, please try after some time.";
      }
      setError(message);
      setLoadingBtn((prev) => {
        return { ...prev, login: false };
      });
    }
  };
  const onPasswordResetSubmit = async (values) => {
    setLoadingBtn((prev) => {
      return { ...prev, resetPassword: true };
    });
    const resp = await resetPassword(values);
    if (resp.status === "Success") setCurrentScreen("login");
    else {
      setResetCodeSent(false);
      setError(resp.message);
    }
    setLoadingBtn((prev) => {
      return { ...prev, resetPassword: false };
    });
  };

  const handleSendResetCode = async () => {
    setLoadingBtn((prev) => {
      return { ...prev, resetCode: true };
    });

    const resp = await sendResetCode(resetPasswordForm.getValues().email);
    if (resp.status === "Success") setResetCodeSent(true);
    else setError(resp.message);
    setLoadingBtn((prev) => {
      return { ...prev, resetCode: false };
    });
  };

  const onSetPasswordSubmit = async (values) => {
    setLoadingBtn((prev) => {
      return { ...prev, login: true };
    });

    const resp = await login(values);
    if (resp.status === "Success") {
      dispatch(getUserData());
    }
    setLoadingBtn((prev) => {
      return { ...prev, login: false };
    });
  };

  useEffect(() => {
    setError(null);
  }, [currentScreen]);
  const getCurrentScreen = () => {
    switch (currentScreen) {
      case "login":
        return (
          <div className="w-full">
            {/* <h1 className="heading-4 text-center text-accent-foreground">Login to your Account</h1> */}
            <h2 className={`text-lg font-semibold text-center`}>Sign in to your account</h2>

            <form
              className="w-full mt-8 mb-2 space-y-6"
              onSubmit={loginForm.handleSubmit(onLoginSubmit)}
            >
              <Input
                label="Email"
                placeholder="Email"
                type="email"
                required
                startIcon={<Mail size={INPUT_ICON_SIZE} />}
                data-testid="login-email-input"
                {...loginForm.register("email")}
              />
              <Input
                label="Password"
                required
                placeholder="Password"
                type={showPassword ? "text" : "password"}
                data-testid="login-password-input"
                endIcon={
                  <Tooltip title={showPassword ? "Hide Password" : "Show Password"} arrow>
                    <IconButton
                      className="!p-2"
                      onClick={() => {
                        setShowPassword((prev) => !prev);
                      }}
                    >
                      {showPassword ? (
                        <EyeOff size={INPUT_ICON_SIZE} />
                      ) : (
                        <Eye size={INPUT_ICON_SIZE} />
                      )}
                    </IconButton>
                  </Tooltip>
                }
                startIcon={<Lock size={INPUT_ICON_SIZE} />}
                {...loginForm.register("password", { minLength: 8 })}
                error={!!loginForm.formState.errors.password}
                helperText={loginForm.formState.errors.password && "Minimum 8 characters"}
              />

              {error && (
                <Alert severity="error">
                  {error?.includes("no available server")
                    ? "Something went wrong, Please try again later"
                    : error}
                </Alert>
              )}
              <div className="between items-center">
                <FormControlLabel
                  control={
                    <Checkbox
                      onChange={(e) => {
                        setRememberMeChecked(e.target.checked);
                      }}
                    />
                  }
                  label="Remember"
                />
                <ShadcnButton
                  variant="ghost"
                  type="button"
                  onClick={() => setCurrentScreen("reset-password")}
                >
                  Forgot password?
                </ShadcnButton>
              </div>
              <div className="flex gap-6">
                <Button
                  startIcon={<LogIn size={INPUT_ICON_SIZE} />}
                  type="submit"
                  loading={loadingBtn.login}
                  loadingText="Logging in..."
                  className="flex-1"
                  data-testid="login-button"
                >
                  Login
                </Button>
              </div>
            </form>
          </div>
        );
      case "set-new-password":
        return (
          <div className="w-full">
            <h2 className={`text-lg font-semibold text-center`}>Set New Password</h2>
            <form
              className="w-full mt-4 mb-2 space-y-4"
              onSubmit={loginForm.handleSubmit(onSetPasswordSubmit)}
            >
              <Input
                label="Email"
                type="email"
                startIcon={<Mail size={INPUT_ICON_SIZE} />}
                data-testid="setpassword-email-input"
                {...loginForm.register("email")}
              />
              <Input
                label="Current Password"
                type="password"
                startIcon={<Lock size={INPUT_ICON_SIZE} />}
                data-testid="setpassword-password-input"
                {...loginForm.register("password")}
              />
              <PasswordInput
                label="New Password"
                showProgress
                startIcon={<Lock size={INPUT_ICON_SIZE} />}
                required
                register={loginForm.register("newPassword", {
                  required: true,
                  minLength: 8,
                  pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/
                })}
                error={!!loginForm.formState.errors.newPassword}
                helperText={loginForm.formState.errors.newPassword && "Create strong Password"}
              />
              <PasswordInput
                label="Confirm Password"
                startIcon={<Lock size={INPUT_ICON_SIZE} />}
                required
                register={loginForm.register("confirmPassword", {
                  required: true,
                  validate: (v) => v === loginForm.watch("newPassword") || "Passwords do not match"
                })}
                error={!!loginForm.formState.errors.confirmPassword}
                helperText={loginForm.formState.errors.confirmPassword && "Minimum 8 characters"}
              />

              {error && <Alert severity="error">{error}</Alert>}

              <Button
                startIcon={<LogIn size={BUTTON_ICON_SIZE} />}
                className="w-full"
                type="submit"
                loading={loadingBtn.login}
                loadingText="Logging in..."
              >
                Set Password
              </Button>
            </form>
            <ShadcnButton
              variant="ghost"
              className="mx-auto w-full mt-2 "
              onClick={() => setCurrentScreen("reset-password")}
            >
              Forgot password?
            </ShadcnButton>
          </div>
        );
      case "reset-password":
        return (
          <div className="w-full">
            <h2 className={`text-lg font-semibold  text-center`}>Reset New Password</h2>
            <form className="w-full mt-8 mb-2 space-y-4" onSubmit={(e) => e.preventDefault()}>
              <Input
                label="Email"
                type="email"
                startIcon={<Mail size={INPUT_ICON_SIZE} />}
                {...resetPasswordForm.register("email")}
                disabled={resetCodeSent}
                placeholder="Enter your email"
              />

              {resetCodeSent && (
                <>
                  <Input
                    label="Reset Code"
                    type="number"
                    required
                    startIcon={<Hash size={INPUT_ICON_SIZE} />}
                    {...resetPasswordForm.register("verificationCode")}
                  />
                  <PasswordInput
                    label="New Password"
                    showProgress
                    startIcon={<Lock size={INPUT_ICON_SIZE} />}
                    required
                    register={resetPasswordForm.register("newPassword", {
                      required: true,
                      minLength: 8
                    })}
                    error={!!resetPasswordForm.formState.errors.newPassword}
                    helperText={
                      resetPasswordForm.formState.errors.newPassword && "Minimum 8 characters"
                    }
                  />
                  {/* <Input
                    label="New Password"
                    required
                    type="password"
                    startIcon={<Lock size={16}/>}
                    {...resetPasswordForm.register("newPassword")}
                  /> */}
                </>
              )}
              {error && <Alert severity="error">{error}</Alert>}
              <div className=" flex items-center gap-8">
                <Button
                  startIcon={<Send size={BUTTON_ICON_SIZE} />}
                  className={clsx("w-full", resetCodeSent && "!hidden")}
                  onClick={handleSendResetCode}
                  loading={loadingBtn.resetCode}
                >
                  Send Reset Code
                </Button>
                <Button
                  startIcon={<RefreshCw size={BUTTON_ICON_SIZE} />}
                  className={clsx("w-full", !resetCodeSent && "!hidden")}
                  type="submit"
                  loading={loadingBtn.resetPassword}
                  onClick={resetPasswordForm.handleSubmit(onPasswordResetSubmit)}
                >
                  Reset Password
                </Button>
              </div>
            </form>
            <ShadcnButton
              variant="ghost"
              className="mx-auto mt-2 w-full "
              onClick={() => setCurrentScreen("login")}
            >
              Back to Login?
            </ShadcnButton>
          </div>
        );
      default:
        return <div />;
    }
  };
  return (
    <main
      className="center bg-secondary dark:bg-background"
      style={{ height: "100vh" }}
      // style={{ backgroundImage: `url(${LoginBG})` }}
      // style={{ backgroundColor :'#152733' }}
    >
      {/* <div className="absolute w-[600px]">
      <img src="https://brandio.io/envato/iofrm/html/images/graphic3.svg"/>
    </div> */}

      <Card className={clsx(" w-[500px] h-min-[600px]  overflow-hidden  p-0", " flex flex-col ")}>
        {/* <section className=" center gap-6 bg-[#d2fff9] rounded-l-md relative sm:hidden lg:flex">
          <img src={LoginBG} alt="Login Background" />

        </section> */}

        <div className="center py-10 flex-1">
          <section className=" w-3/4 mx-auto  ">
            <div className="mb-4">
              <BrandLogo className="h-20 mx-auto" />
            </div>
            {getCurrentScreen()}
          </section>
        </div>
        <div className="h-4 bg-brandColor " />
      </Card>
    </main>
  );
}

export default LoginPage;

export function NetworkBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const nodesRef = useRef<Node[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener("resize", resizeCanvas);

    // Initialize nodes with velocity - reduced density
    const initNodes = () => {
      nodesRef.current = Array.from({ length: 220 }, () => ({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5
      }));
    };

    initNodes();

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Create gradient background
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
      gradient.addColorStop(0, "#1e3a5f");
      gradient.addColorStop(1, "#0f2027");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const nodes = nodesRef.current;

      // Update node positions
      nodes.forEach((node) => {
        node.x += node.vx;
        node.y += node.vy;

        // Bounce off edges
        if (node.x <= 0 || node.x >= canvas.width) node.vx *= -1;
        if (node.y <= 0 || node.y >= canvas.height) node.vy *= -1;

        // Keep nodes in bounds
        node.x = Math.max(0, Math.min(canvas.width, node.x));
        node.y = Math.max(0, Math.min(canvas.height, node.y));
      });

      // Draw connections
      ctx.strokeStyle = "rgba(64, 224, 208, 0.3)";
      ctx.lineWidth = 1;

      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          const dx = nodes[i].x - nodes[j].x;
          const dy = nodes[i].y - nodes[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 120) {
            const opacity = 1 - distance / 120;
            ctx.strokeStyle = `rgba(64, 224, 208, ${opacity * 0.3})`;
            ctx.beginPath();
            ctx.moveTo(nodes[i].x, nodes[i].y);
            ctx.lineTo(nodes[j].x, nodes[j].y);
            ctx.stroke();
          }
        }
      }

      // Draw nodes
      nodes.forEach((node) => {
        ctx.beginPath();
        ctx.arc(node.x, node.y, 3, 0, Math.PI * 2);
        ctx.fillStyle = "#40e0d0";
        ctx.fill();

        // Add enhanced glow
        ctx.beginPath();
        ctx.arc(node.x, node.y, 6, 0, Math.PI * 2);
        ctx.fillStyle = "rgba(64, 224, 208, 0.2)";
        ctx.fill();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener("resize", resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" style={{ zIndex: -1 }} />
  );
}
