import { updateUser } from "@api/user";
import Button from "@components/Button";
import Input from "@components/Input";
import { PhoneInput } from "@components/PhoneNumberInput";
import FormDialog from "@components/FormDialog";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import { getUserData } from "@frontend/shared/store/userSlice";
import { useAppDispatch, useAppSelector } from "@src/store";
import { PHONE_VALIDATE } from "@utils/from_schema";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { MSP_ROLES } from "@utils/utilities";
import { Check,Map, MapPin, X } from "lucide-react";
import { ForwardedRef, forwardRef, useCallback, useImperativeHandle, useState } from "react";
import { useForm } from "react-hook-form";
import * as RPNInput from "react-phone-number-input";

type Ref = {
  openUpdateSideDrawer: () => void;
};
type FormaValues = {
  email : string;
  address : string;
  phone:RPNInput.Value;
  pincode:string;
}
const ProfileUpdate = (props: unknown, ref: ForwardedRef<Ref>) => {
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const user = useAppSelector((state) => state.user.user!);
  const userType = MSP_ROLES.includes(user.role) ? "msp" : "tenant";
  const dispatch = useAppDispatch();
  const userForm = useForm<FormaValues>({
    defaultValues: {
      email: user.email,
      address: user.address,
      phone: user.phone,
      pincode: user.pincode
    }
  });

  const onSubmit = async (values:FormaValues) => {
    const resp = await updateUser({
      ...values,
      userType
    });

    if (resp.status === "Success") {
      setTimeout(() => {
        dispatch(getUserData());
        showSuccessToast("User updated successfully");
        setShowUpdateModal(false);
      }, 500);
    } else {
      showErrorToast(resp.message);
    }
  };

  const openUpdateSideDrawer = useCallback(() => {
    setShowUpdateModal(true);
  }, []);
  useImperativeHandle(ref, () => ({ openUpdateSideDrawer }));

  return (
    <FormDialog
      open={showUpdateModal}
      onClose={() => setShowUpdateModal(false)}
      title="Edit User Details"
      notDismissable
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={() => setShowUpdateModal(false)}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            loading={userForm.formState.isSubmitting}
            type="submit"
            form="update-user-form"
          >
            Update
          </Button>
        </div>
      }
    >
      <form className=" space-y-4" onSubmit={userForm.handleSubmit(onSubmit)} id="update-user-form">
        <Input
          startIcon={<Map size={INPUT_ICON_SIZE} />}
          label="Address"
          {...userForm.register("address")}
        />
        <Input
          startIcon={<MapPin size={INPUT_ICON_SIZE} />}
          label="Pincode"
          type="number"
          {...userForm.register("pincode", {
            minLength: 6,
            maxLength: 6
          })}
          error={!!userForm.formState.errors.pincode}
          helperText={userForm.formState.errors.pincode && "Invalid Pincode"}
        />

        <PhoneInput
          {...userForm.register("phone", PHONE_VALIDATE.schema)}
          required
          label="Phone Number"
          placeholder="Phone Number"
          onChange={(e) => {
            userForm.setValue("phone", e);
          }}
          value={userForm.getValues("phone")}
          error={!!userForm.formState.errors.phone}
          helperText={userForm.formState.errors.phone && PHONE_VALIDATE.message}
        />
      </form>
    </FormDialog>
  );
};

export default forwardRef(ProfileUpdate);
