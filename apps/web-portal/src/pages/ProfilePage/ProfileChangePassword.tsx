import Button from "@components/Button";
import PasswordInput from "@components/PasswordInput";
import FormDialog from "@components/FormDialog";
import { changePassword } from "@frontend/shared/api/user";
import { INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import { useAppSelector } from "@src/store";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Check,Lock, X } from "lucide-react";
import { ForwardedRef, forwardRef, useCallback, useImperativeHandle, useState } from "react";
import { useForm } from "react-hook-form";

type Ref = {
  openUpdateSideDrawer: () => void;
};
type ChangePasswordType = {
  password:string;
  newPassword:string;
  confirmPassword:string;

}
const ProfileChangePassword = (props: unknown, ref: ForwardedRef<Ref>) => {
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const user = useAppSelector((state) => state.user.user!);

  const changePasswordForm = useForm<ChangePasswordType>();
  const onChangePassword = async (values:ChangePasswordType) => {
    const { password, newPassword } = values;

    const resp = await changePassword({
      email: user.email,
      password,
      newPassword
    });

    if (resp.status === "Success") {
      showSuccessToast("Password Changed successfully");
      resetFormState();
    } else {
      showErrorToast(resp.message);
    }
  };

  const resetFormState = () => {
    setShowUpdateModal(false);
    changePasswordForm.reset();
  };
  const openUpdateSideDrawer = useCallback(() => {
    setShowUpdateModal(true);
  }, []);
  useImperativeHandle(ref, () => ({ openUpdateSideDrawer }));

  return (
    <FormDialog
      open={showUpdateModal}
      onClose={resetFormState}
      title="Edit User Details"
      notDismissable
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={resetFormState}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            loading={changePasswordForm.formState.isSubmitting}
            type="submit"
            form="change-password-form"
          >
            Update
          </Button>
        </div>
      }
    >
      <form
        className="space-y-4"
        onSubmit={changePasswordForm.handleSubmit(onChangePassword)}
        id="change-password-form"
      >
        <PasswordInput
          label="Current Password"
          startIcon={<Lock size={INPUT_ICON_SIZE} />}
          required
          register={changePasswordForm.register("password", {
            required: true,
            minLength: 8
          })}
          error={!!changePasswordForm.formState.errors.password}
          helperText={changePasswordForm.formState.errors.password && "Minimum 8 characters"}
        />
        <PasswordInput
          label="New Password"
          showProgress
          startIcon={<Lock size={INPUT_ICON_SIZE} />}
          required
          register={changePasswordForm.register("newPassword", {
            required: true,
            minLength: 8,
            pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/
          })}
          error={!!changePasswordForm.formState.errors.newPassword}
          helperText={changePasswordForm.formState.errors.newPassword && "Create strong Password"}
        />
        <PasswordInput
          label="Confirm Password"
          startIcon={<Lock size={INPUT_ICON_SIZE} />}
          required
          register={changePasswordForm.register("confirmPassword", {
            required: true,
            validate: (v) =>
              v === changePasswordForm.watch("newPassword") || "Passwords do not match"
          })}
          error={!!changePasswordForm.formState.errors.confirmPassword}
          helperText={changePasswordForm.formState.errors.confirmPassword && "Minimum 8 characters"}
        />
      </form>
    </FormDialog>
  );
};

export default forwardRef(ProfileChangePassword);
