import Button from "@components/Button";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE } from "@frontend/shared/config/defaults";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import { useAppSelector } from "@src/store";
import { ROLES_Mapped } from "@utils/utilities";
import {
  AtSign,
  Calendar,
  Clock,
  Edit2,
  Key,
  KeyIcon,
  Map,
  MapPin,
  Phone,
  User
} from "lucide-react";
import { ElementRef, useRef } from "react";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import ProfileChangePassword from "./ProfileChangePassword";
import ProfileUpdate from "./ProfileUpdate";
import colors from "tailwindcss/colors";

const ProfileDetailTab = () => {
  const user = useAppSelector((state) => state.user.user!);
  const { data: permissions } = useUserGroupPermissions();
  const updateUserRef = useRef<ElementRef<typeof ProfileUpdate>>(null);
  const changePasswordRef = useRef<ElementRef<typeof ProfileChangePassword>>(null);

  return (
    <>
      <div>
        <div className="flex gap-2 items-center justify-between mb-4">
          <HeadingIcon title="User Details" Icon={User} />
          <div className="flex gap-4">
            <Button
              startIcon={<KeyIcon size={BUTTON_ICON_SIZE} />}
              onClick={() => {
                changePasswordRef?.current?.openUpdateSideDrawer();
              }}
              color="blue"
              noAccess={permissions.tntUsers !== "write"}
            >
              Change Password
            </Button>
            <Button
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
              onClick={() => {
                updateUserRef?.current?.openUpdateSideDrawer();
              }}
              color="gray"
              noAccess={permissions.tntUsers !== "write"}
            >
              Edit
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <DetailsCell icon={<User size={DATACELL_ICON_SIZE} />} title="Name" data={user?.name} />
          <DetailsCell
            icon={<Key size={DATACELL_ICON_SIZE} />}
            title="Role"
            data={
              <Label
                text={ROLES_Mapped.find((role) => role.key === user.role)?.value}
                color={ROLES_Mapped.find((role) => role.key === user.role)?.color as keyof typeof colors}
              />
            }
          />
          <DetailsCell
            icon={<Phone size={DATACELL_ICON_SIZE} />}
            title="Phone"
            data={user?.phone}
          />
          <DetailsCell
            icon={<AtSign size={DATACELL_ICON_SIZE} />}
            title="Email"
            data={user.email}
          />
          <DetailsCell
            icon={<Map size={DATACELL_ICON_SIZE} />}
            title="Address"
            data={user.address}
          />
          <DetailsCell
            icon={<MapPin size={DATACELL_ICON_SIZE} />}
            title="Pincode"
            data={user?.pincode}
          />
          <DetailsCell
            icon={<Calendar size={DATACELL_ICON_SIZE} />}
            title="Create Date"
            data={convetUTCToLocal(user?.createdAt)}
          />
          <DetailsCell
            icon={<Clock size={DATACELL_ICON_SIZE} />}
            title="Update Date"
            data={convetUTCToLocal(user?.updatedAt)}
          />
        </div>
      </div>
      <ProfileUpdate ref={updateUserRef} />
      <ProfileChangePassword ref={changePasswordRef} />
    </>
  );
};

export default ProfileDetailTab;
