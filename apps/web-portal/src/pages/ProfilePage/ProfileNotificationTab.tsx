import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";

import ContactShift from "../NotificationPage/Contacts/ContactShift";
import HeadingIcon from "@components/HeadingIcon";
import { Bell, CircleMinus, User } from "lucide-react";
import { DoNotDisturbTab, PreferencesTab } from "../NotificationPage/Contacts/ContactDetailPage";
import useContactDetails from "@hooks/notifications/useContactDetials";
import { useAppSelector } from "@src/store";



const ProfileNotificationTab = () => {
  const user = useAppSelector((state) => state.user.user!);

  const { data: contactDetails, isLoading: contactLoading } = useContactDetails({
    contactId: user?.contactId,
    enabled: Bo<PERSON>an(user.role.includes("Tenant"))
  });
  return contactLoading || !contactDetails || !contactDetails?.preferences ? (
    <CardLoadingSkeleton />
  ) : (
    <div className="space-y-6">
      <div className="space-y-4">
        <HeadingIcon Icon={User} title="User Shifts" />
        {!contactDetails?.shifts.length ? (
          <DataNotFound title="No Shifts Added" />
        ) : (
          <ContactShift teamsData={contactDetails?.shifts} />
        )}
      </div>
      <hr className="hr" />

      <div className="space-y-4">
        <HeadingIcon Icon={Bell} title="Notification Preferences" color="blue" />
        <PreferencesTab contactDetails={contactDetails} />
      </div>
      <hr className="hr" />
      <div className="space-y-4">
        <HeadingIcon Icon={CircleMinus} title="Do Not Disturb" />
        <DoNotDisturbTab contactDetails={contactDetails} />
      </div>
    </div>
  );
};

export default ProfileNotificationTab;
