import HeaderSection from "@components/layout/HeaderSection";
import SelectDuration from "../MonitorPage/layout/SelectDuration";
import LoggerGraph from "./layout/LoggerGraph";
import LoggerTable from "./layout/LoggerTable";

const LoggerPage = () => {
  return (
    <main className="flex flex-col gap-4 ">
      <div className="flex items-center justify-between">
        <HeaderSection title="Logs" description="View logs from your devices" />

        <SelectDuration type="logs" />
      </div>

      <LoggerGraph />
      <LoggerTable />
    </main>
  );
};

export default LoggerPage;
