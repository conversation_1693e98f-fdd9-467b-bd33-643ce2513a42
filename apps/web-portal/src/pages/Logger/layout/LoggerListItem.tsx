import DetailsCell from "@components/DetailsCell";
import Label from "@components/Label";
import { But<PERSON> } from "@components/shadcn/components/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@components/shadcn/components/dialog";
import { ScrollArea } from "@components/shadcn/components/scroll-area";
import { Separator } from "@components/shadcn/components/separator";
import { TableRow } from "@components/Table";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { ChevronRight, X } from "lucide-react";
import React, { useState } from "react";
import colors from "tailwindcss/colors";
import { useAppDispatch, useAppSelector } from "../../../store";

export const eventColor: Record<string, keyof typeof colors> = {
  "message.publish": "emerald",
  "mqttPosition.publish": "emerald",
  "message.delivered": "blue",
  "client.connack": "orange",
  "client.connect": "emerald",
  "client.disconnected": "rose",
  "client.connected": "emerald",
  "session.subscribed": "emerald",
  "client.subscribe": "pink",
  "client.authenticate": "emerald",
  "session.created": "emerald",
  "notification.failure": "rose",
  "sms.failure": "rose",
  "app-ws-events-api.getShadow": "purple",
  "app-ws-events-api.publishShadow": "purple",
  "notification.success": "emerald",
  "pushNotification.success": "emerald",
  "pushNotification.failure": "rose",
  "email.success": "blue",
  "email.failure": "rose",
  "sms.success": "cyan",
  "teams.success": "indigo",
  "teams.failure": "rose",
  "ota-releases.created": "blue",
  "ota-releases.deleted": "rose",
  "product.created": "sky",
  "thing.created": "emerald",
  "ota-jobs.success": "emerald",
  "thing.deleted": "rose",
  "product.deleted": "rose",
  "ota-jobs.failure": "rose"
};

export const getEventColor = (event: keyof typeof eventColor) => {
  const color = eventColor[event];
  if (color) {
    return colors[color][500];
  }
  return colors["gray"][500];
};

const LogDetailsModal = ({ selectedLog, setSelectedLog }) => {
  if (!selectedLog) return null;

  return (
    <Dialog open={!!selectedLog} onOpenChange={() => setSelectedLog(null)}>
      <DialogContent className="max-w-2xl max-h-[80vh] [&>button]:hidden p-0 gap-0">
        <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b border-border">
          <div className="flex items-start justify-between">
            <DialogTitle className="text-left text-xl font-semibold">
              {/* {getEventIcon(selectedLog.event)} */}
              Log Details - {selectedLog.event}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 -mt-1"
              onClick={() => setSelectedLog(null)}
            >
              <X size={BUTTON_ICON_SIZE} />
            </Button>
          </div>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] p-6">
          <div className="space-y-6">
            {/* Basic Info */}
            <div>
              <div className="grid grid-cols-2 gap-4">
                <DetailsCell
                  title="Event"
                  data={<Label text={selectedLog.event} color={eventColor[selectedLog.event]} />}
                />

                <DetailsCell title="Timestamp" data={convetUTCToLocal(selectedLog.timestamp)} />
                <DetailsCell title="Client ID" data={selectedLog.clientId} />

                <DetailsCell title="Username" data={selectedLog.username} />
                <DetailsCell title="Protocol" data={selectedLog.protocol} />

                <DetailsCell title="Tenant" data={selectedLog.tenant} />
              </div>
            </div>

            <Separator />

            {/* Raw Data */}
            <div className="space-y-4">
              <h3 className="heading-3">Raw Data</h3>
              <div className="p-3 bg-secondary rounded">
                <pre className="text-xs overflow-auto">{JSON.stringify(selectedLog, null, 2)}</pre>
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

const LoggerListItem = React.forwardRef(
  ({ item, index, onClick }: { item: any; index: number; onClick?: () => void }, ref: any) => {
    const { timestamp, event, id, clientId } = item;
    const color = getEventColor(event);
    const selected = useAppSelector(({ logger }) => logger.selectedId === id);
    const dispatch = useAppDispatch();
    const [selectedLog, setSelectedLog] = useState(null);

    return (
      <>
        {" "}
        <tr
          // className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
          onClick={() => setSelectedLog(item)}
          ref={ref}
          className="cursor-pointer"
        >
          <TableRow>{convetUTCToLocal(timestamp)}</TableRow>
          <TableRow>
            <Label text={event} color={eventColor[event]} />
          </TableRow>
          <TableRow className="">{clientId}</TableRow>
          <TableRow className="">{item.username}</TableRow>
          <TableRow className="">{item.protocol}</TableRow>
          <TableRow className="max-w-xs">
            <div className=" text-xs">
              {item.productName && <div>Product: {item.productName}</div>}
              {item.thingName && <div>Thing: {item.thingName}</div>}
              {item.topic && <div className="truncate">Topic: {item.topic}</div>}
              {!item.topic && !item.productName && !item.thingName && "NA"}
            </div>
          </TableRow>
          <TableRow className="">
            <ChevronRight size={BUTTON_ICON_SIZE} className="mx-auto" />
          </TableRow>
        </tr>
        <LogDetailsModal selectedLog={selectedLog} setSelectedLog={setSelectedLog} />
      </>
    );
  }
);

export default LoggerListItem;
