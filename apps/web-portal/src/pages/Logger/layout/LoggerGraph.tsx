import React, { useMemo } from "react";
import { useAppSelector } from "../../../store";
import { useQuery } from "@tanstack/react-query";
import { getLoggerGraph } from "../../../api/logger";
import Graph from "./Graph";
import dayjs from "dayjs";
import { getDiffDate } from "../../MonitorPage/utils";
import { useSearchParams } from "react-router-dom";
import { parseRawElasticCharData } from "@hooks/timeseries/parseRawChartData";
import { ChartData } from "chart.js";
import { getEventColor } from "./LoggerListItem";
import { getDateFromDurationString } from "@src/pages/UserTypes/Tracking/HomeSection/utils";

const LoggerGraph = () => {
  const duration = useAppSelector(({ logger }) => logger.duration.value);
  const clientId = useAppSelector(({ logger }) => logger.filter.clientId);

  const searchParams = useSearchParams()[0];
  const loggerType = searchParams.get("type") || "mqtt";

  const event = useMemo(() => {
    return searchParams.get("event") ? JSON.parse(searchParams.get("event") as string) : [];
  }, [searchParams]);

  const tenants = useMemo(() => {
    return searchParams.get("tenant") ? JSON.parse(searchParams.get("tenant") as string) : [];
  }, [searchParams]);

  const { data } = useQuery({
    queryKey: ["logger-data", duration, clientId, loggerType, event, tenants],
    queryFn: () => {
      const { startDate, endDate } = getDateFromDurationString(duration);
      return getLoggerGraph(
        `${startDate}TO${endDate}`,
        { clientId, event, tenant: tenants },
        loggerType
      );
    }
  });

  const { labels, datapoints, elaboratedDataKeys, elaboratedDataPoint } = data || {
    labels: [],
    datapoints: [],
    aggregations: {},
    elaboratedDataKeys: [],
    elaboratedDataPoint: []
  };
  const unit = getDiffDate(labels)[1];
  const formatter =
    unit === "month" ? "MMM YYYY" : unit === "day" ? "MMM DD, YYYY" : "MMM D, YYYY h:mm A";

  const chartData: ChartData["datasets"] = useMemo(() => {
    return parseRawElasticCharData({
      stacked: true,
      expanded: false,
      elaboratedDataPoint,
      elaboratedDataKeys,
      datapoints,
      dataKey: "event",
      chartType: "bar",
      generateColor: (key) => getEventColor(key)
    });
  }, [labels, elaboratedDataPoint, elaboratedDataKeys]);

  return (
    <div>
      <Graph labels={labels} datasets={chartData} />
      {labels?.length > 0 && (
        <div className="flex justify-between items-center mt-0.5 mx-1 text-sm font-medium">
          {[labels[0], labels[labels.length - 1]].map((item) => (
            <p key={item} className=" text-gray-600 dark:text-gray-400">
              {dayjs(item).format(formatter)}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};

export default LoggerGraph;
