import HeadingIcon from "@components/HeadingIcon";
import { useQuery } from "@tanstack/react-query";
import { LogsIcon } from "lucide-react";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { getUniqueFilterOptions } from "../../../api/logger";
import Dropdown from "../../../components/Dropdown";
import { setLoggerFilter } from "../../../features/loggerSlice";
import useDebounce from "../../../hooks/useDebounce";
import { useAppDispatch, useAppSelector } from "../../../store";
import SelectDuration from "../../MonitorPage/layout/SelectDuration";
import { MQTT_EVENT_LIST, PLATFORM_EVENT_LIST } from "../events";
import RefetchLogger from "./RefetchLogger";
import HeaderSection from "@components/layout/HeaderSection";

const LOGGER_TYPES = [
  { title: "Platform Logs", value: "platform" },
  { title: "Connectivity Logs", value: "mqtt" }
];

const LoggerConfig = () => {
  const selectedFilter = useAppSelector(({ logger }) => logger.filter);

  const [searchedText, setSearchedText] = useState("");
  const [searchParams, setSearchParams] = useSearchParams();
  const tenant = useAppSelector(({ user }) => user.tenant?.name || "");

  const loggerType = searchParams.get("type") || "mqtt";

  const dispatch = useAppDispatch();

  const debouncedSearchText = useDebounce(searchedText, 500);
  const { data, isLoading } = useQuery({
    queryKey: ["unique-filters", "clientId", tenant, debouncedSearchText],
    initialData: [],
    queryFn: () => getUniqueFilterOptions("clientId", debouncedSearchText)
  });

  const handleFilterChange = (option: any[], key: "clientId" | "event" | "tenant") => {
    if (!option) {
      dispatch(setLoggerFilter(null));
      return;
    }
    const filter = {
      [key]: option
    };
    if (key === "event") {
      setSearchParams((prev) => {
        prev.set("event", JSON.stringify(option));
        return prev;
      });
      return;
    }

    dispatch(setLoggerFilter(filter));
  };

  const selectedEvents = searchParams.get("event")
    ? JSON.parse(searchParams.get("event") as string)
    : [];

  return (
    <div className="flex items-center justify-between">
      <div className="flex gap-4 w-full items-end ">
        <Dropdown
          onChange={(option) => handleFilterChange(option, "clientId")}
          value={selectedFilter.clientId || []}
          options={data || []}
          isSearchable
          className="flex-grow"
          labelClassName=" text-muted-foreground text-xs mb-1 !ml-0"
          placeHolder="Select Client"
          label="Client"
          isMulti
          optionsLoading={isLoading}
          deepSearch={(text) => {
            setSearchedText(text);
          }}
        />
        <Dropdown
          onChange={(option) => handleFilterChange(option, "event")}
          value={selectedEvents}
          options={loggerType === "mqtt" ? MQTT_EVENT_LIST : PLATFORM_EVENT_LIST}
          isSearchable
          className="flex-grow"
          labelClassName=" text-muted-foreground text-xs mb-1 !ml-0"
          label="Events"
          placeHolder="Events"
          isMulti
        />

        <Dropdown
          onChange={(option: any) => {
            setSearchParams((prev) => {
              prev.set("type", option.value);
              prev.delete("event");
              return prev;
            });
          }}
          value={LOGGER_TYPES.find((item) => item.value === loggerType)}
          options={LOGGER_TYPES}
          getOptionLabel="title"
          className="w-[180px]"
          placeHolder="Select logs"
        />
        <RefetchLogger />
      </div>
    </div>
  );
};

export default LoggerConfig;
