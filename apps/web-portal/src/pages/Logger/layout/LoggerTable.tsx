import DataNotFound from "@components/DataNotFound";
import Table, { TableHead } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import { CircularProgress } from "@mui/material";
import { getDateFromDurationString } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useInfiniteQuery } from "@tanstack/react-query";
import { Hash } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";
import { useSearchParams } from "react-router-dom";
import { getLoggerTable } from "../../../api/logger";
import { useAppSelector } from "../../../store";
import LoggerConfig from "./LoggerConfig";
import LoggerListItem from "./LoggerListItem";

// DEFINED_IN_BACKEND
const LIMIT = 50;

// const EmptyLogs = () => {
//   const duration = useAppSelector(({ logger }) => logger.duration);

//   return (
//     <div className="absolute left-0 top-0 right-0 bottom-0 flex items-center justify-center">
//       <Card className="text-center">
//         <p className=" text-lg"> No logs found in</p>
//         <p>{duration.title !== "custom" ? duration.title : "Selected Interval"}</p>
//       </Card>
//     </div>
//   );
// };

const LoggerTable = () => {
  const { ref, inView } = useInView();
  const clientId = useAppSelector(({ logger }) => logger.filter.clientId);
  const duration = useAppSelector(({ logger }) => logger.duration.value);
  // const selectedItem = useAppSelector(({ logger }) => logger.selectedItem);

  const searchParams = useSearchParams()[0];
  const loggerType = searchParams.get("type") || "mqtt";

  const event = useMemo(() => {
    return searchParams.get("event") ? JSON.parse(searchParams.get("event") as string) : [];
  }, [searchParams]);

  const tenants = useMemo(() => {
    return searchParams.get("tenant") ? JSON.parse(searchParams.get("tenant") as string) : [];
  }, [searchParams]);

  const { isLoading, data, isSuccess, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ["table", duration, clientId, loggerType, event, tenants],
      queryFn: ({ pageParam = 1 }) => {
        const { startDate, endDate } = getDateFromDurationString(duration);

        return getLoggerTable(
          `${startDate}TO${endDate}`,
          { clientId, event, tenant: tenants },
          pageParam,
          loggerType
        );
      },
      getNextPageParam: (lastPage, allPages) => {
        const nextPage = lastPage.length === LIMIT ? allPages.length + 1 : undefined;
        return nextPage;
      },
      initialPageParam: 1
    });

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);

  const isEmpty = data?.pages[0]?.length === 0 && isSuccess && !isLoading;

  return (
    <Card variant="second" className="space-y-4">
      <LoggerConfig />
      <Table
        head={
          <>
            <TableHead>Timestamp</TableHead>
            <TableHead>Event</TableHead>
            <TableHead>Client ID</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Protocol</TableHead>
            <TableHead>Details</TableHead>
            <TableHead>Actions</TableHead>
          </>
        }
        body={
          <>
            {isSuccess ? (
              data?.pages.map((page) =>
                page.map((item: any, i: number) => {
                  if (page.length === i + 1) {
                    return <LoggerListItem ref={ref} key={item.id} item={item} index={i} />;
                  }
                  return <LoggerListItem key={item.id} index={i} item={item} />;
                })
              )
            ) : (
              <TableRowsSkeleton />
            )}
            {isEmpty && <DataNotFound title="No Logs Available" isTable Icon={Hash} />}
            {(isFetchingNextPage || isLoading) && (
              <tr>
                <td className="w-full p-4 text-center" colSpan={100}>
                  <CircularProgress size={16} />
                </td>
              </tr>
            )}
          </>
        }
      />
    </Card>
  );
};

export default LoggerTable;
