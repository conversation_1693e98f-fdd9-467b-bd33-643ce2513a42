import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import useProductDevicesCount from "@hooks/classic/useProductDevicesCount";
import useProductList from "@hooks/classic/useProductList";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { PRODUCT_DEFAULT_IMAGE } from "@utils/deviceMapping";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Search } from "lucide-react";
import React, { useState } from "react";
import { deleteProduct } from "../../api/product";
import { deleteRule } from "../../api/project";
import ConfirmPrompt from "../../components/ConfirmPrompt";
import { FilterData } from "../../components/FilterData";
import Label from "../../components/Label";
import Table, { TableHead, TableRow } from "../../components/Table";
import useTableSort from "../../hooks/useTableSort";
import { showErrorToast, showSuccessToast } from "../../utils";

const turncateText = (text = "") => {
  const truncatedText = text.slice(0, 50) + (text.length > 50 ? "..." : "");
  return truncatedText;
};
export function getAuthType(authType) {
  if (authType === "tls") {
    return <Label text="Certificate" color="yellow" />;
  } else if (authType === "basic") {
    return <Label text="Credential Based" color="cyan" />;
  } else {
    return "N/A";
  }
}

const generateColor = (type) => {
  if (!type || typeof type !== "string") return "gray";
  switch (type) {
    case "standard":
    case "shadow":
      return "green";
    case "managed":
      return "orange";
    case "simulated":
      return "violet";
    case "gatewaymanaged":
      return "yellow";
    case "timeSeries":
      return "blue";
    default:
      return "gray";
  }
};
function Projects() {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [deleteProject, setDeleteProject] = useState({
    id: null,
    name: "",
    thingType: null
  });
  const [selectedFilter, setSelectedFilters] = React.useState([]);

  const [sortFn, sort] = useTableSort();
  const navigate = useCustomNavigate();

  const { data: permissions } = useUserGroupPermissions();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: productDeviceCounts } = useProductDevicesCount({ enabled: true });

  const {
    data: products,
    isLoading: productsLoading,
    refetch: refetchProducts
  } = useProductList({
    page,
    limit,
    search: debouncedSearchQuery,
    productType: selectedFilter.length ? selectedFilter.join(",") : undefined
  });

  const handleDelete = async () => {
    const resp = await deleteProduct(deleteProject);
    if (resp.status === "Success") {
      deleteRule({ select: deleteProject.thingType });

      setTimeout(() => {
        showSuccessToast("Project deleted successfully");
        refetchProducts();
        setDeleteProject({ id: null, name: "", thingType: null });
      }, 1000);
    } else {
      showErrorToast(resp.message);
    }
  };

  return (
    <Card className="space-y-4">
      <h3 className="heading-2">{PRODUCT_VAR}s List</h3>

      <div className="flex gap-4 ">
        <Input
          className="flex-1"
          value={searchQuery}
          onChange={(e) => {
            setPage(1);
            setSearchQuery(e.target.value);
          }}
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        <FilterData
          title={`${PRODUCT_VAR} Type`}
          filters={selectedFilter}
          setFilters={setSelectedFilters}
          options={["managed", "standard", "mqtt", "gps"]}
        />
      </div>

      <Table
        resizable
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("productName", order)}>{PRODUCT_VAR} Name</TableHead>
            <TableHead onSort={(order) => sort("productType", order)}>{PRODUCT_VAR} Type</TableHead>
            <TableHead onSort={(order) => sort("productType", order)}>Authentication</TableHead>
            <TableHead onSort={(order) => sort("productType", order)}>Data Management</TableHead>
            <TableHead onSort={(order) => sort("otaType", order)}>OTA Type</TableHead>
            <TableHead>{THING_VAR} Counts</TableHead>
          </>
        }
        body={
          productsLoading ? (
            <TableRowsSkeleton />
          ) : !products?.productList.length ? (
            <DataNotFound title={`No ${PRODUCT_VAR}s Available`} isTable />
          ) : (
            products.productList.toSorted(sortFn).map((p, i) => (
              <tr
                onClick={() => {
                  navigate(`${p.productName}?productType=${p.productType}`);
                }}
                className="cursor-pointer"
                key={Object.values(p).join()}
              >
                <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                <TableRow title>
                  <div className="flex gap-4 items-center">
                    <img
                      className="object-cover size-10 rounded-lg"
                      alt="product"
                      src={p.imgURL || PRODUCT_DEFAULT_IMAGE}
                    />
                    <div className="">
                      <p className="font-medium">{p.productName}</p>
                      <p className="text-xs text-muted-foreground ">
                        {turncateText(p.description || "")}
                      </p>
                    </div>
                  </div>
                </TableRow>
                <TableRow>
                  <Label
                    color={generateColor(p.properties?.simulatorName ? "simulated" : p.productType)}
                    text={p.properties?.simulatorName ? "Simulated" : p.productType}
                    lowercase
                  />
                </TableRow>
                <TableRow>{getAuthType(p?.authentication?.type)} </TableRow>
                <TableRow>
                  <div className="flex gap-3">
                    {!p.dataManagement.length
                      ? "N/A"
                      : p.dataManagement.map((item) => (
                          <Label color={generateColor(item)} text={item} lowercase />
                        ))}
                  </div>
                </TableRow>
                <TableRow>{p?.otaType} </TableRow>

                <TableRow>
                  {productDeviceCounts.find((product) => product.productName === p.productName)
                    ?.devices || "0"}
                </TableRow>
              </tr>
            ))
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: products?.pages || 0
        }}
      />
      <ConfirmPrompt
        show={deleteProject.id !== null}
        item={deleteProject.name}
        validate
        onCancel={() => setDeleteProject({ id: null, name: "" })}
        onConfirm={handleDelete}
      />
    </Card>
  );
}

export default Projects;
