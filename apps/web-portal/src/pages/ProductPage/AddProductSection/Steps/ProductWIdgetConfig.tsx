import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import { IconButton } from "@mui/material";
import { useAppSelector } from "@src/store";
import { ChevronDown, ChevronUp, <PERSON><PERSON>, <PERSON>h, Plus, Trash } from "lucide-react";
import React from "react";
import Button from "../../../../components/Button";
import Dropdown from "../../../../components/Dropdown";
import Input from "../../../../components/Input";
import { CHART_TYPES, OPERATIONS } from "../../../DevicesPage/layout/utils";
import { formatIndexElasticIndex, generateRandomString } from "../../../MonitorPage/utils";
import { EMPTY_WIDGET } from "../AddProductProvider";
import ExpandedWidgetConfig from "./ExpandedWidgetConfig";
import { BUTTON_ICON_SIZE } from "@utils/utilities";

/**
 * @typedef {import("../../../../..").ThingWidget} ThingWidget
 */

/**
 * @typedef {object} Props
 * @property {ThingWidget[]} productWidgets
 * @property {boolean} [allowEmpty]
 * @property {Record<string,string[]>} sourcesMap
 * @property {React.Dispatch<React.SetStateAction<ThingWidget[]>>} setProductWidgets
 */

/**
 * @param {Props} param0
 * @returns {React.ReactElement}
 */
const ProductWIdgetConfig = ({
  productWidgets,
  setProductWidgets,
  sourcesMap,
  allowEmpty = false,
  access = false
}) => {
  const [expandedWidget, setExpandedWidget] = React.useState(null);

  const tenantName = useAppSelector(({ user }) => user.tenant?.name);
  const featureType = useAppSelector(({ user }) => user.tenant?.featureType);

  const addTemplate = () => {
    setProductWidgets((prev) => [...prev, { ...EMPTY_WIDGET, id: generateRandomString(10) }]);
  };

  const editTemplate = (id, data) => {
    setProductWidgets((prev) =>
      prev.map((item) => {
        if (item.id === id) {
          if (data.source) {
            return {
              ...item,
              ...data,

              field:
                data.type === "History"
                  ? data.field
                  : sourcesMap[data.source]?.includes(item.field)
                    ? item.field
                    : ""
            };
          }

          return { ...item, ...data };
        }
        return item;
      })
    );
  };

  const toggleExpandEditWidget = (widgetId) => {
    setExpandedWidget((prev) => (prev === widgetId ? null : widgetId));
  };

  const deleteTemplate = (id) => {
    setProductWidgets((prev) => prev.filter((item) => item.id !== id));
  };

  const cloneTemplate = (item) => {
    setProductWidgets((prev) => [
      ...prev,
      { ...item, id: generateRandomString(10), title: undefined }
    ]);
  };

  const allowHistoryWidget =
    !productWidgets.some((item) => item.type === "History") &&
    Object.keys(sourcesMap).find((key) => key.includes("time-series"));

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center justify-between">
        <h2 className="heading-2">Widget Templates</h2>
        <Button
          noAccess={access}
          startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          type="button"
          small
          onClick={addTemplate}
        >
          Add Widget Template
        </Button>
      </div>
      {!productWidgets.length ? (
        <DataNotFound title="No Widgets Added" />
      ) : (
        productWidgets.map((item) => (
          <div key={item.id}>
            <div className="flex gap-4 items-center">
              <Input
                placeholder="title"
                required
                label="Title"
                className="flex-1"
                labelClassName="helper-text"
                onChange={({ target: { value } }) => {
                  editTemplate(item.id, { title: value });
                }}
                value={item.title}
              />

              <Dropdown
                options={Object.keys(sourcesMap).map((source) => ({
                  title: formatIndexElasticIndex(source),
                  value: source
                }))}
                label="Source"
                required
                className="flex-1"
                placeHolder="Select source"
                labelClassName="helper-text"
                getOptionLabel="title"
                value={
                  item.source && {
                    title: formatIndexElasticIndex(item.source),
                    value: item.source
                  }
                }
                onChange={({ value }) => {
                  if (value === "Device") {
                    editTemplate(item.id, {
                      source: value,
                      type: "Info",
                      operation: "latest",
                      field: ""
                    });
                    return;
                  }
                  editTemplate(item.id, {
                    source: value,
                    type:
                      value.includes("shadow") && !["Fill", "Gauge"].includes(item.type)
                        ? "Gauge"
                        : item.type
                  });
                }}
              />

              <Dropdown
                options={sourcesMap[item.source] || []}
                placeHolder="field"
                required
                className="flex-1"
                labelClassName="helper-text"
                label="Field"
                // onAddField={(value) => {
                //   editTemplate(item.id, { field: value });
                // }}
                onChange={(value) => {
                  if (Array.isArray(value)) {
                    editTemplate(item.id, { field: value.filter((item) => item !== "All") });
                    return;
                  }
                  editTemplate(item.id, { field: value });
                }}
                value={item.field?.length ? item.field : []}
                isMulti={item.type === "History"}
              />

              <Dropdown
                options={
                  item.source.includes("shadow")
                    ? ["Fill", "Gauge", "Switch"]
                    : allowHistoryWidget
                      ? ["History", ...CHART_TYPES]
                      : CHART_TYPES
                }
                required
                disabled={item.source === "Device"}
                label="Widget type"
                className="flex-1"
                placeHolder="Widget type"
                labelClassName="helper-text"
                value={item.source === "Device" ? "Info Card" : item.type}
                onChange={(value) => {
                  if (value === "History") {
                    editTemplate(item.id, {
                      type: value,
                      source: `${tenantName}-time-series`,
                      field: []
                    });
                    return;
                  }
                  editTemplate(item.id, { type: value, field: "" });
                }}
              />
              <Dropdown
                options={OPERATIONS}
                required
                className="flex-1"
                placeHolder="Value condition"
                label="Value condition"
                disabled={item.source === "Device" || item.type === "History"}
                value={item.source === "Device" ? "latest" : item.operation}
                labelClassName="helper-text"
                onChange={(value) => {
                  editTemplate(item.id, { operation: value });
                }}
              />
              <div className="flex items-center gap-3  justify-center">
                <IconButton
                  color="inherit"
                  onClick={() => cloneTemplate(item)}
                  className="!p-2 !bg-accent-foreground/10 !mt-5"
                  disabled={item.type === "History"}
                >
                  <Copy size={BUTTON_ICON_SIZE} />
                </IconButton>
                {featureType !== "geo" && (
                  <IconButton
                    color="inherit"
                    disabled={item.type === "Switch" || item.type === "History"}
                    onClick={() => toggleExpandEditWidget(item.id)}
                    className="!p-2 !bg-accent-foreground/10 !mt-5"
                  >
                    {expandedWidget === item.id ? (
                      <ChevronUp size={BUTTON_ICON_SIZE} />
                    ) : (
                      <ChevronDown size={BUTTON_ICON_SIZE} />
                    )}
                  </IconButton>
                )}
                <IconButton
                  color="inherit"
                  onClick={() => deleteTemplate(item.id)}
                  className="!p-2 !bg-accent-foreground/10 !mt-5"
                  disabled={!allowEmpty && productWidgets.length === 1}
                >
                  <Trash size={BUTTON_ICON_SIZE} />
                </IconButton>
              </div>
            </div>
            {expandedWidget === item.id && (
              <ExpandedWidgetConfig
                updateWidget={(data) => {
                  editTemplate(item.id, data);
                }}
                className="mr-[9.6rem]"
                widget={item}
              />
            )}
          </div>
        ))
      )}
    </div>
  );
};

export default ProductWIdgetConfig;
