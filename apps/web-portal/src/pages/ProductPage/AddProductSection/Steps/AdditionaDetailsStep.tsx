import HeadingIcon from "@components/HeadingIcon";
import useNotificationEscalationGroup from "@hooks/notifications/useNotificationEscalationGroup";
import usePolicyTemplateList from "@hooks/security/usePolicyTemplateList";
import useCaNameList from "@hooks/tenant/useCANameList";
import useDebounce from "@hooks/useDebounce";
import { PRODUCT_VAR } from "@utils/featureLabels";
import { DEFAULT_VALIDATE } from "@utils/from_schema";
import { BookLock, Hash, MessageSquareCode, PackagePlus } from "lucide-react";
import { useEffect, useState } from "react";
import Dropdown from "../../../../components/Dropdown";
import Switch from "../../../../components/Switch";
import {
  PRODUCT_ADDITIONAL_DETAILS_KEY,
  PRODUCT_DATA_KEY,
  PRODUCT_META_DATA_KEY,
  useAddProducts
} from "../AddProductProvider";
import { PROTOCOL_VALUES } from "@src/pages/UserTypes/Tracking/ProductPage/utils";
import { Card } from "@components/ui";

const AdditionaDetailsStep = () => {
  const {
    productAdditionalDetails,
    setProductAdditionalDetails,
    productMetaData,
    productForm,
    productData,
    setProductData,
    productTopicsList,
    setProductTopicsList
  } = useAddProducts();

  const [escalationGroupsSearchQuery, setEscalationGroupsSearchQuery] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: escalationGroups, isLoading: escalationGroupsLoading } =
    useNotificationEscalationGroup({
      searchQuery: escalationGroupsSearchQuery
    });

  const caNames = useCaNameList({});
  const [authenticationType, setAuthenticationType] = useState(
    productAdditionalDetails.authenticationType || null
  );
  const [dataManagement, setDataManagement] = useState(productAdditionalDetails.dataManagement);
  const [authorization, setAuthorization] = useState(
    productAdditionalDetails.authorization || null
  );
  const [caName, setCaName] = useState(productAdditionalDetails.caName || null);

  const [isTopicEnable, setIsTopicEnable] = useState(productData.isTopic);
  const [otaType, setOtaType] = useState(productAdditionalDetails.otaType || "none");

  const onPageLeave = (key, data) => {
    localStorage.setItem(key, JSON.stringify(data));
  };

  const { data: policyTemplate, isLoading: isPolicyTemplateLoading } = usePolicyTemplateList({
    search: debouncedSearchQuery
  });

  useEffect(() => {
    onPageLeave(PRODUCT_DATA_KEY, productData);
  }, [productData]);

  useEffect(() => {
    onPageLeave(PRODUCT_ADDITIONAL_DETAILS_KEY, productAdditionalDetails);
  }, [productAdditionalDetails]);

  useEffect(() => {
    onPageLeave(PRODUCT_META_DATA_KEY, productMetaData);
  }, [productMetaData]);
  useEffect(() => {
    if (!dataManagement.includes("shadow")) {
      const filteredProductTopicsList = productTopicsList.filter(
        (obj) => obj.shortName !== "Shadow Reported"
      );
      setProductTopicsList(filteredProductTopicsList);
    }
    if (!dataManagement.includes("timeSeries")) {
      const filteredProductTopicsList = productTopicsList.filter(
        (obj) => obj.shortName !== "timeSeries"
      );
      setProductTopicsList(filteredProductTopicsList);
    }
  }, [dataManagement]);

  return (
    <div className="space-y-8">
      <HeadingIcon Icon={Hash} title="Additional Details" />

      <div className="grid grid-cols-12 gap-8  child:w-full divide-x-2 items-start">
        <div className="grid col-span-6 grid-cols-2 gap-4">
          {productData.productType === "geo" && (
            <Dropdown
              onChange={(option) => {
                setProductAdditionalDetails((prev) => ({
                  ...prev,
                  geoProductType: option
                }));
                if (option === "gps") {
                  productForm.unregister([
                    "policyTemplate",
                    "authority",
                    "authentication",
                    "otaType"
                  ]);
                } else {
                  productForm.unregister("protocol");
                }
                productForm.setValue("geoProductType", option);
                productForm.trigger("geoProductType");
              }}
              value={productAdditionalDetails.geoProductType}
              options={["mqtt", "gps"]}
              label="Geo Product Type"
              required
              placeHolder="Select Geo Product Type"
            />
          )}
          <Dropdown
            label="Monitoring Groups "
            onChange={(option) => {
              setProductData((prev) => ({
                ...prev,
                monitoringGroups: option
              }));
            }}
            options={escalationGroups?.escalationGroups?.map((item) => item.groupName) || []}
            optionsLoading={escalationGroupsLoading}
            isSearchable
            isMulti
            value={productData.monitoringGroups}
            placeHolder="Select Escalations"
            newOption={{
              placeHolder: "Create New Escalations...",
              target: "/notifications/escalations"
            }}
            deepSearch={(value) => setEscalationGroupsSearchQuery(value)}
          />

          {(productData.productType !== "geo" ||
            (productData.productType === "geo" &&
              productAdditionalDetails.geoProductType === "mqtt")) && (
            <>
              <Dropdown
                onChange={(option) => {
                  if (option.length) {
                    setIsTopicEnable(true);
                    setProductData((prev) => ({
                      ...prev,
                      isTopic: true
                    }));
                  } else {
                    setIsTopicEnable(false);
                    setProductData((prev) => ({
                      ...prev,
                      isTopic: false
                    }));
                  }
                  setDataManagement(option);
                  setProductAdditionalDetails((prev) => ({
                    ...prev,
                    dataManagement: option
                  }));
                }}
                options={["shadow", "timeSeries"]}
                label="Data Management"
                isMulti
                placeHolder="Select Storage Options"
                value={dataManagement}
              />
              <Dropdown
                {...productForm.register("policyTemplate", DEFAULT_VALIDATE.schema)}
                onChange={(option) => {
                  setAuthorization(option);
                  setProductAdditionalDetails((prev) => ({
                    ...prev,
                    authorization: option
                  }));
                  productForm.setValue("policyTemplate", option);
                  productForm.trigger("policyTemplate");
                }}
                getOptionLabel="templateName"
                value={authorization}
                deepSearch={(val) => setSearchQuery(val)}
                options={
                  productData.productTemplate?.authorization
                    ? [
                        { templateName: "${productName}_template (New template)" },
                        ...(policyTemplate?.templates || [])
                      ]
                    : policyTemplate?.templates
                }
                optionsLoading={isPolicyTemplateLoading}
                label="Policy Template"
                required
                newOption={{
                  placeHolder: "Create New Policy Template...",
                  target: "/security/template",
                  openCreateModal: true,
                  from: "createProduct"
                }}
                isSearchable
                placeHolder="Select Policy Template"
                error={!!productForm.formState.errors.policyTemplate}
                helperText={productForm.formState.errors.policyTemplate && DEFAULT_VALIDATE.message}
              />

              <Dropdown
                {...productForm.register("otaType", DEFAULT_VALIDATE.schema)}
                onChange={(option) => {
                  setOtaType(option);
                  setProductAdditionalDetails((prev) => ({
                    ...prev,
                    otaType: option
                  }));
                  productForm.setValue("otaType", option);
                  productForm.trigger("otaType");
                }}
                value={otaType}
                options={["http", "mqtt", "none"]}
                label="OTA Type"
                required
                placeHolder="Select OTA Type"
                error={!!productForm.formState.errors.otaType}
                helperText={productForm.formState.errors.otaType && DEFAULT_VALIDATE.message}
              />
              <Dropdown
                {...productForm.register("authentication", DEFAULT_VALIDATE.schema)}
                onChange={(option) => {
                  setAuthenticationType(option);
                  setProductAdditionalDetails((prev) => ({
                    ...prev,
                    authenticationType: option
                  }));
                  productForm.setValue("authentication", option);
                  productForm.trigger("authentication");
                  if (option.value === "basic") {
                    productForm.unregister("authority");
                  }
                }}
                value={authenticationType}
                options={[
                  { key: "Certificate Based", value: "tls" },
                  { key: "Username/Password", value: "basic" }
                ]}
                getOptionLabel="key"
                label="Authentication "
                required
                placeHolder="Select Authentication Type"
                error={!!productForm.formState.errors.authentication}
                helperText={productForm.formState.errors.authentication && DEFAULT_VALIDATE.message}
              />
              {authenticationType?.value === "tls" && (
                <Dropdown
                  {...productForm.register("authority", DEFAULT_VALIDATE.schema)}
                  onChange={(option) => {
                    setCaName(option);
                    setProductAdditionalDetails((prev) => ({
                      ...prev,
                      caName: option
                    }));
                    productForm.setValue("authority", option);
                    productForm.trigger("authority");
                  }}
                  options={caNames.data?.list || []}
                  value={caName}
                  label="Certificate authority"
                  required
                  isSearchable
                  placeHolder="Select Ca Name"
                  newOption={{
                    placeHolder: "Create Cert Authority...",
                    target: "/security/certificateAuth/addCertAuth"
                  }}
                  error={!!productForm.formState.errors.authority}
                  helperText={productForm.formState.errors.authority && DEFAULT_VALIDATE.message}
                />
              )}
            </>
          )}
          {productData.productType === "geo" &&
            productAdditionalDetails.geoProductType === "gps" && (
              <Dropdown
                {...productForm.register(
                  "protocol",
                  productAdditionalDetails.geoProductType === "gps" ? DEFAULT_VALIDATE.schema : null
                )}
                onChange={(option) => {
                  productForm.setValue("protocol", option);
                  productForm.trigger("protocol");
                  setProductAdditionalDetails((prev) => ({
                    ...prev,
                    geoProtocol: option
                  }));
                }}
                className="col-span-2"
                required
                value={productAdditionalDetails.geoProtocol}
                options={PROTOCOL_VALUES}
                isSearchable
                label="Protocols"
                getOptionLabel="key"
                placeHolder="Select protocol "
                error={!!productForm.formState.errors.protocol}
                helperText={productForm.formState.errors.protocol && DEFAULT_VALIDATE.message}
              />
            )}
        </div>
        <div className="space-y-2 col-span-6 pl-8">
          <OptionalCards
            title={"Enable Topics"}
            description={`Unlock seamless connectivity with our IoT ${PRODUCT_VAR}'s advanced topic enablement.`}
            Icon={MessageSquareCode}
          >
            <Switch
              onChange={(e) => {
                setIsTopicEnable(e.target.checked);
                setProductData((prev) => ({
                  ...prev,
                  isTopic: e.target.checked
                }));
              }}
              defaultChecked={isTopicEnable}
            />
          </OptionalCards>
          {productData.productType !== "managed" && (
            <OptionalCards
              title={"Enable Inventory"}
              description={`  Tracks device status in a central system to ensure only registered devices are
                    provisioned.`}
              Icon={PackagePlus}
            >
              <Switch
                onChange={(e) => {
                  setProductData((prev) => ({
                    ...prev,
                    inventoryEnabled: e.target.checked
                  }));
                }}
                defaultChecked={productData.inventoryEnabled}
              />
            </OptionalCards>
          )}

          {(productData.productType !== "geo" ||
            (productData.productType === "geo" &&
              productAdditionalDetails.geoProductType === "mqtt")) && (
            <OptionalCards
              title={"Enable Secure Provisioning"}
              description={`Securely delivers credentials to verified devices during onboarding.`}
              Icon={PackagePlus}
            >
              <Switch
                onChange={(e) => {
                  setProductData((prev) => ({
                    ...prev,
                    provisioningEnabled: e.target.checked
                  }));
                }}
                defaultChecked={productData.provisioningEnabled}
              />
            </OptionalCards>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdditionaDetailsStep;

const OptionalCards = ({ title, description, Icon, children }) => {
  return (
    <Card variant="second" className="  flex gap-6 items-center justify-between  p-4 ">
      <div className="flex gap-4 items-center">
        <div className="border p-4 rounded-lg bg-secondary">
          <Icon size="1.2rem" />
        </div>
        <div className="space-y-1">
          <h3 className="font-medium text-base">{title}</h3>

          <p className="text-[0.8rem] text-muted-foreground">{description}</p>
        </div>
      </div>
      {children}
    </Card>
  );
};
