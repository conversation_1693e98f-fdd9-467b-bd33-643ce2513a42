import Button from "@components/Button";
import HeaderSection from "@components/layout/HeaderSection";
import { LoadingSkeleton } from "@components/Skeletons/LoadingSkeletons";
import { StatCard } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useThingsStats from "@hooks/classic/useThingStats";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useProductCount from "@hooks/product/useProductCount";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { useAppSelector } from "@src/store";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { CircleCheck, Cpu, Package, Plus, XCircle } from "lucide-react";
import Projects from "./Projects";

function ProductsPage() {
  // const [searchParam, setSearchParam] = useSearchParams();

  const tenantName = useAppSelector(({ user }) => user.user?.tenant || "");
  const { data: productCount, isLoading: productCountLoading } = useProductCount({
    partnerName: tenantName
  });

  const { data: thingStats, isLoading: thingStatsLoading } = useThingsStats({ enabled: true });

  const { data: permissions } = useUserGroupPermissions();
  const navigate = useCustomNavigate();

  return (
    <main className="space-y-4">
      <HeaderSection
        title={`${PRODUCT_VAR}s`}
        description="Manage your products"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("addProduct")}
            noAccess={permissions.product !== "write"}
          >
            Add {PRODUCT_VAR}
          </Button>
        }
      />
      {productCountLoading || thingStatsLoading ? (
        <LoadingSkeleton variant="stats" />
      ) : (
        <section className="flex w-full flex-wrap gap-4 ">
          <StatCard
            className="cursor-pointer"
            title={`Online ${THING_VAR}s`}
            value={thingStats?.connected}
            colorScheme="success"
            icon={CircleCheck}
            description={`${((thingStats.connected / thingStats.total) * 100).toFixed(1)}%`}
          />
          <StatCard
            className="cursor-pointer"
            title={`Offline ${THING_VAR}s`}
            value={thingStats.disconnected}
            colorScheme="danger"
            icon={XCircle}
            description={`${((thingStats.disconnected / thingStats.total) * 100).toFixed(1)}%`}
          />
          <StatCard
            className="cursor-pointer"
            title={`Total ${THING_VAR}s`}
            value={thingStats.total}
            colorScheme="info"
            icon={Cpu}
          />
          <StatCard
            title={`Total ${PRODUCT_VAR}`}
            loading={productCountLoading}
            value={productCount}
            colorScheme="info"
            icon={Package}
          />
        </section>
      )}

      <Projects />
    </main>
  );
}

export default ProductsPage;
