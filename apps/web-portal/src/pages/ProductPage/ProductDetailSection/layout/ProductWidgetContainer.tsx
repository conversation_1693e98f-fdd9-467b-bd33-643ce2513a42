import { ThingWidget } from "@/index";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useGeoThingDetails from "@hooks/geo/useGeoThingDetails";
import useGeoThingList from "@hooks/geo/useGeoThingList";
import useShadowOptions from "@hooks/timeseries/useShadowOptions";
import useTimeSeriesOptions from "@hooks/timeseries/useTimeseriesOptions";
import { SENSOR_KEY } from "@src/pages/MonitorPage/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import isEqual from "lodash.isequal";
import { Check } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { fetchProductDetials, updateProductData } from "../../../../api/product";
import Button from "../../../../components/Button";
import { queryClient } from "../../../../utils/queryClient";
import ChartLoader from "../../../MonitorPage/Charts/ChartLoader";
import ProductWIdgetConfig from "../../AddProductSection/Steps/ProductWIdgetConfig";
import DisplayFieldsData from "../DetailsComponents/DisplayFieldsData";

export const EXCLUDED_FIELDS = [
  "thingName",
  "productName",
  "version",
  "status",
  "tenant",
  "positionId",
  "productType",
  "groupId",
  "geom",
  "deviceId",
  "updatedAt",
  "latestPositionId",
  "uniqueId",
  "meta",
  "attributes",
  "imgURL"
];

const fetchProductWidgets = async (productName: string) => {
  try {
    const res = await fetchProductDetials(productName);

    return (res.data.widgets as ThingWidget[]) || [];
  } catch (error: any) {
    console.log(error.message);
    return [];
  }
};

const EMPTY_WIDGET: ThingWidget[] = [];

const ProductWidgetContainer = ({ productName }: { productName: string }) => {
  const [widgetCopy, setWidgetCopy] = useState(EMPTY_WIDGET);

  const { isLoading, data: widgetList } = useQuery({
    queryKey: [`${productName}-widgets`],
    queryFn: () => fetchProductWidgets(productName)
  });
  const { data: permissions } = useUserGroupPermissions();

  const mutation = useMutation({
    mutationFn: updateProductData,
    onMutate: async (newWidgets) => {
      const prevWidgets = queryClient.getQueryData(["thing-widgets", productName]);
      queryClient.setQueryData([`${productName}-widgets`], newWidgets.widgets);
      return { prevWidgets, newWidgets };
    },
    onError: (error) => {
      showErrorToast(error.message);
      queryClient.invalidateQueries({ queryKey: ["thing-widgets", productName] });
    },
    onSuccess: () => {
      showSuccessToast("Widgets template created successfully.");
    }
  });

  const { data: shadowOptions } = useShadowOptions({ productName });
  const { data: timeSeriesOptions } = useTimeSeriesOptions({ productName });

  const data = useMemo(() => {
    const res: Record<string, string[]> = {};
    if (shadowOptions) {
      res["shadow"] = shadowOptions;
    }
    if (timeSeriesOptions) {
      res[SENSOR_KEY] = timeSeriesOptions;
    }

    return res;
  }, [shadowOptions, timeSeriesOptions]);

  const { data: productThingList } = useGeoThingList({ productName });

  const { data: productThing } = useGeoThingDetails({
    thingName: productThingList?.things?.[0]?.thingName!,
    enabled: Boolean(productThingList?.things?.[0]?.thingName)
  });

  const deviceKeyMap = useMemo(() => {
    const productThingMap = productThing! || {};
    const items = Object.keys(productThingMap);
    const keys: string[] = [];

    for (const item of items) {
      if (!EXCLUDED_FIELDS.includes(item)) {
        keys.push(item);
      }
      if (item === "attributes") {
        keys.push(...Object.keys(productThingMap.attributes || {}));
      }
    }

    if (keys.length === 0) {
      return { Device: [] };
    }

    return { Device: keys };
  }, [productThingList, productThing]);

  const checkDuplicateWidgetNameExists = (widgets: ThingWidget[]) => {
    const map = new Map();
    for (let i = 0; i < widgets.length; i++) {
      const element = widgets[i]!;
      if (map.has(element.title)) {
        return true;
      }
      map.set(element.title, true);
    }
    return false;
  };

  const handleMutationUpdate = async (e) => {
    e.preventDefault();
    const isDuplicate = checkDuplicateWidgetNameExists(widgetCopy);

    if (isDuplicate) {
      showErrorToast("Duplicate widget name not allowed");
      return;
    }
    mutation.mutate({ productName, body: { widgets: widgetCopy } });
  };
  useEffect(() => {
    if (widgetList) {
      setWidgetCopy(widgetList);
    }
  }, [widgetList]);

  if (isLoading || !data) {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center mt-4">
          <ChartLoader />
        </div>
      );
    }
    return <div />;
  }

  return (
    <div className="space-y-4">
      <Card className="relative">
        <form onSubmit={handleMutationUpdate}>
          <ProductWIdgetConfig
            productWidgets={widgetCopy}
            setProductWidgets={setWidgetCopy}
            sourcesMap={{
              ...data,
              ...deviceKeyMap
            }}
            allowEmpty
            access={permissions.product !== "write"}
          />

          {(widgetCopy.length > 0 || widgetList?.length !== widgetCopy.length) && (
            <Button
              className="ml-auto mt-6"
              startIcon={<Check size={BUTTON_ICON_SIZE} />}
              color="gray"
              loading={mutation.isPending}
              type="submit"
              small
              loadingText="Save Templates"
              disabled={isEqual(widgetCopy, widgetList)}
            >
              Save Templates
            </Button>
          )}
        </form>
      </Card>

      <DisplayFieldsData />
    </div>
  );
};

export default ProductWidgetContainer;
