import ActionButton from "@components/ActionButton";
import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import { Popover, PopoverContent, PopoverTrigger } from "@components/shadcn/components/popover";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useProductDetails from "@hooks/geo/useProductDetails";
import useUpdateDefaultProduct from "@hooks/product/useUpdateDefaultProduct";
import useTimeSeriesOptions from "@hooks/timeseries/useTimeseriesOptions";
import { generateRandomString } from "@src/pages/MonitorPage/utils";
import { showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import {
  Activity,
  BarChart3,
  Briefcase,
  Calendar,
  Check,
  CheckCircle,
  Clock,
  Cog,
  Compass,
  Container,
  Database,
  Droplets,
  Fuel,
  Gauge,
  GaugeIcon,
  Hash,
  Layers,
  LineChart,
  MapPin,
  Move,
  Navigation,
  Package,
  Percent,
  Plus,
  Power,
  RotateCcw,
  RotateCw,
  Settings,
  Target,
  Thermometer,
  Timer,
  TrendingUp,
  Truck,
  User,
  Users,
  Wrench,
  Zap
} from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const MonitorIconOptions = [
  { name: "Truck", icon: Truck },
  { name: "Container", icon: Container },
  { name: "Package", icon: Package },
  { name: "Engine", icon: Zap },
  { name: "Fuel", icon: Fuel },
  { name: "Gauge", icon: Gauge },
  { name: "Pressure", icon: GaugeIcon },
  { name: "Location", icon: MapPin },
  { name: "GPS", icon: Navigation },
  { name: "Compass", icon: Compass },
  { name: "Status", icon: CheckCircle },
  { name: "Activity", icon: Activity },
  { name: "Power", icon: Power },
  { name: "Production", icon: TrendingUp },
  { name: "Chart", icon: BarChart3 },
  { name: "Trending", icon: LineChart },
  { name: "Clock", icon: Clock },
  { name: "Timer", icon: Timer },
  { name: "Cycle", icon: RotateCcw },
  { name: "Temperature", icon: Thermometer },
  { name: "Oil", icon: Droplets },
  { name: "Hash", icon: Hash },
  { name: "Client", icon: User },
  { name: "Team", icon: Users },
  { name: "Equipment", icon: Wrench },
  { name: "Shift", icon: Calendar },
  { name: "Work", icon: Briefcase },
  { name: "Rotation", icon: RotateCw },
  { name: "Swing", icon: Move },
  { name: "Queue", icon: Layers },
  { name: "Average", icon: Target },
  { name: "Rate", icon: Percent },
  { name: "RPM", icon: Settings },
  { name: "Machinery", icon: Cog },
  { name: "Material", icon: Database }
];

const DefaultDisplayFields = {
  icon: "Hash",
  field: null,
  unit: "",
  displayName: ""
};

interface DisplayField {
  id: string;
  icon: string;
  field: string | null;
  unit: string;
  displayName: string;
}

const DisplayFieldsData = () => {
  const [monitorDataPoints, setMonitorDataPoints] = useState<DisplayField[]>([]);

  const productName = useParams().productName || "";
  const { data: productDetails, isLoading: productIsLoading } = useProductDetails({ productName });

  useEffect(() => {
    if (productDetails?.monitorDataPoints) {
      setMonitorDataPoints(productDetails.monitorDataPoints);
    }
  }, [productDetails]);

  const { data: timeSeriesOptions, isLoading: isTimeSeriesLoading } = useTimeSeriesOptions({
    productName
  });
  const updateProductMutation = useUpdateDefaultProduct({
    onSuccess: () => {
      showSuccessToast("Display fields updated successfully");
      queryClient.invalidateQueries({ queryKey: ["product-details", productName] });
    }
  });

  const addDisplayField = () => {
    setMonitorDataPoints((prev) => [
      ...prev,
      { ...DefaultDisplayFields, id: generateRandomString(10) }
    ]);
  };
  const removeDisplayField = (id: string) => {
    setMonitorDataPoints((prev) => prev.filter((displayField) => displayField.id !== id));
  };

  const onEditDisplayField = (id: string, data: Partial<DisplayField>) => {
    setMonitorDataPoints((prev) =>
      prev.map((displayField) => {
        if (displayField.id === id) {
          return {
            ...displayField,
            ...data
          };
        }
        return displayField;
      })
    );
  };

  return (
    <Card className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="heading-2">Monitoring Data Points</h2>
      </div>
      <div className="grid grid-cols-1 gap-4">
        {!monitorDataPoints.length || productIsLoading ? (
          <DataNotFound title="No Field Added" className="col-span-2" />
        ) : (
          monitorDataPoints.map((displayField) => (
            <Card variant="second" key={displayField.id}>
              <div className="flex items-end gap-2">
                <div>
                  <p className="input-label-text helper-text !ml-0 ">Icon</p>
                  <IconSelection
                    selectedIcon={displayField.icon}
                    setSelectedIcon={(icon) => onEditDisplayField(displayField.id, { icon: icon })}
                  />
                </div>

                <Dropdown
                  options={timeSeriesOptions || []}
                  optionsLoading={isTimeSeriesLoading}
                  placeHolder="Select field"
                  required
                  className="flex-1"
                  labelClassName="helper-text"
                  label="Field"
                  onChange={(value) => {
                    onEditDisplayField(displayField.id, { field: value });
                  }}
                  value={displayField.field}
                />
                <Input
                  label="Display Name"
                  placeholder="Display Name"
                  className="flex-1"
                  value={displayField.displayName}
                  onChange={(e) =>
                    onEditDisplayField(displayField.id, { displayName: e.target.value })
                  }
                />

                <Input
                  label="Unit"
                  placeholder="Unit"
                  value={displayField.unit}
                  onChange={(e) => onEditDisplayField(displayField.id, { unit: e.target.value })}
                />
                <ActionButton
                  className="!mb-2"
                  type="delete"
                  onClick={() => removeDisplayField(displayField.id)}
                />
              </div>
            </Card>
          ))
        )}
      </div>
      <ShadcnButton
        variant="outline"
        onClick={addDisplayField}
        className="w-full h-12 border-dashed border  border-gray-500 !bg-transparent"
      >
        <Check className="h-4 w-4 mr-2" />
        Add New Field
      </ShadcnButton>
      <Button
        small
        onClick={() => updateProductMutation.mutate({ productName, body: { monitorDataPoints } })}
        className="ml-auto"
        startIcon={<Plus size={BUTTON_ICON_SIZE} />}
      >
        Save Fields
      </Button>
    </Card>
  );
};

const IconSelection = ({
  selectedIcon,
  setSelectedIcon
}: {
  selectedIcon: string;
  setSelectedIcon: (icon: string) => void;
}) => {
  const Icon = MonitorIconOptions.find((icon) => icon.name === selectedIcon)?.icon;
  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="flex items-center justify-center w-10  rounded-lg border  border-gray-300 input-container">
          {Icon && <Icon className="w-5 h-6 text-foreground" />}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-100 p-0" align="start">
        <div className="p-4">
          <div className="max-h-80 overflow-y-auto">
            <div className="grid grid-cols-8 gap-2">
              {MonitorIconOptions.map((icon) => {
                const IconComponent = icon.icon;
                const isSelected = selectedIcon === icon.name;

                return (
                  <ShadcnButton
                    key={icon.name}
                    variant={isSelected ? "default" : "ghost"}
                    size="sm"
                    className={`h-10 text-muted-foreground w-10 p-0 hover:bg-secondary ${isSelected && "bg-secondary text-foreground"}`}
                    onClick={() => setSelectedIcon(icon.name)}
                    title={icon.name}
                  >
                    <IconComponent className="h-5 w-5" />
                  </ShadcnButton>
                );
              })}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DisplayFieldsData;
