import { useQuery } from "@tanstack/react-query";
import { fetchSimulatorDetails } from "./Simulators";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import Button from "@components/Button";
import { Edit2 } from "lucide-react";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import DataNotFound from "@components/DataNotFound";
import { Card } from "@components/ui";

const SimulatorPropertiesTab = ({ name, editSimulatorModalRef }: { name: string }) => {
  const { data: simulatorDetails, isLoading } = useQuery({
    queryKey: ["simulators", name],
    queryFn: () => fetchSimulatorDetails(name)
  });

  if (isLoading) return <CardLoadingSkeleton col={4} />;
  if (!simulatorDetails) return <div>Something went wrong while </div>;
  const { parameters } = simulatorDetails;
  return (
    <Card className="space-y-4">
      <div className="between">
        <h3 className="heading-2">Simulator Properties</h3>

        <Button
          // noAccess={permissions.product !== "write"}
          startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
          type="button"
          small
          onClick={() => {
            editSimulatorModalRef.current?.openModal(name);
          }}
        >
          Edit Properties
        </Button>
      </div>
      <div className="grid grid-cols-3 gap-4 ">
        {!parameters?.length ? (
          <DataNotFound className="col-span-3" title="No Properties Found" />
        ) : (
          parameters.map((property) => (
            <SimulatorCard
              key={property.id}
              property={property}
              //   setProperties={setProperties}
            />
          ))
        )}
      </div>
    </Card>
  );
};
const SimulatorCard = ({ property }) => {
  return (
    <Card variant="second" className=" space-y-3">
      <h3 className="heading-3">Property</h3>

      <div className="flex gap-3 items-center">
        <Input value={property.name} medium className=" flex-1" />
        <Dropdown
          disabled
          options={["string", "boolean", "number"]}
          value={property.type}
          className="w-28 ml-1"
        />
      </div>

      <div className="flex gap-4 items-end ">
        {property.type === "number" && (
          <>
            <Input value={property.min} type="number" className=" flex-1" label="Min" medium />
            <Input value={property.max} type="number" medium label="Max" className=" flex-1" />
          </>
        )}
        {property.type === "string" && (
          <Dropdown
            value={property.values || []}
            className=" flex-1"
            label="Values"
            isMulti
            isAddField
            disabled
            options={[...property.values]}
          />
        )}
        {property.type === "boolean" && (
          <Dropdown
            label="values"
            className=" flex-1"
            value={["true", "false"]}
            disabled
            options={["true", "false"]}
            isMulti
            onChange={() => {}}
          />
        )}
      </div>
    </Card>
  );
};

export default SimulatorPropertiesTab;
