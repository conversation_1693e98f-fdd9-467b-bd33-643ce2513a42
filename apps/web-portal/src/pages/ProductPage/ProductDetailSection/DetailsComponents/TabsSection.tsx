import { useState } from "react";
import ProductWidgetContainer from "../layout/ProductWidgetContainer";
import ConnectTab from "./ConnectTab";
import ProductDetailTab from "./ProductDetailTab";
import ProductSettingsTab from "./ProductSettingsTab";
import SimulatorPropertiesTab from "./SimulatorPropertiesTab";
import TopicsTab from "./TopicsTab";
import ProductConfigSetting from "@src/pages/UserTypes/Tracking/ProductPage/ProductDetailSection/ProductSettingsTab";
import { PRODUCT_VAR } from "@utils/featureLabels";
import { Tabs, TabsContent } from "@components/shadcn/components/tabs";
import TabsListV2 from "@components/Tabs/TabsListV2";
import TabsTriggerV2 from "@components/Tabs/TabsTriggerV2";

type TabsType =
  | "productDetails"
  | "simulatorProperties"
  | "topics"
  | "productSettings"
  | "widgets"
  | "connect"
  | "settings";

const TabsSection = ({
  productDetails,
  setShowProductSettingsModal,
  customVariables,
  editSimulatorModalRef
}) => {
  const [value, setValue] = useState("productDetails");

  const simulatorName = productDetails?.properties?.simulatorName;

  const handleChange = (val: TabsType) => {
    setValue(val);
  };
  return (
    <Tabs value={value}>
      <TabsListV2 className="mb-0">
        <TabsTriggerV2 value="productDetails" onClick={() => handleChange("productDetails")}>
          {PRODUCT_VAR} Details
        </TabsTriggerV2>
        {productDetails.properties?.simulatorName && (
          <TabsTriggerV2
            value="simulatorProperties"
            onClick={() => handleChange("simulatorProperties")}
          >
            Simulator Properties
          </TabsTriggerV2>
        )}

        <TabsTriggerV2 value="topics" onClick={() => handleChange("topics")}>
          Topics
        </TabsTriggerV2>
        {productDetails.productType === "managed" && (
          <TabsTriggerV2 value="productSettings" onClick={() => handleChange("productSettings")}>
            {PRODUCT_VAR} Config
          </TabsTriggerV2>
        )}
        <TabsTriggerV2 value="widgets" onClick={() => handleChange("widgets")}>
          Widget Templates
        </TabsTriggerV2>
        <TabsTriggerV2 value="connect" onClick={() => handleChange("connect")}>
          How to Connect
        </TabsTriggerV2>
        <TabsTriggerV2 value="settings" onClick={() => handleChange("settings")}>
          {PRODUCT_VAR} Settings
        </TabsTriggerV2>
      </TabsListV2>

      <TabsContent value="productDetails">
        <ProductDetailTab productDetails={productDetails} />
      </TabsContent>

      <TabsContent value="simulatorProperties">
        <SimulatorPropertiesTab
          name={simulatorName}
          editSimulatorModalRef={editSimulatorModalRef}
        />
      </TabsContent>

      <TabsContent value="topics">
        <TopicsTab productDetails={productDetails} />
      </TabsContent>

      <TabsContent value="productSettings">
        <ProductSettingsTab
          productDetails={productDetails}
          setShowProductSettingsModal={setShowProductSettingsModal}
          customVariables={customVariables}
        />
      </TabsContent>

      <TabsContent value="widgets">
        <ProductWidgetContainer productName={productDetails.productName} />
      </TabsContent>

      <TabsContent value="connect">
        <ConnectTab />
      </TabsContent>
      <TabsContent value="settings">
        <ProductConfigSetting />
      </TabsContent>
    </Tabs>
  );
};

export default TabsSection;
