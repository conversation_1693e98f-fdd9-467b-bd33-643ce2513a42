import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import { Badge } from "@components/shadcn/components/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@components/shadcn/components/tooltip";
import { Card } from "@components/ui";
import useProductDocInfo from "@hooks/product/useProductDocInfo";
import { IconButton } from "@mui/material";
import { THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { INPUT_ICON_SIZE } from "@utils/utilities";
import { Cable, Copy, Link } from "lucide-react";

const handleCopyClick = async (textToCopy: string) => {
  try {
    await navigator.clipboard.writeText(textToCopy);
    showSuccessToast("URL copied to clipboard!");
  } catch (err) {
    console.log(err);
    showErrorToast("Failed to copy URL.");
  }
};

const ConnectTab = () => {
  const { data: productDocInfo, isLoading: isProductDocLoading } = useProductDocInfo({});

  return isProductDocLoading ? (
    <CardLoadingSkeleton className="w-full" />
  ) : !productDocInfo ? (
    <DataNotFound title="Something went wrong" />
  ) : (
    <Card className="space-y-4">
      <div className="space-y-4">
        <p className="heading-3-normal ">You can connect your {THING_VAR}s with this url :</p>

        <Card
          variant="second"
          className=" w-1/2 space-y-2 flex gap-6 items-center justify-between "
        >
          <div className="flex gap-4 items-center">
            <Card variant="third">
              <Link size="1.2rem" />
            </Card>
            <div className="space-y-0.5">
              <h3 className="font-medium text-base">HOST</h3>
            </div>
          </div>
          <Input
            className="w-[25rem]"
            value={productDocInfo[0].info}
            endIcon={
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <IconButton
                      onClick={() => {
                        handleCopyClick(String(productDocInfo[0].info));
                      }}
                    >
                      <Copy size={INPUT_ICON_SIZE} />
                    </IconButton>
                  </TooltipTrigger>
                  <TooltipContent>Copy URL</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            }
          />
        </Card>
      </div>
      <p className="heading-3-normal !my-8">
        Based on selected preferences, you can connect your {THING_VAR}s with this url and below
        ports :
      </p>

      <Card variant="second" className=" w-1/2 space-y-2 flex gap-6 items-center justify-between ">
        <div className="flex gap-4 items-center">
          <Card variant="third">
            <Cable size="1.2rem" />
          </Card>
          <div className="space-y-0.5">
            <h3 className="font-medium text-base">MQTTS-MTLS</h3>
            <p className="text-[0.8rem] text-muted-foreground">Certificate based Authentication</p>
          </div>
        </div>
        <Badge>{productDocInfo[1].info}</Badge>
      </Card>
      <Card variant="second" className=" w-1/2 space-y-2 flex gap-6 items-center justify-between ">
        <div className="flex gap-4 items-center">
          <Card variant="third">
            <Cable size="1.2rem" />
          </Card>
          <div className="space-y-0.5">
            <h3 className="font-medium text-base">WSS</h3>
            <p className="text-[0.8rem] text-muted-foreground">
              Username/Password based Authentication
            </p>
          </div>
        </div>
        <Badge>{productDocInfo[2].info}</Badge>
      </Card>
      <Card variant="second" className=" w-1/2 space-y-2 flex gap-6 items-center justify-between ">
        <div className="flex gap-4 items-center">
          <Card variant="third">
            <Cable size="1.2rem" />
          </Card>
          <div className="space-y-0.5">
            <h3 className="font-medium text-base">MQTTS</h3>
            <p className="text-[0.8rem] text-muted-foreground">
              Username/Password based Authentication
            </p>
          </div>
        </div>
        <Badge>{productDocInfo[3].info}</Badge>
      </Card>
    </Card>
  );
};

export default ConnectTab;
