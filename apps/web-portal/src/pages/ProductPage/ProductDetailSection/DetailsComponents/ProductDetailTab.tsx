import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { THING_VAR } from "@utils/featureLabels";
import { DATACELL_ICON_SIZE } from "@utils/utilities";
import {
  BookLock,
  Calendar,
  Clipboard,
  ClipboardList,
  Clock,
  Cpu,
  Database,
  Hash,
  ListTodo,
  Monitor,
  PackagePlus,
  Settings,
  Sliders,
  TextSearch
} from "lucide-react";
import DetailsCell2 from "../../../../components/DetailCell2";
import Label from "../../../../components/Label";
import { getAuthType } from "../../Projects";
import { fetchSimulatorDetails } from "./Simulators";
import { Card } from "@components/ui";
import DetailsCell from "@components/DetailsCell";

const ProductDetailTab = ({ productDetails }) => {
  const navigate = useCustomNavigate();
  const { data: simulatorDetails, isLoading: simulatorDetailsLoading } = useQuery({
    queryKey: ["simulators", productDetails.properties?.simulatorName],
    queryFn: () => fetchSimulatorDetails(productDetails.properties?.simulatorName),
    enabled: !!productDetails.properties?.simulatorName
  });
  return (
    <div className="grid grid-cols-2 gap-4 items-start">
      <Card className="space-y-4">
        <h2 className="heading-2">Basic Details</h2>
        <div className="grid grid-cols-2 gap-4">
          <DetailsCell
            title="Authentication Type "
            data={getAuthType(productDetails.authentication?.type)}
            icon={<ListTodo size={DATACELL_ICON_SIZE} />}
          />

          <DetailsCell
            title="CA Name "
            data={
              <span
                onClick={() =>
                  navigate(`/security/certificateAuth/${productDetails.authentication.caName}`)
                }
                className="font-semibold text-foreground cursor-pointer hover:underline"
              >
                {productDetails.authentication?.caName || "N/A"}
              </span>
            }
            icon={<ClipboardList size={DATACELL_ICON_SIZE} />}
          />

          <DetailsCell
            title="Data Management "
            flexDirectionRow
            data={
              !productDetails.dataManagement.length
                ? "N/A"
                : productDetails.dataManagement.map((item) => (
                    <Label color="blue" key={item} text={item} lowercase linear />
                  ))
            }
            icon={<Database size={DATACELL_ICON_SIZE} />}
          />
          <DetailsCell
            title="Policy Template "
            data={
              <span
                onClick={() =>
                  navigate(`/security/template/${productDetails.authorization["policy-template"]}`)
                }
                className="font-semibold text-foreground cursor-pointer hover:underline"
              >
                {(productDetails.authorization &&
                  productDetails.authorization["policy-template"]) ||
                  "N/A"}
              </span>
            }
            icon={<Clipboard size={DATACELL_ICON_SIZE} />}
          />
          {productDetails.productType === "managed" && (
            <>
              <DetailsCell
                title="Category "
                data={productDetails.category}
                icon={<Clipboard size={DATACELL_ICON_SIZE} />}
              />
              <DetailsCell
                flexDirectionRow
                title="Capabilities "
                icon={<Settings size={DATACELL_ICON_SIZE} />}
                data={productDetails.capabilities.map(
                  (capability) =>
                    capability && (
                      <Label color="blue" key={capability} text={capability} lowercase linear />
                    )
                )}
              />
            </>
          )}
          {!productDetails.properties?.simulatorName && (
            <>
              <DetailsCell
                flexDirectionRow
                title="Monitoring Groups  "
                icon={<Monitor size={DATACELL_ICON_SIZE} />}
                data={
                  !productDetails?.monitoringGroups?.length
                    ? "N/A"
                    : productDetails.monitoringGroups.map((item) => (
                        <Label key={item} text={item} lowercase />
                      ))
                }
              />
            </>
          )}
          {productDetails.properties?.simulatorName && !simulatorDetailsLoading && (
            <DetailsCell
              title={`${THING_VAR} Prefix`}
              data-testid="product-details-description"
              icon={<Cpu size={DATACELL_ICON_SIZE} />}
              data={simulatorDetails?.thingPrefix}
            />
          )}
        </div>

        {!productDetails.properties?.simulatorName && (
          <>
            {" "}
            <hr />
            <DetailsCell
              flexDirectionRow
              title="Tags "
              icon={<TextSearch size={DATACELL_ICON_SIZE} />}
              data={
                !productDetails?.metadata?.length
                  ? "N/A"
                  : productDetails.metadata.map((item) => (
                      <Label
                        color={item.required ? "green" : "gray"}
                        key={item.key}
                        text={item.key}
                        lowercase
                      />
                    ))
              }
            />
          </>
        )}
      </Card>
      <Card className="space-y-4">
        <h3 className="heading-2">Security & Compliance</h3>
        <div className="grid grid-cols-2 gap-4">
          {productDetails.productType === "managed" && (
            <>
              <DetailsCell
                title="Alexa "
                data={`${productDetails.alexa}`}
                icon={<Hash size={DATACELL_ICON_SIZE} />}
              />
              <DetailsCell
                title="GoogleHome "
                data={`${productDetails.googleHome}`}
                icon={<Hash size={DATACELL_ICON_SIZE} />}
              />
            </>
          )}
          <DetailsCell
            title="Version "
            icon={<Sliders size={DATACELL_ICON_SIZE} />}
            data={productDetails.version}
          />
          <DetailsCell
            title="Created At "
            icon={<Calendar size={DATACELL_ICON_SIZE} />}
            data={convetUTCToLocal(productDetails.createdAt)}
          />
          <DetailsCell
            title="Updated At "
            icon={<Clock size={DATACELL_ICON_SIZE} />}
            data={convetUTCToLocal(productDetails.updatedAt)}
          />
          <DetailsCell
            title="Inventory Enabled "
            icon={<PackagePlus size={DATACELL_ICON_SIZE} />}
            data={
              <Label
                color={productDetails.inventoryEnabled ? "green" : "red"}
                text={productDetails.inventoryEnabled ? "True" : "False"}
              />
            }
          />
          <DetailsCell
            title="Provisioning Enabled "
            icon={<BookLock size={DATACELL_ICON_SIZE} />}
            data={
              <Label
                color={productDetails.provisioningEnabled ? "green" : "red"}
                text={productDetails.provisioningEnabled ? "True" : "False"}
              />
            }
          />
        </div>
      </Card>
    </div>
  );
};

export default ProductDetailTab;
