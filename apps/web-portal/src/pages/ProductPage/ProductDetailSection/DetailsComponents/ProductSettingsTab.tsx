import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import { PRODUCT_VAR } from "@utils/featureLabels";
import { BUTTON_ICON_SIZE, safeEval } from "@utils/utilities";
import { useEffect, useState } from "react";
import { Edit2, ExternalLink } from "lucide-react";
import Button from "../../../../components/Button";
import Input from "../../../../components/Input";
import Switch from "../../../../components/Switch";
import { Card } from "@components/ui";

const modifyProductSettings = (settings, updateVars, updateVarMapping) => {
  const vars = {};
  const toggles = [];
  const stringInputs = [];
  const numInputs = [];
  const inputs = [];

  for (let v of settings) {
    const description = JSON.parse(v.description);
    if (description.type.value === "BINARY") {
      // if (v.default == "1" || v.default == "0"){
      toggles.push(v.key);
    } else if (description.type.value === "INPUT_STR") {
      stringInputs.push(v.key);
    } else if (description.type.value === "INPUT_INT") {
      numInputs.push(v.key);
    } else if (v.depends === "") {
      inputs.push(v.key);
    } else {
      const parsedValue = JSON.parse(v.depends);
      const dependant = settings.find((item) => item.key === parsedValue.key);
      const expression = `"${dependant?.default || 0}" ${parsedValue?.condition || "=="} "${parsedValue?.value || 0}"`;

      if (safeEval(expression)) {
        inputs.push(v.key);
      }
    }

    vars[v.key] = v;
  }

  updateVars(vars);
  updateVarMapping({ inputs, toggles, stringInputs, numInputs });
};

const ProductSettingsTab = ({
  productDetails,
  setShowProductSettingsModal,
  setUpdatingProductVars,
  setUpdatingVarMap,
  customVariables,
  productVariables
}) => {
  const [varMapping, setVarMapping] = useState({
    inputs: [],
    toggles: [],
    stringInputs: [],
    numInputs: []
  });

  const [productVars, setProductVars] = useState({});
  const { data: permissions } = useUserGroupPermissions();

  useEffect(() => {
    if (customVariables.data?.productSettings) {
      modifyProductSettings(customVariables.data.productSettings, setProductVars, setVarMapping);
    }
  }, [customVariables]);

  useEffect(() => {
    if (productVariables && customVariables.data.productSettings) {
      const mergedSettings = [...customVariables.data.productSettings];
      for (const obj of productVariables.productSettings) {
        if (!mergedSettings.some((item) => item.key === obj.key)) {
          mergedSettings.push(obj);
        }
      }

      modifyProductSettings(mergedSettings, setUpdatingProductVars, setUpdatingVarMap);
    }
  }, [productVariables]);

  return (
    <Card className="space-y-4">
      <div className="between ">
        <h5 className="heading-2">{PRODUCT_VAR} Config</h5>
        <div className="flex gap-4 items-center">
          <a href={productDetails.documentLink} target="_blank">
            <Button startIcon={<ExternalLink size={BUTTON_ICON_SIZE} />} color="gray" small>
              Document Link
            </Button>
          </a>
          <Button
            noAccess={permissions.product !== "write"}
            startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
            type="button"
            small
            onClick={() => {
              setShowProductSettingsModal(true);
            }}
          >
            Edit Settings
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-4 gap-4">
        {varMapping.stringInputs.map((v) => (
          <Input label={productVars[v].name} value={productVars[v].default} disabled />
        ))}
        {varMapping.numInputs.map((v) => (
          <Input disabled label={productVars[v].name} value={productVars[v].default} />
        ))}
        {varMapping.inputs.map((v) => (
          <Input
            key={productVars[v].name}
            label={productVars[v].name}
            value={`GPIO ${productVars[v].default}`}
            disabled
          />
        ))}
      </div>
      <div className="grid grid-cols-3 gap-6 mt-8">
        {varMapping.toggles.map((v) => (
          <Switch
            key={productVars[v].name}
            checked={productVars[v].default == "1"}
            label={productVars[v].name}
            disabled
          />
        ))}
      </div>
    </Card>
  );
};

export default ProductSettingsTab;
