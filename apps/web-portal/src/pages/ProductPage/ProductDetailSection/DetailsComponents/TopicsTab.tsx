import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from "@mui/material";
import { Fragment, useContext, useState } from "react";
import { ChevronRight, Edit2 } from "lucide-react";
import Table, { TableHead, TableRow } from "../../../../components/Table";
import { JsonViewer } from "@textea/json-viewer";
import { DarkModeContext } from "../../../../hooks/useDarkMode";
import clsx from "clsx";
import useCustomNavigate from "@hooks/useCustomNavigate";
import DataNotFound from "@components/DataNotFound";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import Button from "@components/Button";
import { PRODUCT_VAR } from "@utils/featureLabels";
import TopicHighlighter from "@components/TopicHighlighter";
import { Card } from "@components/ui";

const TopicsTab = ({ productDetails }) => {
  const navigate = useCustomNavigate();
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [darkMode] = useContext(DarkModeContext);

  const { data: permissions } = useUserGroupPermissions();

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <h3 className="heading-2">Topics</h3>
        <Button
          noAccess={permissions.product !== "write"}
          startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
          type="button"
          small
          color="gray"
          onClick={() => {
            navigate("editTopics", {
              state: {
                productData: productDetails
              }
            });
          }}
        >
          Edit Topics
        </Button>
      </div>

      {productDetails.topics ? (
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Topic</TableHead>
              <TableHead>Payloads</TableHead>
              <TableHead>More</TableHead>
            </>
          }
          body={
            !productDetails.topics.length ? (
              <DataNotFound title="No Topics Added" isTable />
            ) : (
              productDetails.topics.map((topic, i) => (
                <Fragment key={topic.shortName}>
                  <tr className="bg-white ">
                    <TableRow>{i + 1}</TableRow>
                    <TableRow>{topic.shortName}</TableRow>

                    <TableRow>
                      <TopicHighlighter topicTemplate={topic.topic || "N/A"} />
                    </TableRow>
                    <TableRow>{topic.payload.length || "0"}</TableRow>
                    <TableRow dense>
                      <Tooltip
                        arrow
                        title={selectedTopic?.shortName === topic.shortName ? "Collapse" : "Expand"}
                      >
                        <IconButton
                          className="!p-1.5"
                          onClick={(e) => {
                            e.stopPropagation();
                            selectedTopic?.shortName == topic.shortName
                              ? setSelectedTopic(null)
                              : setSelectedTopic(topic);
                          }}
                        >
                          <ChevronRight
                            size={INPUT_ICON_SIZE}
                            className={clsx(
                              "text-gray-500 transition",
                              selectedTopic?.shortName === topic.shortName && "rotate-90"
                            )}
                          />
                        </IconButton>
                      </Tooltip>
                    </TableRow>
                  </tr>

                  {selectedTopic?.shortName === topic.shortName && (
                    <tr>
                      <td colSpan={100}>
                        <div className="p-4 py-8  w-full">
                          <JsonViewer
                            value={topic.payload}
                            theme={darkMode ? "dark" : "light"}
                            style={{
                              backgroundColor: "inherit",
                              fontSize: "0.875rem",
                              fontFamily: "inherit"
                            }}
                            displayDataTypes={false} // Hides data types
                            displaySize={false}
                            enableClipboard={false} // Disables clipboard
                            quotesOnKeys={false}
                          />
                        </div>
                      </td>
                    </tr>
                  )}
                </Fragment>
              ))
            )
          }
        />
      ) : (
        <DataNotFound title={`No Topics added for this ${PRODUCT_VAR}. Update Topics to add.`} />
      )}
    </Card>
  );
};

export default TopicsTab;
