import { fetchApi } from "@api/_helpers";
import { SIMULATOR_URL } from "@api/index";
import { SimulatorItem } from "@components/AddSimulationModal";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import HeaderSection from "@components/layout/HeaderSection";
import ZoomableImage from "@components/ZoomableImage";
import useProductDetails from "@hooks/geo/useProductDetails";
import useDeleteSimulatorProduct from "@hooks/product/useDeleteSimulatorProduct";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { useQuery } from "@tanstack/react-query";
import { generateColor } from "@utils/color";
import { PRODUCT_DEFAULT_IMAGE } from "@utils/deviceMapping";
import { THING_VAR } from "@utils/featureLabels";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Cpu } from "lucide-react";
import { ElementRef, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { deleteProduct } from "../../../api/product";
import Button from "../../../components/Button";
import Label from "../../../components/Label";
import { getCustomVariables, getProductVars } from "../../../features/productSlice";
import { useAppDispatch, useAppSelector } from "../../../store";
import { showErrorToast, showSuccessToast } from "../../../utils";
import EditSimulatorModal from "../EditSimulatorModal";
import ToggleSimulatorState from "../ToggleSimulatorState";
import ActionButton from "./DetailsComponents/ActionButton";
import DevicesCards from "./DetailsComponents/DevicesCards";
import TabsSection from "./DetailsComponents/TabsSection";
import ProductSettingsModal from "./ProductSettingsModal";
import ProductUpdateModal from "./ProductUpdateModal";

export const fetchSimulatorDetails = async (id: string) => {
  const fetchResponse = await fetchApi(`/simulator/${id}`, {}, SIMULATOR_URL);

  const res = await fetchResponse.json();

  if (res.status === "Success") {
    return res.data as SimulatorItem;
  }
  if (res.status === "Failure") {
    throw new Error(res.message);
  }
  throw new Error("Something went wrong");
};

const DefaultProductDetails = () => {
  const { productName } = useParams();
  const dispatch = useAppDispatch();
  const [updatingProductVars, setUpdatingProductVars] = useState({});
  const [updatingVarMap, setUpdatingVarMap] = useState({
    inputs: [],
    toggles: [],
    stringInputs: [],
    numInputs: []
  });
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [showProductSettingsModal, setShowProductSettingsModal] = useState(false);

  const [updateProductModal, setUpdateProductModal] = useState(false);

  const [loadingBtn, setLoadingBtn] = useState(false);

  const navigate = useCustomNavigate();
  const editSimulatorModalRef = useRef<ElementRef<typeof EditSimulatorModal>>(null);

  const customVariables = useAppSelector(({ product }) => product.customVariables);

  const productVariables = useAppSelector(({ product }) => product.productVars);

  const { data: productDetails, isLoading: productIsLoading } = useProductDetails({ productName });

  const { data: simulatorDetails, isLoading: simulatorDetailsLoading } = useQuery({
    queryKey: ["simulators", productDetails?.properties?.simulatorName],
    queryFn: () => fetchSimulatorDetails(productDetails?.properties?.simulatorName || ""),
    enabled: !!productDetails?.properties?.simulatorName
  });

  const deleteSimulationMutation = useDeleteSimulatorProduct({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["simulators"] });
      navigate(-1);
      showSuccessToast("Simulation Product deleted successfully");
    }
  });

  useEffect(() => {
    if (productDetails?.productType === "managed") {
      dispatch(getCustomVariables(productDetails.productName));
    }
  }, [productDetails]);

  useEffect(() => {
    if (productDetails?.productType === "managed" && customVariables?.productSettings) {
      dispatch(
        getProductVars({
          id: customVariables.data.projectName
        })
      );
    }
  }, [customVariables]);

  const handleDelete = async () => {
    setLoadingBtn(true);
    const resp = await deleteProduct(productName, productDetails?.productType, true);
    if (resp.status === "Success") {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["product-list"] });
        navigate("/products");
        showSuccessToast("Product deleted successfully");
      }, 1000);
    } else {
      showErrorToast(resp.message);
    }
    setLoadingBtn(false);
  };

  return (
    <>
      <main className="space-y-4">
        <section>
          {productIsLoading || !productDetails ? (
            <CardLoadingSkeleton col={5} className="w-full" />
          ) : (
            <div className="space-y-4">
              <HeaderSection
                title={
                  <div className="flex gap-2 items-center">
                    <h3>{productDetails.productName}</h3>
                    <Label
                      color="blue"
                      text={
                        productDetails.properties?.simulatorName
                          ? "SIMULATED"
                          : productDetails.productType.toUpperCase()
                      }
                      lowercase
                    />
                    {productDetails?.properties?.simulatorName && !simulatorDetailsLoading && (
                      <Label
                        text={simulatorDetails?.status}
                        color={generateColor(simulatorDetails?.status)}
                      />
                    )}
                  </div>
                }
                description={productDetails.description}
                startContent={
                  <ZoomableImage>
                    <img
                      className="object-cover size-20 rounded-xl "
                      src={productDetails.imgURL || PRODUCT_DEFAULT_IMAGE}
                      alt="product-img"
                    />
                  </ZoomableImage>
                }
                actions={
                  <div className="flex gap-4 items-center">
                    {productDetails.properties?.simulatorName && !simulatorDetailsLoading && (
                      <ToggleSimulatorState
                        id={simulatorDetails.name}
                        status={simulatorDetails.status}
                      />
                    )}

                    <Button
                      startIcon={<Cpu size={BUTTON_ICON_SIZE} />}
                      className="!w-max"
                      onClick={() =>
                        navigate("manageDevice", {
                          state: {
                            isSimulator: productDetails.properties?.simulatorName,
                            productName: productDetails.productName,
                            productType: productDetails.productType,
                            metadata: productDetails.metadata,
                            ...(productDetails.productType !== "gatewayManaged" && {
                              templateName: productDetails?.authorization["policy-template"],
                              authType: productDetails?.authentication.type
                            })
                          }
                        })
                      }
                      color="orange"
                    >
                      Manage {THING_VAR}s
                    </Button>

                    <ActionButton
                      setUpdateProductModal={setUpdateProductModal}
                      setShowDeletePrompt={setShowDeletePrompt}
                      productDetails={productDetails}
                      customVariables={customVariables}
                    />
                  </div>
                }
              />
              <DevicesCards />

              {/* <div className="flex justify-between items-center gap-4">
                <div className="flex  items-center space-x-4">
                  <ZoomableImage>
                    <img
                      className="object-cover w-32 h-32 rounded-xl "
                      src={productDetails.imgURL || PRODUCT_DEFAULT_IMAGE}
                      alt="product-img"
                    />
                  </ZoomableImage>
                  <div className="">
                    <div className="flex gap-2 items-center">
                      <h3 className="heading-2">{productDetails.productName}</h3>
                      <Label
                        color="blue"
                        text={
                          productDetails.properties?.simulatorName
                            ? "SIMULATED"
                            : productDetails.productType.toUpperCase()
                        }
                        lowercase
                      />
                      {productDetails?.properties?.simulatorName && !simulatorDetailsLoading && (
                        <Label
                          text={simulatorDetails?.status}
                          color={generateColor(simulatorDetails?.status)}
                        />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {truncateText(productDetails.description || "", 250)}
                    </p>
                  </div>
                </div>
                <div className="flex gap-4 items-center">
                  {productDetails.properties?.simulatorName && !simulatorDetailsLoading && (
                    <ToggleSimulatorState
                      id={simulatorDetails.name}
                      status={simulatorDetails.status}
                    />
                  )}

                  <Button
                    startIcon={<Cpu size={BUTTON_ICON_SIZE} />}
                    className="!w-max"
                    onClick={() =>
                      navigate("manageDevice", {
                        state: {
                          isSimulator: productDetails.properties?.simulatorName,
                          productName: productDetails.productName,
                          productType: productDetails.productType,
                          metadata: productDetails.metadata,
                          ...(productDetails.productType !== "gatewayManaged" && {
                            templateName: productDetails?.authorization["policy-template"],
                            authType: productDetails?.authentication.type
                          })
                        }
                      })
                    }
                    color="orange"
                  >
                    Manage {THING_VAR}s
                  </Button>

                  <ActionButton
                    setUpdateProductModal={setUpdateProductModal}
                    setShowDeletePrompt={setShowDeletePrompt}
                    productDetails={productDetails}
                    customVariables={customVariables}
                  />
                </div>
              </div> */}

              <TabsSection
                productDetails={productDetails}
                setShowProductSettingsModal={setShowProductSettingsModal}
                setUpdatingProductVars={setUpdatingProductVars}
                setUpdatingVarMap={setUpdatingVarMap}
                customVariables={customVariables}
                productVariables={productVariables}
                editSimulatorModalRef={editSimulatorModalRef}
              />
            </div>
          )}
        </section>
        <section>
          {updateProductModal && (
            <ProductUpdateModal
              show={updateProductModal}
              setShow={setUpdateProductModal}
              productData={productDetails}
              isSimulatorProduct={Boolean(productDetails.properties?.simulatorName)}
            />
          )}
          <EditSimulatorModal ref={editSimulatorModalRef} />
          {showProductSettingsModal && (
            <ProductSettingsModal
              show={showProductSettingsModal}
              setShow={setShowProductSettingsModal}
              prodVars={updatingProductVars}
              varMap={updatingVarMap}
              productVariables={productVariables}
              projectId={customVariables.data.projectId}
              productData={productDetails}
            />
          )}
        </section>
      </main>

      {productDetails?.properties?.simulatorName ? (
        <ConfirmPrompt
          show={showDeletePrompt}
          validate
          item={productName}
          onCancel={() => setShowDeletePrompt(false)}
          loading={deleteSimulationMutation.isPending}
          onConfirm={() =>
            deleteSimulationMutation.mutate({
              simulatorId: productDetails?.properties?.simulatorName
            })
          }
        />
      ) : (
        <ConfirmPrompt
          show={showDeletePrompt}
          title={`Confirm Delete ${productName}`}
          message="Deleting this product will also result in deleting all associated devices. Please ensure you have backed up any necessary data before proceeding."
          onCancel={() => setShowDeletePrompt(false)}
          validate
          item={productName}
          loading={loadingBtn}
          onConfirm={handleDelete}
        />
      )}
    </>
  );
};

export default DefaultProductDetails;
