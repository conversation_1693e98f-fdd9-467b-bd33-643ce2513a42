import { colors, lighten } from "@mui/material";
import { differenceInMinutes, differenceInSeconds, format } from "date-fns";
import { isDateNumber } from "../../utils/date";

import {
  Activity,
  Airplay,
  AlertCircle,
  AudioLines,
  BarChart,
  BarChart2,
  Battery,
  BatteryCharging,
  BellOff,
  Bike,
  Bluetooth,
  Bookmark,
  Cable,
  Calendar,
  Camera,
  Cast,
  ChartArea,
  ChartBar,
  ChartBarBig,
  ChartBarStacked,
  ChartCandlestick,
  ChartColumn,
  ChartColumnBig,
  ChartColumnStacked,
  ChartGantt,
  ChartLine,
  ChartNetwork,
  ChartNoAxesCombined,
  ChartPie,
  ChartScatter,
  ChartSpline,
  Check,
  CircleFadingPlus,
  CircleParking,
  Clipboard,
  Clock,
  Cloud,
  CloudOff,
  Code,
  Command,
  Cpu,
  Database,
  Download,
  Edit,
  ExternalLink,
  EyeOff,
  FastForward,
  FileText,
  Filter,
  Gauge,
  GitPullRequest,
  Github,
  Gitlab,
  Globe,
  Hash,
  Headphones,
  HelpCircle,
  Home,
  Image,
  Info,
  Key,
  Layers,
  Layout,
  Link2,
  List,
  Loader,
  Lock,
  LogOut,
  Mail,
  MapPin,
  MapPinCheck,
  MapPinOff,
  MapPinned,
  Menu,
  MessageCircle,
  MicOff,
  Phone,
  PhoneOff,
  QrCode,
  Radar,
  Radio,
  Router,
  TrendingDown,
  TrendingUp,
  Truck,
  Webhook,
  Wifi
} from "lucide-react";
import { DashboardGraphItem } from "../../..";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";

export const ICON_SCHEMES = [
  { Icon: ChartArea, label: "ChartArea" },
  { Icon: ChartBarBig, label: "ChartBarBig" },
  { Icon: ChartBarStacked, label: "ChartBarStacked" },
  { Icon: ChartCandlestick, label: "ChartCandlestick" },
  { Icon: ChartColumn, label: "ChartColumn" },
  { Icon: ChartColumnBig, label: "ChartColumnBig" },
  { Icon: ChartColumnStacked, label: "ChartColumnStacked" },
  { Icon: ChartGantt, label: "ChartGantt" },
  { Icon: ChartNetwork, label: "ChartNetwork" },
  { Icon: ChartNoAxesCombined, label: "ChartNoAxesCombined" },
  { Icon: ChartScatter, label: "ChartScatter" },
  { Icon: ChartSpline, label: "ChartSpline" },
  { Icon: ChartLine, label: "ChartLine" },
  { Icon: TrendingDown, label: "TrendingDown" },
  { Icon: TrendingUp, label: "TrendingUp" },
  { Icon: ChartBar, label: "ChartBar" },
  { Icon: ChartPie, label: "ChartPie" },
  { Icon: AudioLines, label: "AudioLines" },
  { Icon: CircleFadingPlus, label: "CircleFadingPlus" },
  { Icon: Phone, label: "Phone" },
  { Icon: PhoneOff, label: "PhoneOff" },
  { Icon: BatteryCharging, label: "BatteryCharging" },
  { Icon: Cable, label: "Cable" },
  { Icon: Wifi, label: "Wifi" },
  { Icon: Router, label: "Router" },
  { Icon: QrCode, label: "QrCode" },
  { Icon: Radio, label: "Radio" },
  { Icon: MapPin, label: "MapPin" },
  { Icon: MapPinCheck, label: "MapPinCheck" },
  { Icon: MapPinOff, label: "MapPinOff" },
  { Icon: MapPinned, label: "MapPinned" },
  { Icon: Bike, label: "Bike" },
  { Icon: Truck, label: "Truck" },
  { Icon: CircleParking, label: "CircleParking" },
  { Icon: Gauge, label: "Gauge" },

  { Icon: Radar, label: "Radar" },
  { Icon: Airplay, label: "Airplay" },
  { Icon: Webhook, label: "Webhook" },
  { Icon: MicOff, label: "MicOff" },
  { Icon: Activity, label: "Activity" },
  { Icon: AlertCircle, label: "AlertCircle" },
  { Icon: BarChart, label: "BarChart" },
  { Icon: BarChart2, label: "BarChart2" },
  { Icon: Battery, label: "Battery" },
  { Icon: BellOff, label: "BellOff" },
  { Icon: Bluetooth, label: "Bluetooth" },
  { Icon: Bookmark, label: "Bookmark" },
  { Icon: Calendar, label: "Calendar" },
  { Icon: Camera, label: "Camera" },
  { Icon: Cast, label: "Cast" },
  { Icon: Check, label: "Check" },
  { Icon: Clipboard, label: "Clipboard" },
  { Icon: Clock, label: "Clock" },
  { Icon: Cloud, label: "Cloud" },
  { Icon: CloudOff, label: "CloudOff" },
  { Icon: Code, label: "Code" },
  { Icon: Command, label: "Command" },
  { Icon: Cpu, label: "Cpu" },
  { Icon: Database, label: "Database" },
  { Icon: Download, label: "Download" },
  { Icon: Edit, label: "Edit" },
  { Icon: ExternalLink, label: "ExternalLink" },
  { Icon: EyeOff, label: "EyeOff" },
  { Icon: FastForward, label: "FastForward" },
  { Icon: FileText, label: "FileText" },
  { Icon: Filter, label: "Filter" },
  { Icon: GitPullRequest, label: "GitPullRequest" },
  { Icon: Github, label: "Github" },
  { Icon: Gitlab, label: "Gitlab" },
  { Icon: Globe, label: "Globe" },
  { Icon: Hash, label: "Hash" },
  { Icon: Headphones, label: "Headphones" },
  { Icon: HelpCircle, label: "HelpCircle" },
  { Icon: Home, label: "Home" },
  { Icon: Image, label: "Image" },
  { Icon: Info, label: "Info" },
  { Icon: Key, label: "Key" },
  { Icon: Layers, label: "Layers" },
  { Icon: Layout, label: "Layout" },
  { Icon: Link2, label: "Link2" },
  { Icon: List, label: "List" },
  { Icon: Loader, label: "Loader" },
  { Icon: Lock, label: "Lock" },
  { Icon: LogOut, label: "LogOut" },
  { Icon: Mail, label: "Mail" },

  { Icon: Menu, label: "Menu" },
  { Icon: MessageCircle, label: "MessageCircle" }
];

const intl = new Intl.NumberFormat("en", { notation: "compact" });
export const formattedNumber = (number, decimal = 0) => {
  if (typeof number === "string") {
    return number;
  }
  try {
    if (decimal) {
      const intlDecimal = new Intl.NumberFormat("en", {
        notation: "compact",
        maximumFractionDigits: decimal
      });
      return intlDecimal.format(number);
    }

    return intl.format(number);
  } catch (error) {
    return number;
  }
};

const intlDistance = new Intl.NumberFormat("en", {
  style: "unit",
  unit: "meter",
  unitDisplay: "long",
  maximumFractionDigits: 0
});

export const formattedNumberDistance = (number: number) => {
  try {
    return intlDistance.format(number);
  } catch (error) {
    return number;
  }
};

export const getStartAndEndOfDay = (dateStr, stepSize = 1) => {
  const date = new Date(dateStr);

  const start = new Date(date);
  const end = new Date(date);

  if (stepSize > 1) {
    end.setDate(end.getDate() + stepSize - 1);
  }

  start.setHours(0, 0, 0, 0);
  end.setHours(23, 59, 59, 999);

  return { start: start.toISOString(), end: end.toISOString() };
};

export const getStartAndEndOfHour = (dateTimeString, stepSize = 1) => {
  const inputDate = new Date(dateTimeString);

  const start = new Date(inputDate);
  const end = new Date(inputDate);

  start.setHours(start.getHours());
  end.setHours(end.getHours() + stepSize);

  return { start: start.getTime(), end: end.getTime() };
};

export const getStartAndEndOfMinute = (dateTimeString, stepSize = 1) => {
  const inputDate = new Date(dateTimeString);

  const start = new Date(inputDate);
  start.setMinutes(start.getMinutes());

  const end = new Date(inputDate);
  end.setMinutes(end.getMinutes() + stepSize);

  return { start: start.getTime(), end: end.getTime() };
};

export const getStartAndEndOfSecond = (dateTimeString, stepSize = 1) => {
  const inputDate = new Date(dateTimeString);

  const start = new Date(inputDate);
  start.setSeconds(start.getSeconds());

  const end = new Date(inputDate);
  end.setSeconds(end.getSeconds() + stepSize);

  return { start: start.getTime(), end: end.getTime() };
};

export const getStartAndEndOfMonth = (date) => {
  const currentDate = new Date(date);
  currentDate.setDate(1);
  const start = new Date(currentDate);
  currentDate.setMonth(currentDate.getMonth() + 1);
  currentDate.setDate(0);
  const end = new Date(currentDate);

  return { start: start.toISOString(), end: end.toISOString() };
};

export const getTodayTimestamp = () => {
  const { start } = getStartAndEndOfDay(new Date().toDateString());

  const res = `${start}TO${new Date().toISOString()}`;
  return res;
};

export const SIZING_OPTIONS = {
  X: { w: 1, h: 5 },
  "1X": { w: 2, h: 5 },
  "2X": { w: 4, h: 10 },
  "4X": { w: 8, h: 10 }
};

/**
 * @type {import("../../..").GraphColor[]}
 */
export const GRAPH_COLORS = ["blue", "green", "pink", "purple", "amber"];

export const resizedText = (text, count = 5) => {
  try {
    if (text.length > count * 2 + 3) {
      const first = text.slice(0, count);
      const last = text.slice(count * -1);
      return `${first}...${last}`;
    }
    return text;
  } catch (error) {
    return text;
  }
};

export const getColorFromType = (type, light = false, text = false) => {
  const colorPlace = text ? "text" : "bg";
  const brightness = light ? 100 : 500;
  if (type === "sky") {
    return `${colorPlace}-sky-${brightness}`;
  }
  if (type === "red") {
    return `${colorPlace}-red-${brightness}`;
  }
  if (type === "green") {
    return `${colorPlace}-green-${brightness}`;
  }
  if (type === "grey") {
    return `${colorPlace}-grey-${brightness}`;
  }
  if (type === "purple") {
    return `${colorPlace}-purple-${brightness}`;
  }
  return `${colorPlace}-blue-${brightness}`;
};

export function getArrayStats(arr: number[]) {
  if (arr.length === 0) {
    return { min: { value: 0 }, max: { value: 0 }, avg: { value: 0 }, latest: { value: 0 } };
  }

  let min = arr[0]!;
  let max = arr[0]!;
  let sum = 0;
  let latest = arr[0]!;

  for (let i = 0; i < arr.length; i++) {
    const current = arr[i]!;
    sum += current;
    if (current < min) min = current;
    if (current > max) max = current;
    latest = current;
  }

  const avg = sum / arr.length;

  return {
    min: { value: min },
    max: { value: max },
    avg: { value: avg },
    latest: { value: latest }
  };
}

export const getGraphColorFromType = (chartType) => {
  const type =
    chartType === "Temperature"
      ? colors.red
      : chartType === "Humidity"
        ? colors.cyan
        : chartType === "Precipitation"
          ? colors.green
          : colors.blue;

  return type;
};
/**
 * @param {string} s
 * @returns {string}
 */
export function camelCaseToWords(s) {
  const result = s.replace(/([A-Z])/g, " $1");
  return result.charAt(0).toUpperCase() + result.slice(1);
}

/**
 *
 * @param {number} [length]
 * @returns {string}
 */
export function generateRandomString(length = 10) {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let randomString = "";

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }

  return randomString;
}

export function generateRandomId(length = 10) {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;

  // Use Uint8Array for cryptographic randomness (more secure and unpredictable)
  const randomValues = new Uint8Array(length);
  crypto.getRandomValues(randomValues);

  // Use Array to build the string for performance
  const result = Array.from(randomValues, (byte) => characters[byte % charactersLength]);
  return result.join("");
}

export const pointLabelsPlugin = {
  id: "pointLabels",
  afterDatasetsDraw(chart) {
    // Only run this plugin for line charts
    if (chart.config.type !== "line") {
      return;
    }

    const { ctx } = chart;

    chart.data.datasets.forEach((dataset, datasetIndex) => {
      const meta = chart.getDatasetMeta(datasetIndex);

      if (!meta.hidden) {
        meta.data.forEach((element, index) => {
          const { x, y } = element.getCenterPoint();
          const value = dataset.data[index];

          // Label offset strategy to prevent overlap
          let yOffset = -20; // Default offset above the point

          // Simple overlap prevention for adjacent points
          if (index > 0 && Math.abs(dataset.data[index] - dataset.data[index - 1]) < 10) {
            yOffset = index % 2 === 0 ? -20 : 20;
          }

          // Draw label
          ctx.save();
          ctx.font = "bold 12px Arial";
          ctx.fillStyle = "rgba(54, 162, 235, 1)";
          ctx.textAlign = "center";
          ctx.fillText(formattedNumber(value, 1), x, y + yOffset);
          ctx.restore();
        });
      }
    });
  }
};

export const chartFontStyle = { size: 10.5, weight: "500", fontFamily: "Inter, sans-serif" };
function calculateChartMin(min: number, max: number) {
  // Calculate the range and relative difference
  const range = max - min;
  const relativeDiff = range / min;

  // If min is close to 0 or negative, return 0
  if (min <= 1) {
    return 0;
  }

  if (Number.isNaN(relativeDiff)) {
    return 0;
  }

  if (relativeDiff < 0.05) {
    return 0;
  }

  if (relativeDiff < 0.1) {
    // For high values with small variations, start close to min
    if (min > 10000) {
      // Round down to nearest hundred/thousand based on magnitude
      const magnitude = Math.floor(Math.log10(min));
      const roundingFactor = Math.pow(10, magnitude - 1);
      return Math.floor(min / roundingFactor) * roundingFactor;
    }
    // For smaller values with small variations, start from 0
    return 0;
  }

  // Case 2: Medium relative difference (1% to 20%)
  if (relativeDiff <= 0.2) {
    // Round down to nearest power of 10 based on magnitude
    const magnitude = Math.floor(Math.log10(min));
    const roundingFactor = Math.pow(10, magnitude - 1);
    return Math.floor(min / roundingFactor) * roundingFactor;
  }

  return min * 0.9; // 10% padding
}

export const valueAxis = {
  suggestedMax: (scale) => {
    const max = Math.max(...scale.chart.data.datasets.flatMap((d) => d.data));
    return max * 1.1; // 10% padding
  },
  suggestedMin: (scale) => {
    const data = scale.chart.data.datasets
      .flatMap((d) => d.data)
      .map((item) => (typeof item === "object" ? item.y : item));

    const min = Math.min(...data);
    const max = Math.max(...data);

    return calculateChartMin(min, max);
  },
  ticks: { font: chartFontStyle },
  border: { display: false }
};

export const getDiffDate = (items: string[] = []): [number, string] => {
  try {
    const item1 = new Date(items[items.length - 1]);
    const item2 = new Date(items[items.length - 2]);
    const minutesDifference = differenceInMinutes(item1, item2);
    const secondsDifference = differenceInSeconds(item1, item2);

    if (minutesDifference >= 24 * 60 * 27) {
      return [Math.floor((minutesDifference / 24) * 60 * 30), "month"];
    }

    if (minutesDifference >= 1440) {
      return [Math.floor(minutesDifference / 1440), "day"];
    }

    if (minutesDifference >= 60) {
      return [Math.floor(minutesDifference / 60), "hour"];
    }

    if (minutesDifference === 0) {
      return [secondsDifference, "second"];
    }
    return [minutesDifference, "minute"];
  } catch (error) {
    return [1, "minute"];
  }
};

export function adjustTimeToBrowserOffset(date: Date): Date {
  const offsetMinutes = new Date().getTimezoneOffset();
  const offsetMilliseconds = Math.abs(offsetMinutes) * 60 * 1000;
  const shouldAdd = offsetMinutes < 0;

  return new Date(
    shouldAdd ? date.getTime() + offsetMilliseconds : date.getTime() - offsetMilliseconds
  );
}

export const getFormattedTimeStamp = (timestamp: string, unit: string, timeGap: number) => {
  if (unit === "month") {
    return convetUTCToLocal(timestamp, "MMM YYYY");
  }
  if (unit === "day") {
    if (timeGap === 1) {
      return convetUTCToLocal(timestamp, "MMM DD, YYYY");
    }
    return `${convetUTCToLocal(timestamp, "MMM DD, YYYY")} - ${convetUTCToLocal(
      new Date(timestamp).setDate(new Date(timestamp).getDate() + 1),
      "MMM DD, YYYY"
    )}`;
  }
  return convetUTCToLocal(timestamp);
};

export const selectDurationFromGraph = ({ element, labels, unit, stepSize }) => {
  if (element.length > 0) {
    const index = element[0].index;
    const currentTimestamp = labels[index];

    if (unit === "month") {
      const { start, end } = getStartAndEndOfMonth(currentTimestamp);
      const res = {
        title: "custom",
        value: `${adjustTimeToBrowserOffset(new Date(start)).toISOString()}TO${adjustTimeToBrowserOffset(new Date(end)).toISOString()}`
      };
      return res;
    }

    if (unit === "day") {
      const { start, end } = getStartAndEndOfDay(currentTimestamp, stepSize);
      const res = {
        title: "custom",
        value: `${adjustTimeToBrowserOffset(
          new Date(start)
        ).toISOString()}TO${adjustTimeToBrowserOffset(new Date(end)).toISOString()}`
      };
      return res;
    }
    if (unit === "hour") {
      const { start, end } = getStartAndEndOfHour(currentTimestamp, stepSize);
      const res = {
        title: "custom",
        value: `${adjustTimeToBrowserOffset(new Date(start)).toISOString()}TO${adjustTimeToBrowserOffset(new Date(end)).toISOString()}`
      };
      return res;
    }

    if (unit === "minute") {
      const { start, end } = getStartAndEndOfMinute(currentTimestamp, stepSize);
      const res = {
        title: "custom",
        value: `${adjustTimeToBrowserOffset(new Date(start)).toISOString()}TO${adjustTimeToBrowserOffset(new Date(end)).toISOString()}`
      };
      return res;
    }

    if (unit === "second") {
      const { start, end } = getStartAndEndOfSecond(currentTimestamp, stepSize);
      const res = {
        title: "custom",
        value: `${adjustTimeToBrowserOffset(new Date(start)).toISOString()}TO${adjustTimeToBrowserOffset(new Date(end)).toISOString()}`
      };
      return res;
    }

    return null;
  }
  return null;
};

/**
 * @param {import("../../features/dashboardBuilderSlice").GraphDuration} graphDuration
 * @returns {string}
 */
export const getDateRangeFormatter = (graphDuration) => {
  const { title, value } = graphDuration;
  try {
    const [date1, date2] = value.split("TO");

    const startDate = new Date(date1);
    const endDate = new Date(date2);

    let formatter = "Lo LLL k:mm";

    if (startDate.getFullYear() !== endDate.getFullYear()) {
      formatter = `${formatter} yy`;
    }

    const formattedStart = format(startDate, formatter);
    const formattedEnd = format(endDate, formatter);

    return `${formattedStart} To ${formattedEnd}`;
  } catch (error) {
    return title;
  }
};

export const isTopLevelFilter = (key = "", allKeys: any = []) => {
  const res = [];

  try {
    for (let i = 0; i < allKeys.length; i++) {
      const element = allKeys[i];
      if (element !== key && element.split(".")[0] === key) {
        res.push(element);
      }
    }
    return res;
  } catch (error) {
    return [];
  }
};

export const SENSOR_KEY = "sensor-data";
export const DIGITAL_TWIN_PRODUCT = "DiGiTaL-TwiN-ProDucT";
export const digitalTwinProductThing = (product: string) => `DiGiTaL-TwiN-${product}-ThInG`;

export const isProductLevelDigitalTwin = (thingName: string) => {
  try {
    return thingName.startsWith("DiGiTaL-TwiN-") && thingName.endsWith("-ThInG");
  } catch (error) {
    return false;
  }
};

export const formatIndexElasticIndex = (index = "") => {
  try {
    if (index === SENSOR_KEY) return "time series";
    const parts = index.split("-");
    return parts.length > 1 ? parts[1] : index;
  } catch (error) {
    return index;
  }
};

export const formatPayloadField = (str = "") => {
  try {
    return str.replace(/_/g, " ");
  } catch (error) {
    return str;
  }
};

export const dateFormatter = new Intl.DateTimeFormat("en-US", {
  year: "numeric",
  month: "2-digit",
  day: "2-digit",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit"
});

/**
 *
 * @param {import("../../..").ChartType} chartType
 * @returns {boolean}
 */
export const isChartCircular = (chartType): boolean => {
  const res = { Pie: true, Donut: true, Polar: true, Radar: true, Radial: true };
  return res[chartType];
};

/**
 * @param {string[]} [arr]
 * @returns {Record<string,boolean>}
 */
export const getBoolMapOfKeys = (arr) => {
  /**
   * @type {Record<string,boolean>}
   */
  const res = {};
  if (!arr) {
    return res;
  }
  try {
    for (let index = 0; index < arr.length; index++) {
      const item = arr[index];
      res[item] = true;
    }

    return res;
  } catch (error) {
    return res;
  }
};

export const isConnectionStatusOrShadow = (index = "") => {
  return index.includes("connection-status") || index.includes("-shadow");
};

export const getTextForValue = (value) => {
  if (value === null) {
    return "N/A";
  }

  if (isDateNumber(value)) {
    return isDateNumber(value) === "s" ? convetUTCToLocal(value) : convetUTCToLocal(value * 1000);
  }
  if (typeof value === "number") {
    return formattedNumber(value, 2);
  }
  return value;
};

export const getFilterOptions = (elasticIndex) => {
  if (!elasticIndex) {
    return [];
  }
  if (elasticIndex.includes("-connection-status")) {
    return ["productName", "version", "status"];
  }
  if (elasticIndex.includes("-time-series")) {
    return ["event", "from", "productName"];
  }
  if (elasticIndex.includes("-shadow")) {
    return ["productName"];
  }
  if (elasticIndex.includes("-logs")) {
    return ["event", "clientId"];
  }
  return [];
};

export const COLORS = [
  "#2662D9",
  "#2EB88A",
  "#E23670",
  "#E88C30",
  "#AF57DB",
  "#2A9D90",
  "#E76E50",
  "#274754",
  "#E8C468",
  "#F4A462"
];

export const COLOR_SCHEMES = {
  default: COLORS,
  sapphire: [
    "#2463EB",
    "#60A8FB",
    "#3B86F7",
    "#90C7FE",
    "#BEDCFE",
    "#1D4ED8",
    "#4A90E2",
    "#2F80ED",
    "#6CA7F8",
    "#B3D8FF"
  ],
  ruby: [
    "#E21D48",
    "#FBD5DA",
    "#F17E92",
    "#F7ABB6",
    "#E9536F",
    "#E53E3E",
    "#F66D74",
    "#EB4D4B",
    "#F6936D",
    "#F9A8A5"
  ],
  emerald: [
    "#125427",
    "#1DC355",
    "#098637",
    "#113B1D",
    "#0E2014",
    "#1E402B",
    "#2AB250",
    "#1F8923",
    "#4CAF50",
    "#A5D6A7"
  ],
  retro: [
    "#f46a9b",
    "#ea5545",
    "#ef9b20",
    "#27aeef",
    "#edbf33",
    "#ede15b",
    "#b33dc6",
    "#bdcf32",
    "#87bc45"
  ],
  spring: [
    "#bd7ebe",
    "#ffb55a",
    "#ffee65",
    "#beb9db",
    "#fdcce5",
    "#8bd3c7",
    "#fd7f6f",
    "#7eb0d5",
    "#b2e061"
  ],
  bold: [
    "#4bc0c0",
    "#e6d800",
    "#9b19f5",
    "#ffa300",
    "#dc0ab4",
    "#b3d4ff",
    "#00bfa0",
    "#e60049",
    "#0bb4ff"
  ],
  mixed: [
    "#dc0ab4",
    "#b3d4ff",
    "#00bfa0",
    "#e60049",
    "#0bb4ff",
    "#4bc0c0",
    "#e6d800",
    "#9b19f5",
    "#ffa300",
    "#bd7ebe"
  ]
};

/**
 * @param {number} i
 * @param {'default'|'retro'|'spring'|'bold'} scheme
 * @returns {string}
 */
export const colorAtI = (i, scheme = "default") => {
  const schemeColors = COLOR_SCHEMES[scheme];
  const index = i % schemeColors.length;

  return lighten(schemeColors[index], 0.15);
};

/**
 * @param {number[]} [arr]
 * @returns {{min:number;max:number}}
 */
export function findMinMax(arr = []) {
  if (!arr || !Array.isArray(arr) || arr.length === 0) {
    return { min: 0, max: 0 };
  }

  let min = arr[0].y || arr[0];
  let max = arr[0].y || arr[0];

  for (let i = 1; i < arr.length; i++) {
    const item = arr[i].y || arr[i];
    if (item < min) {
      min = Math.round(item);
    }
    if (item > max) {
      max = Math.round(item);
    }
  }

  return { min, max };
}

export const isChartEnabled = (
  dataIndex: string,
  dataKey: string,
  _filters: DashboardGraphItem["filters"],
  count = false
) => {
  const filters = _filters?.filter((item) => item.enabled) || [];
  if (!dataIndex || !dataKey) {
    return false;
  }
  if (dataKey === "payload") {
    const productNameFilter = filters.find((item) => item.field === "productName")?.value?.length;
    return Boolean(productNameFilter);
  }

  if (count && dataKey === "payload") {
    const productNameFilter = filters.find((item) => item.field === "productName")?.value?.length;
    const fieldFilter = filters.find((item) => item.field === "field")?.value?.length;

    return Boolean(productNameFilter) && Boolean(fieldFilter);
  }
  return true;
};
