import { DashboardGraphItem } from "@/index";
import ActionButton from "@components/ActionButton";
import { X } from "lucide-react";
import { motion } from "framer-motion";
import { useCallback } from "react";
import { setExpandedGraph } from "../../../features/expandedGraphSlice";
import { useAppDispatch, useAppSelector } from "../../../store";
import ChartExpandHandler from "../layout/ChartExpandHandler";
import ChartIcon from "../layout/ChartIcon";
import ChartRenderer from "../layout/ChartRenderer";
import SelectDuration from "../layout/SelectDuration";
import { colorAtI } from "../utils";
import ChartOverview from "./ChartOverview";

const getv2Filters = (deepFilter: any) => {
  try {
    const {
      field: [field],
      productName: [productName],
      thingName: [thingName]
    } = deepFilter;

    return [
      { enabled: true, operation: "is", field: "payload.field", value: [field], id: field },
      { enabled: true, operation: "is", field: "thingName", value: [thingName], id: thingName },
      {
        enabled: true,
        operation: "is",
        field: "productName",
        value: [productName],
        id: productName
      }
    ] as DashboardGraphItem["filters"];
  } catch (error) {
    return [];
  }
};

const RenderGraph = ({ closeModal }: { closeModal: () => void }) => {
  const expandedGraph = useAppSelector(({ expandedGraph: expanded }) => expanded.expandedGraph);
  if (!expandedGraph) {
    return null;
  }

  const { id, expanded, stacked, dataIndex, dataKey, chartType, colorScheme, widget } =
    expandedGraph;

  const filters =
    "deepFilter" in expandedGraph ? getv2Filters(expandedGraph.deepFilter) : expandedGraph.filters;

  const color = widget && expandedGraph.color ? expandedGraph.color : colorAtI(0, colorScheme);

  return (
    <div className="h-full w-full pb-4">
      <div className="flex items-center justify-between mb-2 pb-2">
        <div className="flex items-center gap-4">
          <ChartIcon chartType={chartType} color={color} />
          <ChartOverview
            dataIndex={dataIndex}
            dataKey={dataKey}
            className="!font-semibold !heading-1 !text-black dark:!text-white h-5"
            modal
            filters={filters}
            widget={widget}
          />
        </div>
        <div className="flex gap-3 mr-12 items-center">
          <div className=" flex gap-3">
            <ChartExpandHandler
              stacked={stacked}
              color={color}
              modalGraph
              expanded={expanded}
              id={id}
            />
          </div>
          <SelectDuration type="modal" />
        </div>
        <ActionButton className="!p-1 !absolute !right-4" Icon={X} onClick={closeModal} />
      </div>

      <ChartRenderer graphData={{ ...expandedGraph, size: "2X", filters }} modal />
    </div>
  );
};

const ExpandedGraphModal = () => {
  const dispatch = useAppDispatch();
  // const isCollapsed = useAppSelector(({ sidebarSlice }) => sidebarSlice.isCollapsed);
  const expandedId = useAppSelector(({ expandedGraph }) => expandedGraph.expandedGraph?.id);

  const closeModal = useCallback(() => {
    dispatch(setExpandedGraph(null));
  }, []);

  if (!expandedId) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="z-[100]"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        margin: 0,
        // paddingLeft: "4rem",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "rgba(0, 0, 0, 0.6)"
      }}
      onClick={closeModal}
    >
      <motion.div
        layoutId={expandedId}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        className="card ignore-click w-[90%] md:w-10/12 h-[60vh] md:h-[70vh] pt-4 relative pb-14 "
      >
        <RenderGraph closeModal={closeModal} />
      </motion.div>
    </motion.div>
  );
};

export default ExpandedGraphModal;
