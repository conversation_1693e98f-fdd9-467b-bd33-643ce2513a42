import ActionButton from "@components/ActionButton";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r<PERSON>ontent,
  <PERSON>ubar<PERSON><PERSON>,
  <PERSON>ubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import useSelectedSiteName from "@hooks/site/useSelectedSiteName";
import { showErrorToast } from "@utils/index";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import {
  ArrowDownToLine,
  Check,
  CircleMinus,
  Copy,
  Edit,
  EllipsisVertical,
  EyeClosed,
  Trash,
  ZoomIn,
  ZoomOut
} from "lucide-react";
import { MouseEventHandler } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  cloneDashboardWidget,
  openDashboardDrawer,
  updateDashboardGraph
} from "../../../features/dashboardBuilderSlice";
import { useAppDispatch, useAppSelector } from "../../../store";
import { isChartCircular } from "../utils";
import useHandleDownloadTable from "./useHandleDownloadTable";
import ChartExpandHandler from "../layout/ChartExpandHandler";

const Options = ({
  removeGraph,
  id,
  chartType,
  size,
  isOverview = false,
  nestedDashboardId,
  isCircularChart,
  filterBy,
  dataKey,
  expanded,
  stacked
}: {
  removeGraph: any;
  id: any;
  chartType: any;
  size: any;
  isOverview?: boolean;
  nestedDashboardId?: number;

  isCircularChart?: any;
  filterBy?: any;
  dataKey?: any;
  color?: any;
  expanded?: any;
  stacked?: any;
}) => {
  const areaName = useParams().areaName;

  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const handleClick: MouseEventHandler<HTMLButtonElement> = (event) => {
    event.stopPropagation();
  };
  const handleClose = () => {};
  const siteName = useSelectedSiteName();
  const graphList = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.dashboardGraphList);

  const circular = isChartCircular(chartType);
  const zoomOutDisabled = size === "1X" || (chartType === "Table" && size === "2X");

  const zoomInDisabled = size === "4X" || (size === "2X" && circular);

  const handleDownload = useHandleDownloadTable(id);

  const handleZoomIn: MouseEventHandler<HTMLDivElement> = (e) => {
    e.stopPropagation();
    handleClose();
    if (size === "1X") {
      dispatch(updateDashboardGraph({ id, size: "2X" }));
    } else if (size === "2X") {
      dispatch(updateDashboardGraph({ id, size: "4X" }));
    }
  };
  const handleZoomOut: MouseEventHandler<HTMLDivElement> = (e) => {
    e.stopPropagation();
    handleClose();
    if (size === "4X") {
      dispatch(updateDashboardGraph({ id, size: "2X" }));
    } else if (size === "2X") {
      dispatch(updateDashboardGraph({ id, size: "1X" }));
    }
  };

  const handleMarkAsOverview = () => {
    if (!isOverview) {
      const currentOverViewItemCount = graphList.filter((item) => item.isOverview).length;
      if (currentOverViewItemCount >= 4) {
        showErrorToast("You can only have 4 overview widgets at a time , remove one to add more");
        return;
      }
    }
    dispatch(updateDashboardGraph({ id, isOverview: !isOverview }));
  };

  const navigateToNestedAreaDashboard = () => {
    navigate(`/site/${siteName}/${areaName}?nested=${nestedDashboardId}`);
  };

  return (
    <Menubar className="!bg-transparent border-none p-0">
      <MenubarMenu>
        <MenubarTrigger className="rounded-full mr-1">
          <ActionButton
            Icon={EllipsisVertical}
            onClick={handleClick}
            data-testid="widget-more-options"
          />
        </MenubarTrigger>
        <MenubarContent>
          <ChartExpandHandler menuOption={true} expanded={expanded} stacked={stacked} id={id} />

          <MenubarItem
            onClick={(e) => {
              e.stopPropagation();
              removeGraph();
              handleClose();
            }}
            data-testid="remove-widget"
          >
            <CircleMinus size={BUTTON_ICON_SIZE} className="mr-2" />
            Remove
          </MenubarItem>

          <MenubarItem
            onClick={(e) => {
              e.stopPropagation();
              handleClose();
              dispatch(cloneDashboardWidget(id));
            }}
            data-testid="clone-widget"
          >
            <Copy size={BUTTON_ICON_SIZE} className="mr-2" />
            Clone
          </MenubarItem>

          {!["Nested"].includes(chartType) && (
            <MenubarItem
              onClick={(e) => {
                e.stopPropagation();
                handleClose();
                dispatch(openDashboardDrawer(id));
              }}
              data-testid="edit-widget"
            >
              <Edit size={BUTTON_ICON_SIZE} className="mr-2" />
              Edit
            </MenubarItem>
          )}
          {chartType === "Table" && (
            <MenubarItem onClick={handleDownload} data-testid="download-table-widget">
              <ArrowDownToLine size={BUTTON_ICON_SIZE} className="mr-2" />
              Export
            </MenubarItem>
          )}

          {siteName && chartType === "Nested" && (
            <MenubarItem onClick={navigateToNestedAreaDashboard} data-testid="zoom-in-widget">
              <EyeClosed size={BUTTON_ICON_SIZE} className="mr-2" />
              Details
            </MenubarItem>
          )}
          {!["Count", "Meter", "Nested", "Trigger"].includes(chartType) && (
            <>
              <MenubarItem
                onClick={handleZoomIn}
                disabled={zoomInDisabled}
                data-testid="zoom-in-widget"
              >
                <ZoomIn size={BUTTON_ICON_SIZE} className="mr-2" />
                Zoom in
              </MenubarItem>
              <MenubarItem
                onClick={handleZoomOut}
                disabled={zoomOutDisabled}
                data-testid="zoom-out-widget"
              >
                <ZoomOut size={BUTTON_ICON_SIZE} className="mr-2" />
                Zoom out
              </MenubarItem>
            </>
          )}

          {areaName && chartType !== "Table" && (
            <MenubarItem
              // disabled
              onClick={handleMarkAsOverview}
              data-testid="download-table-widget"
            >
              {isOverview ? (
                <Trash size={BUTTON_ICON_SIZE} className="mr-2" />
              ) : (
                <Check size={BUTTON_ICON_SIZE} className="mr-2" />
              )}
              {isOverview ? "Remove from overview" : "Mark as overview"}
            </MenubarItem>
          )}
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  );
};

export default Options;
