import { I<PERSON><PERSON>utton } from "@mui/material";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { JsonViewer } from "@textea/json-viewer";
import clsx from "clsx";
import { X } from "lucide-react";
import { useContext } from "react";
import { DarkModeContext } from "../../../hooks/useDarkMode";
import JSONDisplay from "../Charts/JSONText";
import { getTextForValue } from "../utils";

const ExpandedJson = ({
  data,
  onClose,
  table = false,
  width = "400px",
  onlyJson = false,
  header = null
}) => {
  const [darkMode] = useContext(DarkModeContext);

  if (!data) {
    return null;
  }

  return (
    <div
      id="dashboard-table-container"
      data-testid="expanded-json-sidebar"
      className={clsx("right-0 overflow-y-scroll z-50 p-4 card", table ? "fixed" : "absolute")}
      style={{
        top: table ? "3.4rem" : 0,
        bottom: table ? "3.5rem" : 0,
        width
      }}
    >
      {!onlyJson && (
        <>
          <div className="mb-2">
            <h2 className="text-gray-400 font-medium mb-1 description">Timestamp</h2>
            <h3 className="font-medium description">{convetUTCToLocal(data.timestamp)}</h3>
          </div>
          <div className="device-y flex flex-col mb-2 rounded-md py-1 bg-secondary">
            {Object.entries(data).map(([key, value]) => (
              <div key={key} className="flex py-1 ml-2">
                <div className="min-w-[120px] ">
                  <p className="description text-gray-500 capitalize">{key}</p>
                </div>
                {value && typeof value === "object" ? (
                  <JSONDisplay className="description" dataObject={value} />
                ) : (
                  <p className="overflow-hidden description">{getTextForValue(value)}</p>
                )}
              </div>
            ))}
          </div>
        </>
      )}

      {header}

      <h2 className="text-gray-400 font-medium mb-1 description">JSON view</h2>
      <JsonViewer
        value={data}
        theme={darkMode ? "dark" : "light"}
        style={{
          backgroundColor: "inherit",
          fontSize: "14px",
          fontFamily: "inherit"
        }}
        enableClipboard
        quotesOnKeys={false}
        displayDataTypes={false}
      />

      <div className="absolute top-1 right-0 dashboard-item-content">
        <IconButton data-testid="close-expanded-json" onClick={onClose} className="!p-1">
          <X size={14} />
        </IconButton>
      </div>
    </div>
  );
};

export default ExpandedJson;
