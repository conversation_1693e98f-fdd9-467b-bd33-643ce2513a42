import { alpha, colors } from "@mui/material";
import clsx from "clsx";
import {
  CircleDot,
  ChartBarDecreasing,
  ChartColumnDecreasing,
  LayoutDashboard,
  ChartPie,
  Gauge,
  ChartLine,
  PaintBucket,
  Flame,
  MapPinned,
  ChartNoAxesCombined,
  Hash,
  Menu,
  Route,
  Rss,
  Radar,
  ChartArea
} from "lucide-react";
import { ChartType, LooseAutoComplete } from "../../../..";
import { ICON_SCHEMES } from "../utils";

const OPTIONS: Record<ChartType, typeof TableRows> = {
  Table: Menu,
  Bar: ChartColumnDecreasing,
  "Bar-H": ChartBarDecreasing,
  Area: ChartArea,
  Line: ChartLine,
  Pie: ChartPie,
  Donut: ChartPie,
  Count: Hash,
  Radar,
  Polar: ChartNoAxesCombined,
  Meter: Gauge,
  Gauge: Gauge,
  Fill: PaintBucket,
  MapPinned,
  Switch: CircleDot,
  Scatter: MapPinned,
  Path: Route,
  Nested: LayoutDashboard,
  Trigger: Rss,
  "Heat Map": Flame
};

type Props = {
  chartType: ChartType;
  iconScheme?: string;
  color: LooseAutoComplete<keyof typeof colors>;
  small?: boolean;
};

const ChartIcon = ({ chartType, iconScheme, color, small = false }: Props) => {
  const computedColor = colors[color] ? colors[color][500] : color;

  if (iconScheme) {
    const { Icon } = ICON_SCHEMES.find((icon) => icon.label === iconScheme) || {
      Icon: LayoutDashboard,
      lable: "Dashboard"
    };
    return <Icon className={small ? "!h-5 !w-6" : "!h-6 !w-6"} style={{ color: computedColor }} />;
  }

  if (OPTIONS[chartType]) {
    const Icon = OPTIONS[chartType];
    return <Icon className={small ? "!h-5 !w-6" : "!h-6 !w-6"} style={{ color: computedColor }} />;
  }
  return (
    <div className="p-1 bg-brandColor/20 rounded-md">
      <LayoutDashboard className="h-5 w-5  text-brandColor" />
    </div>
  );
};

export default ChartIcon;
