import { Checkbox } from "@components/shadcn/components/checkbox";
import { Label } from "@components/shadcn/components/label";
import { Chip, IconButton } from "@mui/material";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown, ChevronRight, ChevronUp, Filter } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import colors from "tailwindcss/colors";

type NestedOptions = { [key: string]: NestedOptions | boolean };

interface OptionSelectorProps {
  options: string[];
  onChange: (selectedOptions: string[]) => void;
  isLoading?: boolean;
  selected: string[];
}

const buildNestedOptions = (options: string[]): NestedOptions => {
  const result: NestedOptions = {};
  options.forEach((option) => {
    const parts = option.split(".");
    let current = result;

    parts.forEach((part, index) => {
      if (index === parts.length - 1) {
        current[part] = true;
      } else {
        if (!current[part] || typeof current[part] !== "object") {
          current[part] = {};
        }
        current = current[part] as NestedOptions;
      }
    });
  });
  return result;
};

const GenericOptionSelector: React.FC<OptionSelectorProps> = ({
  options,
  onChange,
  isLoading,
  selected
}) => {
  const nestedOptions = useMemo(() => buildNestedOptions(options), [options]);
  const [selectedOptions, setSelectedOptions] = useState<string[]>(selected);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    if (selectedOptions.length === 0) {
      setExpanded(true);
    }
  }, []);

  const handleOptionChange = (path: string[], checked: boolean) => {
    const updateOptions = (current: string[], pathString: string, isChecked: boolean): string[] => {
      const relevantOptions = options.filter((opt) => opt.startsWith(pathString));
      if (isChecked) {
        return [...new Set([...current, ...relevantOptions])];
      }
      return current.filter((opt) => !relevantOptions.includes(opt));
    };

    const pathString = path.join(".");
    const newOptions = updateOptions(selectedOptions, pathString, checked);
    setSelectedOptions(newOptions);
    onChange(newOptions);
  };

  const isFullySelected = (path: string[]): boolean => {
    const pathString = path.join(".");
    const relevantOptions = options.filter((opt) => opt.startsWith(pathString));
    return relevantOptions.every((opt) => selectedOptions.includes(opt));
  };

  const toggleCategory = (path: string) => {
    setExpandedCategories((prev) =>
      prev.includes(path) ? prev.filter((p) => p !== path) : [...prev, path]
    );
  };

  const renderOptions = (opts: NestedOptions, path: string[] = []) => {
    return Object.entries(opts).map(([key, value]) => {
      const currentPath = [...path, key];
      const isObject = typeof value === "object";
      const currentPathString = currentPath.join(".");
      const isExpanded = expandedCategories.includes(currentPathString);

      return (
        <motion.div
          key={currentPathString}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className={`${path.length > 0 ? "ml-4" : ""} py-1`}
        >
          <div className="flex items-center space-x-4 rounded-lg p-1.5 hover:bg-secondary transition-colors duration-200">
            {isObject && (
              <button
                onClick={() => toggleCategory(currentPathString)}
                className=" rounded-full focus:outline-none focus:ring-2 focus:ring-gray-300"
                aria-label={isExpanded ? "Collapse" : "Expand"}
              >
                {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </button>
            )}
            <Checkbox
              id={currentPathString}
              checked={isFullySelected(currentPath)}
              onCheckedChange={(checked: boolean) => handleOptionChange(currentPath, checked)}
              className={clsx(!isObject && "ml-5")}
            />
            <Label
              htmlFor={currentPathString}
              className={`font-medium cursor-pointer capitalize mt-2 ${
                isObject ? " text-gray-700 dark:text-gray-300" : ""
              }`}
            >
              {key}
            </Label>
          </div>
          <AnimatePresence>
            {isObject && isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="mt-1 border-l border-gray-300 dark:border-gray-700">
                  {renderOptions(value as NestedOptions, currentPath)}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      );
    });
  };

  const renderLoadingState = () => {
    return (
      <div className="space-y-4 animate-pulse">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center space-x-2 ml-2 mt-3">
            <div className="h-4 w-4 rounded-sm bg-gray-500" />
            <div className="h-4 w-32 animate-pulse rounded-sm bg-gray-500" />
          </div>
        ))}
      </div>
    );
  };

  const renderEmptyState = () => {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No options available</p>
      </div>
    );
  };

  return (
    <Card className="mt-4 px-2 pb-2 pt-3 max-h-[24rem] overflow-y-auto">
      <div className="flex gap-3 items-center ">
        <Filter
          className="rounded p-1"
          size={18}
          style={{
            backgroundColor: colors.sky[500],
            color: "#fff"
          }}
        />
        <h3 className="heading-6">Data Points</h3>
        <IconButton
          className="!p-1 !ml-auto"
          size="small"
          onClick={() => {
            setExpanded((prev) => !prev);
          }}
        >
          {expanded ? (
            <ChevronUp size={BUTTON_ICON_SIZE} />
          ) : (
            <ChevronDown size={BUTTON_ICON_SIZE} />
          )}
        </IconButton>
      </div>
      {expanded && (
        <motion.div
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ type: "spring", damping: 10, stiffness: 100 }}
          className=" flex-wrap gap-3 my-1 "
        >
          {isLoading
            ? renderLoadingState()
            : options.length === 0
              ? renderEmptyState()
              : renderOptions(nestedOptions)}
        </motion.div>
      )}
      {!expanded && (
        <motion.div
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ type: "spring", damping: 10, stiffness: 100 }}
          className=" flex-wrap gap-3 my-2 mt-3"
        >
          {!selectedOptions.length ? (
            <div className="text-center py-1">No data points selected</div>
          ) : (
            <div className="flex flex-row gap-2 gap-y-2 flex-wrap">
              {selectedOptions.map((item) => (
                <Chip
                  key={item}
                  label={item}
                  size="small"
                  className="!text-sm !rounded-md"
                  onDelete={() => {
                    const newOption = selectedOptions.filter((opt) => opt !== item);
                    onChange(newOption);
                    setSelectedOptions(newOption);
                  }}
                />
              ))}
            </div>
          )}
        </motion.div>
      )}
    </Card>
  );
};

export default GenericOptionSelector;
