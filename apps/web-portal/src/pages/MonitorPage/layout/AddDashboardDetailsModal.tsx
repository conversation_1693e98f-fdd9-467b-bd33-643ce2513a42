import { Modal } from "@mui/material";
import React, { useC<PERSON>back, useImperative<PERSON>andle, useState } from "react";
import { useForm } from "react-hook-form";
import { Cpu, Plus, X, Grid } from "lucide-react";
import { Card } from "@components/ui";
import Input from "../../../components/Input";
import Button from "../../../components/Button";
import { useParams } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { store, useAppDispatch } from "../../../store";
import { createUserDashboard, updateUserDashboard } from "../../../api/dashboardBuilder";
import { showErrorToast } from "../../../utils";
import { setPrimaryDashboardId } from "../../../features/dashboardBuilderSlice";
import useCustomNavigate from "@hooks/useCustomNavigate";
import HeadingIcon from "@components/HeadingIcon";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";

/**
 * @param {never} props
 * @param {any} ref
 * @returns {React.ReactElement}
 */
const AddDashboardDetailsModal = React.forwardRef((props, ref) => {
  const [createRuleModal, setCreateRuleModal] = useState(false);
  const { dashboardId } = useParams();
  const navigate = useCustomNavigate();
  const dispatch = useAppDispatch();
  const { data: permissions } = useUserGroupPermissions();

  const updateOrDeleteMutation = useMutation({
    mutationKey: [dashboardId, "userDashboard"],
    mutationFn: dashboardId ? updateUserDashboard : createUserDashboard,
    onSuccess: (res) => {
      // eslint-disable-next-line no-underscore-dangle
      const newDashboardId = res.usersDashboard?.[0]?.id;

      if (newDashboardId) {
        dispatch(setPrimaryDashboardId(newDashboardId));
      }

      setCreateRuleModal(false);
      navigate(-1);
    },
    onError: (error) => {
      showErrorToast(error?.message);
    }
  });

  const dashboardDetails = useForm({
    defaultValues: {
      title: "",
      description: ""
    }
  });

  const showModal = useCallback(({ title, description }) => {
    setCreateRuleModal(true);
    dashboardDetails.reset({
      title,
      description
    });
  }, []);

  useImperativeHandle(ref, () => ({ showModal }), []);

  const handleFormSubmit = (e) => {
    e.preventDefault();
    const { title, description } = dashboardDetails.getValues();

    const {
      dashboardBuilder: { dashboardGraphList, nextLayoutPosition, userDashboards }
    } = store.getState();
    /**
     * @typedef {Exclude<import("../../../..").SavedDashboards,'id'>}
     */
    const newDashboard = {
      dashboardList: dashboardGraphList,
      title,
      description,
      layout: nextLayoutPosition,
      primaryDashboard: userDashboards.length === 0
    };

    if (dashboardId) {
      updateOrDeleteMutation.mutate({ dashboardId, newDashboard });
    } else {
      const isTitlePresent = userDashboards.find((item) => item.title === title);
      if (isTitlePresent) {
        showErrorToast("Dashboard with same title exists! Choose a different title to proceed.");
        return;
      }
      updateOrDeleteMutation.mutate(newDashboard);
    }
  };

  return (
    <Modal
      open={createRuleModal}
      onClose={() => {
        setCreateRuleModal(false);
      }}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      }}
    >
      <Card className="!p-4 max-w-4xl">
        <HeadingIcon title="Dashboard Details" Icon={Grid} />
        <form className="w-full mt-4 mb-2 space-y-4" onSubmit={handleFormSubmit}>
          <div className="w-[500px] scrollbar-thin scrollbar-thumb-gray-500 !overflow-auto max-h-[70vh] p-2 overflow-y-auto space-y-4">
            <Input
              label="Title"
              startIcon={<Cpu size={16} />}
              required
              {...dashboardDetails.register("title")}
            />

            <div>
              <p className="input-label-text">Description *</p>
              <Input
                {...dashboardDetails.register("description")}
                required
                inputType="textarea"
                // multiline={10}
              />
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <Button
              startIcon={<X size={16} />}
              onClick={() => {
                setCreateRuleModal(false);
                dashboardDetails.reset({
                  title: "",
                  description: ""
                });
              }}
              type="button"
              color="red"
            >
              Cancel
            </Button>

            <Button
              startIcon={<Plus size={16} />}
              disabled={updateOrDeleteMutation.isPending}
              loading={updateOrDeleteMutation.isPending}
              loadingText={dashboardId ? "Save" : "Create"}
              type="submit"
              noAccess={permissions.dashboard !== "write"}
            >
              {dashboardId ? "Save" : "Create"}
            </Button>
          </div>
        </form>
      </Card>
    </Modal>
  );
});

export default AddDashboardDetailsModal;
