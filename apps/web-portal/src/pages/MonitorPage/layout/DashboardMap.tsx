/* eslint-disable no-use-before-define */
import { ThingWidget } from "@/index";
import DataNotFound from "@components/DataNotFound";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import useFetchPositionDataById from "@hooks/geo/usePositionDataById";
import MapPopover from "@src/pages/UserTypes/Tracking/Component/MapPopover";
import CustomMapTile from "@src/pages/UserTypes/Tracking/HomeSection/Components/CustomMapTile";
import CustomScrollManager from "@src/pages/UserTypes/Tracking/HomeSection/Components/CustomScrollManager";
import LeafletCacheLayer from "@src/pages/UserTypes/Tracking/HomeSection/Components/MapCacheLayer";
import SkeletonList from "@src/pages/UserTypes/Tracking/HomeSection/Components/SkeletonList";
import useShowGeofenceLabel from "@src/pages/UserTypes/Tracking/HomeSection/hooks/useShowGeofenceLabel";
import { circlePoint, smallCircle } from "@src/pages/UserTypes/Tracking/HomeSection/mapUtils";
import {
  convetUTCToLocal,
  metersToKilometers
} from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { parseObject } from "@src/test";
import L, { DivIcon, Icon, latLngBounds } from "leaflet";
import "leaflet.heat";
import "leaflet/dist/leaflet.css";
import { Flag } from "lucide-react";
import { ElementRef, memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { MapContainer, Marker, Polyline, Tooltip, useMap, ZoomControl } from "react-leaflet";
import { create } from "zustand";
import "../../UserTypes/Tracking/HomeSection/leafletStyle.css";
import { camelCaseToWords, colorAtI, formattedNumber } from "../utils";

const useShowLabelState = create<{
  hideLabel: boolean;
  toggleLabel: () => void;
}>((set, get) => ({
  hideLabel: localStorage.getItem("@showLabelInGeoWidget") === "true",
  toggleLabel: () => {
    set((state) => ({ hideLabel: !state.hideLabel }));
    localStorage.setItem("@showLabelInGeoWidget", `${!get().hideLabel}`);
  }
}));

const DashboardMap = ({
  className,
  positions,
  mapType,
  attributes,
  range
}: {
  className?: string;
  mapType: "Path" | "Position" | "Heat Map";
  positions: {
    thingName: string;
    timestamp: string;
    latitude: number;
    longitude: number;
    uniqueId: number;
  }[][];
  attributes: Record<string, number[]>;
  range: any;
}) => {
  const scrollEnabled = useShowGeofenceLabel((state) => state.scrollEnabled);
  const { toggleLabel } = useShowLabelState();

  const mapRef = useRef<ElementRef<typeof MapContainer>>(null);

  const pointExists = positions.flat().length > 0;

  if (!pointExists) {
    return <DataNotFound title="Position data not available" />;
  }

  return (
    <div className={className}>
      <MapContainer ref={mapRef} zoomControl={false} scrollWheelZoom={scrollEnabled}>
        <CustomScrollManager position="topright" />
        <CustomMapTile />
        <LeafletCacheLayer />
        <ZoomControl position="topright" />
        <MapView positionsArray={positions} type={mapType} attributes={attributes} range={range} />
      </MapContainer>
      <div className="absolute bottom-2 right-2 flex flex-col gap-2 z-[1000]">
        <ShadcnButton size="icon" onClick={toggleLabel} title="Add Start Point">
          <Flag className="h-4 w-4" />
        </ShadcnButton>
      </div>
    </div>
  );
};

const MapView = ({
  positionsArray,
  type,
  attributes,
  range
}: {
  positionsArray: {
    thingName: string;
    timestamp: string;
    latitude: number;
    longitude: number;
    uniqueId: number;
  }[][];
  type: "Path" | "Position" | "Heat Map" | "Scatter";
  attributes: Record<string, number[]>;
  range: any;
}) => {
  const map = useMap();

  const [selectedThing, setSelectedThing] = useState<string>();
  const polylineRefs = useRef<{ [key: number]: L.Polyline | null }>({});
  const [heatLayer, setHeatLayer] = useState<L.Heat.HeatLayer | null>(null);

  useEffect(() => {
    if (selectedThing) {
      const thingPositions = positionsArray.find((item) => item[0]?.thingName === selectedThing);
      if (thingPositions) {
        const bounds = latLngBounds(thingPositions.map((item) => [item.latitude, item.longitude]));
        if (bounds.isValid()) {
          map.fitBounds(bounds, { padding: [0, 0], maxZoom: 18 });
        }
      }
      return;
    }

    const bounds =
      positionsArray.length === 1
        ? latLngBounds(
            positionsArray
              .map((item) =>
                item.map((position) => [position.latitude, position.longitude] as [number, number])
              )
              .flat()
          )
        : latLngBounds(positionsArray.map((item) => [item[0].latitude, item[0].longitude]));
    if (bounds.isValid()) {
      map.fitBounds(bounds, { padding: [0, 0], maxZoom: 18 });
    }
  }, [positionsArray, selectedThing]);

  const segments = useMemo(() => {
    const _segments = [];

    if (type === "Position" || !positionsArray) {
      return [];
    }
    positionsArray.forEach((positions) => {
      for (let i = 0; i < positions.length - 1; i++) {
        const currentPoint = positions[i];
        const nextPoint = positions[i + 1];
        if (currentPoint && nextPoint) {
          _segments.push({
            positions: [
              [currentPoint.latitude, currentPoint.longitude],
              [nextPoint.latitude, nextPoint.longitude]
            ] as [number, number][],
            color: getColor(attributes, i, range),
            id: i
          });
        }
      }
    });

    return _segments;
  }, [positionsArray, range, type, attributes]);

  const onAdd = useCallback((name: string, index: number) => {
    if (polylineRefs.current[index]) {
      polylineRefs.current[index]!.bringToFront();
    }
    setSelectedThing(name);
  }, []);

  const onRemove = useCallback(() => {
    setSelectedThing("");
  }, []);

  useEffect(() => {
    if (!map) {
      return;
    }

    if (type !== "Heat Map") {
      if (heatLayer) {
        map.removeLayer(heatLayer);
        setHeatLayer(null);
      }

      return;
    }

    const heatData = positionsArray
      .map((item) => {
        return item.map((item) => [item.latitude, item.longitude, 1.5 as number] as const);
      })
      .flat();

    if (map && heatData.length > 0 && positionsArray.length > 0 && !heatLayer) {
      const heat = L.heatLayer(heatData, {
        radius: 20,
        blur: 15,
        maxZoom: 10,
        max: 10,
        minOpacity: 0.4,
        gradient: {
          0.4: "blue",
          0.6: "cyan",
          0.7: "lime",
          0.8: "yellow",
          1.0: "red"
        }
      }).addTo(map);
      setHeatLayer(heat);
    }
  }, [positionsArray, type]);

  if (type === "Path") {
    return (
      <>
        {positionsArray.map((item, i) => (
          <Polyline
            key={item[0]?.id}
            ref={(el) => (polylineRefs.current[i] = el)}
            positions={item.map((item) => [item.latitude, item.longitude])}
            color={colorAtI(i)}
            pathOptions={{
              lineCap: "round",
              weight: selectedThing ? (selectedThing === item[0]?.thingName ? 6 : 4) : 5,
              opacity: selectedThing ? (selectedThing === item[0]?.thingName ? 1 : 0.56) : 1
            }}
          />
        ))}

        {positionsArray.map((item, i) => (
          <MapMarkerWithDetails
            key={item[0]?.timestamp}
            type={type}
            item={item[0]}
            markerIcon={smallCircle}
            onAdd={onAdd}
            onRemove={onRemove}
            index={i}
          />
        ))}
      </>
    );
  }

  if (type === "Position" || type === "Scatter") {
    return (
      <>
        {positionsArray.map((item, i) => {
          return (
            <MapMarkerWithDetails
              index={i}
              key={item[0]?.timestamp}
              item={item[0]}
              markerIcon={circlePoint}
            />
          );
        })}
      </>
    );
  }

  return <></>;
};

const NotMemoMapMarkerWithDetails = ({
  item,
  markerIcon,
  onAdd,
  onRemove,
  index
}: {
  item: {
    latitude: number;
    longitude: number;
    timestamp: string;
    id?: number;
    thingName: string;
  };
  markerIcon: DivIcon | Icon<any>;
  onAdd?: (name: string) => void;
  onRemove?: () => void;
  index: number;
}) => {
  const [tooltipOpen, setTooltipOpen] = useState(false);
  const { hideLabel } = useShowLabelState();
  const { data: pointDetails, isLoading } = useFetchPositionDataById({
    id: String(item?.id),
    enabled: Boolean(item?.id) && tooltipOpen
  });

  const keyValuesToShow = useMemo(() => {
    if (!pointDetails) return [];
    const keyValues = [
      { key: "Speed", value: `${pointDetails.speed.toFixed(2)} km/h` },
      { key: "Address", value: pointDetails.address }
    ];

    Object.entries(pointDetails.attributes || {}).forEach(([key, value]) => {
      if (typeof value === "object") {
        parseObject(value).forEach((parsedItem) =>
          keyValues.push({
            key: camelCaseToWords(parsedItem.key),
            value: `${parsedItem}`
          })
        );
        return;
      }
      if (key === "totalDistance") {
        keyValues.push({ key: "Odometer", value: metersToKilometers(value) });
        return;
      }
      if (key === "motion" || key === "distance") {
        return;
      }
      keyValues.push({
        key: key.replace(/_/g, " "),
        value: typeof value === "number" ? formattedNumber(value) : value
      });
    });

    return keyValues;
  }, [pointDetails]);

  if (!item) return null;

  return (
    <Marker
      eventHandlers={{
        click: () => {
          setTooltipOpen((prev) => !prev);
        }
      }}
      key={item.timestamp}
      icon={markerIcon}
      position={[item.latitude, item.longitude]}
    >
      <MapPopover
        eventHandlers={{
          remove: () => {
            onRemove?.();
          },
          add: () => {
            onAdd?.(item.thingName, index);
          }
        }}
        title={convetUTCToLocal(item.timestamp)}
        offset={[0, 12]}
      >
        <div className="">
          {isLoading ? (
            <SkeletonList count={4} className="w-full" childrenClassName=" !h-5 !my-1" />
          ) : !pointDetails ? (
            <>
              <div className="flex items-center gap-2 text-xs">
                <p className=" min-w-[4.5rem] text-muted-foreground ">Thing</p>
                <span className="font-medium text-foreground">{item.thingName}</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <p className=" min-w-[4.5rem] text-muted-foreground ">Lat</p>
                <span className="font-medium text-foreground">{item.latitude}</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <p className=" min-w-[4.5rem] text-muted-foreground ">Lon</p>
                <span className="font-medium text-foreground">{item.longitude}</span>
              </div>
            </>
          ) : (
            <>
              {keyValuesToShow.map(({ key, value }) => (
                <div className="flex items-center gap-2 text-xs">
                  <p className=" min-w-[4.5rem] text-muted-foreground">{key}</p>
                  <span className="font-medium text-foreground">{value}</span>
                </div>
              ))}
            </>
          )}
        </div>
      </MapPopover>
      {!hideLabel && (
        <Tooltip
          permanent
          interactive
          className="!bg-transparent !border-none !shadow-none before:!border-none"
          eventHandlers={{}}
          direction="right"
        >
          <div className="flex items-center gap-2 font-semibold text-base">
            <p>{item.thingName}</p>
          </div>
        </Tooltip>
      )}
    </Marker>
  );
};

const MapMarkerWithDetails = memo(NotMemoMapMarkerWithDetails);

function getColor(
  attributes: Record<string, number[]>,
  index: number,
  range: ThingWidget["range"]
) {
  // Your condition to determine color goes here
  // For example, based on distance between points:

  for (let i = 0; i < range.length; i++) {
    const source = range[i]?.conditionKey!;

    const conditionValue = Number(range[i]?.conditionValue!);
    const attributesItem = attributes[source] || [];
    const value = attributesItem[index] || 0;
    const operation = range[i]?.conditionOperator!;

    if (operation === "==") {
      if (value === conditionValue) {
        return range[i]?.color!;
      }
    }
    if (operation === "!=") {
      if (value !== conditionValue) {
        return range[i]?.color!;
      }
    }
    if (operation === ">") {
      if (value > conditionValue) {
        return range[i]?.color!;
      }
    }
    if (operation === ">=") {
      if (value >= conditionValue) {
        return range[i]?.color!;
      }
    }
    if (operation === "<") {
      if (value < conditionValue) {
        return range[i]?.color!;
      }
    }
    if (operation === "<=") {
      if (value <= conditionValue) {
        return range[i]?.color!;
      }
    }
  }
  return "#60a5fa";
}

export default DashboardMap;
