import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import { alpha, IconButton } from "@mui/material";
import clsx from "clsx";
import { motion } from "framer-motion";
import { Download, Search } from "lucide-react";
import React, { RefAttributes } from "react";
import ComponentErrorBoundary from "../../../components/ComponentErrorBoundary";
import { removeDashboardGraph } from "../../../features/dashboardBuilderSlice";
import { setExpandedGraph, updateExpandedDuration } from "../../../features/expandedGraphSlice";
import { store, useAppDispatch, useAppSelector } from "../../../store";
// import ChartOverview from "../Components/ChartOverview";
import Options from "../Components/Options";
import useHandleDownloadTable from "../Components/useHandleDownloadTable";
import { colorAtI, isChartCircular } from "../utils";
import ChartExpandHandler from "./ChartExpandHandler";
import ChartIcon from "./ChartIcon";
import { Card } from "@components/ui";

const draggingPatterns = /react-draggable-dragging/;

interface Props {
  children: React.ReactNode;
  edit: boolean;
  index: number;
  className?: string;
  style?: { height: string; width: string };
  graphItem: import("../../../..").DashboardGraphItem;
}

const DashboardWrapperCard = (
  { children, graphItem, edit = false, index, ...rest }: Props,
  ref: RefAttributes<HTMLDivElement>
) => {
  const {
    size,
    chartType,
    label,
    id,
    expanded,
    stacked,
    filterBy,
    dataKey,
    // dataIndex,
    colorScheme,
    iconScheme,
    // filters,

    isOverview,
    nestedDashboardId
  } = graphItem;

  const isCircularChart = isChartCircular(chartType);
  const editingId = useAppSelector(
    ({ dashboardBuilder }) => dashboardBuilder.editingDashboardGraph
  );
  const editing = editingId === id;

  // subtracting the header height of the wrapper from the height that is needed to be given to chart
  const widgetHeaderHeight = document.getElementById(`widget-header-${index}`)?.offsetHeight;
  const newWidgetHeight = Number(rest?.style?.height.slice(0, -2)) - (widgetHeaderHeight || 0);

  const widgetHeaderElement = document.getElementById(id);
  const widgetWidth = widgetHeaderElement?.offsetWidth || 100;

  const dispatch = useAppDispatch();

  const removeGraph = () => {
    dispatch(removeDashboardGraph(id));
  };
  const openGraphInModal = () => {
    dispatch(updateExpandedDuration(store.getState().dashboardBuilder.graphDuration));
    dispatch(setExpandedGraph(graphItem));
  };

  const isDragging = draggingPatterns.test(rest.className || "");
  const color = colorAtI(0, colorScheme);
  const handleDownload = useHandleDownloadTable(id);

  return (
    <Card
      ref={ref}
      {...rest}
      className={clsx(
        rest.className,
        "flex relative z-10 rounded-md border-none",
        editing && " !outline-red-500 outline-2 outline",
        isDragging ? "!z-20" : "z-10"
      )}
      data-testid={`widget-container-${index}`}
    >
      <motion.div
        layoutId={id}
        id={id}
        animate={{ opacity: 1 }}
        className="absolute left-0 card right-0 top-0 bottom-0 rounded-lg overflow-hidden bg-white dark:bg-transparent border border-gray-300 shadow-sm dark:shadow-lg dark:shadow-black/50  dark:border-gray-600  pr-0 pb-0 pl-0 pt-0 flex flex-col "
        data-testid={`widget-inside-${index}`}
      >
        <div
          data-testid={`widget-header-${index}`}
          id={`widget-header-${index}`}
          className={clsx(
            "flex items-center justify-between p-1 pl-3 border-b border-card-border",
            !edit ? "" : isDragging ? "cursor-grabbing" : "cursor-grab"
          )}
        >
          <div className="flex items-center gap-3">
            <ChartIcon iconScheme={iconScheme} chartType={chartType} color={color} />

            <div className="py-2">
              <h3 className="font-medium heading-3 overflow-y-hidden capitalize">{label}</h3>
              {/* <ChartOverview dataIndex={dataIndex} dataKey={dataKey} filters={filters} /> */}
            </div>
          </div>

          <div className="flex items-center">
            {edit ? (
              <>
                <Options
                  removeGraph={removeGraph}
                  id={id}
                  chartType={chartType}
                  isOverview={isOverview}
                  size={size}
                  nestedDashboardId={nestedDashboardId}
                  isCircularChart={isCircularChart}
                  filterBy={filterBy}
                  dataKey={dataKey}
                  color={color}
                  expanded={expanded}
                  stacked={stacked}
                />
              </>
            ) : !["Table", "Count", "Meter", "Map", "Trigger"].includes(chartType) &&
              !isCircularChart ? (
              <IconButton
                onClick={openGraphInModal}
                className="!h-8 !w-8 flex !mr-1 !-mt-1 !p-0 dark:!text-gray-400 !text-gray-500"
              >
                <Search size={BUTTON_ICON_SIZE} />
              </IconButton>
            ) : null}
            {chartType === "Table" && !edit && (
              <div className="flex items-center gap-2 mr-2">
                <IconButton
                  onClick={handleDownload}
                  className="!p-1"
                  style={{ backgroundColor: alpha(color, 0.2) }}
                >
                  <Download size="1.2rem" color={color} />
                </IconButton>
              </div>
            )}
          </div>
        </div>

        <div
          style={{ width: "100%", height: `${newWidgetHeight}px` }}
          className={clsx(
            "relative flex flex-col flex-1",
            chartType === "Table" && "overflow-scroll !p-0"
          )}
          id="dashboard-table-container"
        >
          <div className="flex flex-col flex-1 h-full w-full">
            <ComponentErrorBoundary>{children}</ComponentErrorBoundary>
          </div>
        </div>
      </motion.div>
    </Card>
  );
};

export default React.forwardRef(DashboardWrapperCard);
