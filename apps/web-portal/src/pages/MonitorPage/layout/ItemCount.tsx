import { CardContent } from "@components/shadcn/components/card";
import useTimeSeriesGaugeData from "@hooks/timeseries/useTimeSeriesGaugeData";
import { useQuery } from "@tanstack/react-query";
import { separateCamelCase } from "@utils/utilities";
import clsx from "clsx";
import { useMemo, useState } from "react";
import { DashboardGraphItem } from "../../../..";
import { fetchGaugeChartData, fetchGraphPlotData } from "../../../api/dashboardBuilder";
import { updateDashboardGraph } from "../../../features/dashboardBuilderSlice";
import { useAppDispatch, useAppSelector } from "../../../store";
import ChartLoader from "../Charts/ChartLoader";
import { getGaugeFieldsFromFilters } from "../Components/ChartOverview";
import {
  colorAtI,
  formattedNumber,
  generateRandomString,
  isChartEnabled,
  SENSOR_KEY
} from "../utils";
import FilterEvent from "./FilterEvent";
import { Card } from "@components/ui";

const ItemCount = ({ graphData, edit }: { graphData: DashboardGraphItem; edit: boolean }) => {
  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);
  const {
    dataIndex,
    dataKey,
    id,
    filterBy,
    filters,
    colorScheme,
    assetFields,
    displayName,
    unit: _unit,
    labelMapping
  } = graphData;
  const graphLayout = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.nextLayoutPosition);
  const positionData = graphLayout[id];

  const allowZeros = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.allowZeros);

  const { productName, thingName, field, operations } = getGaugeFieldsFromFilters(filters, false);

  const { data: sensorGaugeData, isLoading: isSensorGaugeLoading } = useTimeSeriesGaugeData({
    productName,
    duration,
    key: dataKey,
    enabled:
      Boolean(productName && dataKey && dataIndex === SENSOR_KEY) || Boolean(assetFields?.length),
    filters,
    assetFields
  });

  const fetchCountForPayload = () => {
    const gaugeMapping = {
      productName,
      thingName,
      field,
      operations
    };
    return fetchGaugeChartData(gaugeMapping, dataIndex, duration, allowZeros);
  };

  const filterCount = useMemo(() => {
    return filters?.filter((item) => item.enabled)?.length || 0;
  }, [filters]);

  const dispatch = useAppDispatch();
  const { isLoading, data } = useQuery({
    queryKey: [id, dataIndex, dataKey, duration, filterCount, allowZeros, operations],
    enabled: isChartEnabled(dataIndex, dataKey, filters, true) && dataIndex !== SENSOR_KEY,
    queryFn: async () => {
      if (dataKey === "payload") {
        return fetchCountForPayload();
      }
      return fetchGraphPlotData(
        dataIndex,
        dataKey,
        duration,
        true,
        filters,
        undefined,
        false,
        allowZeros
      );
    }
  });

  const handleFilterChange = (filter: string) => {
    if (dataKey === "payload" || dataIndex === SENSOR_KEY) {
      dispatch(
        updateDashboardGraph({
          id,
          filters: filters.find((item) => item.field === "operations")
            ? filters.map((item) => {
                if (item.field === "operations") {
                  return {
                    ...item,
                    value: [filter]
                  };
                }
                return item;
              })
            : [
                ...filters,
                {
                  field: "operations",
                  value: [filter],
                  enabled: true,
                  operation: "is",
                  id: generateRandomString(5)
                }
              ]
        })
      );

      return;
    }
    dispatch(updateDashboardGraph({ id, filterBy: filter }));
  };

  const { datapoints, labels } =
    data && "labels" in data && Array.isArray(data.labels) && Array.isArray(data.datapoints)
      ? {
          labels: data.labels,
          datapoints: data.datapoints
        }
      : {
          labels: [] as string[],
          datapoints: [] as number[]
        };

  const count = useMemo(() => {
    try {
      const res = datapoints.reduce((total, curr) => curr + total, 0);
      return res;
    } catch (error) {
      console.log(error);
      return 0;
    }
  }, [datapoints]);

  const fieldKey = assetFields?.length ? Object.keys(sensorGaugeData || {})[0] : dataKey;

  const selectedText = assetFields?.length
    ? fieldKey
    : dataIndex === SENSOR_KEY
      ? dataKey
      : dataKey === "payload"
        ? operations
        : filterBy || "ALL";

  if (sensorGaugeData && Object.keys(sensorGaugeData || {}).length > 1) {
    return (
      <EnvironmentalDashboard
        sensorData={sensorGaugeData}
        labelMapping={labelMapping}
        positionData={positionData}
      />
    );
  }

  const unit = _unit || sensorGaugeData?.[fieldKey]?.unit;

  return (
    <div className="flex flex-col items-stretch h-full">
      <div className="flex gap-1 mr-2 justify-end items-center">
        {(dataIndex === SENSOR_KEY || assetFields?.length) && (
          <FilterEvent
            selected={operations || "avg"}
            labels={Object.keys(sensorGaugeData?.[fieldKey] || [])}
            edit={edit}
            onSave={handleFilterChange}
          />
        )}

        {dataIndex && dataKey !== "payload" && dataIndex !== SENSOR_KEY && (
          <FilterEvent
            selected={filterBy || "ALL"}
            labels={["ALL", ...labels]}
            edit={edit}
            onSave={handleFilterChange}
          />
        )}
        {dataIndex && dataKey === "payload" && dataIndex !== SENSOR_KEY && (
          <FilterEvent
            selected={operations || "Total"}
            labels={["min", "max", "avg", "sum"]}
            edit={edit}
            onSave={handleFilterChange}
          />
        )}
      </div>
      <div className={clsx("flex flex-col flex-1 items-center justify-center relative mb-2")}>
        <h3 className="font-semibold text-6xl " style={{ color: colorAtI(0, colorScheme) }}>
          {dataIndex === SENSOR_KEY || assetFields?.length
            ? formattedNumber(sensorGaugeData?.[fieldKey]?.[operations] || 0)
            : dataKey === "payload"
              ? // @ts-expect-error  this line data is Record<string, number>
                formattedNumber(data?.[field] || 0)
              : filterBy
                ? formattedNumber(datapoints?.[labels.indexOf(filterBy)] || count || 0)
                : formattedNumber(count || 0)}

          {Boolean(unit) && ` ${unit}`}
        </h3>

        <p className="text-md font-semibold capitalize" style={{ color: colorAtI(0, colorScheme) }}>
          {labelMapping?.[dataKey]?.label
            ? separateCamelCase(labelMapping?.[dataKey]?.label)
            : selectedText}
        </p>

        {typeof sensorGaugeData?.[dataKey] === "object" && (
          <CountStats
            min={sensorGaugeData?.[dataKey]?.min}
            max={sensorGaugeData?.[dataKey]?.max}
            avg={sensorGaugeData?.[dataKey]?.avg}
          />
        )}
      </div>

      {(isLoading || isSensorGaugeLoading) && dataKey && (
        <div className="absolute top-0 left-0 bottom-0 right-0 z-10 flex items-center justify-center">
          <ChartLoader color={colorAtI(0, colorScheme)} />
        </div>
      )}
    </div>
  );
};

const CountStats = ({
  min,
  max,
  avg
}: {
  min: number | string;
  max: number | string;
  avg: number | string;
}) => {
  return (
    <Card variant="third" className="flex justify-between !p-3  w-3/4 mt-4">
      <div className="text-center">
        <div className="text-sm font-medium text-muted-foreground">Min</div>
        <div className="text-lg font-semibold">{formattedNumber(min || 0)}</div>
      </div>
      <div className="text-center">
        <div className="text-sm font-medium text-muted-foreground">Avg</div>
        <div className="text-lg font-semibold">{formattedNumber(avg || 0)}</div>
      </div>
      <div className="text-center">
        <div className="text-sm font-medium text-muted-foreground">Max</div>
        <div className="text-lg font-semibold">{formattedNumber(max || 0)}</div>
      </div>
    </Card>
  );
};

export function EnvironmentalDashboard({
  sensorData,
  labelMapping,
  positionData
}: {
  labelMapping?: DashboardGraphItem["labelMapping"];
  positionData?: { w: number };
  sensorData: Record<
    string,
    {
      avg: number;
      min: number;
      max: number;
      latest: number;
      sum: number;
      unit?: string;
      displayName?: string;
      decimal?: number;
    }
  >;
}) {
  const firstKey = Object.keys(sensorData)[0];
  const [activeMetric, setActiveMetric] = useState(firstKey);

  const activeMetricData = sensorData[activeMetric];

  // Filter out the active metric from bottom cards
  const bottomMetrics = Object.entries(sensorData).filter(([key]) => key !== activeMetric);
  const unit = labelMapping?.[activeMetric]?.unit || sensorData[activeMetric].unit;

  return (
    <div className="flex flex-col h-full justify-between px-2 dashboard-item-content">
      <div className=" border-gray-500 p-2">
        <div className="text-lg font-medium">
          {activeMetricData.displayName ||
            separateCamelCase(labelMapping?.[activeMetric]?.label || activeMetric)}
        </div>
        <div className="flex items-end gap-4 justify-between">
          <div className="">
            <div className="flex items-baseline">
              <span className="text-3xl font-bold whitespace-nowrap">
                {formattedNumber(activeMetricData.latest || 0, activeMetricData.decimal)}{" "}
                {unit && <span className="text-muted-foreground text-2xl">{unit}</span>}
              </span>
            </div>
          </div>

          {positionData && positionData?.w >= 2 && (
            <div className="grid grid-cols-3 gap-3 text-center">
              <Card variant="third" className=" !px-3 !py-2 ">
                <div className="text-sm text-muted-foreground">Min</div>
                <div className="text-md font-semibold whitespace-nowrap">
                  {formattedNumber(activeMetricData.min || 0)}
                </div>
              </Card>
              <Card variant="third" className=" !px-3 !py-2 ">
                <div className="text-sm text-muted-foreground">Max</div>
                <div className="text-md font-semibold whitespace-nowrap ">
                  {formattedNumber(activeMetricData.max || 0)}
                </div>
              </Card>
              <Card variant="third" className=" !px-3 !py-2 ">
                <div className="text-sm text-muted-foreground">Avg</div>
                <div className="text-md font-semibold whitespace-nowrap">
                  {formattedNumber(activeMetricData.avg || 0)}
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
      {positionData && positionData?.w >= 2 && bottomMetrics.length > 0 && (
        <div className={`  border-t  grid grid-cols-${bottomMetrics.length}  divide-x `}>
          {bottomMetrics.map(([key, metric]) => (
            <div
              key={key}
              className="cursor-pointer  transition-colors  px-1 hover:bg-secondary"
              onClick={() => setActiveMetric(key as any)}
            >
              <CardContent className="p-2 py-3">
                <div>
                  <div className="text-sm text-muted-foreground truncate">
                    {metric.displayName || separateCamelCase(key)}
                  </div>
                  <div className="flex items-baseline">
                    <span className="text-lg font-bold whitespace-nowrap">
                      {" "}
                      {formattedNumber(metric.latest || 0)}
                      {metric.unit && ` ${metric.unit}`}
                    </span>
                    {/* <span className="ml-1 text-sm text-muted-foreground">{metric.unit}</span> */}
                  </div>
                </div>
              </CardContent>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
export default ItemCount;
