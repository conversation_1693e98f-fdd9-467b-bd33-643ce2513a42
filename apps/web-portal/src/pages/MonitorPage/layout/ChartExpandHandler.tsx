import { MenubarItem } from "@components/shadcn/components/menubar";
import { updateDashboardGraph } from "../../../features/dashboardBuilderSlice";
import { updateExpandedGraph } from "../../../features/expandedGraphSlice";
import { useAppDispatch } from "../../../store";
import { Tabs, TabsList, TabsTrigger } from "@components/shadcn/components/tabs";
import { ChartBarBig, ChartColumnStacked } from "lucide-react";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";

const ChartExpandHandler = ({
  expanded,
  stacked,
  id,
  modalGraph = false,
  menuOption = false
}: {
  expanded: boolean | undefined;
  stacked: boolean | undefined;
  id: string;
  modalGraph?: boolean;
  menuOption?: boolean;
}) => {
  const dispatch = useAppDispatch();

  const toggleExpanded = () => {
    const payload = {
      id,
      expanded: !expanded,
      stacked: !stacked
    };
    if (modalGraph) {
      dispatch(updateExpandedGraph(payload));
    } else {
      dispatch(updateDashboardGraph(payload));
    }
  };
  const toggleStacked = () => {
    const payload = {
      id,
      stacked: !stacked,
      expanded: !expanded
    };
    if (modalGraph) {
      dispatch(updateExpandedGraph(payload));
    } else {
      dispatch(updateDashboardGraph(payload));
    }
  };

  // if (typeof expanded !== "boolean" && typeof stacked !== "boolean") {
  //   return null;
  // }

  if (menuOption) {
    return (
      <MenubarItem onClick={expanded ? toggleExpanded : toggleStacked}>
        {expanded ? (
          <ChartBarBig size={BUTTON_ICON_SIZE} className="mr-2" />
        ) : (
          <ChartColumnStacked size={BUTTON_ICON_SIZE} className="mr-2" />
        )}

        {expanded ? "Expanded" : "Stacked"}
      </MenubarItem>
    );
  }

  return (
    <Tabs className="dashboard-item-content" defaultValue={expanded ? "Expanded" : "Stacked"}>
      <TabsList className="grid  grid-cols-2">
        <TabsTrigger value="Expanded" onClick={toggleExpanded}>
          Expanded
        </TabsTrigger>
        <TabsTrigger value="Stacked" onClick={toggleStacked}>
          Stacked
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default ChartExpandHandler;
