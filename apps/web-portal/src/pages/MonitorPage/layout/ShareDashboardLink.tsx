import ActionButton from "@components/ActionButton";
import Button from "@components/Button";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import { DateTimePicker } from "@components/shadcn/components/date-time-picker";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@components/shadcn/components/dialog";
import SideSheet from "@components/SideSheet";
import useCreatePublicLinkMutation from "@hooks/publicLinks/useCreatePublicLinkMutation";
import useDeletePublicLinkMutation from "@hooks/publicLinks/useDeletePublicLinkMutation";
import useSharedLinksList from "@hooks/publicLinks/useSharedLinksList";
import SkeletonList from "@src/pages/UserTypes/Tracking/HomeSection/Components/SkeletonList";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useAppSelector } from "@src/store";
import { showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import copy from "copy-to-clipboard";
import { Copy, CopyPlus, Link, Plus, Share2, X } from "lucide-react";
import {
  ElementRef,
  ForwardedRef,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState
} from "react";

type Ref = {
  openModal: () => void;
  closeModal: () => void;
};

type Props = {
  type: "site" | "dashboard";
  id: number;
};

const today = new Date();

const getFullLinkFromId = (linkId: string, tenantName: string) => {
  return `${window.location.origin}/shared/${tenantName}/${linkId}`;
};

const ShareDashboardLink = (props: Props, ref: ForwardedRef<Ref>) => {
  const [open, setOpen] = useState(false);
  const addLinkRef = useRef<ElementRef<typeof AddSharedLinkDialogWithRef>>(null);
  const { data: sharedLinks, isLoading } = useSharedLinksList({
    siteId: props.type === "site" ? props.id : undefined,
    dashboardId: props.type === "dashboard" ? props.id : undefined,
    enabled: open
  });
  const tenantName = useAppSelector((state) => state.user?.tenant?.name || "");
  const [linkToDelete, setLinkToDelete] = useState<{
    id: number;
    name: string;
  }>();

  const deleteLinkMutation = useDeletePublicLinkMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["shared-links"] });
      setLinkToDelete(undefined);
    }
  });

  const openModal = useCallback(() => {
    setOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setOpen(false);
  }, []);

  useImperativeHandle(ref, () => ({ openModal, closeModal }));

  // Footer content with action buttons
  const footerContent = (
    <>
      <Button onClick={closeModal} small color="gray" startIcon={<X size={BUTTON_ICON_SIZE} />}>
        Close
      </Button>
      <Button
        small
        startIcon={<Plus size={BUTTON_ICON_SIZE} />}
        onClick={() => {
          closeModal();
          addLinkRef.current?.openModal();
        }}
        type="submit"
      >
        Add Link
      </Button>
    </>
  );

  return (
    <>
      <SideSheet
        open={open}
        onClose={closeModal}
        title="Shared Links"
        side="right"
        size="lg"
        footer={footerContent}
        showBackdrop={false}
      >
        <div className="space-y-4">
          {isLoading ? (
            <SkeletonList count={4} />
          ) : !sharedLinks?.sharedLinks.length ? (
            <DataNotFound title="No Shared Links Found" />
          ) : (
            <div className="space-y-3">
              {sharedLinks?.sharedLinks.map((link) => (
                <div key={link.id} className="flex gap-2 items-center card p-4 justify-between">
                  <div className="flex-1">
                    <p className="heading-3 font-medium">{link.name || "N/A"}</p>
                    <p className="text-muted-foreground text-sm">
                      Expiration: {convetUTCToLocal(link.expirationTime)}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <ActionButton
                      Icon={Copy}
                      onClick={() => {
                        showSuccessToast("Link copied successfully");
                        copy(getFullLinkFromId(link.linkId, tenantName));
                      }}
                      visibleBg
                    />

                    <ActionButton
                      type="delete"
                      onClick={() => {
                        setLinkToDelete({
                          id: link.id,
                          name: link.name || ""
                        });
                      }}
                      visibleBg
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </SideSheet>
      <AddSharedLinkDialogWithRef
        onSuccess={openModal}
        ref={addLinkRef}
        type={props.type}
        id={props.id}
      />
      <ConfirmPrompt
        show={Boolean(linkToDelete)}
        title={`Delete ${linkToDelete?.name} Link`}
        item={linkToDelete?.name}
        onCancel={() => {
          setLinkToDelete(undefined);
        }}
        onConfirm={() => {
          if (!linkToDelete) return;
          deleteLinkMutation.mutate({ linkId: linkToDelete?.id });
        }}
      />
    </>
  );
};

const AddSharedLinkDialog = (
  props: {
    type: "site" | "dashboard";
    id: number;
    onSuccess: () => void;
  },
  ref: ForwardedRef<Ref>
) => {
  const [generatedId, setGeneratedId] = useState("");
  const tenantName = useAppSelector((state) => state.user?.tenant?.name || "");
  const [open, setOpen] = useState(false);
  const [expirationTime, setExpirationTime] = useState<Date>(new Date());
  const [linkName, setLinkName] = useState("");

  const closeModal = useCallback(() => {
    setOpen(false);
    setLinkName("");
    setExpirationTime(new Date());
  }, []);

  const openModal = useCallback(() => {
    setGeneratedId("");
    setOpen(true);
  }, []);

  useImperativeHandle(ref, () => ({ openModal, closeModal }));

  const createLinkMutation = useCreatePublicLinkMutation({
    onSuccess: (data) => {
      setGeneratedId(getFullLinkFromId(data.linkId, tenantName));
      queryClient.invalidateQueries({ queryKey: ["shared-links"] });
    }
  });

  const copyLink = () => {
    copy(generatedId);
    showSuccessToast("Link copied successfully");
    closeModal();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Share2 className="text-yellow-600 !h-5 !w-5 mr-2" />
            Share {props.type} Link
          </DialogTitle>
          <DialogDescription>
            Create a public readonly link to share your {props.type} with others.
          </DialogDescription>

          <div>
            <label className="text-sm font-medium text-gray-500">Link Name</label>
            <Input
              value={linkName}
              onChange={(e) => setLinkName(e.target.value)}
              required
              placeholder="link name"
            />
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Expiration Time</label>
            <DateTimePicker
              value={expirationTime}
              onChange={(newValue) => {
                if (!newValue) return;
                setExpirationTime(newValue);
              }}
              disabled={{
                before: today
              }}
            />
          </div>
        </DialogHeader>

        <DialogFooter>
          {generatedId ? (
            <div className="flex gap-3 items-center">
              <code className="truncate whitespace-nowrap max-w-[20rem] bg-secondary items-center flex shadow-sm py-2.5 text-sm px-2 rounded-md">
                {generatedId}
              </code>
              <Button onClick={copyLink} startIcon={<CopyPlus size={BUTTON_ICON_SIZE} />}>
                Copy Link
              </Button>
            </div>
          ) : (
            <Button
              loading={createLinkMutation.isPending}
              onClick={() => {
                createLinkMutation.mutate(
                  props.type === "site"
                    ? {
                        type: "site",
                        siteId: props.id,
                        name: linkName,
                        expirationTime: expirationTime.toISOString()
                      }
                    : {
                        type: "dashboard",
                        dashboardId: props.id,
                        name: linkName,
                        expirationTime: expirationTime.toISOString()
                      }
                );
              }}
              startIcon={<Link size={BUTTON_ICON_SIZE} />}
            >
              Generate Link
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const AddSharedLinkDialogWithRef = forwardRef(AddSharedLinkDialog);

export default forwardRef(ShareDashboardLink);
