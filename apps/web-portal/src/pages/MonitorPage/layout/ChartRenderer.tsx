import useChartData from "@hooks/classic/useChartData";
import { getBackgroundColor, parseRawElasticCharData } from "@hooks/timeseries/parseRawChartData";
import useTimeSeriesData from "@hooks/timeseries/useTimeSeriesData";
import { DarkModeContext } from "@hooks/useDarkMode";
import { darken, lighten } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { PRODUCT_VAR } from "@utils/featureLabels";
import { ChartData } from "chart.js";
import clsx from "clsx";
import isEqual from "lodash.isequal";
import React, { useCallback, useContext, useMemo } from "react";
import { DashboardGraphItem } from "../../../..";
import { fetchGraphPlotData } from "../../../api/dashboardBuilder";
import { updateDashboardGraph, updateGraphDuration } from "../../../features/dashboardBuilderSlice";
import { useAppDispatch, useAppSelector } from "../../../store";
import CircularChart2 from "../Charts/CircularChart2";

import ChartLoader from "../Charts/ChartLoader";
import SummaryCardInfo from "../Charts/SummaryCardInfo";
import SummaryCharts from "../Charts/SummaryCharts";
import { getGaugeFieldsFromFilters } from "../Components/ChartOverview";
import {
  colorAtI,
  isChartCircular,
  isChartEnabled,
  isConnectionStatusOrShadow,
  SENSOR_KEY
} from "../utils";
import { updateExpandedGraph } from "@src/features/expandedGraphSlice";
import CommonChart from "../Charts/CommonChart";

const defaultEmptyData = {
  datapoints: [] as number[],
  labels: [] as string[],
  count: 0,
  elaboratedDataKeys: [] as string[],
  elaboratedDataPoint: [] as Record<string, number>[],
  aggregations: {}
};

type Props = { graphData: DashboardGraphItem; printerMode?: boolean; modal?: boolean };

const getLabel = ({
  mapping,
  suffix,
  prefix,
  key
}: {
  mapping: DashboardGraphItem["labelMapping"];
  suffix?: string;
  prefix?: string;
  key: string;
}) => {
  let label = mapping?.[key]?.label || key;
  if (prefix) {
    label = `${prefix}_${label}`;
  }
  if (suffix) {
    label = `${label}_${suffix}`;
  }

  return label;
};

const ChartRenderer = ({ graphData, printerMode, modal }: Props) => {
  const allowZeros = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.allowZeros);
  const graphLayout = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.nextLayoutPosition);
  const chartLayoutData = graphLayout[graphData.id];

  const [darkMode, _] = useContext(DarkModeContext);

  const {
    chartType,
    dataIndex,
    dataKey,
    size,
    aggregation,
    id,
    filterBy,
    filters,
    stacked,
    expanded,
    hidden,
    aggregationType,
    colorScheme,
    widget,
    comparer,
    assetFields,
    anomaly,
    labelMapping,
    // only exists for widget that has been zoomed in
    // @ts-expect-error - TS doesn't know this exists
    colorMap
  } = graphData;

  const filterCount = useMemo(() => {
    return filters?.filter((item) => item.enabled)?.length || 0;
  }, [filters]);

  // Undefined color is used to get the default color from the color scheme
  const color = !widget ? undefined : graphData.color;

  // const color = !modal
  // ? undefined
  // : colors[graphData.color]
  // ? colors[graphData.color][500]
  // : graphData.color;

  const dispatch = useAppDispatch();

  const isCircularChart = isChartCircular(chartType);
  const { isLoading, data: cachedData } = useChartData(graphData);
  // for sensor data
  const { productName } = getGaugeFieldsFromFilters(filters, false);
  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);

  const expandedGraphDuration = useAppSelector(({ expandedGraph }) => expandedGraph.duration.value);

  const thingNameFilterForWidget = useMemo(() => {
    if (!widget) return undefined;
    return filters.find((item) => item.field === "thingName")?.value?.[0];
  }, [widget, filters]);

  const { data: sensorData, isLoading: isSensorLoading } = useTimeSeriesData({
    thingName: thingNameFilterForWidget,
    productName: widget ? undefined : productName,
    key: dataKey,
    enabled: (Boolean(assetFields?.length) || Boolean(dataKey)) && dataIndex === SENSOR_KEY,
    duration: modal ? expandedGraphDuration : duration,
    filters: widget ? [] : filters,
    circular: isCircularChart,
    assetFieldIds: assetFields,
    confidenceMap: chartType === "Range",
    anomalies: anomaly
  });

  const sensorChartData = useMemo(() => {
    const res: ChartData["datasets"] = [];
    if (!dataIndex) {
      return res;
    }
    if (!sensorData?.datapointsMap) return res;

    const maxValue = Math.max(...Object.values(sensorData.datapointsMap).flat());

    const items = sensorData.confidenceMap
      ? Object.entries(sensorData.confidenceMap)
          .map(([key, values], index) => {
            const fadedColor = darkMode
              ? darken(colorAtI(index, colorScheme), 0.6)
              : lighten(colorAtI(index, colorScheme), 0.6);

            return [
              {
                label: getLabel({ mapping: labelMapping, key, suffix: "avg" }),
                // @ts-expect-error - i need key
                key: key,
                data: values.avg,
                borderColor: colorAtI(index, colorScheme),
                backgroundColor: colorAtI(index, colorScheme),
                fill: false,
                borderWidth: 2.5,
                order: -40
              },
              {
                label: getLabel({ mapping: labelMapping, key, suffix: "max" }),
                data: values.max,
                // @ts-expect-error - i need key
                key: key,
                borderColor: fadedColor,
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.4,

                fill: { target: "-1", above: fadedColor }
              },
              {
                label: getLabel({ mapping: labelMapping, key, suffix: "min" }),
                // @ts-expect-error - i need key
                key: key,
                tension: 0.4,
                data: values.min,
                borderColor: fadedColor,
                borderWidth: 2,
                pointRadius: 0,

                fill: { target: "-1", below: fadedColor }
              }
            ] satisfies ChartData["datasets"];
          })
          .flat()
      : Object.entries(sensorData.datapointsMap)
          .sort((a, b) => -Math.max(...b[1].flat()) + Math.max(...a[1].flat()))
          .map(([key, values], index) => {
            const color = colorMap?.[key] || colorAtI(index, colorScheme);
            const lineChartExtras: Partial<ChartData["datasets"][number]> =
              chartType === "Line"
                ? {
                    borderWidth: 2,
                    tension: 0.3,
                    pointRadius: 3,
                    pointBackgroundColor: color,
                    pointBorderColor: darkMode ? "hsl(216,25,12)" : "#fff",
                    pointBorderWidth: 2,
                    pointHoverRadius: 4
                  }
                : {};

            return {
              fill: chartType === "Area" ? "stack" : false,
              label: getLabel({ mapping: labelMapping, key }),
              data: values,
              key: key,
              type: chartType === "Scatter" ? "scatter" : undefined,
              borderColor: color,
              // borderWidth: 2,
              backgroundColor: ({ chart, dataIndex }) => {
                const { ctx, height } = chart;

                const chartHeight = values[dataIndex]
                  ? (height / maxValue) * values[dataIndex]
                  : height;
                const startHeight = 0;

                return getBackgroundColor(ctx, chartHeight, color, chartType, startHeight);
              },
              hidden: hidden?.[key],
              ...lineChartExtras
            } satisfies ChartData["datasets"][number];
          });

    return items;
  }, [dataIndex, sensorData, chartType, colorScheme, color, hidden, darkMode]);
  const anomolies = useMemo(() => {
    if (!sensorData) {
      return undefined;
    }

    if (sensorData.anomaly) {
      const anomolyDataSets: ChartData["datasets"][number] = {
        type: "bar",
        label: "Anomalies",
        data: sensorData.anomaly.map((item) => item.value),
        xAxisID: "xAnomaly",
        backgroundColor: "rgba(220, 28, 38, 0.5)",
        borderColor: "rgba(220, 38, 38, 1)",
        borderWidth: 2
      };

      return anomolyDataSets;
    }

    return undefined;
  }, [sensorData, chartType]);

  const { data: sensorDataComparerRaw } = useTimeSeriesData({
    productName,
    duration: comparer,
    key: dataKey,
    enabled: Boolean(dataKey) && dataIndex === SENSOR_KEY && Boolean(comparer),
    filters
  });

  // Sensor logic end

  const { data: elasticComparatorData } = useQuery({
    queryKey: [
      `graph-plot-comparator-${comparer}`,
      dataIndex,
      dataKey,
      filterCount,
      comparer,
      allowZeros
    ],
    enabled:
      dataIndex === SENSOR_KEY
        ? false
        : Boolean(comparer) && isChartEnabled(dataIndex, dataKey, filters || []),
    queryFn: () =>
      fetchGraphPlotData(
        dataIndex,
        dataKey,
        comparer || "",
        isCircularChart || isConnectionStatusOrShadow(dataIndex),
        filters,
        { stacked, expanded, id, isBar: ["Bar", "Bar-H"].includes(chartType) },
        modal,
        allowZeros
      )
  });

  const comparerChartData = useMemo(() => {
    if (sensorDataComparerRaw) {
      const labels = sensorDataComparerRaw.labels;
      const items: ChartData["datasets"] = Object.entries(sensorDataComparerRaw.datapointsMap).map(
        ([key, values], index) => {
          const label = `compared-${key}`;
          return {
            fill: true,
            label,
            data: values,
            borderWidth: 2,
            borderColor: colorAtI(4 + index),
            backgroundColor: ({ chart: { ctx, height } }) =>
              getBackgroundColor(ctx, height, colorAtI(4 + index), "Area"),
            xAxisID: "xComparer",
            hidden: hidden?.[label]
          };
        }
      );
      return {
        datasets: items,
        labels,
        aggregations: Object.keys(sensorDataComparerRaw?.aggregations).reduce(
          (prev, key) => {
            const comparerKey = `compared-${key}`;
            prev[comparerKey] = sensorDataComparerRaw.aggregations[key];
            return prev;
          },
          {} as Record<string, any>
        )
      };
    }

    if (elasticComparatorData) {
      let datasets: ChartData["datasets"] = [];
      const labels = elasticComparatorData.labels;

      if (elasticComparatorData.datapoints) {
        datasets = [
          {
            label: "comparator",
            data: elasticComparatorData.datapoints.map((item, index) => ({
              x: labels[index],
              y: item
            })),
            type: "line",
            borderWidth: 2,
            borderColor: colorAtI(4),
            fill: true,
            backgroundColor: ({ chart: { ctx, height } }) =>
              getBackgroundColor(ctx, height, colorAtI(4), "Area"),
            order: 99,
            xAxisID: "xComparer"
          }
        ];
      } else if (
        elasticComparatorData.elaboratedDataKeys &&
        elasticComparatorData.elaboratedDataPoint
      ) {
        console.log(elasticComparatorData.elaboratedDataKeys);
        console.log(elasticComparatorData.elaboratedDataPoint);
      }

      return { datasets, labels, aggregations: elasticComparatorData.aggregations || {} };
    }
    return { labels: [], datasets: [], aggregations: {} };
  }, [elasticComparatorData, sensorDataComparerRaw, hidden]);

  const {
    datapoints,
    labels: _labels,
    elaboratedDataKeys,
    elaboratedDataPoint,
    aggregations: _elasticAgg
  } = cachedData?.labels ? cachedData : defaultEmptyData;

  const aggregations = sensorData?.aggregations || _elasticAgg;
  const labels = sensorData?.labels || _labels;

  const chartData: ChartData["datasets"] = useMemo(() => {
    return parseRawElasticCharData({
      expanded,
      stacked,
      filters,
      elaboratedDataPoint,
      elaboratedDataKeys,
      datapoints,
      dataKey,
      aggregation,
      color,
      colorScheme,
      chartType,
      hidden
    });
  }, [labels, expanded || stacked, colorScheme, elaboratedDataPoint, elaboratedDataKeys, hidden]);

  const title = `${dataIndex}: ${dataKey}`;

  const chartLabelAndFilter = useMemo(
    () => (
      <div
        className={clsx(
          size === "1X" && "ml-auto",
          size !== "1X" && "absolute flex w-full gap-3 items-center",
          isCircularChart ? "right-1 top-0 justify-end" : "bottom-1 justify-center"
        )}
      />
    ),
    [elaboratedDataKeys, title, labels?.length, size]
  );

  const emptyCard = useMemo(() => {
    if (isLoading || isSensorLoading) {
      if (isChartEnabled(dataIndex, dataKey, filters) || Boolean(assetFields?.length)) {
        return (
          <div
            className={clsx(
              "absolute top-0 left-0 bottom-0 right-0 z-10 flex items-center justify-center",
              modal && "top-16"
            )}
          >
            <ChartLoader color={color || colorAtI(0, colorScheme)} />
          </div>
        );
      }
      return null;
    }
    if (labels.length) {
      return null;
    }
    return (
      <div
        className={clsx(
          "absolute left-0 right-0 z-10  flex items-center justify-center",
          size === "1X" ? "top-10 bottom-10" : "top-0 bottom-9",
          modal && "top-16"
        )}
      >
        <div className="card py-4 px-2 font-medium">
          {!isChartEnabled(dataIndex, dataKey, filters)
            ? dataKey === "payload"
              ? `Select ${PRODUCT_VAR} Name to show payload`
              : "Select graph configuration in sidebar."
            : "No Data Points present in interval"}
        </div>
      </div>
    );
  }, [labels?.length, isLoading, size, isSensorLoading]);

  const datasets = dataIndex === SENSOR_KEY ? sensorChartData : chartData;

  const handleDurationChange = (dateString = "") => {
    const res = { title: "custom", value: dateString };
    dispatch(updateGraphDuration(res));
  };

  const updateWidgetData = useCallback((data: Partial<DashboardGraphItem>) => {
    if (modal) {
      dispatch(updateExpandedGraph(data));
      return;
    }

    dispatch(updateDashboardGraph({ id, ...data }));
  }, []);

  if (size === "1X" && !isCircularChart) {
    return (
      <>
        <SummaryCharts
          type={chartType}
          // stacked={Boolean(filterBy) || stacked}
          stacked={chartType === "Bar" || chartType === "Bar-H"}
          // stacked={Boolean(filterBy) || stacked || true}
          className="h-full"
          headingClassName="flex"
          data={{ datasets, labels }}
          timeSeries={!isConnectionStatusOrShadow(dataIndex)}
          handleDurationChange={handleDurationChange}
        >
          <SummaryCardInfo
            datasets={datasets}
            labelMapping={labelMapping}
            aggregation={aggregations || aggregation}
            hidden={hidden}
            aggregationType={aggregationType}
          />
          {chartLabelAndFilter}
        </SummaryCharts>
        {emptyCard}
      </>
    );
  }

  return (
    <div className="w-full h-full py-2">
      {["Bar", "Bar-H", "Area", "Line", "Range", "Scatter", "Step"].includes(chartType) && (
        <CommonChart
          chartLayoutData={chartLayoutData}
          enableZoom={true}
          data={{ datasets, labels }}
          indexAxis={chartType === "Bar-H" ? "y" : "x"}
          stacked={stacked}
          chartType={chartType}
          type={chartType.includes("Bar") ? "Bar" : "Line"}
          timeSeries={!isConnectionStatusOrShadow(dataIndex)}
          modal={modal}
          aggregation={aggregations || aggregation}
          hidden={hidden}
          aggregationType={aggregationType}
          updateWidget={updateWidgetData}
          size={size}
          compareChartData={comparer ? comparerChartData : undefined}
          hideLabel={printerMode}
          labelMapping={labelMapping}
          anomolies={anomaly ? anomolies : undefined}
        />
      )}

      {isCircularChart && (
        <CircularChart2
          chartLayoutData={chartLayoutData}
          type={chartType as any}
          datasets={dataIndex === SENSOR_KEY ? sensorData?.datapoints || [] : datapoints}
          labels={labels}
          colorScheme={colorScheme}
          filterBy={filterBy}
          size={size}
          fullLabel={printerMode}
        >
          {chartLabelAndFilter}
        </CircularChart2>
      )}

      {emptyCard}
    </div>
  );
};

// export default ChartRenderer;

const matchProps = (prev, curr) => {
  return prev.edit === curr.edit && isEqual(curr.graphData, prev.graphData);
};

export default React.memo(ChartRenderer, matchProps);
