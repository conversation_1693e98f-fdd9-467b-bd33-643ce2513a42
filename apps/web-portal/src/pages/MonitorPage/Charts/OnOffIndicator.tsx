import clsx from "clsx";
import { Circle } from "lucide-react";
import ControlImg from "../../../../assets/icons/control-button.png";
import "./loader.css";

/**
 * @typedef {object} Props
 * @property {string} [className]
 * @property {boolean} value
 */

const OPTIONS = [
  { title: "Running", value: true },
  { title: "Stopped", value: false }
];

/**
 * @param {Props} param0
 * @returns {React.ReactElement}
 */
const OnOffIndicator = ({ className, value }) => {
  return (
    <div className={clsx(className, "p-2 pt-5 h-full flex flex-col justify-between")}>
      <img src={ControlImg} className="h-18 w-18 self-center" alt="control" />
      <div className=" grid grid-cols-2 gap-2.5 p-2.5  bg-secondary rounded-md">
        {OPTIONS.map((item) => (
          <div key={item.title} className="card !p-2 flex items-center justify-between">
            <p className="text-sm">{item.title}</p>
            {item.value === Boolean(value) ? (
              <div
                className={clsx(
                  item.title === "Running" ? "dot-pulse-success" : "dot-pulse-danger"
                )}
              />
            ) : (
              <Circle color={item.title === "Running" ? "success" : "error"} size={18} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default OnOffIndicator;
