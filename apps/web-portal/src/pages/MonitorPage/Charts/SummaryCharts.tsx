import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend,
  TimeScale
} from "chart.js";
import { Bar, Line } from "react-chartjs-2";
import zoomPlugin from "chartjs-plugin-zoom";
import clsx from "clsx";
import {
  adjustTimeToBrowserOffset,
  formattedNumber,
  getDiffDate,
  selectDurationFromGraph,
  valueAxis
} from "../utils";
import { chartSecondaryColor, timeAxis } from "./BarChart";
import { ReactNode, useRef } from "react";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend,
  TimeScale,
  zoomPlugin
);

const grid = {
  drawBorder: false,
  display: false
};

const SummaryCharts = ({
  type,
  className,
  data,
  stacked = false,
  children,
  timeSeries = false,
  headingClassName = "",
  showAxis = false,
  handleDurationChange
}: {
  type: string;
  className: string;
  data: any;
  stacked?: boolean;
  children?: ReactNode;
  timeSeries?: boolean;
  headingClassName?: string;
  showAxis?: boolean;
  handleDurationChange?: (dateString: string) => void;
}) => {
  const isHorizontal = type === "Bar-H";
  const [chartTimeGap, unit] = getDiffDate(data.labels);
  const indexAxisConfig = timeSeries ? timeAxis(unit, type === "Bar-H") : {};
  const indexAxis = isHorizontal ? "y" : "x";
  const lastZoomEvent = useRef<string>();

  const scales: ChartJS["scales"] =
    type === "Bar-H"
      ? {
          x: {
            ...valueAxis,
            display: showAxis,
            stacked,
            grid,
            beginAtZero: false,
            ticks: {
              color: chartSecondaryColor,
              precision: 0,
              callback: (value: any) => {
                return formattedNumber(value);
              }
            }
          },
          y: { ...indexAxisConfig, stacked, grid, display: true }
        }
      : {
          y: {
            ...valueAxis,
            display: showAxis,
            stacked,
            grid,
            beginAtZero: false,
            ticks: {
              precision: 0,
              color: chartSecondaryColor,
              callback: (value: any) => {
                return formattedNumber(value);
              }
            }
          },
          x: { ...indexAxisConfig, stacked, grid, display: true }
        };

  const options: ChartJS["options"] = {
    scales,
    indexAxis,
    onClick: (_e: any, element: any) => {
      if (handleDurationChange) {
        if (unit === "minute" && chartTimeGap >= 10) {
          return;
        }

        const duration = selectDurationFromGraph({
          element,
          labels: data.labels,
          unit,
          stepSize: chartTimeGap
        });

        if (duration?.value) {
          handleDurationChange(duration?.value);
        }
      }
    },
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          label: (context) => {
            if (!timeSeries) {
              return `${context.label}: ${context.formattedValue}`;
            }

            if (context.dataset.label?.includes(" ")) {
              return `${context.dataset.label.split(" ")[0]}: ${context.formattedValue}`;
            }
            return `${context.dataset.label}: ${context.formattedValue}`;
          },
          title: (context) => {
            if (!timeSeries) {
              return "";
            }
            const timestamp = data.labels[context[0].dataIndex];
            const nextTimestamp = data.labels[context[0]?.dataIndex + 1];

            if (unit === "month") {
              return convetUTCToLocal(timestamp, "MMM YYYY");
            }
            if (unit === "day") {
              if (chartTimeGap === 1 || !nextTimestamp) {
                return convetUTCToLocal(timestamp, "MMM DD, YYYY");
              }
              return `${convetUTCToLocal(timestamp, "MMM DD, YYYY")} - ${convetUTCToLocal(nextTimestamp, "MMM DD, YYYY")}`;
            }
            if (unit === "hour") {
              if (chartTimeGap === 1 || !nextTimestamp) {
                return convetUTCToLocal(timestamp, "MMM DD HH:mm");
              }
              return `${convetUTCToLocal(timestamp, "MMM DD HH:mm")} - ${convetUTCToLocal(nextTimestamp, "MMM DD HH:mm")}`;
            }
            return convetUTCToLocal(timestamp);
          },
          labelColor: function (context) {
            const color = context.chart.data.datasets[context.datasetIndex].borderColor || "#fff";
            return {
              borderColor: color,
              backgroundColor: color
            };
          }
        },
        mode: "index",
        intersect: true
      },
      zoom: {
        limits: {
          x: { max: "original", min: "original", minRange: 2 },
          y: { max: "original", min: "original", minRange: 2 }
        },
        pan: {
          enabled: Boolean(handleDurationChange),
          mode: indexAxis
        },
        zoom: {
          drag: { enabled: Boolean(handleDurationChange), threshold: 10 },
          mode: indexAxis,
          wheel: {
            enabled: true,
            speed: 0.1
          },
          onZoomStart(context) {
            if (lastZoomEvent.current !== context.event.type) {
              lastZoomEvent.current = context.event.type;
            }
            const zoomLevel = context.chart.getZoomLevel();

            if (zoomLevel !== 1 && context.event.type == "mousedown") {
              return false;
            }

            return true;
          },
          onZoomComplete: ({ chart }) => {
            if (lastZoomEvent.current === "wheel") {
              return;
            }

            const zoomLevel = chart.getZoomLevel();
            if (zoomLevel === 1) {
              return;
            }
            const ticks = chart.scales[indexAxis]?.ticks;
            if (!handleDurationChange) return;

            if (ticks.length === 0) {
              return;
            }
            if (ticks?.length === 1) {
              //  only one present handle accordingly
            } else {
              const start = ticks[0].value; // 5: 30 mins
              const end = ticks[ticks.length - 1].value; // 5: 30 mins

              const durationString = `${adjustTimeToBrowserOffset(
                new Date(start)
              ).toISOString()}TO${adjustTimeToBrowserOffset(new Date(end)).toISOString()}`;

              handleDurationChange(durationString);
            }
          }
        }
      }
    },
    layout: {
      padding: {
        top: children ? 10 : 0,
        right: 0
      }
    },

    elements: {
      bar: { borderRadius: 3 },

      point: { radius: 0.9, hoverBorderColor: "white" },

      line: {
        tension: 0.2,
        borderCapStyle: "round",
        borderWidth: 2
      }
    },
    interaction: {
      mode: "nearest",
      intersect: true
    },

    maintainAspectRatio: false
  };

  return (
    <div
      className={clsx("flex flex-1 border-none p-0 flex-col relative overflow-hidden", className)}
    >
      {children && <div className={clsx(" m-2 mt-0", headingClassName)}>{children}</div>}

      {["Bar", "Bar-H"].includes(type) ? (
        <div
          className={clsx(
            "absolute bottom-0 right-0 left-0 ",
            type === "Bar-H" ? "top-[25%]" : "top-[10%]"
          )}
        >
          <Bar options={options} data={data} className="mt-auto" />
        </div>
      ) : (
        <div className="absolute bottom-0 top-[10%]" style={{ left: "-.7px", right: "-1px" }}>
          <Line options={options} data={data} />
        </div>
      )}
    </div>
  );
};

export default SummaryCharts;
