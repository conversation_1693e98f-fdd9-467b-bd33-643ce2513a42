import { ReactNode, useMemo, useRef, useState } from "react";
import clsx from "clsx";
import { alpha } from "@mui/material";

import {
  ComposedChart,
  Bar,
  Line,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  ReferenceArea
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartConfig
} from "../../../components/shadcn/components/chart";
import {
  adjustTimeToBrowserOffset,
  formattedNumber,
  getDiffDate,
  selectDurationFromGraph
} from "../utils";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { editKeyToLabel } from "@utils/chartConfig";

interface SummaryCharts2Props {
  type: string;
  className: string;
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[] | Array<{ x: string; y: number }>;
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      type?: string;
      fill?: boolean;
      [key: string]: any;
    }>;
  };
  stacked?: boolean;
  children?: ReactNode;
  timeSeries?: boolean;
  headingClassName?: string;
  showAxis?: boolean;
  handleDurationChange?: (dateString: string) => void;
}

const SummaryCharts2 = ({
  type,
  className,
  data,
  stacked = false,
  children,
  timeSeries = false,
  headingClassName = "",
  showAxis = false,
  handleDurationChange
}: SummaryCharts2Props) => {
  const isHorizontal = type === "Bar-H";
  const [chartTimeGap, unit] = getDiffDate(data.labels);
  const [zoomArea, setZoomArea] = useState<{ x1?: string; x2?: string } | null>(null);
  const [isMouseDown, setIsMouseDown] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);

  // Transform data for Recharts
  const chartData = useMemo(() => {
    return data.labels.map((label, index) => {
      const dataPoint: any = {
        label,
        index,
        timestamp: label
      };

      data.datasets.forEach((dataset, datasetIndex) => {
        let value = 0;
        let color = null;

        if (Array.isArray(dataset.data)) {
          const dataItem = dataset.data[index];
          if (typeof dataItem === "object" && dataItem !== null && "y" in dataItem) {
            value = dataItem.y;
          } else if (typeof dataItem === "number") {
            value = dataItem;
          }
        }

        // Get color for this specific data point if colors are arrays
        if (Array.isArray(dataset.borderColor) && dataset.borderColor[index]) {
          color = dataset.borderColor[index];
        } else if (Array.isArray(dataset.backgroundColor) && dataset.backgroundColor[index]) {
          color = dataset.backgroundColor[index];
        }

        dataPoint[dataset.label || `dataset${datasetIndex}`] = value || 0;
        if (color) {
          dataPoint[`${dataset.label || `dataset${datasetIndex}`}_color`] = color;
        }
      });

      return dataPoint;
    });
  }, [data]);

  // Create chart config for shadcn
  const chartConfig: ChartConfig = useMemo(() => {
    const config: ChartConfig = {};

    data.datasets.forEach((dataset, index) => {
      const datasetKey = dataset.label || `dataset${index}`;

      // Handle color arrays by taking the first color or using a fallback
      let color = `hsl(${(index * 137.5) % 360}, 70%, 50%)`;
      if (dataset.borderColor) {
        color = Array.isArray(dataset.borderColor) ? dataset.borderColor[0] : dataset.borderColor;
      } else if (dataset.backgroundColor) {
        color = Array.isArray(dataset.backgroundColor)
          ? dataset.backgroundColor[0]
          : dataset.backgroundColor;
      }

      config[datasetKey] = {
        label: editKeyToLabel(dataset.label || `Dataset ${index + 1}`),
        color: color
      };
    });

    return config;
  }, [data.datasets]);

  // Handle chart click for duration change
  const handleChartClick = (event: any) => {
    if (!handleDurationChange || !event?.activeLabel) return;

    if (unit === "minute" && chartTimeGap >= 10) {
      return;
    }

    const clickedIndex = event.activeTooltipIndex;
    if (clickedIndex !== undefined) {
      const element = [{ index: clickedIndex }];
      const duration = selectDurationFromGraph({
        element,
        labels: data.labels,
        unit,
        stepSize: chartTimeGap
      });

      if (duration?.value) {
        handleDurationChange(duration.value);
      }
    }
  };

  // Handle zoom functionality
  const handleMouseDown = (event: any) => {
    if (!handleDurationChange) return;
    setIsMouseDown(true);
    setZoomArea({ x1: event?.activeLabel });
  };

  const handleMouseMove = (event: any) => {
    if (!isMouseDown || !handleDurationChange) return;
    setZoomArea((prev) => (prev ? { ...prev, x2: event?.activeLabel } : null));
  };

  const handleMouseUp = () => {
    if (!isMouseDown || !handleDurationChange || !zoomArea?.x1 || !zoomArea?.x2) {
      setIsMouseDown(false);
      setZoomArea(null);
      return;
    }

    const startIndex = Math.min(data.labels.indexOf(zoomArea.x1), data.labels.indexOf(zoomArea.x2));
    const endIndex = Math.max(data.labels.indexOf(zoomArea.x1), data.labels.indexOf(zoomArea.x2));

    if (startIndex >= 0 && endIndex >= 0 && startIndex !== endIndex) {
      const startTime = data.labels[startIndex];
      const endTime = data.labels[endIndex];

      const durationString = `${adjustTimeToBrowserOffset(
        new Date(startTime)
      ).toISOString()}TO${adjustTimeToBrowserOffset(new Date(endTime)).toISOString()}`;

      handleDurationChange(durationString);
    }

    setIsMouseDown(false);
    setZoomArea(null);
  };

  // Custom tooltip content
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    const getTooltipTitle = () => {
      if (!timeSeries) return "";

      const timestamp = label;
      const currentIndex = data.labels.indexOf(timestamp);
      const nextTimestamp = data.labels[currentIndex + 1];

      if (unit === "month") {
        return convetUTCToLocal(timestamp, "MMM YYYY");
      }
      if (unit === "day") {
        if (chartTimeGap === 1 || !nextTimestamp) {
          return convetUTCToLocal(timestamp, "MMM DD, YYYY");
        }
        return `${convetUTCToLocal(timestamp, "MMM DD, YYYY")} - ${convetUTCToLocal(nextTimestamp, "MMM DD, YYYY")}`;
      }
      if (unit === "hour") {
        if (chartTimeGap === 1 || !nextTimestamp) {
          return convetUTCToLocal(timestamp, "MMM DD HH:mm");
        }
        return `${convetUTCToLocal(timestamp, "MMM DD HH:mm")} - ${convetUTCToLocal(nextTimestamp, "MMM DD HH:mm")}`;
      }
      return convetUTCToLocal(timestamp);
    };

    const tooltipTitle = getTooltipTitle();

    return (
      <div className="rounded-lg border bg-background p-2 shadow-sm">
        {tooltipTitle && <div className="mb-2 font-medium text-foreground">{tooltipTitle}</div>}
        <div className="grid gap-2">
          {payload.map((entry: any, index: number) => {
            const displayName = String(entry.name || entry.dataKey).includes(" ")
              ? String(entry.name || entry.dataKey).split(" ")[0]
              : String(entry.name || entry.dataKey);

            return (
              <div key={index} className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full" style={{ backgroundColor: entry.color }} />
                <span className="text-sm text-muted-foreground">
                  {editKeyToLabel(displayName)}: {formattedNumber(entry.value)}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderChart = () => {
    const layout = isHorizontal ? "horizontal" : "vertical";

    return (
      <ChartContainer config={chartConfig} className="w-full h-full">
        <ComposedChart
          data={chartData}
          layout={layout}
          onClick={handleChartClick}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <CartesianGrid
            strokeDasharray="3 3"
            stroke="hsl(var(--muted-foreground))"
            opacity={0.2}
          />

          {isHorizontal ? (
            <>
              <XAxis
                type="number"
                hide={!showAxis}
                tickFormatter={(value) => formattedNumber(value)}
              />
              <YAxis type="category" dataKey="label" hide={!showAxis} width={60} />
            </>
          ) : (
            <>
              <XAxis
                type="category"
                dataKey="label"
                hide={!showAxis}
                tickFormatter={(value) => (timeSeries ? convetUTCToLocal(value, "MMM DD") : value)}
              />
              <YAxis
                type="number"
                hide={!showAxis}
                tickFormatter={(value) => formattedNumber(value)}
              />
            </>
          )}

          <ChartTooltip content={<CustomTooltip />} />

          {zoomArea?.x1 && zoomArea?.x2 && (
            <ReferenceArea
              x1={zoomArea.x1}
              x2={zoomArea.x2}
              strokeOpacity={0.3}
              fillOpacity={0.1}
            />
          )}

          {data.datasets.map((dataset, index) => {
            const datasetKey = dataset.label || `dataset${index}`;
            const color =
              chartConfig[datasetKey]?.color || `hsl(${(index * 137.5) % 360}, 70%, 50%)`;

            if (type.includes("Line") || dataset.type === "line") {
              // Use Area component if fill is true, otherwise use Line
              if (dataset.fill) {
                return (
                  <Area
                    key={datasetKey}
                    type="monotone"
                    dataKey={datasetKey}
                    stroke={color}
                    strokeWidth={2}
                    fill={color}
                    fillOpacity={0.3}
                    dot={{ r: 1 }}
                    connectNulls={false}
                  />
                );
              } else {
                return (
                  <Line
                    key={datasetKey}
                    type="monotone"
                    dataKey={datasetKey}
                    stroke={color}
                    strokeWidth={2}
                    dot={{ r: 1 }}
                    connectNulls={false}
                  />
                );
              }
            } else {
              return (
                <Bar
                  key={datasetKey}
                  dataKey={datasetKey}
                  fill={color}
                  stackId={stacked ? "stack" : undefined}
                  radius={[3, 3, 0, 0]}
                />
              );
            }
          })}
        </ComposedChart>
      </ChartContainer>
    );
  };

  return (
    <div
      ref={chartRef}
      className={clsx("flex flex-1 border-none p-0 flex-col relative overflow-hidden", className)}
    >
      {children && <div className={clsx("m-2 mt-0", headingClassName)}>{children}</div>}

      <div
        className={clsx(
          "absolute bottom-0 right-0 left-0",
          type === "Bar-H" ? "top-[25%]" : "top-[10%]"
        )}
      >
        {renderChart()}
      </div>
    </div>
  );
};

export default SummaryCharts2;
