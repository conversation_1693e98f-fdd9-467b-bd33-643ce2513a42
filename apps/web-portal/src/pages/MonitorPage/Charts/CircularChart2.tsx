import React, { useMemo } from "react";
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  RadialBarChart,
  RadialBar,
  LabelList
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartConfig,
  ChartLegend
} from "../../../components/shadcn/components/chart";
import { colorAtI, formattedNumber, isTopLevelFilter, resizedText } from "../utils";
import { alpha } from "@mui/material";
import clsx from "clsx";
import { editKeyToLabel } from "@utils/chartConfig";

interface Props {
  type: "Pie" | "Radar" | "Donut" | "Radial";
  labels: string[];
  filterBy?: string;
  size: import("../../../..").ChartSize;
  children: React.ReactNode;
  colorScheme: import("../../../..").ColorScheme;
  datasets: number[];
  fullLabel?: boolean;
  showLegends?: boolean | undefined;
  chartLayoutData?: ReactGridLayout.Layout;
}

const CircularChart2 = ({
  type,
  labels,
  datasets,
  colorScheme,
  children,
  filterBy,
  size,
  fullLabel = false,
  chartLayoutData,
  showLegends
}: Props): React.ReactElement => {
  const smallSize = size === "1X";

  const processedData = useMemo(() => {
    let newDataset = [...datasets];
    let newLabels = [...labels];
    const availableCount = size === "1X" ? 5 : 20;

    if (newDataset.length > availableCount) {
      const othersCount = newDataset.slice(availableCount - 1);
      newDataset = newDataset.slice(0, availableCount - 1);
      newDataset.push(othersCount.reduce((a, b) => a + b, 0));
      newLabels = newLabels.slice(0, availableCount - 1);
      newLabels.push("others");
    }

    if (filterBy) {
      const filterChildren = isTopLevelFilter(filterBy, labels);
      const isTopFilter = filterChildren.length > 0;

      newLabels = labels.filter((item) => {
        const bool = isTopFilter ? !filterChildren.includes(item) : item !== filterBy;
        if (bool) {
          return false;
        }
        return true;
      });
      newDataset = datasets.filter((_, index) => {
        const labelAtIndex = labels[index];
        if (isTopFilter) {
          return filterChildren.includes(labelAtIndex);
        }
        return labelAtIndex === filterBy;
      });
    }

    return { newDataset, newLabels };
  }, [labels, datasets, filterBy, size]);

  const { newDataset, newLabels } = processedData;

  // Transform data for Recharts
  const chartData = useMemo(() => {
    if (type === "Radar") {
      // For Radar charts, we need a different data structure
      return newLabels.map((label, index) => ({
        subject: editKeyToLabel(fullLabel ? label : resizedText(label, 10)),
        value: newDataset[index] || 0,
        fullValue: formattedNumber(newDataset[index] || 0)
      }));
    } else {
      // For Pie and Donut charts
      return newDataset.map((value, index) => ({
        name: `${newLabels[index]} ${formattedNumber(value)}`,
        value: value,
        fill: alpha(colorAtI(index, colorScheme), 0.7)
      }));
    }
  }, [newDataset, newLabels, colorScheme, type, fullLabel]);

  // Create chart config for shadcn
  const chartConfig: ChartConfig = useMemo(() => {
    const config: ChartConfig = {};
    newLabels.forEach((label, index) => {
      config[label] = { label: label, color: colorAtI(index, colorScheme) };
    });
    return config;
  }, [newLabels, colorScheme]);

  const shouldShowLegends = () => {
    if (!chartLayoutData) return true;
    if (showLegends !== undefined) return showLegends;
    if (chartLayoutData?.w >= 2 && chartLayoutData?.h > 9) return true;
    return false;
  };

  const renderPieChart = () => {
    // For pie/donut charts, we need pie chart data structure
    const pieData = newDataset.map((value, index) => ({
      name: `${newLabels[index]} ${formattedNumber(value)}`,
      value: value,
      fill: alpha(colorAtI(index, colorScheme), 0.7)
    }));

    return (
      <RechartsPieChart>
        <Pie
          data={pieData}
          cx="50%"
          cy="50%"
          innerRadius={type === "Donut" ? (smallSize ? "30%" : "40%") : 0}
          outerRadius={smallSize ? "80%" : "90%"}
          paddingAngle={0}
          dataKey="value"
          stroke="rgba(255, 255, 255, 0.5)"
          strokeWidth={1}
        >
          {pieData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.fill} />
          ))}
        </Pie>

        <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
        {shouldShowLegends() && (
          <ChartLegend
            content={({ payload }) => (
              <div className="flex flex-wrap gap-2 justify-center">
                {payload?.map((entry, index) => (
                  <div key={index} className="flex items-center gap-1 text-xs">
                    <div className="w-2 h-2 rounded-sm" style={{ backgroundColor: entry.color }} />
                    <span>
                      {editKeyToLabel(pieData[index]?.name) || editKeyToLabel(entry.value)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          />
        )}
      </RechartsPieChart>
    );
  };

  const renderRadarChart = () => (
    <RadarChart
      data={chartData}
      margin={
        smallSize
          ? { top: 10, right: 20, bottom: 10, left: 20 }
          : { top: 20, right: 40, bottom: 20, left: 40 }
      }
    >
      <PolarGrid gridType="polygon" />
      <PolarAngleAxis dataKey="subject" tick={{ fontWeight: "bold" }} />
      <PolarRadiusAxis angle={90} domain={[0, Math.max(...newDataset) * 1.1]} tick={false} />
      <Radar
        name="Value"
        dataKey="value"
        stroke={colorAtI(0, colorScheme)}
        fill={alpha(colorAtI(0, colorScheme), 0.3)}
        fillOpacity={1}
        strokeWidth={2}
      />
      <ChartTooltip content={<ChartTooltipContent />} />
    </RadarChart>
  );

  const renderRadialChart = () => {
    const radialData = newDataset.map((value, index) => ({
      category: newLabels[index],
      name: editKeyToLabel(newLabels[index]),
      value: value,
      fill: colorAtI(index, colorScheme)
    }));
    return (
      <RadialBarChart
        data={radialData}
        startAngle={-90}
        endAngle={380}
        innerRadius={smallSize ? 20 : 30}
        outerRadius={smallSize ? 80 : 110}
      >
        <ChartTooltip
          cursor={false}
          content={({ active, payload }) => {
            if (!active || !payload || !payload.length) return null;

            const data = payload[0];
            const label = data.payload?.name || editKeyToLabel(data.dataKey);
            const value = formattedNumber(data.value);
            const color = data.payload?.fill || data.color;

            return (
              <div className="rounded-lg border border-border/50 bg-background px-2 py-1 text-xs shadow-xl">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-sm" style={{ backgroundColor: color }} />
                  <span>
                    {label}: {value}
                  </span>
                </div>
              </div>
            );
          }}
        />
        <RadialBar dataKey="value" background cornerRadius={10}>
          <LabelList
            position="insideStart"
            dataKey="name"
            className="fill-white capitalize mix-blend-luminosity"
            fontSize={smallSize ? 9 : 11}
          />
        </RadialBar>
        {shouldShowLegends() && (
          <ChartLegend
            content={({ payload }) => (
              <div className="flex flex-wrap gap-2 justify-center">
                {payload?.map((entry, index) => (
                  <div key={index} className="flex items-center gap-1 text-xs">
                    <div className="w-2 h-2 rounded-sm" style={{ backgroundColor: entry.color }} />
                    <span>{radialData[index]?.name || editKeyToLabel(entry.value)}</span>
                  </div>
                ))}
              </div>
            )}
          />
        )}
      </RadialBarChart>
    );
  };

  const renderChart = () => {
    switch (type) {
      case "Pie":
        return renderPieChart();
      case "Donut":
        return renderPieChart();
      case "Radar":
        return renderRadarChart();
      case "Radial":
        return renderRadialChart();
      default:
        return renderPieChart();
    }
  };

  if (smallSize) {
    return (
      <>
        <div className="flex items-center relative h-full w-full dashboard-item-content">
          <div className="absolute top-0 left-0 right-0 flex justify-end m-2 mt-0 items-start mb-0 z-10">
            {children}
          </div>
          <div className="w-full h-full">
            <ChartContainer config={chartConfig} className="w-full h-full">
              {renderChart()}
            </ChartContainer>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className={clsx("flex items-center h-full w-full dashboard-item-content")}>
        <div className="w-full h-full">
          <ChartContainer config={chartConfig} className="w-full h-full">
            {renderChart()}
          </ChartContainer>
        </div>
      </div>
    </>
  );
};

export default CircularChart2;
