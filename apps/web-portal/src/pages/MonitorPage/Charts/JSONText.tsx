import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { isDateNumber } from "@utils/date";
import React from "react";

interface Props {
  dataObject: Record<string, unknown>;
  className?: string;
  small?: boolean;
  formatTime?: boolean;
}

const JSONDisplay = ({
  dataObject,
  className,
  small = false,
  formatTime = false
}: Props): React.ReactElement => {
  const formatJSON = (data: Record<string, unknown>) => {
    // Convert the JSON object to an array of key-value pairs
    const entries = Object.entries(data);

    return entries.map(([key, value]) => (
      <span key={key} style={{ lineHeight: small ? "8px" : "20px" }}>
        <span className="  capitalize rounded-sm px-0.5 content-1 font-semibold">{key}:</span>
        <span className="mx-1 content-1 text-foreground">
          {formatTime && isDateNumber(Number(value))
            ? convetUTCToLocal(Number(value) * 1000)
            : JSON.stringify(value).replace(/"/g, " ")}{" "}
        </span>
      </span>
    ));
  };

  return <div className={className}>{formatJSON(dataObject)}</div>;
};

export default JSONDisplay;
