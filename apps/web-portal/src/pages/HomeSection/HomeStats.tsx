import useHomeThingStats from "@hooks/geo/useHomeThingStats";
import { useAppSelector } from "@src/store";
import { THING_VAR } from "@utils/featureLabels";
import { Cloud, CloudOff, Cpu } from "lucide-react";
import { useMemo } from "react";
import { AreaChart, Area, BarChart, Bar, LineChart, Line } from "recharts";
import { colorAtI } from "../MonitorPage/utils";
import { ChartStatCard } from "@components/ui";
import {
  ChartContainer,
  ChartTooltip,
  ChartConfig
} from "../../components/shadcn/components/chart";

const intl = new Intl.NumberFormat("en", { notation: "standard" });
export const formattedNumber = (number: number) => {
  if (!number) return 0;
  try {
    return intl.format(number);
  } catch {
    return number;
  }
};

const createChartConfig = (label: string, color: string): ChartConfig => ({
  value: {
    label,
    color
  },
  timestamp: {
    label: "Time",
    color: "hsl(var(--muted-foreground))"
  }
});
const parseTimestamp = (label: any): string => {
  let date: Date;

  if (typeof label === "string" && !isNaN(Date.parse(label))) {
    date = new Date(label);
  } else if (typeof label === "number") {
    date = label < 946684800 ? new Date(label * 1000) : new Date(label);
  } else {
    date = new Date();
  }

  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="rounded-lg border border-border/50 bg-background px-3 py-2 text-xs shadow-xl">
        <div className="text-muted-foreground mb-1">{data.timestamp}</div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full" style={{ backgroundColor: payload[0].color }} />
          <div className="font-medium text-foreground">{data?.name}:</div>
          <span className="font-medium">{payload[0].value}</span>
        </div>
      </div>
    );
  }
  return null;
};

const getPercentage = (sum: number, num: number[]) => {
  try {
    if (num.length === 0) return 0;
    const secondLast = num[num.length - 2] || 0;
    const data = ((sum - (secondLast || 0)) / secondLast) * 100;
    return Number.isNaN(data) ? 0 : data;
  } catch{
    return 0;
  }
};

const HomeStats = () => {
  const duration = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.graphDuration.value);

  const { data: statsData } = useHomeThingStats({ duration });

  const { onlineDevices, offlineDevices, totalDevices, count } = statsData;

  return (
    <div className="grid grid-cols-3 gap-4 lg:grid-cols-3">
      <ChartStatCard
        title={`Online ${THING_VAR}s`}
        value={count.online}
        icon={Cloud}
        colorScheme="success"
        trending={getPercentage(count.online, onlineDevices.values)}
      >
        <SmallChart
          name="Online"
          type="gradient"
          labels={onlineDevices.labels}
          index={0}
          points={onlineDevices.values}
        />
      </ChartStatCard>

      <ChartStatCard
        title={`Offline ${THING_VAR}s`}
        value={count.offline}
        icon={CloudOff}
        colorScheme="danger"
        trending={getPercentage(count.offline, offlineDevices.values)}
      >
        <SmallChart
          type="area"
          name="Offline"
          labels={offlineDevices.labels}
          index={2}
          points={offlineDevices.values}
        />
      </ChartStatCard>

      <ChartStatCard
        title={`Total ${THING_VAR}s`}
        value={count.total}
        icon={Cpu}
        colorScheme="info"
        trending={getPercentage(count.total, totalDevices.values)}
      >
        <SmallChart
          type="gradient"
          name="Total"
          labels={totalDevices.labels}
          index={1}
          points={totalDevices.values}
        />
      </ChartStatCard>
    </div>
  );
};

const SmallChart = ({
  type,
  labels,
  points,
  index = 0,
  name
}: {
  type: "line" | "bar" | "area" | "gradient";
  labels: number[];
  points: number[];
  index: number;
  name: string;
}) => {
  const chartData = useMemo(() => {
    return labels.map((label, i) => ({
      name: name.toString(),
      value: points[i] || 0,
      timestamp: parseTimestamp(label)
    }));
  }, [labels, points]);

  const chartConfig = useMemo(() => createChartConfig(name, colorAtI(index)), [name, index]);

  const commonProps = {
    data: chartData,
    margin: { top: 0, right: 0, left: 0, bottom: 0 }
  };

  if (type === "bar") {
    return (
      <div style={{ height: "64px" }}>
        <ChartContainer config={chartConfig} className="w-full h-full">
          <BarChart {...commonProps}>
            <Bar dataKey="value" fill={colorAtI(index)} radius={[3, 3, 0, 0]} />
            <ChartTooltip content={<CustomTooltip />} />
          </BarChart>
        </ChartContainer>
      </div>
    );
  }

  if (type === "area" || type === "gradient") {
    return (
      <div style={{ height: "64px" }}>
        <ChartContainer config={chartConfig} className="w-full h-full">
          <AreaChart {...commonProps}>
            <defs>
              <linearGradient id={`gradient-${index}`} x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={colorAtI(index)} stopOpacity={0.3} />
                <stop offset="95%" stopColor={colorAtI(index)} stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <Area
              type="monotone"
              dataKey="value"
              stroke={colorAtI(index)}
              strokeWidth={2}
              fill={type === "gradient" ? `url(#gradient-${index})` : colorAtI(index)}
              fillOpacity={type === "area" ? 0.1 : 1}
            />
            <ChartTooltip content={<CustomTooltip />} />
          </AreaChart>
        </ChartContainer>
      </div>
    );
  }

  return (
    <div style={{ height: "64px" }}>
      <ChartContainer config={chartConfig} className="w-full h-full">
        <LineChart {...commonProps}>
          <Line
            type="monotone"
            dataKey="value"
            stroke={colorAtI(index)}
            strokeWidth={2}
            dot={false}
          />
          <ChartTooltip content={<CustomTooltip />} />
        </LineChart>
      </ChartContainer>
    </div>
  );
};

export default HomeStats;
