import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useTablePagination from "@hooks/classic/useTablePagination";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useMspUserList from "@hooks/user/useMspUserList";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { INPUT_ICON_SIZE, MSP_ROLES, ROLES_Mapped } from "@utils/utilities";
import { Search, Users } from "lucide-react";
import  colors from  "tailwindcss/colors"

const TableSection = () => {
  const { page, setPage, limit, setLimit, searchQuery, setSearchQuery } = useTablePagination();
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const {
    data: mspUsers,
    isLoading,
    isRefetching
  } = useMspUserList({ page, limit, search: debouncedSearchQuery, msp: false });

  return (
    <section className="space-y-6">
      <div className="between items-center">
        <HeadingIcon Icon={Users} title="All Users" />

        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search by Name"
          className="w-[25rem]"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
            <TableHead onSort={(order) => sort("role", order)}>Role</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading || isRefetching ? (
            <TableRowsSkeleton />
          ) : !mspUsers || !mspUsers.users.length ? (
            <DataNotFound title="No Users Available" isTable />
          ) : (
            <>
              {mspUsers.users.toSorted(sortFn).map((user, i) => (
                <tr
                  className="cursor-pointer"
                  onClick={() =>
                    navigate(
                      MSP_ROLES.includes(user.role)
                        ? `/UM/mspUsers/${user.email}`
                        : `/TM/tenantUsers/${user.email}`
                    )
                  }
                  key={user.email}
                >
                  <TableRow>{i + 1}</TableRow>
                  <TableRow>{user.name}</TableRow>

                  <TableRow>{user.email}</TableRow>

                  <TableRow>
                    <Label
                      text={ROLES_Mapped.find((role) => role.key === user.role)?.value}
                      color={ROLES_Mapped.find((role) => role.key === user.role)?.color as keyof typeof colors}
                    />
                  </TableRow>
                  <TableRow>{convetUTCToLocal(user.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(user.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: mspUsers?.pages || 1
        }}
      />
    </section>
  );
};

export default TableSection;
