import { fetchOperatorViewUsers } from "@api/user";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { getTableIndex } from "@utils/tableUtils";
import {
  DEFAULT_PAGE_COUNT,
  INPUT_ICON_SIZE,
  MSP_ROLES,
  OPERATOR_ROLES,
  ROLES_Mapped
} from "@utils/utilities";
import { useState } from "react";
import { Search, Users } from "lucide-react";
import colors from "tailwindcss/colors"

const TableSection = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const {
    data: operatorUsers,
    isLoading,
    isRefetching
  } = useQuery({
    queryKey: [`operator-user-list-operator`, page, limit, debouncedSearchQuery],
    queryFn: fetchOperatorViewUsers
  });

  return (
    <section className="space-y-6">
      <div className="between items-center">
        <HeadingIcon Icon={Users} title="All Users" />

        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search by Name"
          className="w-[25rem]"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
      </div>

      <Table
        className="!max-h-full"
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
            <TableHead onSort={(order) => sort("role", order)}>Role</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading || isRefetching ? (
            <TableRowsSkeleton />
          ) : !operatorUsers.data.users.length || !operatorUsers ? (
            <DataNotFound title="No Users Available" isTable />
          ) : (
            <>
              {operatorUsers.data.users.toSorted(sortFn).map((user, i) => (
                <tr
                  className="cursor-pointer"
                  onClick={() =>
                    navigate(
                      OPERATOR_ROLES.includes(user.role)
                        ? `/UM/opUsers/${user.email}`
                        : MSP_ROLES.includes(user.role)
                          ? `/mspManagement/mspUsers/${user.email}`
                          : `/TM/tenantUsers/${user.email}`
                    )
                  }
                  key={user.email}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow>{user.name}</TableRow>

                  <TableRow>{user.email}</TableRow>

                  <TableRow>
                    <Label
                      text={ROLES_Mapped.find((role) => role.key === user.role)?.value}
                      color={ROLES_Mapped.find((role) => role.key === user.role)?.color  as keyof typeof colors}
                    />
                  </TableRow>
                  <TableRow>{convetUTCToLocal(user.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(user.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: operatorUsers?.data?.pages
        }}
      />
    </section>
  );
};

export default TableSection;
