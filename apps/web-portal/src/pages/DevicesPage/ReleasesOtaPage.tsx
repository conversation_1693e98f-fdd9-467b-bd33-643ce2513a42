import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import HeaderSection from "@components/layout/HeaderSection";
import { Card, StatCard } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useOtaReleaseCount from "@hooks/ota/useOtaReleaseCount";
import useOtaReleaseList from "@hooks/ota/useOtaReleaseList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { PRODUCT_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Cloud, CloudOff, Cpu, Plus, Search } from "lucide-react";
import { useState } from "react";
import Input from "../../components/Input";
import Label from "../../components/Label";
import Table, { TableHead, TableRow } from "../../components/Table";
import TableRowsSkeleton from "../../components/Table/TableRowsSkeleton";
import useTableSort from "../../hooks/useTableSort";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";

export default function ReleasesOtaPage() {
  const [sortFn, sort] = useTableSort();

  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);

  const { data: otaReleases, isLoading: otaReleasesLoading } = useOtaReleaseCount({
    enabled: true
  });

  const navigate = useCustomNavigate();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();

  const { data: otaReleaseList, isLoading: isOtaListLoading } = useOtaReleaseList({
    page,
    limit,
    search: debouncedSearchQuery
  });

  return (
    <main className=" space-y-4">
       <HeaderSection
        title={`OTA Releases`}
        description="Manage your OTA Releases"
        actions={
          <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              onClick={() => navigate("createOta")}
              noAccess={permissions.ota !== "write"}
          >
           Add OTA Release
          </Button>
        }
      />
      <section className="flex w-full flex-wrap gap-4">
        <StatCard
          title="Completed OTA Jobs"
          value={otaReleases?.completed}
          loading={otaReleasesLoading}
          colorScheme="success"
          icon={Cloud}
          description={
            ((otaReleases.completed / (otaReleases.completed + otaReleases.failed)) * 100).toFixed(
              1
            ) + "%"
          }
        />
        <StatCard
          title="Failed OTA Jobs"
          value={otaReleases.failed}
          colorScheme="danger"
          loading={otaReleasesLoading}
          icon={CloudOff}
          description={
            ((otaReleases.failed / (otaReleases.completed + otaReleases.failed)) * 100).toFixed(1) +
            "%"
          }
        />
        <StatCard
          title="Total OTA Jobs"
          loading={otaReleasesLoading}
          value={otaReleases.failed + otaReleases.completed}
          colorScheme="info"
          icon={Cpu}
        />
      </section>
       <Card className="space-y-4">
         <h3 className="heading-2">OTA Releases List</h3>
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("jobId", order)}>Job ID</TableHead>
              <TableHead onSort={(order) => sort("otaType", order)}>OTA Type</TableHead>
              <TableHead onSort={(order) => sort("productName", order)}>
                {PRODUCT_VAR} Name
              </TableHead>

              <TableHead onSort={(order) => sort("target", order)}>Target</TableHead>
              <TableHead onSort={(order) => sort("closed", order)}>Status</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            </>
          }
          body={
            isOtaListLoading ? (
              <TableRowsSkeleton />
            ) : !otaReleaseList || !otaReleaseList?.OTA.length ? (
              <DataNotFound
                title={!otaReleaseList ? "SomeThing went wrong!" : "No OTA Found"}
                isTable
              />
            ) : (
              <>
                {/* FILTER NEED TO BE CHANGE  */}
                {otaReleaseList.OTA.toSorted(sortFn).map((ota, i) => (
                  <tr
                    key={ota.uniqueId}
                    onClick={() => navigate(`${ota.uniqueId}`)}
                    className="cursor-pointer"
                  >
                    <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                    <TableRow title>{ota.jobId}</TableRow>
                    <TableRow>{ota.otaType}</TableRow>
                    <TableRow>{ota.productName}</TableRow>
                    <TableRow>{ota.target}</TableRow>
                    <TableRow>
                      <Label
                        color={ota.closed ? "red" : "green"}
                        text={ota.closed ? "Closed" : "Active"}
                      />
                    </TableRow>
                    <TableRow>{convetUTCToLocal(ota.createdAt)}</TableRow>

                    {/* <TableRow className="w-2">
                        <span className="flex flex-wrap gap-2">
                          {Array.isArray(ota.targetName) ? (
                            ota.targetName?.map((user, i) => (
                              <Label key={i} lowercase text={user} color="blue" linear />
                            ))
                          ) : (
                            <Label lowercase text={ota.targetName} color="blue" linear />
                          )}
                        </span>
                      </TableRow> */}
                  </tr>
                ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: otaReleaseList?.pages || 1
          }}
        />
      </Card>
    </main>
  );
}