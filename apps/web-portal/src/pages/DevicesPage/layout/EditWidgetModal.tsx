/* eslint-disable max-len */
import HeadingIcon from "@components/HeadingIcon";
import useGeoThingDetails from "@hooks/geo/useGeoThingDetails";
import { Modal } from "@mui/material";
import { EXCLUDED_FIELDS } from "@src/pages/ProductPage/ProductDetailSection/layout/ProductWidgetContainer";
import clsx from "clsx";
import React, { ForwardedRef, useCallback, useImperativeHandle, useMemo, useState } from "react";
import { AssetDetails, ThingWidget } from "../../../..";
import Button from "../../../components/Button";
import { Card } from "@components/ui";
import Dropdown from "../../../components/Dropdown";
import Input from "../../../components/Input";
import { useAppSelector } from "../../../store";
import RangeColorSelector from "../../MonitorPage/Charts/RangeColorSelector";
import {
  colorAtI,
  formatIndexElasticIndex,
  generateRandomString,
  ICON_SCHEMES,
  SENSOR_KEY
} from "../../MonitorPage/utils";
import GeoMapWidget from "./GeoMapWidget";
import InfoWidget from "./InfoWidget";
import ThingMonitorWidget from "./ThingMonitorWidget";
import { CHART_TYPES, OPERATIONS } from "./utils";
import useTimeSeriesOptions from "@hooks/timeseries/useTimeseriesOptions";
import WidgetSizeConfig from "./WidgetSizeConfig";
import { Popover, PopoverContent, PopoverTrigger } from "@components/shadcn/components/popover";
import { ScrollArea } from "@components/shadcn/components/scroll-area";
import { showErrorToast } from "@utils/index";
import { Grid2X2Plus, Plus } from "lucide-react";
import { useForm } from "react-hook-form";
import WidgetFieldListItem, {
  buildWidgetFieldList,
  LINE_CHARTS,
  MAP_CHARTS,
  SHADOW_CHARTS
} from "./WidgetFieldListItem";
import WidgetAssetFieldSelector from "./WidgetAssetFieldSelector";
import useShadowOptions from "@hooks/timeseries/useShadowOptions";

type Props = {
  onAdd: (item: ThingWidget) => void;
  onUpdate: (item: ThingWidget) => void;
  duration: string;
  // this is a special case for digital twin some options are disabled
  digitalTwinWidget?: boolean;
  assets?: AssetDetails[];
};

type Ref = {
  showModal: (
    item: Partial<ThingWidget>,

    t: { productName: string; thingName: string; mutationType: "Edit" | "Add" }
  ) => void;
};
const getAppropriateWidgetFields = (widgetDetails: ThingWidget, type: ThingWidget["type"]) => {
  if (SHADOW_CHARTS.includes(type) && SHADOW_CHARTS.includes(widgetDetails.type)) {
    return widgetDetails.fieldDetailsList;
  }
  if (LINE_CHARTS.includes(type) && LINE_CHARTS.includes(widgetDetails.type)) {
    return widgetDetails.fieldDetailsList;
  }
  if (
    (LINE_CHARTS.includes(type) || SHADOW_CHARTS.includes(type)) &&
    (LINE_CHARTS.includes(widgetDetails.type) || SHADOW_CHARTS.includes(widgetDetails.type))
  ) {
    return widgetDetails.fieldDetailsList?.[0] ? [widgetDetails.fieldDetailsList[0]] : [];
  }
  if (MAP_CHARTS.includes(type) && MAP_CHARTS.includes(widgetDetails.type)) {
    return widgetDetails.fieldDetailsList;
  }
  return [];
};

const EditWidgetModal = (props: Props, ref: ForwardedRef<Ref>) => {
  const [widgetDetails, setWidgetDetails] = useState<ThingWidget>();
  const [thingDetails, setThingDetails] = useState({
    thingName: "",
    productName: "",
    mutationType: ""
  });
  const [isIconPickerOpen, setIsIconPickerOpen] = useState(false);

  const { onAdd, onUpdate, duration, digitalTwinWidget, assets } = props;
  const featureType = useAppSelector(({ user }) => user.tenant?.featureType);
  const tenant = useAppSelector(({ user }) => user.tenant?.name);
  const widgetForm = useForm({
    mode: "onBlur",
    defaultValues: {
      widgetName: ""
    }
  });
  const { data: productThing } = useGeoThingDetails({
    thingName: thingDetails.thingName!,
    enabled: Boolean(thingDetails.thingName && featureType === "geo")
  });

  const deviceInfoFields = useMemo(() => {
    const productThingMap = productThing! || {};
    const items = Object.keys(productThingMap);
    const keys = new Set<string>();

    for (const item of items) {
      if (!EXCLUDED_FIELDS.includes(item)) {
        keys.add(item);
      }
      if (item === "attributes") {
        const attributesEntries = Object.entries(productThingMap.attributes || {});
        attributesEntries.forEach(([key, value]) => {
          if (typeof value === "object") {
            // TODO: handle nested attributes
            if (Array.isArray(value)) {
              keys.add(key);
            } else {
              Object.keys(value).forEach((nested) => {
                keys.add(nested);
              });
            }
          } else {
            keys.add(key);
          }
        });
      }
    }

    if (keys.size === 0) {
      return [];
    }

    return Array.from(keys);
  }, [productThing]);

  const { data: tsOptions } = useTimeSeriesOptions({
    thingName: thingDetails.thingName,
    enabled: Boolean(thingDetails.thingName)
  });

  const { data: shadowOptions } = useShadowOptions({
    thingName: thingDetails.thingName,
    enabled: Boolean(thingDetails.thingName) && widgetDetails?.source === "shadow"
  });

  const DATA_INDEXES = useMemo(() => {
    const res = [`${tenant}-logs`];

    if (featureType !== "geo") {
      res.push(`shadow`);
    }

    if (featureType === "geo") {
      res.push(`${tenant}-position-data`);
    }

    res.push(SENSOR_KEY);

    return res;
  }, []);
  const isLogsIndex = widgetDetails?.source.includes("logs");

  // TODO: will be changed after latest shadow changes

  const { data: sensorOptions, isLoading } = useTimeSeriesOptions({
    thingName: thingDetails.thingName,
    enabled: Boolean(thingDetails.thingName) && widgetDetails?.source === SENSOR_KEY
  });

  const [modalVisibility, setModalVisibility] = useState(false);

  const hideModal = () => {
    setModalVisibility(false);
    setWidgetDetails(undefined);
  };

  const showModal = useCallback((wData: Partial<ThingWidget>, options: any) => {
    setThingDetails(options);
    setModalVisibility(true);

    // @ts-expect-error it can accept partial as well
    setWidgetDetails(wData);
    widgetForm.setValue("widgetName", wData.title || "");
  }, []);

  useImperativeHandle(ref, () => ({ showModal }), []);

  const updateWidgetDetails = (widgetData: Partial<ThingWidget>) => {
    // @ts-expect-error it can accept partial as well
    setWidgetDetails((prev) => ({ ...prev, ...widgetData }));
  };

  const fieldDetailsList = useMemo(() => {
    return buildWidgetFieldList(widgetDetails);
  }, [widgetDetails]);

  if (!widgetDetails) {
    return null;
  }

  const { type, title, operation, color, iconScheme, range, source, size } = widgetDetails;
  const { thingName, productName, mutationType } = thingDetails;

  const isGridOrFill = ["Gauge", "Fill"].includes(type);

  // this is for the case where can not select multiple fields
  const singleField = fieldDetailsList[0]?.field;
  const isFieldsEmpty = fieldDetailsList.some((item) => item.field === "");
  const validated = Boolean(
    type && !isFieldsEmpty && (type === "Switch" || color) && (isGridOrFill ? range.length : true)
  );

  const onSave = () => {
    const fieldsThatExeedsLimit = fieldDetailsList.find((item) => item.displayName?.length > 30);
    if (fieldsThatExeedsLimit) {
      showErrorToast("Field name should be less than 30 characters");
      return;
    }
    if (mutationType === "Add") {
      localStorage.setItem("@last-widget-source", source);
      onAdd(widgetDetails);
    }
    if (mutationType === "Edit") {
      onUpdate(widgetDetails);
    }
    setModalVisibility(false);
  };

  const getAvailableCharts = (index: string) => {
    if (index.endsWith("-position-data")) {
      return [...MAP_CHARTS, ...LINE_CHARTS];
    }
    if (index.includes("shadow")) {
      return SHADOW_CHARTS;
    }
    if (index.includes("logs")) {
      return LINE_CHARTS;
    }
    return CHART_TYPES;
  };

  const colorPickerType = isGridOrFill || MAP_CHARTS.includes(type) ? "range" : "single";

  const handleAddNewField = () =>
    updateWidgetDetails({
      fieldDetailsList: [
        ...fieldDetailsList,
        {
          id: generateRandomString(8),
          decimal: 2,
          displayName: "",
          color: colorAtI(fieldDetailsList.length),
          unit: "",
          field: ""
        }
      ]
    });

  const toggleAssetField = (assetFields: number[]) => {
    updateWidgetDetails({
      assetFields: assetFields
    });
  };

  return (
    <Modal
      open={modalVisibility}
      onClose={hideModal}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      }}
      className="mt-auto mb-auto"
    >
      <Card className="!p-3 w-[600px] space-y-3">
        <div className="flex items-center justify-between">
          <Popover open={isIconPickerOpen} onOpenChange={setIsIconPickerOpen}>
            <PopoverTrigger>
              <HeadingIcon
                title={mutationType === "Add" ? "Create Widget" : "Edit Widget"}
                Icon={
                  iconScheme
                    ? ICON_SCHEMES.find((icon) => icon.label === iconScheme)?.Icon
                    : Grid2X2Plus
                }
              />
            </PopoverTrigger>
            <PopoverContent className="z-[10000] w-[35rem]">
              <ScrollArea className=" rounded-md  h-[28vh]">
                <div className="grid grid-cols-10 gap-2  ">
                  {ICON_SCHEMES.map(({ Icon, label }) => (
                    <div
                      onClick={() => {
                        updateWidgetDetails({ iconScheme: label });
                        setIsIconPickerOpen(false);
                      }}
                      key={label}
                      className={clsx(
                        "h-10 w-10 cursor-pointer center rounded-md border border-gray-500 ",
                        label === iconScheme && "border-green-500 border bg-green-500/20"
                      )}
                    >
                      <Icon
                        size="1.2rem"
                        className={label === iconScheme ? "text-green-500" : ""}
                      />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </PopoverContent>
          </Popover>

          {!digitalTwinWidget && (
            <WidgetSizeConfig onChange={updateWidgetDetails} size={widgetDetails.size} />
          )}
        </div>

        <div className={clsx("h-56 bg-secondary p-2 rounded-md")}>
          {source === "Device" ? (
            <div className=" flex items-center justify-center h-full">
              <InfoWidget
                widgets={[widgetDetails]}
                thingName={thingName}
                single
                editElement={(item) => <div>{item.title}</div>}
              />
            </div>
          ) : (type === "Scatter" || type === "Path" || type === "Heat Map") &&
            singleField === "position" ? (
            <div className="flex items-center justify-center h-full">
              <GeoMapWidget
                className="h-52 flex-1 !rounded-t-md"
                type={type}
                widgets={widgetDetails}
                thingName={thingName}
                productName={productName}
                duration={duration}
              />
            </div>
          ) : (
            <ThingMonitorWidget
              thingName={thingName}
              productName={productName}
              type={type}
              widgetFields={fieldDetailsList}
              operation={operation}
              range={range}
              duration={duration}
              source={source}
              // showAxis={showAxis}
              showAxis={size === "2X" || size === "4X"}
              assetFields={widgetDetails.assetFields}
            />
          )}
        </div>
        <form className="w-full mt-4 mb-2 space-y-4" onSubmit={widgetForm.handleSubmit(onSave)}>
          <div className="max-h-[56vh] overflow-y-scroll space-y-3 px-1">
            <div className="grid grid-cols-2 gap-4">
              <Input
                {...widgetForm.register("widgetName", {
                  required: true,
                  maxLength: 30,
                  minLength: 5
                })}
                value={title}
                label="Widget name"
                labelClassName="helper-text"
                onChange={(e) => {
                  updateWidgetDetails({ title: e.target.value });
                }}
                error={!!widgetForm.formState.errors.widgetName}
                helperText={
                  widgetForm.formState.errors.widgetName &&
                  "Widget Name length should be between 5 to 30"
                }
              />

              <div>
                <Dropdown
                  label="Data Source"
                  value={
                    source && {
                      title: formatIndexElasticIndex(source),
                      value: source
                    }
                  }
                  options={(featureType === "geo" && deviceInfoFields
                    ? [...DATA_INDEXES, "Device"]
                    : DATA_INDEXES
                  ).map((item) => ({
                    title: formatIndexElasticIndex(item),
                    value: item
                  }))}
                  disabled={digitalTwinWidget}
                  getOptionLabel="title"
                  onChange={({ value }) => {
                    if (value === "Device") {
                      updateWidgetDetails({
                        source: value,
                        type: "Info",
                        operation: "latest",
                        fieldDetailsList: []
                      });
                      return;
                    }
                    if (value === SENSOR_KEY) {
                      updateWidgetDetails({
                        source: value,
                        type: undefined,
                        fieldDetailsList: []
                      });
                      return;
                    }
                    if (widgetDetails.source === SENSOR_KEY && value !== SENSOR_KEY) {
                      updateWidgetDetails({
                        source: value,
                        type: undefined,
                        fieldDetailsList: []
                      });
                      return;
                    }
                    updateWidgetDetails({
                      source: value,
                      type: !type
                        ? undefined
                        : getAvailableCharts(value).includes(type)
                          ? type
                          : getAvailableCharts(value)[0],
                      operation: value.includes("logs")
                        ? undefined
                        : value.includes("shadow")
                          ? "latest"
                          : operation
                    });
                  }}
                />
              </div>

              <div>
                <Dropdown
                  label="Widget Type"
                  options={getAvailableCharts(source)}
                  disabled={source === "Device"}
                  value={widgetDetails.type}
                  placeHolder="Select widget type"
                  direction="bottom"
                  onChange={(value) => {
                    updateWidgetDetails({
                      type: value,
                      fieldDetailsList: getAppropriateWidgetFields(widgetDetails, value),
                      assetFields: ["Gauge", "Fill", "Switch"].includes(value)
                        ? []
                        : widgetDetails.assetFields
                    });
                  }}
                />
              </div>

              <div>
                <Dropdown
                  label="Value Type"
                  options={OPERATIONS}
                  value={operation}
                  disabled={source === "Device" || source.includes("shadow") || isLogsIndex}
                  placeHolder={isLogsIndex ? "Count" : undefined}
                  onChange={(value) => {
                    updateWidgetDetails({ operation: value });
                  }}
                />
              </div>

              {assets ? (
                <WidgetAssetFieldSelector
                  assets={assets}
                  selectedFields={widgetDetails.assetFields || []}
                  toggleField={toggleAssetField}
                  chartType={type}
                />
              ) : (
                <div className="col-span-2 relative">
                  <div className="flex items-center justify-between mb-1">
                    <p className="helper-text">Fields</p>

                    <Button
                      small
                      type="button"
                      className="h-6 mb-1"
                      onClick={handleAddNewField}
                      disabled={
                        !(
                          LINE_CHARTS.includes(type) ||
                          ["Pie", "Donut", "Polar", "Count"].includes(type)
                        )
                      }
                      startIcon={<Plus size={10} />}
                    >
                      Add Field
                    </Button>
                  </div>
                  {fieldDetailsList.map((fieldDetails) => (
                    <WidgetFieldListItem
                      key={fieldDetails.id}
                      colorPickerType={colorPickerType}
                      fieldDetails={fieldDetails}
                      updateFieldDetails={(updatedFieldDetails) => {
                        updateWidgetDetails({
                          fieldDetailsList: fieldDetailsList.map((field) => {
                            if (fieldDetails.id === field.id) {
                              return {
                                ...field,
                                ...updatedFieldDetails
                              };
                            }
                            return field;
                          })
                        });
                      }}
                      source={source}
                      sensorOptions={sensorOptions || []}
                      shadowOptions={shadowOptions || []}
                      widgetType={type}
                      optionsLoading={isLoading}
                      deviceKeys={deviceInfoFields}
                      disabledRemove={fieldDetailsList.length === 1}
                      removeWidgetField={() => {
                        updateWidgetDetails({
                          fieldDetailsList: fieldDetailsList.filter(
                            (field) => field.id !== fieldDetails.id
                          )
                        });
                      }}
                    />
                  ))}
                </div>
              )}
            </div>

            {colorPickerType === "range" && (
              <RangeColorSelector
                colorConfig={range}
                conditionKeys={singleField === "position" ? tsOptions || [] : []}
                onUpdate={(value) => updateWidgetDetails({ range: value })}
                disabled={range.length >= 4}
              />
            )}

            <div className="grid grid-cols-2 gap-4 pt-2">
              <Button color="red" type="button" onClick={hideModal}>
                Cancel
              </Button>
              <Button type="submit" disabled={!validated}>
                {mutationType === "Edit" ? "Update" : "Create"}
              </Button>
            </div>
          </div>
        </form>
      </Card>
    </Modal>
  );
};

export default React.forwardRef(EditWidgetModal);
