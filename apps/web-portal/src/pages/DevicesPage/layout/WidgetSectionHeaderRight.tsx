import ActionButton from "@components/ActionButton";
import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@components/shadcn/components/dropdown-menu";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useWidgetConfig from "@hooks/classic/useWidgetConfig";
import { toggleAllowZeros } from "@src/features/dashboardBuilderSlice";
import { availableOptions } from "@src/pages/MonitorPage/layout/SelectDuration";
import { useAppDispatch, useAppSelector } from "@src/store";
import { getPartnerAndFeature } from "@utils/url";
import { BUTTON_ICON_SIZE, checkForHost, INPUT_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Check, Edit2, EllipsisVertical, Eye, EyeOff, Plus, Radio, X } from "lucide-react";
import { useMemo } from "react";

const isSasken = checkForHost("sasken");

const WidgetSectionHeaderRight = ({
  addNewWidget,
  saveWidgetNewLayout
}: {
  addNewWidget: () => void;
  saveWidgetNewLayout: () => void;
}) => {
  const { editing, setEditing, setDuration, duration, toggleLiveMode, liveMode } =
    useWidgetConfig();
  const { data: permissions } = useUserGroupPermissions();

  const featureType =
    useAppSelector(({ user }) => user.tenant?.featureType!) || getPartnerAndFeature().featureType;

  const selectedDuration = useMemo(() => {
    const durationAvailable = availableOptions.find((item) => item.value === duration);
    if (durationAvailable) {
      return durationAvailable;
    }
    return { title: "Today", value: duration };
  }, [duration]);

  return (
    <div className="flex items-center gap-3">
      {!isSasken && (
        <Button
          className="shrink-0"
          small
          startIcon={
            <Radio size={BUTTON_ICON_SIZE} className={clsx(liveMode && " animate-pulse")} />
          }
          onClick={toggleLiveMode}
          color={liveMode ? "orange" : "green"}
          endIcon={
            liveMode && (
              <X size="1rem" className=" !bg-red-300/20 !text-red-500 ml-1  rounded-full" />
            )
          }
        >
          {liveMode ? "In Live" : "View Live"}
        </Button>
      )}

      {permissions.thing === "write" && (
        <div className="shrink-0">
          {editing ? (
            <Button
              startIcon={<Check size={BUTTON_ICON_SIZE} />}
              color="blue"
              small
              onClick={saveWidgetNewLayout}
            >
              Save Widgets
            </Button>
          ) : (
            <Button
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
              color="gray"
              small
              onClick={() => setEditing(true)}
            >
              Edit Widgets
            </Button>
          )}
        </div>
      )}

      {featureType !== "geo" && (
        <Dropdown
          className="min-w-[200px]"
          menuClassName=" !max-h-[150px]"
          value={selectedDuration}
          options={availableOptions}
          getOptionLabel="title"
          onChange={({ value }) => {
            setDuration(value);
          }}
        />
      )}

      {/* eslint-disable-next-line no-use-before-define */}
      <GeoWidgetOptions addNewWidget={addNewWidget} />
    </div>
  );
};

const GeoWidgetOptions = ({ addNewWidget }: { addNewWidget: () => void }) => {
  const dispatch = useAppDispatch();
  const allowZeros = useAppSelector(({ dashboardBuilder }) => dashboardBuilder.allowZeros);
  const { data: permissions } = useUserGroupPermissions();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <ActionButton Icon={EllipsisVertical} visibleBg className="!p-2.5" />
      </DropdownMenuTrigger>
      <DropdownMenuContent className=" w-[11rem]">
        <DropdownMenuGroup>
          <DropdownMenuItem
            disabled={permissions.thing !== "write"}
            className="gap-3 cursor-pointer"
            onClick={addNewWidget}
          >
            <Plus size={INPUT_ICON_SIZE} />
            Add Widget
          </DropdownMenuItem>

          <DropdownMenuItem
            className="gap-3 cursor-pointer"
            onClick={() => {
              dispatch(toggleAllowZeros());
            }}
          >
            {allowZeros ? <Eye size={BUTTON_ICON_SIZE} /> : <EyeOff size={BUTTON_ICON_SIZE} />}
            {!allowZeros ? "Ignore Zero" : "Count Zero"}
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default WidgetSectionHeaderRight;
