/* eslint-disable camelcase */
/* eslint-disable no-use-before-define */
import { ThingWidget } from "@/index";
import { Card } from "@components/ui";
import useTimeSeriesPositions from "@hooks/timeseries/useTimeSeriesPositions";
import ChartLoader from "@src/pages/MonitorPage/Charts/ChartLoader";
import DashboardMap from "@src/pages/MonitorPage/layout/DashboardMap";
import { colorAtI } from "@src/pages/MonitorPage/utils";
import clsx from "clsx";
import { DivIcon } from "leaflet";
import { useMemo } from "react";

const markerHtmlStyles = (color: string) => {
  return `
  background: radial-gradient(circle at center, ${color} 0%, #fff 100%);
  width: 1.5rem;
  height: 1.5rem;
  display: block;
  left: -0.75rem;
  top: -0.75rem;
  position: relative;
  border-radius: 50%;
  `;
};

export const CustomIcon = (color: string) =>
  new DivIcon({
    className: "my-custom-pin",
    iconAnchor: [0, 24],
    popupAnchor: [0, -36],
    html: `<span style="${markerHtmlStyles(color)}" />`
  });

const GeoMapWidget = ({
  className,
  widgets,
  type,
  productName,
  duration,
  thingName
}: {
  className?: string;
  widgets: ThingWidget;
  type: "Scatter" | "Path" | "Heat Map";
  thingName: string;
  productName: string;
  duration: string;
}) => {
  const { range } = widgets;

  const conditionKeys = useMemo(() => {
    const keys: string[] = [];
    if (range) {
      range.forEach((item) => {
        if (item.conditionKey) {
          keys.push(item.conditionKey);
        }
      });
    }
    return keys;
  }, [range]);

  const { data: positionsData, isLoading } = useTimeSeriesPositions({
    thingName,
    enabled: Boolean(thingName),
    duration,
    geoTenant: true,
    filters: [],
    // lat long not required for geo tenant
    latKey: "",
    longKey: "",
    conditionKeys,
    type
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <ChartLoader color={colorAtI(0)} />
      </div>
    );
  }

  if (!isLoading && !positionsData) {
    return (
      <div className="flex items-center justify-center h-full">
        <Card className="flex flex-col gap-2 p-3 items-center justify-center bg-card">
          <p className="heading-3">No Points Available</p>
          <p className="content-2 text-muted-foreground">
            Please select a different duration or try again later
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className={clsx(" !p-2 flex  overflow-hidden flex-col  rounded-lg", className)}>
      <DashboardMap
        className="flex-1 rounded-lg overflow-hidden"
        positions={positionsData?.positions || []}
        mapType={type}
        attributes={positionsData?.extras || {}}
        range={range}
      />
    </div>
  );
};

export default GeoMapWidget;
