import Button from "@components/Button";
import HeaderSection from "@components/layout/HeaderSection";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { THING_VAR } from "@utils/featureLabels";
import { Plus } from "lucide-react";
import CardSection from "./CardSection";
import TableSection from "./TableSection";

function DevicesListPage() {
  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();

  return (
    <main className="space-y-4">
      <HeaderSection
        title={`${THING_VAR}s`}
        description="Manage your Things"
        actions={
          <Button
            noAccess={permissions.thing !== "write"}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => {
              navigate("addThing");
            }}
          >
            Add {THING_VAR}
          </Button>
        }
      />
      <CardSection />
      <TableSection />
    </main>
  );
}

export default DevicesListPage;
