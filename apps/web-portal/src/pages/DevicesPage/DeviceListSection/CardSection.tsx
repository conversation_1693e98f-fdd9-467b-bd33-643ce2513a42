import { StatCard } from "@components/ui";
import useThingsStats from "@hooks/classic/useThingStats";
import { THING_VAR } from "@utils/featureLabels";
import { CircleCheck, XCircle, Cpu, CircleAlertIcon } from "lucide-react";

const CardSection = () => {
  const { data: thingStats } = useThingsStats({ enabled: true });

  return (
    <section className="flex w-full flex-wrap gap-4">
      <StatCard
        title={`Online ${THING_VAR}s`}
        value={thingStats?.connected}
        colorScheme="success"
        icon={CircleCheck}
        description={`${((thingStats.connected / thingStats.total) * 100).toFixed(1)}%`}
      />
      <StatCard
        title={`Offline ${THING_VAR}s`}
        value={thingStats.disconnected}
        colorScheme="danger"
        icon={XCircle}
        description={`${((thingStats.disconnected / thingStats.total) * 100).toFixed(1)}%`}
      />
      <StatCard
        title={`Inactive ${THING_VAR}s`}
        value={thingStats.inactive}
        colorScheme="warning"
        icon={CircleAlertIcon}
        description={`${((thingStats.inactive / thingStats.total) * 100).toFixed(1)}%`}
      />
      <StatCard
        title={`Total ${THING_VAR}s`}
        value={thingStats.total}
        colorScheme="info"
        icon={Cpu}
      />
    </section>
  );
};

export default CardSection;
