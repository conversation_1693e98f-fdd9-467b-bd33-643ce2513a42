import { deleteMultiThing } from "@api/thing";
import Button from "@components/Button";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import Label from "@components/Label";
import SelectFilterData from "@components/SelectFilterData";
import { Checkbox } from "@components/shadcn/components/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@components/shadcn/components/dialog";
import { Card } from "@components/ui";
import useDebounce from "@frontend/shared/hooks/utils/useDebounce";
import useTablePagination from "@hooks/classic/useTablePagination";
import useThingList from "@hooks/classic/useThingList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useAppSelector } from "@src/store";
import { THING_DEFAULT_IMAGE } from "@utils/deviceMapping";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Search, Trash } from "lucide-react";
import { useState } from "react";
import Input from "../../../components/Input";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import useTableSort from "../../../hooks/useTableSort";

const THINGS_FILTERS = [
  { label: "All", value: "all" },
  { label: "Online", value: "connected" },
  { label: "Offline", value: "disconnected" },
  { label: "Inactive", value: "inactive" }
];

const TableSection = () => {
  const {
    limit,
    page,
    setPage,
    setLimit,
    searchQuery,
    setSearchQuery,
    searchParams,
    addSearchParams
  } = useTablePagination();
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();
  const [deleteThingList, setDeleteThingList] = useState<
    {
      thingName: string;
      type: string;
      productName: string;
    }[]
  >([]);
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [deleteModalData, setDeleteModalData] = useState(null);
  const [showDeletedInfo, setShowDeletdInfo] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(searchParams.get("filter") || "all");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const tenant = useAppSelector((state) => state.user.tenant);
  const { data: thingList, isLoading: thingListLoading } = useThingList({
    search: debouncedSearchQuery,
    limit,
    page,
    status: selectedFilter === "all" ? "" : selectedFilter
  });

  const setFilter = (value: string) => {
    addSearchParams({ filter: value });
  };

  const handleDeleteThings = async () => {
    setLoadingBtn(true);

    const resp = await deleteMultiThing({ things: deleteThingList });
    setLoadingBtn(false);

    if (resp.status === "Success") {
      setDeleteModalData(resp.data);
      setShowDeletdInfo(true);
      setShowDeletePrompt(false);
      setLimit(DEFAULT_PAGE_COUNT);
      setDeleteThingList([]);

      queryClient.invalidateQueries({ queryKey: ["things-list"] });
      showSuccessToast("Things deleted successfully");
    } else {
      showErrorToast(resp.message);
    }
  };

  return (
    <Card className="space-y-4">
      <h3 className="heading-2">{THING_VAR}s List</h3>
      <div className="flex items-center  gap-4">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setPage(1);
          }}
          className="flex-1"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        {!!deleteThingList.length && (
          <Button
            onClick={() => setShowDeletePrompt(true)}
            startIcon={<Trash size={BUTTON_ICON_SIZE} />}
            color="red"
          >
            Delete {THING_VAR} ({deleteThingList.length})
          </Button>
        )}
        <SelectFilterData
          options={THINGS_FILTERS}
          selected={selectedFilter}
          setSelected={(value) => {
            setSelectedFilter(value);
            setFilter(value);
            setPage(1);
          }}
          className="w-40"
        />
      </div>
      <Table
        head={
          <>
            <TableHead>
              <Checkbox
                className="!bg-gray-100  "
                onCheckedChange={(value) => {
                  if (value) {
                    setDeleteThingList(
                      thingList?.things.map((thing) => ({
                        thingName: thing.thingName,
                        type: thing.type,
                        productName: thing.productName
                      })) || []
                    );
                  } else {
                    setDeleteThingList([]);
                  }
                }}
              />
            </TableHead>
            <TableHead>No .</TableHead>
            <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
            {tenant?.subscriptionType === "managed" && (
              <TableHead onSort={(order) => sort("type", order)}>{THING_VAR} Type</TableHead>
            )}
            <TableHead onSort={(order) => sort("productName", order)}>{PRODUCT_VAR} Name</TableHead>
            <TableHead onSort={(order) => sort("displayName", order)}>Display Name</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          thingListLoading ? (
            <TableRowsSkeleton />
          ) : !thingList?.things.length ? (
            <DataNotFound title={`No ${THING_VAR} Available`} isTable />
          ) : (
            <>
              {thingList.things.toSorted(sortFn).map((thing, i) => (
                <tr
                  className="cursor-pointer"
                  onClick={() => navigate(`${thing.thingName}`)}
                  key={thing.thingName}
                  data-state={
                    deleteThingList.some((item) => item.thingName === thing.thingName)
                      ? "selected"
                      : ""
                  }
                >
                  <TableRow onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      disabled={
                        deleteThingList.length >= 10 &&
                        !deleteThingList.some((item) => item.thingName === thing.thingName)
                      }
                      checked={deleteThingList.some((item) => item.thingName === thing.thingName)}
                      onCheckedChange={(value) => {
                        if (value) {
                          setDeleteThingList((prev) => [
                            ...prev,
                            {
                              thingName: thing.thingName,
                              productName: thing.productName,
                              type: thing.type
                            }
                          ]);
                        } else {
                          setDeleteThingList((prev) =>
                            prev.filter((item) => item.thingName !== thing.thingName)
                          );
                        }
                      }}
                    />
                  </TableRow>
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>
                    <div className="flex items-center gap-2">
                      <div
                        className={clsx(
                          "mr-2  !w-3 !h-3 shrink-0",
                          thing.status === "connected" ? "dot-pulse-success" : "dot-pulse-danger"
                        )}
                      />
                      <img
                        className="object-contain size-10 rounded-lg"
                        alt={thing.thingName}
                        src={thing.imgURL || THING_DEFAULT_IMAGE}
                      />

                      <div className="">
                        <p>{thing.thingName}</p>
                        <p className="text-xs text-muted-foreground">{thing.displayName}</p>
                      </div>
                    </div>
                  </TableRow>
                  {tenant?.subscriptionType === "managed" && (
                    <TableRow>
                      <Label
                        text={thing.type}
                        color={thing.type === "standard" ? "green" : "blue"}
                      />
                    </TableRow>
                  )}
                  <TableRow>{thing.productName}</TableRow>
                  <TableRow>{thing.displayName || "N/A"}</TableRow>
                  <TableRow>{convetUTCToLocal(thing.createdAt)}</TableRow>

                  <TableRow>{convetUTCToLocal(thing.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        checkable={deleteThingList}
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: thingList?.page || 1
        }}
      />

      <ConfirmPrompt
        validate
        show={showDeletePrompt}
        onCancel={() => setShowDeletePrompt(false)}
        loading={loadingBtn}
        onConfirm={() => {
          handleDeleteThings();
        }}
      />
      <Dialog
        open={showDeletedInfo}
        onOpenChange={(open) => {
          setShowDeletdInfo(open);
        }}
      >
        <DialogContent className="!max-w-[45rem] w-[45rem] ">
          <DialogHeader>
            <DialogTitle>Delete {THING_VAR} Status</DialogTitle>
          </DialogHeader>

          <Table
            className="!max-h-[80vh] overflow-auto"
            head={
              <>
                <TableHead>{THING_VAR}</TableHead>
                <TableHead>Status</TableHead>
              </>
            }
            body={
              <>
                {deleteModalData?.successes.map((thing) => (
                  <tr className="cursor-pointer" key={thing.thingName}>
                    <TableRow>{thing.thingName}</TableRow>
                    <TableRow>
                      <Label
                        color={thing.status === "success" ? "green" : "yellow"}
                        text={thing.status}
                      />
                    </TableRow>
                  </tr>
                ))}
                {deleteModalData?.failures.map((thing) => (
                  <tr className="bg-white" key={thing.thingName}>
                    <TableRow>{thing.thingName}</TableRow>
                    <TableRow>
                      <Label color="red" text="Failed" />
                    </TableRow>
                  </tr>
                ))}
              </>
            }
          />
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default TableSection;
