import ActionButton from "@components/ActionButton";
import Button from "@components/Button";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import HeaderSection from "@components/layout/HeaderSection";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card, StatCard } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useDeleteBulkCreateEntry from "@hooks/geo/mutations/useDeleteBulkCreateEntry";
import useBulkCreateStats from "@hooks/geo/useBulkThingCreateStats";
import useBulkUpdateGoingOn from "@hooks/geo/useBulkUpdateGoingOn";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { DEFAULT_PAGE_COUNT } from "@utils/utilities";
import { CheckCircle, Clock, Cpu, Plus, XCircle } from "lucide-react";
import { useState } from "react";

function paginateArray(array, page, limit) {
  // Calculate the starting index
  const startIndex = (page - 1) * limit;
  // Calculate the ending index (exclusive)
  const endIndex = startIndex + limit;
  // Slice the array from startIndex to endIndex
  return array.slice(startIndex, endIndex);
}
const BulkThingsList = () => {
  const [sortFn, sort] = useTableSort();
  const { data, isLoading } = useBulkUpdateGoingOn();
  const { data: _stats } = useBulkCreateStats();
  const navigate = useCustomNavigate();
  const [deleteBulkId, setDeleteBulkId] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);

  const deleteMutation = useDeleteBulkCreateEntry(() => {
    setDeleteBulkId("");
  });

  const { data: permissions } = useUserGroupPermissions();

  const stats = {
    success: _stats?.success || 0,
    failed: _stats?.failure || 0,
    total: _stats?.total || 0,
    pending: _stats?.pending || 0
  };
  return (
    <main className=" space-y-4">
      <HeaderSection
        title={`Bulk Creates`}
        description="Manage your Bulk Thing Create"
        actions={
          <Button
            onClick={() => navigate(`/things/addThing?buildType=multiple`)}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          >
            Create Bulk Things 
          </Button>
        }
      />
      <section className="flex w-full flex-wrap gap-4">
        <StatCard
          title={`Successful ${THING_VAR}s`}
          value={stats.success}
          colorScheme="success"
          icon={CheckCircle}
          description={`${((stats.success / stats.total) * 100).toFixed(1)}%`}
        />
        <StatCard
          title={`Failed ${THING_VAR}s`}
          value={stats.failed}
          colorScheme="danger"
          icon={XCircle}
          description={`${((stats.failed / stats.total) * 100).toFixed(1)}%`}
        />
        <StatCard
          title={`Pending ${THING_VAR}s`}
          value={stats.pending}
          colorScheme="default"
          icon={Clock}
          description={`${((stats.pending / stats.total) * 100).toFixed(1)}%`}
        />
        <StatCard title={`Total ${THING_VAR}s`} value={stats.total} colorScheme="info" icon={Cpu} />
      </section>
      <Card className="space-y-4">
        <h3 className="heading-2">Bulk Thing List</h3>
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead>Job Name</TableHead>
              <TableHead onSort={(order) => sort("targetType", order)}>Completed</TableHead>
              <TableHead onSort={(order) => sort("version", order)}>Total {THING_VAR}s</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Timestamp</TableHead>
              <TableHead>{`${""}`}</TableHead>
            </>
          }
          body={
            isLoading ? (
              <TableRowsSkeleton />
            ) : !data?.length ? (
              <DataNotFound title="No Bulk Create Found" isTable />
            ) : (
              <>
                {/* FILTER NEED TO BE CHANGE  */}
                {paginateArray(data, page, limit)
                  .sort(sortFn)
                  .map((bulkJob, i) => (
                    <tr
                      key={bulkJob.id}
                      onClick={() => navigate(`${bulkJob.id}`)}
                      className="cursor-pointer"
                    >
                      <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                      <TableRow title>{bulkJob.fileName || "N/A"}</TableRow>

                      <TableRow>
                        <span className=" font-semibold mr-1">{bulkJob.count}</span> Completed
                      </TableRow>

                      <TableRow>
                        <span className="font-semibold mr-1">{bulkJob.expectedCount}</span>
                        Expected
                      </TableRow>

                      <TableRow>{convetUTCToLocal(bulkJob.createdAt)}</TableRow>
                      <TableRow>
                        <ActionButton
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteBulkId(bulkJob.id);
                          }}
                          disabled={permissions.thing !== "write"}
                          type="delete"
                        />
                      </TableRow>
                    </tr>
                  ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: Math.ceil(data?.length / limit)
          }}
        />
      </Card>
      <ConfirmPrompt
        validate
        show={Boolean(deleteBulkId)}
        onCancel={() => setDeleteBulkId("")}
        loading={deleteMutation.isPending}
        onConfirm={() => {
          deleteMutation.mutate(deleteBulkId);
        }}
      />
    </main>
  );
};

export default BulkThingsList;
