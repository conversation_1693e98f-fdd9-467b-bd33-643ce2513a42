import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ubar<PERSON><PERSON>,
  MenubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import { ClassicProduct } from "@/index";
import { useDeviceContext } from "./DeviceProvider";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useThingCertificateDetail from "@hooks/security/useThingCertificateDetail";
import Button from "@components/Button";
import { Box, ChevronDown, Clipboard, Download, Edit2, Edit3, Trash } from "lucide-react";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import { certDownload } from "@utils/utilities";

const ActionButton = ({
  deviceInfo,
  productDetails,
  setShowDeletePrompt,
  setShowRebootPrompt,
  onEdit
}: {
  deviceInfo: any;
  productDetails?: ClassicProduct;
  setShowDeletePrompt: any;
  setShowRebootPrompt: any;
  onEdit: any;
}) => {
  const {setShowCreateCertificate } = useDeviceContext();

  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();
  const { data: certificateDetail, isLoading: isCertificateLoading } = useThingCertificateDetail({
    certificateId: deviceInfo.certificateId,
    enabled: Boolean(deviceInfo?.certificateId)
  });

  return (
    <Menubar className="!bg-transparent border-none p-0 ">
      <MenubarMenu>
        <MenubarTrigger>
          <Button endIcon={<ChevronDown size={BUTTON_ICON_SIZE} />}>Actions</Button>
        </MenubarTrigger>
        <MenubarContent>
          <MenubarItem
            onClick={()=> 
                onEdit(deviceInfo,productDetails?.productType)
              }
            disabled={permissions.thing !== "write"}
          >
            <Edit2 size={BUTTON_ICON_SIZE} className="mr-2" />
            Edit
          </MenubarItem>
          {/* <MenubarItem
            disabled={deviceInfo?.type === "standard" || permissions.thing !== "write"}
            onClick={() => {
              setShowRebootPrompt(true);
            }}
          >
            <RefreshCcw size={BUTTON_ICON_SIZE} className="mr-2" />
            Reboot
          </MenubarItem> */}

          <MenubarItem
            onClick={() => {
              setShowDeletePrompt(true);
            }}
            data-testid="Delete"
            disabled={permissions.thing !== "write"}
          >
            <Trash size={BUTTON_ICON_SIZE} className="mr-2" />
            Delete
          </MenubarItem>
          <MenubarItem onClick={() => navigate("ota")}>
            <Box size={BUTTON_ICON_SIZE} className="mr-2" />
            OTA Updates
          </MenubarItem>
          <MenubarItem onClick={() => navigate(`tickets`)}>
            <Clipboard size={BUTTON_ICON_SIZE} className="mr-2" />
            Tickets
          </MenubarItem>
          {productDetails?.authentication?.type === "tls" && (
            <>
              <hr className="hr !my-1" />
              <MenubarItem
                disabled={isCertificateLoading || !deviceInfo.certificateId}
                onClick={() => {
                  certDownload(null, certificateDetail);
                }}
              >
                <Download size={BUTTON_ICON_SIZE} className="mr-2" />
                Download Certificate
              </MenubarItem>
              <MenubarItem
                disabled={deviceInfo.certificateId || permissions.tls !== "write"}
                onClick={() => {
                  setShowCreateCertificate(true);
                }}
              >
                <Edit3 size={BUTTON_ICON_SIZE} className="mr-2" />
                Generate Certificate
              </MenubarItem>
            </>
          )}

          <MenubarItem onClick={() => navigate(`digitaltwin`)}>
            <Box size={BUTTON_ICON_SIZE} className="mr-2" />
            Digital Twin
          </MenubarItem>
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  );
};

export default ActionButton;
