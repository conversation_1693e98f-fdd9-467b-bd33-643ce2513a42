import { ScrollArea } from "@components/shadcn/components/scroll-area";
import { Tabs, TabsContent } from "@components/shadcn/components/tabs";
import TabsListV2 from "@components/Tabs/TabsListV2";
import TabsTriggerV2 from "@components/Tabs/TabsTriggerV2";
import useProductDetails from "@hooks/geo/useProductDetails";
import useThingDetails from "@hooks/geo/useThingDetails";
import { useRemainingHeight } from "@hooks/useRemaningHeight";
import ThingLoggerTab from "@src/pages/UserTypes/Tracking/HomeSection/DeviceDetialSection/ThingLoggerTab";
import SelectDataTime from "@src/pages/UserTypes/Tracking/HomeSection/SelectDataTime";
import { ElementRef, useRef } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import MetricWidgetGrid from "../../layout/MetricWidgetGrid";
import WidgetSectionHeaderRight from "../../layout/WidgetSectionHeaderRight";
import DeviceShadowTab from "./DeviceShadowTab";
import DeviceTimeSeriesTable, { RefetchTimeSeries } from "./DeviceTimeSeriesTable";
import LocationTab from "./LocationTab";
import PolicyUpdateTab from "./PolicyUpdateTab";
import ThingOverviewTab from "./ThingOverviewTab";

type TabType = "Location" | "Overview" | "Widgets" | "Policy" | "Logs" | "Shadow" | "TimeSeries";

const ThingDetailsTabs = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const currentTabView = searchParams.get("tab") || "Overview";
  const handleChange = (newValue: TabType) => {
    setSearchParams({ tab: newValue });
  };
  const thingName = useParams().thingName!;
  const { data: device } = useThingDetails({ thingName });

  const { data: productDetails } = useProductDetails({ thingName });

  if (!device) {
    return null;
  }

  const hasShadow = productDetails?.dataManagement?.includes("shadow");
  const hasTimeSeries = productDetails?.dataManagement?.includes("timeSeries");

  const { ref, remainingHeight } = useRemainingHeight();

  const TAB_HEADER_HEIGHT = 56;
  const tabHeight = remainingHeight - TAB_HEADER_HEIGHT;

  const metricWidgetRef = useRef<ElementRef<typeof MetricWidgetGrid>>(null);

  return (
    <Tabs
      value={currentTabView}
      ref={ref}
      className="relative  flex-grow flex flex-col overflow-hidden"
    >
      <div className="flex items-center justify-between sticky gap-4 top-0 z-20 backdrop-blur-sm">
        <TabsListV2 className="mb-2 gap-3 flex-1">
          <TabsTriggerV2 value="Overview" onClick={() => handleChange("Overview")}>
            Overview
          </TabsTriggerV2>
          <TabsTriggerV2 value="Widgets" onClick={() => handleChange("Widgets")}>
            Insights
          </TabsTriggerV2>
          {device?.meta?.latitude && device?.meta?.longitude && (
            <TabsTriggerV2 value="Location" onClick={() => handleChange("Location")}>
              Location
            </TabsTriggerV2>
          )}

          <TabsTriggerV2 value="Policy" onClick={() => handleChange("Policy")}>
            Policy
          </TabsTriggerV2>

          <TabsTriggerV2 value="Logs" onClick={() => handleChange("Logs")}>
            Logs
          </TabsTriggerV2>

          {hasShadow && (
            <TabsTriggerV2 value="Shadow" onClick={() => handleChange("Shadow")}>
              Shadow
            </TabsTriggerV2>
          )}
          {hasTimeSeries && (
            <TabsTriggerV2 value="TimeSeries" onClick={() => handleChange("TimeSeries")}>
              Time Series
            </TabsTriggerV2>
          )}
        </TabsListV2>

        {currentTabView === "Widgets" && (
          <WidgetSectionHeaderRight
            addNewWidget={() => {
              metricWidgetRef.current?.addNewWidget();
            }}
            saveWidgetNewLayout={() => {
              metricWidgetRef.current?.saveWidgetNewLayout();
            }}
          />
        )}
        {(currentTabView === "Logs" || currentTabView === "TimeSeries") && (
          <div className="ml-auto absolute top-1.5 right-0 flex items-center gap-4">
            {currentTabView === "TimeSeries" && <RefetchTimeSeries thingName={thingName} />}
            <SelectDataTime />
          </div>
        )}
      </div>
      <TabsContent value="Overview">
        <ThingOverviewTab device={device} />
      </TabsContent>
      {device?.meta?.latitude && device?.meta?.longitude && (
        <TabsContent value="Location">
          <LocationTab deviceInfo={device} />
        </TabsContent>
      )}
      <TabsContent value="Widgets">
        <MetricWidgetGrid
          ref={metricWidgetRef}
          productName={device.productName}
          thingName={device.thingName}
        />
      </TabsContent>
      <TabsContent value="Policy" className=" flex-grow">
        <ScrollArea className="flex-grow">
          <PolicyUpdateTab />
        </ScrollArea>
      </TabsContent>
      <TabsContent value="Logs" className="flex-1 mb-2">
        <ScrollArea className="flex-grow" style={{ height: tabHeight }}>
          <ThingLoggerTab thingName={device.thingName} />
        </ScrollArea>
      </TabsContent>
      <TabsContent value="Shadow" className="flex-1 h-[48vh] overflow-y-auto">
        <DeviceShadowTab />
      </TabsContent>
      <TabsContent
        style={{ maxHeight: tabHeight }}
        value="TimeSeries"
        className="flex-1 relative mb-4 overflow-y-auto border card-border rounded-md"
      >
        <DeviceTimeSeriesTable thingName={thingName} productName={device.productName} />
      </TabsContent>
    </Tabs>
  );
};

export default ThingDetailsTabs;
