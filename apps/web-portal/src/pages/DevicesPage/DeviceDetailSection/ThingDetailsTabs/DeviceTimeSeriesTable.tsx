import { DashboardGraphItem } from "@/index";
import ComponentErrorBoundary from "@components/ComponentErrorBoundary";
import DataNotFound from "@components/DataNotFound";
import { Card } from "@components/ui";
import useProductConfig from "@hooks/geo/useProductConfig";
import useSensorTableData from "@hooks/timeseries/useSensorTableData";
import useSensorDataSorting from "@hooks/useSensorDataSorting";
import { CircularProgress } from "@mui/material";
import DashboardTable from "@src/pages/MonitorPage/layout/DashboardTable";
import { useAppSelector } from "@src/store";
import { CellContext } from "@tanstack/react-table";
import { LocateFixed, Locate, RefreshCw } from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";

const DeviceTimeSeriesTable = ({
  thingName,
  productName
}: {
  thingName: string;
  productName?: string;
}) => {
  const duration = useAppSelector((state) => state.geoDevice.duration);
  const [isLoading, setLoading] = useState(true);

  const { data: productConfig } = useProductConfig({
    productName: productName!,
    enabled: Boolean(productName),
    excludeOptions: true
  });

  const { sort, setSort } = useSensorDataSorting();

  const sorting = useMemo(() => {
    return sort[thingName] || [];
  }, [sort, thingName]);

  const setSorting = useCallback(
    (sort: any) => {
      if (typeof sort === "function") {
        const newSort = sort();
        setSort(newSort, thingName);
      }
    },
    [thingName, setSort]
  );

  const filters = useMemo(() => {
    const res: DashboardGraphItem["filters"] = [
      {
        field: "thingName",
        operation: "is",
        value: [thingName],
        enabled: true,
        id: thingName!
      }
    ];
    return res;
  }, [thingName]);
  const featureType = useAppSelector(({ user }) => user.tenant?.featureType);
  const {
    isLoading: tableLoading,
    data: paginatedData,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage
  } = useSensorTableData({
    duration,
    filters,
    sort: sorting,
    isGeoTenant: featureType === "geo"
  });

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const data = useMemo(() => {
    const items = paginatedData?.pages.flatMap((page) => page.data);
    return items;
  }, [paginatedData]);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    });
  }, [paginatedData]);

  if (isLoading || tableLoading) {
    return (
      <div className="w-full flex flex-col gap-4 ">
        {[...Array(10).keys()].map((item) => (
          <div key={item} className="animate-pulse  gap-2 w-full grid grid-cols-8">
            <div className="bg-gray-300 dark:bg-gray-800 h-10 rounded" />
            <div className="bg-gray-300 dark:bg-gray-800 h-10 rounded col-span-2" />
            <div className="bg-gray-300 dark:bg-gray-800 h-10 rounded" />
            <div className="bg-gray-300 dark:bg-gray-800 h-10 rounded" />
            <div className="bg-gray-300 dark:bg-gray-800 h-10 rounded col-span-3" />
          </div>
        ))}
      </div>
    );
  }

  if (!isLoading && !data?.length) {
    return (
      <DataNotFound content=" Time series data not available for this device in selected Interval" />
    );
  }

  return (
    <ComponentErrorBoundary>
      <DashboardTable
        sticky
        dataset={data || []}
        id={thingName!}
        expandPayload
        hidePagination
        isRefreshing={isFetchingNextPage}
        showAllColumns
        onReachEnd={() => {
          handleLoadMore();
        }}
        sort={sorting}
        visibilityConfig={productConfig?.tsTableRows || []}
        onSortingChange={setSorting}
        showSettings
        options={productConfig?.tsTableOptions || []}
        extraColumns={
          featureType === "geo"
            ? [
                {
                  id: "Locate",
                  size: 1,
                  enableResizing: false,
                  header: "Locate ",
                  accessorFn: (row, index) => {
                    return {
                      lat: row.payload.latitude,
                      long: row?.payload?.longitude,
                      uniqueId: `${row.uniqueId}_${index}`
                    };
                  },
                  // eslint-disable-next-line no-use-before-define
                  cell: LocateTimeSeriesPoint
                }
              ]
            : undefined
        }
      />
    </ComponentErrorBoundary>
  );
};

const LocateTimeSeriesPoint = (info: CellContext<unknown, any>) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const { getValue } = info;

  const value = getValue();
  const timeId = searchParams.get("timeId");

  if (!timeId || String(value.uniqueId) !== timeId) {
    return (
      <Locate
        className="!h-4 cursor-pointer"
        onClick={() => {
          setSearchParams((prev) => {
            prev.set("timeId", value.uniqueId);
            prev.set("lat", value.lat);
            prev.set("long", value.long);
            return prev;
          });
        }}
      />
    );
  }

  return (
    <LocateFixed
      className="!h-4 cursor-pointer"
      onClick={() => {
        setSearchParams((prev) => {
          prev.delete("timeId");
          prev.delete("lat");
          prev.delete("long");
          return prev;
        });
      }}
    />
  );
};

const RefetchTimeSeriesWOMemo = ({ thingName }: { thingName: string }) => {
  const duration = useAppSelector((state) => state.geoDevice.duration);
  const { sort } = useSensorDataSorting();

  const sorting = useMemo(() => {
    return sort[thingName] || [];
  }, [sort, thingName]);

  const filters = useMemo(() => {
    const res: DashboardGraphItem["filters"] = [
      {
        field: "thingName",
        operation: "is",
        value: [thingName],
        enabled: true,
        id: thingName!
      }
    ];
    return res;
  }, [thingName]);
  const featureType = useAppSelector(({ user }) => user.tenant?.featureType);

  const {
    refetch: refetchTimeSeries,
    isLoading,
    isRefetching
  } = useSensorTableData({
    duration,
    filters,
    enabled: false,
    isGeoTenant: featureType === "geo",
    sort: sorting
  });

  return (
    <Card
      className="cursor-pointer py-1.5 flex items-center justify-center"
      onClick={() => refetchTimeSeries()}
    >
      {isLoading || isRefetching ? (
        <CircularProgress className="!h-5 !w-5" />
      ) : (
        <RefreshCw className="!h-5 !w-5" />
      )}
    </Card>
  );
};

export const RefetchTimeSeries = memo(RefetchTimeSeriesWOMemo);

export default memo(DeviceTimeSeriesTable);
