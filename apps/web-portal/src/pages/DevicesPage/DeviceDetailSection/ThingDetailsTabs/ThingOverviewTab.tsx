import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import { LoadingSkeleton } from "@components/Skeletons/LoadingSkeletons";
import { Card } from "@components/ui";
import { DATACELL_ICON_SIZE } from "@frontend/shared/config/defaults";
import useProductDetails from "@hooks/geo/useProductDetails";
import useSensorTableData from "@hooks/timeseries/useSensorTableData";
import { MonitorIconOptions } from "@src/pages/ProductPage/ProductDetailSection/DetailsComponents/DisplayFieldsData";
import { useAppSelector } from "@src/store";
import { AlertTriangle, CheckCircle, Hash } from "lucide-react";
import DeviceDetailCard from "../DeviceDetailCard";

const colorOptions = [
  "bg-red-500/10 text-red-500",
  "bg-orange-500/10 text-orange-500",
  "bg-amber-500/10 text-amber-500",
  "bg-lime-500/10 text-lime-500",
  "bg-green-500/10 text-green-500",
  "bg-teal-500/10 text-teal-500",
  "bg-purple-500/10 text-purple-500",
  "bg-pink-500/10 text-pink-500"
];

const ThingOverviewTab = ({ device }) => {
  return (
    <div className="grid grid-cols-12 gap-4 items-start">
      <div className="col-span-8 space-y-4">
        <Card className="col-span-8">
          <DeviceDetailCard deviceInfo={device} />
        </Card>
        <ThingReadingTab thingName={device.thingName} productName={device?.productName} />
      </div>
      <div className="col-span-4 space-y-4">
        <Card className="space-y-4">
          <h3 className="heading-2">Tags</h3>
          <div className="space-y-3">
            {device.meta && !!Object.entries(device.meta).length ? (
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(device.meta).map(([key, value]) => (
                  <DetailsCell title={key} data={value} icon={<Hash size={DATACELL_ICON_SIZE} />} />
                ))}
              </div>
            ) : (
              <DataNotFound
                Icon={Hash}
                title="No Tags found"
                content=" Edit thing to add tags to this device to track it more effectively"
              />
            )}
          </div>
        </Card>
        <Card className="space-y-4">
          <h3 className="heading-2">Recent Alerts</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-3 status-warning rounded-lg">
              <AlertTriangle className="w-4 h-4 complexity-medium mt-0.5" />
              <div>
                <p className="complexity-medium text-sm font-medium">High Temperature</p>
                <p className="text-muted-foreground text-xs">Threshold exceeded at 28.5°C</p>
                <p className="text-muted-foreground text-xs">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 status-success rounded-lg">
              <CheckCircle className="w-4 h-4 complexity-simple mt-0.5" />
              <div>
                <p className="complexity-simple text-sm font-medium">Calibration Complete</p>
                <p className="text-muted-foreground text-xs">All sensors calibrated successfully</p>
                <p className="text-muted-foreground text-xs">1 day ago</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ThingOverviewTab;

const ThingReadingTab = ({
  thingName,
  productName
}: {
  thingName: string;
  productName: string;
}) => {
  const filters = [
    {
      field: "thingName",
      operation: "is",
      value: [thingName],
      enabled: true,
      id: thingName!
    }
  ];
  const { data: productDetails, isLoading: productLoading } = useProductDetails({
    productName: productName,
    enabled: Boolean(productName)
  });
  const duration = useAppSelector((state) => state.geoDevice.duration);
  const { isLoading: tableLoading, data: paginatedData } = useSensorTableData({
    filters,
    duration,
    limit: 1,
    isGeoTenant: false
  });

  const payload = paginatedData?.pages?.[0]?.data?.[0]?.payload ?? {};
  const extractedData = productDetails?.monitorDataPoints.map((point) => ({
    ...point,
    value: payload[point.field] ?? undefined
  }));

  const getColorForField = (field: string) => {
    let hash = 0;
    for (let i = 0; i < field.length; i++) {
      hash = field.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % colorOptions.length;
    return colorOptions[index];
  };

  const iconRenderer = (icon: string) => {
    const Icon = MonitorIconOptions.find((option) => option.name === icon)?.icon;
    return Icon ? <Icon className="w-5 h-5 " /> : <Hash className="w-5 h-5 " />;
  };
  return (
    <Card className="col-span-8 space-y-4">
      <div>
        <h3 className="heading-2">Current Readings</h3>
        <p className="text-muted-foreground text-sm">Real-time sensor data and measurements</p>
      </div>
      <div className="space-y-4">
        <div>
          {productLoading || tableLoading ? (
            <div className="grid  gap-4">
              {[...Array(2)].map((_, idx) => (
                <LoadingSkeleton key={idx} gridRows={1} columns={2} variant="grid" />
              ))}
            </div>
          ) : !productDetails?.monitorDataPoints.length || !extractedData ? (
            <DataNotFound
              Icon={Hash}
              title="No Data Points Added"
              content="Please add data points to the product to view the current readings"
            />
          ) : (
            <div className="grid grid-cols-2 gap-4">
              {extractedData.map((points) => (
                <Card variant="second" className="flex items-center space-x-3 ">
                  <div
                    className={`w-10 h-10 rounded-lg flex items-center justify-center ${getColorForField(points.field)}`}
                  >
                    {iconRenderer(points.icon)}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {points.displayName || points.field}
                    </p>
                    <p className="text-2xl font-bold">
                      {typeof Number(points.value) === "number"
                        ? Number(points.value).toFixed(2)
                        : points.value}
                      <span className="ml-2 text-muted-foreground text-lg">
                        {points.value && points.unit}
                      </span>
                    </p>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};
