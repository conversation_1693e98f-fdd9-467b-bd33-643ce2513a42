import ActionButton from "@components/ActionButton";
import { Card } from "@components/ui";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import PolicyEditor from "@components/PolicyEditor";
import { Tabs, TabsList, TabsTrigger } from "@components/shadcn/components/tabs";
import Table, { TableHead, TableRow } from "@components/Table";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import usePolicyDetail from "@hooks/security/usePolicyDetail";
import useUpdatePolicy from "@hooks/security/useUpdatePolicy";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Check, Hash, X, Edit2, Plus, Trash } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Button from "../../../../components/Button";
import Dropdown from "../../../../components/Dropdown";
import Input from "../../../../components/Input";
import { showSuccessToast } from "../../../../utils";
import {
  topicJsonToObject,
  topicObjectToJson
} from "../../../SecurityPage/PolicyTemplate/utilities";

const TopicTableRow = ({ isEdit, topic, setDeviceTopics, removeTopicHandler, index }) => {
  return isEdit ? (
    <tr key={topic.id}>
      <TableRow>{index + 1}</TableRow>
      <TableRow>
        <Input
          placeholder="topic"
          className="w-full"
          small
          onChange={(e) => {
            setDeviceTopics((prev) =>
              prev.map((topicField) =>
                topicField.id === topic.id ? { ...topicField, topic: e.target.value } : topicField
              )
            );
          }}
          value={topic.topic}
        />
      </TableRow>
      <TableRow>
        {" "}
        <Dropdown
          onChange={(option) => {
            setDeviceTopics((prev) =>
              prev.map((topicField) =>
                topicField.id === topic.id ? { ...topicField, action: option } : topicField
              )
            );
          }}
          value={topic.action}
          options={["publish", "subscribe", "all"]}
          placeHolder="Select Action Type"
          required
        />
      </TableRow>
      <TableRow>
        {" "}
        <Dropdown
          onChange={(option) => {
            setDeviceTopics((prev) =>
              prev.map((topicField) =>
                topicField.id === topic.id ? { ...topicField, permission: option } : topicField
              )
            );
          }}
          value={topic.permission}
          options={["allow", "deny"]}
          placeHolder="Select Permission Type"
          required
        />
      </TableRow>
      <TableRow className="text-center">
        <ActionButton
          type="delete"
          Icon={Trash}
          onClick={() => {
            removeTopicHandler(topic.id);
          }}
        />
      </TableRow>
    </tr>
  ) : (
    <tr key={topic.topic}>
      <TableRow>{index + 1}</TableRow>
      <TableRow>{topic.topic}</TableRow>
      <TableRow>{topic.action}</TableRow>
      <TableRow>{topic.permission}</TableRow>
    </tr>
  );
};

const PolicyUpdateTab = () => {
  const [topicView, setTopicView] = useState("basic");
  const [basicTopicEdit, setBasicTopicEdit] = useState(false);
  const [rawTopicData, setRawTopicData] = useState("");
  const [deviceTopics, setDeviceTopics] = useState([]);
  const { data: permissions } = useUserGroupPermissions();
  const deviceId = useParams().thingName!;

  const { data: devicePolicy, isLoading: isPolicyLoading } = usePolicyDetail({
    thingName: deviceId || "",
    enabled: Boolean(deviceId)
  });

  const updatePolicyMutation = useUpdatePolicy({
    onSuccess: () => {
      showSuccessToast("Policy updated successfully");
      queryClient.invalidateQueries({ queryKey: ["policy-detail", deviceId] });
      setBasicTopicEdit(false);
    }
  });

  useEffect(() => {
    if (devicePolicy?.topics) {
      setDeviceTopics(topicJsonToObject(JSON.stringify(devicePolicy.topics)));
    }
  }, [devicePolicy]);

  const removeTopicHandler = (topicId: number) => {
    setDeviceTopics((prev) => prev.filter((topic) => topic.id !== topicId));
  };
  const handlePolicyUpdate = async (e) => {
    e.preventDefault();
    const transformedTopics =
      topicView === "basic"
        ? deviceTopics.map((topic) => ({
            topic: topic.topic,
            permission: topic.permission,
            action: topic.action
          }))
        : JSON.parse(rawTopicData);
    updatePolicyMutation.mutate({
      thingName: deviceId,
      topics: transformedTopics
    });
  };

  const resetTopic = () => {
    setBasicTopicEdit(false);
    setDeviceTopics(topicJsonToObject(JSON.stringify(devicePolicy?.topics)));
  };

  return (
    <Card>
      <form className="w-full mb-2 space-y-4" onSubmit={handlePolicyUpdate}>
        {isPolicyLoading ? (
          <CardLoadingSkeleton className="w-full" />
        ) : !devicePolicy ? (
          <DataNotFound content="  Thing policy not found, create policy for this Thing." />
        ) : (
          <>
            <div className=" space-y-4">
              <div className="flex justify-between items-center">
                <HeadingIcon Icon={Hash} title="Topic Details" />

                <div className="flex gap-4 items-center">
                  {topicView === "basic" ? (
                    <>
                      {basicTopicEdit ? (
                        <>
                          <Button
                            startIcon={<Check size={BUTTON_ICON_SIZE} />}
                            color="blue"
                            loading={updatePolicyMutation.isPending}
                            type="submit"
                            noAccess={permissions.policy !== "write"}
                          >
                            Save
                          </Button>
                          <Button
                            onClick={resetTopic}
                            startIcon={<X size={BUTTON_ICON_SIZE} />}
                            color="gray"
                            type="button"
                          >
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <Button
                          onClick={() => {
                            setBasicTopicEdit(true);
                          }}
                          startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                          key="edit"
                          small
                          color="gray"
                          type="button"
                        >
                          Edit
                        </Button>
                      )}
                    </>
                  ) : (
                    <Button
                      startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                      loading={updatePolicyMutation.isPending}
                      type="submit"
                      color="gray"
                      noAccess={permissions.policy !== "write"}
                    >
                      Update
                    </Button>
                  )}
                  <Tabs defaultValue={topicView}>
                    <TabsList className="grid  grid-cols-2 ">
                      <TabsTrigger
                        value="basic"
                        onClick={() => {
                          setDeviceTopics(topicJsonToObject(rawTopicData));
                          setTopicView("basic");
                          resetTopic();
                        }}
                      >
                        Basic
                      </TabsTrigger>
                      <TabsTrigger
                        value="json"
                        onClick={() => {
                          setRawTopicData(topicObjectToJson(deviceTopics));
                          setTopicView("json");
                          resetTopic();
                        }}
                      >
                        Json
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </div>
              {topicView === "json" ? (
                <div className="w-full card-border rounded-lg overflow-hidden mt-2">
                  <PolicyEditor
                    height="50vh"
                    value={rawTopicData}
                    onChange={(val) => setRawTopicData(val || "")}
                  />
                </div>
              ) : (
                <div>
                  <Table
                    resizable={false}
                    head={
                      <>
                        <TableHead>No.</TableHead>
                        <TableHead>Topic</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Permission</TableHead>
                        {basicTopicEdit && <TableHead center>Action</TableHead>}
                      </>
                    }
                    body={
                      <>
                        {deviceTopics.map((topic, i) => (
                          <TopicTableRow
                            isEdit={basicTopicEdit}
                            topic={topic}
                            setDeviceTopics={setDeviceTopics}
                            removeTopicHandler={removeTopicHandler}
                            index={i}
                          />
                        ))}
                        {basicTopicEdit && (
                          <tr>
                            <td colSpan={100}>
                              <div
                                onClick={() =>
                                  setDeviceTopics((prev) => [
                                    ...prev,
                                    {
                                      topic: "",
                                      action: null,
                                      permission: null,
                                      id: Math.random()
                                    }
                                  ])
                                }
                                className="h-12 w-full center flex gap-4 cursor-pointer border border-dashed"
                              >
                                <Plus size={BUTTON_ICON_SIZE} className="" />
                                Add New Topic
                              </div>
                            </td>
                          </tr>
                        )}
                      </>
                    }
                  />
                </div>
              )}
            </div>
          </>
        )}
      </form>
    </Card>
  );
};

export default PolicyUpdateTab;
