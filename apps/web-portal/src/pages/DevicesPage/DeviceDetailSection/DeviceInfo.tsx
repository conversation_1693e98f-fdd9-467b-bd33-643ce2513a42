import Alert from "@components/Alert";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import useProductDetails from "@hooks/geo/useProductDetails";
import useThingOtaUpdate from "@hooks/thing/useThingOtaUpdate";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { THING_DEFAULT_IMAGE } from "@utils/deviceMapping";
import { Check } from "lucide-react";
import ActionButton from "./ActionButton";

import HeaderSection from "@components/layout/HeaderSection";
import ZoomableImage from "@components/ZoomableImage";
import { useRef } from "react";
import { useDeviceContext } from "./DeviceProvider";
import UpdateThingModal, { UpdateModalRefType } from "./UpdateThingModal";
import clsx from "clsx";

export type DeviceInfo = {
  certificateId: string;
  connectedTimeStamp: string;
  createdAt: string;
  disconnectedReason: string | null;
  disconnectedTimeStamp: string;
  displayName: string;
  gatewayName: string | null;
  imgURL: string;
  productName: string;
  status: "connected" | "disconnected" | string; // string fallback for other possible states
  thingGroup: string | null;
  thingName: string;
  type: "standard" | string;
  updatedAt: string;
  version: string;
  meta: Record<string, any>;
};

const DeviceInfo = ({
  deviceInfo,
  setShowDeletePrompt,
  setShowRebootPrompt
}: {
  deviceInfo: DeviceInfo;
}) => {
  const { setOpenOtaModal } = useDeviceContext();

  const updateModalRef = useRef<UpdateModalRefType>(null);
  const { data: productDetails } = useProductDetails({
    productName: deviceInfo?.productName,
    enabled: Boolean(deviceInfo?.productName)
  });
  const { data: thingOtaUpdated } = useThingOtaUpdate({
    thingName: deviceInfo.thingName
  });
  const handleEdit = (deviceInfo: DeviceInfo, productType: string) => {
    updateModalRef.current?.openDrawer(deviceInfo, productType);
  };

  return (
    <section className="space-y-4">
      <HeaderSection
        title={
          <div className="flex gap-4 items-center">
            <h3>{deviceInfo.thingName}</h3>
            <div
              className={clsx(
                deviceInfo.status === "connected" ? "dot-pulse-success" : "dot-pulse-danger"
              )}
            />
          </div>
        }
        description={deviceInfo.productName}
        startContent={
          <ZoomableImage>
            <img
              className="object-cover size-20 rounded-xl "
              src={deviceInfo.imgURL || THING_DEFAULT_IMAGE}
              alt="product-img"
            />
          </ZoomableImage>
        }
        actions={
          <ActionButton
            onEdit={handleEdit}
            deviceInfo={deviceInfo}
            productDetails={productDetails}
            setShowDeletePrompt={setShowDeletePrompt}
            setShowRebootPrompt={setShowRebootPrompt}
          />
        }
      />
      {thingOtaUpdated &&
        thingOtaUpdated[deviceInfo.thingName]?.description === "New version available" && (
          <Alert
            title={
              <div className="flex items-start justify-between">
                <span>Info</span>
                {deviceInfo.status !== "connected" && (
                  <ShadcnButton size="sm" onClick={() => setOpenOtaModal(true)}>
                    <Check className="h-4 w-4 mr-2" />
                    Update
                  </ShadcnButton>
                )}
              </div>
            }
            description={`New version ${
              thingOtaUpdated[deviceInfo.thingName]?.version
            } is available for this device. ${
              deviceInfo.status === "connected" ? "" : " Please active the device first for update."
            }`}
            type="warning"
          />
        )}

      <UpdateThingModal ref={updateModalRef} />
    </section>
  );
};

export default DeviceInfo;
