import DetailsCell from "@components/DetailsCell";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { DATACELL_ICON_SIZE } from "@utils/utilities";
import { AlertCircle, AtSign, Ban, Calendar, Clock, Share2 } from "lucide-react";

const DeviceDetailCard = ({ deviceInfo }) => {
  return (
    <section className="space-y-4 ">
      <h2 className="heading-2">Basic Details</h2>
      <div className="grid grid-cols-2 gap-4 ">
        <DetailsCell
          title="Display Name"
          data={deviceInfo.displayName}
          icon={<AlertCircle size={DATACELL_ICON_SIZE} />}
        />

        <DetailsCell
          title="Product Name"
          data={deviceInfo.productName}
          icon={<AlertCircle size={DATACELL_ICON_SIZE} />}
        />

        <DetailsCell
          title="Connected Since "
          data={convetUTCToLocal(deviceInfo.connectedTimeStamp)}
          icon={<Share2 size={DATACELL_ICON_SIZE} />}
        />

        <DetailsCell
          title="Last Disconnected "
          data={convetUTCToLocal(deviceInfo.disconnectedTimeStamp)}
          icon={<Ban size={DATACELL_ICON_SIZE} />}
        />

        <DetailsCell
          title="Disconnection Reason "
          data={deviceInfo.disconnectedReason}
          icon={<AlertCircle size={DATACELL_ICON_SIZE} />}
        />

        <DetailsCell
          title="Thing Type"
          data={deviceInfo.type}
          icon={<AlertCircle size={DATACELL_ICON_SIZE} />}
        />

        <DetailsCell
          title="Version"
          data={deviceInfo.version}
          icon={<AtSign size={DATACELL_ICON_SIZE} />}
        />
        {/* <DetailsCell
          title="Created At"
          data={convetUTCToLocal(deviceInfo.createdAt)}
          icon={<Plus size={DATACELL_ICON_SIZE} />}
        />
        <DetailsCell
          title="Updated At"
          icon={<Edit size={DATACELL_ICON_SIZE} />}
          data={convetUTCToLocal(deviceInfo.updatedAt)}
        /> */}
      </div>
      <hr className="hr" />
      {/* <div>
        <h2 className="heading-3 mb-2 ">Tags</h2>

        {deviceInfo.meta && !!Object.entries(deviceInfo.meta).length ? (
          <div className="flex gap-4">
            {Object.entries(deviceInfo.meta).map(([key, value]) => (
              <Label key={key} text={`${key} : ${value}`} color="blue" />
            ))}
          </div>
        ) : (
          <NoDataFound content="No Tag Added" className="!text-center !my-2" />
        )}
      </div> */}
      <div className=" flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center space-x-1">
          <Calendar className="h-3 w-3" />
          <span>Created: {convetUTCToLocal(deviceInfo.createdAt)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <Clock className="h-3 w-3" />
          <span>Updated: {convetUTCToLocal(deviceInfo.updatedAt)}</span>
        </div>
      </div>
    </section>
  );
};

export default DeviceDetailCard;
