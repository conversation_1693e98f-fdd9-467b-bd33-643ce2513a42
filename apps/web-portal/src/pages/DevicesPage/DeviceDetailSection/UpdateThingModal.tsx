import Button from '@components/Button';
import FormDialog from '@components/FormDialog';
import Input from '@components/Input';
import UploadImagePlaceholder from '@components/UploadImagePlaceholder';
import { THING_VAR } from '@utils/featureLabels';
import { BUTTON_ICON_SIZE } from '@utils/utilities';
import { Check, Plus, X } from 'lucide-react';
import IconActionButton from "@components/ActionButton";
import { ForwardedRef, forwardRef, useImperativeHandle, useState } from 'react';
import { updateStandardThing, uploadThingImage } from '@api/thing';
import { showErrorToast, showSuccessToast } from '@utils/index';
import { THING_DEFAULT_IMAGE } from '@utils/deviceMapping';
import { useForm } from 'react-hook-form';
import { queryClient } from '@utils/queryClient';
import { DeviceInfo } from './DeviceInfo';
import { generateRandomString } from '@src/pages/MonitorPage/utils';
import { MetaDataInputs } from '@src/features/features';
 
export type UpdateModalRefType = {
    openDrawer : (data:DeviceInfo,productType:string)=>void;
}
type FormValues = {
    displayName : string;
}


const UpdateThingModal = forwardRef(( props:unknown,ref:ForwardedRef<UpdateModalRefType>) => {
  const [metaDataInputs, setMetaDataInputs] = useState<MetaDataInputs[]>([]);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [imagePreview, setImagePreview] = useState<string>("");
  const [imageFormData, setImageFormData] = useState<FormData|null>(null);
  const [show,setShow] = useState(false);
  const [deviceInfo,setDeviceInfo] = useState<DeviceInfo|null>(null);
  const[productType,setProductType] = useState<string>("");

  const thingForm = useForm<FormValues>();

    const onSubmit = async (values:FormValues) => {
        if(!deviceInfo) return ;
    setLoadingBtn(true);
    let resp;
    let imgResp;
    const transformedMetaData = metaDataInputs.reduce(
      (obj:Record<string,string>, item) => ((obj[item.key] = item.value), obj),
      {}
    );
    if (imageFormData !== null) {
      imgResp = await uploadThingImage(deviceInfo.thingName, imageFormData);
    }
    if (imgResp?.status === "Success" || imageFormData === null) {
      resp = await updateStandardThing({
        thingName: deviceInfo.thingName,
        type: productType,
        attributes: {
          displayName: values.displayName,
          productName: deviceInfo.productName,
          meta: transformedMetaData,
          ...(imageFormData ? { imgURL: imgResp.data.fileUrl } : {})
    }});
      if (resp.status === "Success") {
        setLoadingBtn(false);
        setShow(false);

        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["thing-details", deviceInfo.thingName] });
        }, 500);
        showSuccessToast(`${THING_VAR} updated successfully`);
      } else {
        setLoadingBtn(false);
        showErrorToast(resp.message);
      }
    } else {
      setLoadingBtn(false);

      showErrorToast(imgResp.message);
    }
  };
  const removeMetaInputHandler = (inputId:string) => {
    setMetaDataInputs((prev) => prev.filter((input) => input.id !== inputId));
  };
  const handleDeviceImageUpload = async (e:React.ChangeEvent<HTMLInputElement>) => {
    if(!e.target.files) return;
    const imageFile = e.target.files[0];
    const imageUrl = URL.createObjectURL(imageFile);

    const formData:FormData = new FormData();
    formData.append("image", imageFile, imageFile.name);

    setImageFormData(formData);
    setImagePreview(imageUrl);
  };

  const handleReset = ()=>{
  setShow(false); 
  setImagePreview("");
  setImageFormData(null);
  setDeviceInfo(null);
  setProductType("")

  }
  
  const openDrawer = (deviceInfo:DeviceInfo,type:string)=>{
    setMetaDataInputs(
                Object.entries(deviceInfo.meta).map(([key, value]) => ({
                  key,
                  value,
                  id: generateRandomString()
                })));
    setShow(true);
    setProductType(type);
    setDeviceInfo(deviceInfo);
  }

  useImperativeHandle(ref,()=>({
    openDrawer,
  }))
  return (
    <FormDialog
        open={show}
        notDismissable
        onClose={handleReset}
        title={`Edit ${THING_VAR}`}
        footer={
          <div className="flex gap-4 justify-end">
            <Button
              onClick={handleReset}
              small
              color="gray"
              type="button"
              startIcon={<X size={BUTTON_ICON_SIZE} />}
            >
              Close
            </Button>
            <Button
              startIcon={<Check size={BUTTON_ICON_SIZE} />}
              small
              loading={loadingBtn}
              type="submit"
              form="edit-thing-form"
            >
              Update
            </Button>
          </div>
        }
      >
        <form
          className=" space-y-4"
          onSubmit={thingForm.handleSubmit(onSubmit)}
          id="edit-thing-form"
        >
          <section className="flex gap-8 ">
            <UploadImagePlaceholder
              className="max-w-[8rem]"
              onUpload={handleDeviceImageUpload}
              imagePreview={imagePreview || deviceInfo?.imgURL || THING_DEFAULT_IMAGE}
            />

            <div className="flex-1 space-y-4">
              <Input
                label={`${THING_VAR} Name`}
                className="w-full"
                small
                disabled
                value={deviceInfo?.thingName}
              />

              <Input
                placeholder="Name to Display"
                label="Display Name"
                className="w-full"
                small
                {...thingForm.register("displayName", {
                  required: true
                })}
                defaultValue={deviceInfo?.displayName}
              />
            </div>
          </section>
          <hr className="hr !my-2" />
          <div className="space-y-4">
            <div className="between">
              <h3 className="sidebar-sub-heading">TAGS</h3>

              <Button
                disabled={metaDataInputs.length >= 5}
                startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                type="button"
                onClick={() =>
                  setMetaDataInputs((prev) => [...prev, { key: "", value: "", id: generateRandomString() }])
                }
                small
                outlined
                color="gray"
              >
                Add Tag
              </Button>
            </div>

            {!metaDataInputs.length ? (
              <p className="center description-lg  text-gray-400">No Tags Added</p>
            ) : (
              metaDataInputs.map((input) => (
                <div className="flex gap-4 items-center  relative" key={input.id}>
                  <Input
                    className="w-full"
                    required
                    small
                    placeholder="Key"
                    onChange={(e) => {
                      setMetaDataInputs((prev) =>
                        prev.map((inputField) =>
                          inputField.id === input.id
                            ? { ...inputField, key: e.target.value }
                            : inputField
                        )
                      );
                    }}
                    value={input.key}
                  />
                  <Input
                    className="w-full"
                    small
                    required
                    onChange={(e) => {
                      setMetaDataInputs((prev) =>
                        prev.map((inputField) =>
                          inputField.id === input.id
                            ? { ...inputField, value: e.target.value }
                            : inputField
                        )
                      );
                    }}
                    placeholder="Value"
                    value={input.value}
                  />
                  <IconActionButton
                    type="delete"
                    onClick={() => removeMetaInputHandler(input.id)}
                  />
                </div>
              ))
            )}
          </div>
        </form>
      </FormDialog>
  )
}
)

export default UpdateThingModal;