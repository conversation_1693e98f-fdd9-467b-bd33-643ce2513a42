import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Button from "../../../components/Button";
import { Card } from "@components/ui";
import Input from "../../../components/Input";
import { accessListModifier, allServiceChange, handleAPIServiceChange } from "../../../utils/misc";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import HeadingIcon from "@components/HeadingIcon";
import { Checkbox } from "@components/shadcn/components/checkbox";
import Table, { TableHead, TableRow } from "@components/Table";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useAccessTemplateList from "@hooks/tenant/useAccessTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { DEFAULT_VALIDATE } from "@utils/from_schema";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { ListTodo, Plus, Users } from "lucide-react";
import { useLocation } from "react-router-dom";
import { createUserGroup, updateUserGroup } from "../../../api/user";
import { showErrorToast, showSuccessToast } from "../../../utils";
import { Type } from "@src/features/features";
import { AuthorizationData, FeatureItem, SubServices } from "@src/features/features";

export const geoServiceNameChange = (serviceName: string) => {
  if (serviceName === "Geo product") {
    return "Product";
  }
  if (serviceName === "Geo thing") {
    return "Thing";
  }

  if (serviceName === "Bulk geo things") {
    return "Bulk Things";
  }

  return serviceName;
};

const CreateUserGroup = ({ mode = "", type }: { mode: string; type: Type }) => {
  const [authorizationList, setAuthorizationList] = useState<Record<string, FeatureItem[]>>({});
  const [selectAllService, setSelectAllService] = useState(false);

  const { state } = useLocation();
  const userGroupForm = useForm({
    defaultValues: {
      name: mode === "edit" ? state.name : "",
      description: mode === "edit" ? state.description : ""
    }
  });

  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();

  const { data: accessList, isLoading: accessListLoading } = useAccessTemplateList({ type });

  useEffect(() => {
    if (!accessList?.data) return;

    const modifiedTemplate = accessListModifier(accessList.data) ?? {};
    let editModifiedTemplate;

    if (mode === "edit") {
      editModifiedTemplate = Object.fromEntries(
        Object.entries(modifiedTemplate as Record<string, FeatureItem[]>).map(
          ([groupName, services]) => [
            groupName,
            services.map((item) => ({
              ...item,
              subServices: item.subServices.map((subService) =>
                subService.actions
                  ? {
                      ...subService,
                      actions: subService.actions.map((action) => ({
                        ...action,
                        isChecked:
                          action.actionLabel === "write"
                            ? state.authorizationData?.[item.service]?.[subService.feature] ===
                              "write"
                            : ["write", "read"].includes(
                                state.authorizationData?.[item.service]?.[subService.feature]
                              )
                      }))
                    }
                  : {
                      ...subService,
                      subFeatures: subService.subFeatures.map((basic) => ({
                        ...basic,
                        actions: basic.actions.map((action) => ({
                          ...action,
                          isChecked:
                            action.actionLabel === "write"
                              ? state.authorizationData?.[item.service]?.[subService.feature]?.[
                                  basic.subFeature
                                ] === "write"
                              : ["write", "read"].includes(
                                  state.authorizationData?.[item.service]?.[subService.feature]?.[
                                    basic.subFeature
                                  ]
                                )
                        }))
                      }))
                    }
              )
            }))
          ]
        )
      );
    }

    if (mode === "edit" && editModifiedTemplate) {
      setAuthorizationList(editModifiedTemplate);
    } else {
      setAuthorizationList(modifiedTemplate as Record<string, FeatureItem[]>);
    }
  }, [accessList, mode]);

  const onSubmit = async (values: { name: string; description: string }) => {
    const authorization = Object.values(authorizationList).reduce((acc, groupItems) => {
      return groupItems.reduce((serviceAcc: AuthorizationData, serviceItem) => {
        const subServiceData = serviceItem.subServices.reduce(
          (
            subAcc: { [feature: string]: string | { [subFeature: string]: string } },
            subService: SubServices
          ) => {
            // Case 1: subService has direct actions
            if (subService.actions) {
              const accessLevel = subService.actions.find(
                (a) => a.actionLabel === "write" && a.isChecked
              )
                ? "write"
                : subService.actions.find((a) => a.actionLabel === "read" && a.isChecked)
                  ? "read"
                  : null;

              if (accessLevel) {
                subAcc[subService.feature] = accessLevel;
              }

              return subAcc;
            }

            // Case 2: subService has subFeatures
            const subFeatureAccess = subService.subFeatures.reduce(
              (sfAcc: { [subFeature: string]: string }, sf) => {
                const accessLevel = sf.actions.find((a) => a.actionLabel === "write" && a.isChecked)
                  ? "write"
                  : sf.actions.find((a) => a.actionLabel === "read" && a.isChecked)
                    ? "read"
                    : null;

                if (accessLevel) {
                  sfAcc[sf.subFeature] = accessLevel;
                }

                return sfAcc;
              },
              {}
            );

            if (Object.keys(subFeatureAccess).length) {
              // Merge if feature already exists
              subAcc[subService.feature] = {
                ...(typeof subAcc[subService.feature] === "object"
                  ? (subAcc[subService.feature] as object)
                  : {}),
                ...subFeatureAccess
              };
            }

            return subAcc;
          },
          {}
        );

        if (Object.keys(subServiceData).length) {
          serviceAcc[serviceItem.service] = {
            ...(serviceAcc[serviceItem.service] || {}),
            ...Object.entries(subServiceData).reduce(
              (merged, [feature, value]) => {
                if (
                  typeof value === "object" &&
                  typeof serviceAcc[serviceItem.service]?.[feature] === "object"
                ) {
                  // Merge subFeature objects
                  merged[feature] = {
                    ...(serviceAcc[serviceItem.service][feature] as object),
                    ...(value as object)
                  };
                } else {
                  merged[feature] = value;
                }

                return merged;
              },
              {} as { [feature: string]: string | { [subFeature: string]: string } }
            )
          };
        }

        return serviceAcc;
      }, acc);
    }, {} as AuthorizationData);

    let resp;
    if (mode === "edit") {
      resp = await updateUserGroup(type, state.id, {
        description: values.description,
        authorization
      });
    } else {
      resp = await createUserGroup(type, {
        ...values,
        authorization
      });
    }

    if (resp.status === "Success") {
      showSuccessToast(
        `User Group ${mode === "edit" ? "Updated" : "Created"} created successfully`
      );

      setTimeout(() => {
        navigate(-1);
      }, 500);
    } else {
      showErrorToast(resp.message);
    }
  };

  return (
    <Card>
      <form className=" space-y-4" onSubmit={userGroupForm.handleSubmit(onSubmit)}>
        <div className="flex justify-between items-center">
          <HeadingIcon Icon={Users} title={`${mode === "edit" ? "Update" : "Create"} User Group`} />

          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            loading={userGroupForm.formState.isSubmitting}
            noAccess={
              type === "msp"
                ? permissions.mspUSers !== "write"
                : type === "tenant"
                  ? permissions.tntUsers !== "write"
                  : false
            }
          >
            Submit
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <Input
            required
            label="Group Name"
            disabled={mode === "edit"}
            {...userGroupForm.register("name", DEFAULT_VALIDATE.schema)}
            error={!!userGroupForm.formState.errors.name}
            helperText={userGroupForm.formState.errors.name && DEFAULT_VALIDATE.message}
          />
          <Input
            required
            label="Description"
            className="col-span-2"
            {...userGroupForm.register("description", DEFAULT_VALIDATE.schema)}
            error={!!userGroupForm.formState.errors.description}
            helperText={userGroupForm.formState.errors.description && DEFAULT_VALIDATE.message}
          />
        </div>
        <hr className="hr !my-8" />
        <HeadingIcon Icon={ListTodo} title="User Group Permissions" />

        {accessListLoading ? (
          <CardLoadingSkeleton />
        ) : (
          <div className=" w-full rounded-md">
            <div className="flex gap-4  items-center my-4">
              <Checkbox
                checked={selectAllService}
                onCheckedChange={(value) => {
                  if (typeof value === "boolean") {
                    setSelectAllService(value);
                    allServiceChange(value, setAuthorizationList);
                  }
                }}
              />
              <h3 className="heading-3">Administrator Access</h3>
              <p className="helper-text">( This will select all the services )</p>
            </div>

            <Table
              resizable={false}
              head={
                <>
                  <TableHead>Service Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Write</TableHead>
                  <TableHead>Read</TableHead>
                </>
              }
              body={
                <>
                  {Object.entries(authorizationList).map(([groupName, services]) => (
                    <React.Fragment key={groupName}>
                      <tr>
                        <TableRow className="font-medium text-base text-foreground" colSpan={100}>
                          {groupName}
                        </TableRow>
                      </tr>

                      {services.map((item) => (
                        <React.Fragment key={item.service}>
                          {item.subServices.map((subService, subIdx) =>
                            subService.actions ? (
                              <tr key={`${item.service}-${subIdx}`} className="cursor-pointer">
                                <TableRow>{geoServiceNameChange(subService.name)}</TableRow>
                                <TableRow className="text-muted-foreground">
                                  {subService.desc}
                                </TableRow>

                                {subService.actions.map((action, actIdx) => (
                                  <TableRow key={`${item.service}-${subIdx}-act-${actIdx}`}>
                                    <Checkbox
                                      checked={action.isChecked}
                                      onCheckedChange={(value) => {
                                        handleAPIServiceChange(
                                          value,
                                          action.actionLabel,
                                          setAuthorizationList,
                                          item.service,
                                          subService.feature
                                        );
                                      }}
                                    />
                                  </TableRow>
                                ))}
                              </tr>
                            ) : (
                              subService.subFeatures.map((basic, basicIdx) => (
                                <tr
                                  key={`${item.service}-${subIdx}-basic-${basicIdx}`}
                                  className="cursor-pointer"
                                >
                                  <TableRow>{geoServiceNameChange(basic.name)}</TableRow>
                                  <TableRow className="text-muted-foreground">
                                    {basic.desc}
                                  </TableRow>

                                  {basic.actions.map((action, actIdx) => (
                                    <TableRow
                                      key={`${item.service}-${subIdx}-basic-${basicIdx}-act-${actIdx}`}
                                    >
                                      <Checkbox
                                        checked={action.isChecked}
                                        onCheckedChange={(value) => {
                                          handleAPIServiceChange(
                                            value,
                                            action.actionLabel,
                                            setAuthorizationList,
                                            item.service,
                                            subService.feature,
                                            basic.subFeature
                                          );
                                        }}
                                      />
                                    </TableRow>
                                  ))}
                                </tr>
                              ))
                            )
                          )}
                        </React.Fragment>
                      ))}
                    </React.Fragment>
                  ))}
                </>
              }
            />
          </div>
        )}
      </form>
    </Card>
  );
};

export default CreateUserGroup;
