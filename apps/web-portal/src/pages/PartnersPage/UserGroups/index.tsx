import { fetchUserGroups } from "@api/user";
import DataNotFound from "@components/DataNotFound";
import Label from "@components/Label";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useUserGroupCount from "@hooks/tenant/useUserGroupCount";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { Type } from "@src/features/features";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Plus, Search } from "lucide-react";
import { useState } from "react";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";


const UserGroups = ({ type }: { type:Type}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [sortFn, sort] = useTableSort();

  const navigate = useCustomNavigate();
  const { data: groupUserCount } = useUserGroupCount();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: permissions } = useUserGroupPermissions();

  const { data: groupList, isLoading } = useQuery({
    queryKey: [`user-group-list`, type, page, limit, debouncedSearchQuery],
    queryFn: fetchUserGroups
  });

  return (
    <main className=" space-y-4">
       <HeaderSection
        title="User Groups"
        description="Manage your User Groups"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("createUserGroup")}
            noAccess={
              type === "msp"
                ? permissions.mspUSers !== "write"
                : type === "tenant"
                  ? permissions.tntUsers !== "write"
                  : false
            }
          >
             Add User Group
          </Button>
        }
      />
      <Card className="space-y-4">
          <Input
            value={searchQuery}
            onChange={(e) => {
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            placeholder="Search"
            className="flex-1"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            <TableHead onSort={(order) => sort("managedBy", order)}> Managed By</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>User Count</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading ? (
            <TableRowsSkeleton />
          ) : !groupList?.data?.userGroups?.length ? (
            <DataNotFound title="No User Group  Available" isTable />
          ) : (
            <>
              {groupList?.data?.userGroups.toSorted(sortFn).map((group, i) => (
                <tr className="cursor-pointer" onClick={() => navigate(group.id)} key={group.id}>
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{group.name}</TableRow>
                  <TableRow className="truncate">
                    <Label
                      className="uppercase"
                      text={group.managedBy}
                      color={group.managedBy === "user" ? "green" : "blue"}
                    />
                  </TableRow>
                  <TableRow className="truncate">{group.description}</TableRow>
                  <TableRow>
                    {groupUserCount?.find((userGroup) => userGroup.userGroup === group.name)
                      ?.count || "0"}
                  </TableRow>
                  <TableRow className="truncate">{convetUTCToLocal(group.createdAt)}</TableRow>

                  <TableRow className="truncate">{convetUTCToLocal(group.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: groupList?.data?.pages || 0
        }}
      />
      </Card>
    </main>
  );
};

export default UserGroups;
