import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import { Checkbox } from "@components/shadcn/components/checkbox";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useAccessTemplateList from "@hooks/tenant/useAccessTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";

import { useQuery } from "@tanstack/react-query";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE, ROLES_Mapped } from "@utils/utilities";
import { Calendar, Clock, Users,Edit2, FileText, Trash, User  } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { deleteUserGroup, fetchUserGroupDetail, fetchUserGroups } from "../../../api/user";
import Button from "../../../components/Button";
import { Card } from "@components/ui";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import Label from "../../../components/Label";
import Table, { TableHead, TableRow } from "../../../components/Table";
import { showErrorToast, showSuccessToast } from "../../../utils";
import { accessListModifier } from "../../../utils/misc";
import { geoServiceNameChange } from "./CreateUserGroup";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import TabsListV2 from "@components/Tabs/TabsListV2";
import TabsTriggerV2 from "@components/Tabs/TabsTriggerV2";
import { Tabs, TabsContent } from "@components/shadcn/components/tabs";
import colors from "tailwindcss/colors";
import { Type } from "@src/features/features";
import { FeatureItem } from "@src/features/features";

type TabsType = "users" | "permissions";


type User = {
  disabled: boolean;
  email: string;
  name: string;
  role: string;
  phone: string;
}


const UserGroupDetailPage = ({ type }:{type :Type}) => {
  const { userGroupId } = useParams();
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [authorizationList, setAuthorizationList] = useState <Record<string,FeatureItem[]>>({});
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [tabValue, setTabValue] = useState("users");

  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();


  const { data: permissions } = useUserGroupPermissions();

  const { data: groupDetails, isLoading } = useQuery({
    queryKey: [`user-group-details`, type, userGroupId],
    queryFn: fetchUserGroupDetail
  });
  const { refetch: refetchGroupList } = useQuery({
    queryKey: [`user-group-list`, type],
    queryFn: fetchUserGroups
  });

  const { data: accessList } = useAccessTemplateList({ type });

  useEffect(() => {
    if (!accessList?.data) return;
    const modifiedTemplate = accessListModifier(accessList.data);
    setAuthorizationList(modifiedTemplate as Record<string, FeatureItem[]>);
  }, [accessList]);

  const handleChange = (val: TabsType) => {
    setTabValue(val);
  };

  const handleGroupDelete = async () => {
    setLoadingBtn(true);
    const resp = await deleteUserGroup(type, groupDetails.data.groupData.id);
    if (resp.status === "Success") {
      showSuccessToast("User group deleted successfully");
      setShowDeletePrompt(false);
      setTimeout(() => {
        refetchGroupList();
        navigate(-1);
      }, 1000);
    } else {
      showErrorToast(resp.message);
    }
    setLoadingBtn(false);
  };
  return (
    <main className="space-y-6">
      <Card className=" space-y-6">
        {isLoading || groupDetails.status === "Failure" ? (
          <CardLoadingSkeleton />
        ) : (
          <>
            {" "}
            <div className="between items-center ">
              <HeadingIcon Icon={User} title="User Group Details" />

              <div className="flex gap-4">
                {(groupDetails?.data?.groupData.managedBy === "user" ||
                  type === "platform-operator") && (
                  <Button
                    onClick={() => navigate(`edit`, { state: groupDetails.data.groupData })}
                    startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                    color="orange"
                    noAccess={
                      type === "msp"
                        ? permissions.mspUSers !== "write"
                        : type === "tenant"
                          ? permissions.tntUsers !== "write"
                          : false
                    }
                  >
                    Edit
                  </Button>
                )}
                <Button
                  onClick={() => setShowDeletePrompt(true)}
                  startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                  color="red"
                  noAccess={
                    type === "msp"
                      ? permissions.mspUSers !== "write"
                      : type === "tenant"
                        ? permissions.tntUsers !== "write"
                        : false
                  }
                >
                  Delete
                </Button>
              </div>
            </div>
            <section className="grid grid-cols-2 gap-4">
              <DetailsCell
                icon={<User size={DATACELL_ICON_SIZE} />}
                title="Name"
                data={groupDetails.data.groupData.name}
              />
              <DetailsCell
                icon={<Users size={DATACELL_ICON_SIZE} />}
                title="Managed By"
                data={groupDetails.data.groupData.managedBy}
              />
              <DetailsCell
                icon={<FileText size={DATACELL_ICON_SIZE} />}
                title="Description"
                data={groupDetails.data.groupData.description}
              />
              <DetailsCell
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                title="Create Date"
                data={convetUTCToLocal(groupDetails.data.groupData.createdAt)}
              />
              <DetailsCell
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                title="Update Date"
                data={convetUTCToLocal(groupDetails.data.groupData.updatedAt)}
              />
            </section>
          </>
        )}
      </Card>

      {!isLoading && groupDetails.status !== "Failure" && (
        <Card className=" space-y-6">
          <Tabs value={tabValue}>
            <TabsListV2>
              <TabsTriggerV2 value="users" onClick={() => handleChange("users")}>
                Users
              </TabsTriggerV2>

              <TabsTriggerV2 value="permissions" onClick={() => handleChange("permissions")}>
                Group Permissions
              </TabsTriggerV2>
            </TabsListV2>
            <TabsContent value="users">
              <Table
                head={
                  <>
                    <TableHead>No.</TableHead>
                    <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
                    <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
                    <TableHead onSort={(order) => sort("role", order)}>Role</TableHead>
                    <TableHead onSort={(order) => sort("phone", order)}>Phone</TableHead>
                  </>
                }
                body={
                  !groupDetails.data.users.length ? (
                    <DataNotFound title="No User Added" isTable />
                  ) : (
                    <>
                      {groupDetails.data.users.toSorted(sortFn).map((user:User, i:number) => (
                        <tr
                          className="cursor-pointer"
                          onClick={() =>
                            navigate(
                              type === "msp"
                                ? `/UM/mspUsers/${user.email}`
                                : `/UM/users/${user.email}`
                            )
                          }
                          key={user.email}
                        >
                          <TableRow>{i + 1}</TableRow>
                          <TableRow>{user.name}</TableRow>
                          <TableRow>{user.email}</TableRow>

                          <TableRow>
                            <Label
                              text={ROLES_Mapped.find((role) => role.key === user.role)?.value}
                              color={ROLES_Mapped.find((role) => role.key === user.role)?.color as  keyof typeof colors}
                            />
                          </TableRow>
                          <TableRow>{user.phone || "N/A"}</TableRow>
                        </tr>
                      ))}
                    </>
                  )
                }
              />
            </TabsContent>
            <TabsContent value="permissions">
              <div className="space-y-4">
                {isLoading ? (
                  <CardLoadingSkeleton />
                ) : (
                  <div className=" w-full ">
                    <Table
                      head={
                        <>
                          <TableHead>Service Name</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Write</TableHead>
                          <TableHead>Read</TableHead>
                        </>
                      }
                      body={
                        <>
                          {Object.entries(authorizationList).map(([groupName, services]) => (
                            <React.Fragment key={groupName}>
                              <tr>
                                <TableRow
                                  className="font-medium text-base text-foreground"
                                  //colSpan={100}
                                >
                                  {groupName}
                                </TableRow>
                              </tr>
                              {services.map((item) =>
                                item.subServices.map((subService) =>
                                  subService.actions ? (
                                    <tr
                                      className="cursor-pointer"
                                      key={`${item.service}-${subService.feature}`}
                                    >
                                      <TableRow>{geoServiceNameChange(subService.name)}</TableRow>
                                      <TableRow className="text-muted-foreground">
                                        {subService.desc}
                                      </TableRow>
                                      <TableRow>
                                        <Checkbox
                                          checked={
                                            groupDetails.data.groupData.authorizationData?.[
                                              item.service
                                            ]?.[subService.feature] === "write"
                                          }
                                        />
                                      </TableRow>
                                      <TableRow>
                                        <Checkbox
                                          checked={["write", "read"].includes(
                                            groupDetails.data.groupData.authorizationData?.[
                                              item.service
                                            ]?.[subService.feature]
                                          )}
                                        />
                                      </TableRow>
                                    </tr>
                                  ) : (
                                    subService.subFeatures.map((basic) => (
                                      <tr
                                        className="cursor-pointer"
                                        key={`${item.service}-${subService.feature}-${basic.subFeature}`}
                                      >
                                        <TableRow>{geoServiceNameChange(basic.name)}</TableRow>
                                        <TableRow className="text-muted-foreground">
                                          {basic.desc}
                                        </TableRow>
                                        <TableRow>
                                          <Checkbox
                                            checked={
                                              groupDetails.data.groupData.authorizationData?.[
                                                item.service
                                              ]?.[subService.feature]?.[basic.subFeature] ===
                                              "write"
                                            }
                                          />
                                        </TableRow>
                                        <TableRow>
                                          <Checkbox
                                            checked={["write", "read"].includes(
                                              groupDetails.data.groupData.authorizationData?.[
                                                item.service
                                              ]?.[subService.feature]?.[basic.subFeature]
                                            )}
                                          />
                                        </TableRow>
                                      </tr>
                                    ))
                                  )
                                )
                              )}
                            </React.Fragment>
                          ))}
                        </>
                      }
                    />
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      )}

      <ConfirmPrompt
        show={showDeletePrompt}
        item={groupDetails?.data?.groupData.name}
        validate
        onCancel={() => setShowDeletePrompt(false)}
        loading={loadingBtn}
        onConfirm={() => {
          handleGroupDelete();
        }}
      />
    </main>
  );
};

export default UserGroupDetailPage;
