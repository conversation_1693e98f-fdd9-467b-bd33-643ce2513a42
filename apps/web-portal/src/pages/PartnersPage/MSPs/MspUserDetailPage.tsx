import { deleteUser } from "@api/user";
import Button from "@components/Button";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import { Card } from "@components/ui";
import useMspUserDetail from "@hooks/msp/useMspUserDetail";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE, ROLES_Mapped } from "@utils/utilities";
import {
  AtSign,
  Calendar,
  Clock,
  Edit2,
  Map,
  MapPin,
  Phone,
  Trash,
  User,
  User<PERSON>heck,
  Users
} from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import colors from "tailwindcss/colors";
import UpdateUserModal from "../UpdateUserModal";

const MspUserDetailPage = () => {
 const updateUserModalRef = useRef<ElementRef<typeof UpdateUserModal>>(null);
  const { userEmail } = useParams();
  const {
    data: mspUserDetail,
    isLoading,
  } = useMspUserDetail(userEmail as string);
  
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const navigate = useCustomNavigate();

  const deleteMutation = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      showSuccessToast("MSP user deleted successfully");
      setTimeout(() => {
        setShowDeletePrompt(false);
        navigate(-1);
      }, 500);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });
   const handleEditClick = () => {
    if (updateUserModalRef.current && mspUserDetail) {
      updateUserModalRef.current.openModal({
        userDetail:mspUserDetail,
        type: "tenant",
        role: mspUserDetail.role ? [mspUserDetail.role] : [],
        email: mspUserDetail.email
      });
    }
  };

  return (
    <main>
      <Card className="space-y-6">
        {isLoading ? (
          <CardLoadingSkeleton />
        ) : (
          <>
            <div className="between items-center">
              <HeadingIcon Icon={Users} title="MSP User Details" />

              <div className="flex gap-4">
                <Button
                  startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                  onClick={handleEditClick}
                  color="orange"
                >
                  Edit
                </Button>
                <Button
                  startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                  onClick={() => setShowDeletePrompt(true)}
                  color="red"
                >
                  Delete
                </Button>
              </div>
            </div>
            <section className="grid grid-cols-3 gap-4">
              <DetailsCell
                icon={<User size={DATACELL_ICON_SIZE} />}
                title="Name"
                data={mspUserDetail?.name}
              />
              <DetailsCell
                title="Email"
                icon={<AtSign size={DATACELL_ICON_SIZE} />}
                data={mspUserDetail?.email}
              />

              <DetailsCell
                title="User Role"
                icon={<UserCheck size={DATACELL_ICON_SIZE} />}
                data={
                  <Label
                    text={ROLES_Mapped.find((role) => role.key === mspUserDetail?.role)?.value}
                    color={ROLES_Mapped.find((role) => role.key === mspUserDetail?.role)?.color as keyof typeof colors}
                  />
                }
              />
              {mspUserDetail?.userGroup && (
                <DetailsCell
                  title="User Group"
                  icon={<Users size={DATACELL_ICON_SIZE} />}
                  data={
                    <span
                      onClick={() => navigate(`/UM/userGroups/${mspUserDetail?.userGroupId}`)}
                      className="font-semibold text-foreground cursor-pointer hover:underline"
                    >
                      {mspUserDetail?.userGroup}
                    </span>
                  }
                />
              )}

              <DetailsCell
                title="Phone"
                icon={<Phone size={DATACELL_ICON_SIZE} />}
                data={mspUserDetail?.phone}
              />
              <DetailsCell
                title="Address"
                icon={<Map size={DATACELL_ICON_SIZE} />}
                data={mspUserDetail?.address}
              />
              <DetailsCell
                title="Pincode"
                icon={<MapPin size={DATACELL_ICON_SIZE} />}
                data={mspUserDetail?.pincode}
              />
              <DetailsCell
                title="Created At"
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(mspUserDetail?.createdAt)}
              />
              <DetailsCell
                title="Updated At"
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(mspUserDetail?.updatedAt)}
              />
            </section>
          </>
        )}
      </Card>
        <UpdateUserModal ref = {updateUserModalRef}  />
      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={mspUserDetail?.name}
        onCancel={() => setShowDeletePrompt(false)}
        loading={deleteMutation.isPending}
        onConfirm={() => {
          deleteMutation.mutate({ type: "msp", userEmail });
        }}
      />
    </main>
  );
};

export default MspUserDetailPage;
