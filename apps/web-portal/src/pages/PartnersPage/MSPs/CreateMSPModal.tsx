import { MODE } from "@api/index";
import { createMsp } from "@api/user";
import { PhoneInput } from "@components/PhoneNumberInput";
import FormDialog from "@components/FormDialog";
import { Step, StepLabel, Stepper } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import {
  DEFAULT_VALIDATE,
  EMAIL_VALIDATE,
  LENGTH_VALIDATE,
  PASSWORD_VALIDATE,
  PHONE_VALIDATE,
  PINCODE_VALIDATE
} from "@utils/from_schema";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import {
  Briefcase,
  ChevronLeft,
  ChevronRight,
  Globe,
  Lock,
  Mail,
  Map,
  MapPin,
  Plus,
  User,
  Users
} from "lucide-react";
import { useForm } from "react-hook-form";
import Button from "../../../components/Button";
import Dropdown from "../../../components/Dropdown";
import Input from "../../../components/Input";
import PasswordInput from "../../../components/PasswordInput";
import { showErrorToast, showSuccessToast } from "../../../utils";
import * as RPNInput from "react-phone-number-input";
import { MspFormData } from "@src/features/features";


export type ModalRefType = {
  openModal: () => void;
}
type Props = unknown; 
//{ show, setShow }
const CreateMSPModal = (props:Props,ref:React.ForwardedRef<ModalRefType>) => {
  const [activeStep, setActiveStep] = useState(0);
  const [show, setShow] = useState(false);

  const mspForm = useForm<MspFormData>({
    mode: "onBlur",
    defaultValues: {
      password: MODE === "development" ? "Qwertyuiop@123" : "",
      confirmPassword: MODE === "development" ? "Qwertyuiop@123" : "",
      website: MODE === "development" ? "https://www.oneiot.io" : ""
    } as Record<string, string>
  });

  const mutation = useMutation({
    mutationFn: createMsp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["msp-list"] });
      showSuccessToast("Msp created successfully");
      setActiveStep(0);
      mspForm.reset();
      setShow(false);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const onSubmit = async (values:MspFormData) => {
       const payload = {
      ...values,
      canCreateTenants: values.canCreateTenants === "true"
    };
    mutation.mutate(payload);
  };

  const handleNext = async (e:React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    let isValid = false;
    isValid = await mspForm.trigger([
      "mspId",
      "userName",
      "email",
      "password",
      "canCreateTenants",
      "confirmPassword",
      "mspFullName"
    ]);

    if (isValid) {
      setActiveStep((prev) => prev + 1);
    }
  };

  useImperativeHandle(ref, () => ({
    openModal: () => {setShow(true);}
  }));

  return (
    <FormDialog
      open={show}
      onClose={() => {
        setShow(false);
        setActiveStep(0);
        mspForm.reset();
      }}
      title="Create MSP"
      notDismissable
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            color="gray"
            outlined
            small
            startIcon={<ChevronLeft size={BUTTON_ICON_SIZE} />}
            onClick={(e) => {
              e.preventDefault();
              setActiveStep((prev) => prev - 1);
            }}
            disabled={activeStep === 0}
          >
            prev
          </Button>

          {activeStep === 1 ? (
            <Button
              small
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              type="submit"
              loading={mutation.isPending}
              form="create-msp-form"
            >
              Add Msp
            </Button>
          ) : (
            <Button
              small
              endIcon={<ChevronRight size={BUTTON_ICON_SIZE} />}
              onClick={(e) => {
                handleNext(e);
              }}
            >
              Next
            </Button>
          )}
        </div>
      }
    >
      <form className="space-y-4" onSubmit={mspForm.handleSubmit(onSubmit)} id="create-msp-form">
        <Stepper activeStep={activeStep} className="mb-4">
          <Step>
            <StepLabel>Basic Info</StepLabel>
          </Step>
          <Step>
            <StepLabel>Additional Info</StepLabel>
          </Step>
        </Stepper>

        {activeStep === 0 && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 ">
              <Input
                label="MSP ID"
                startIcon={<User size={INPUT_ICON_SIZE} />}
                required
                noSpace
                {...mspForm.register("mspId", LENGTH_VALIDATE().schema)}
                error={!!mspForm.formState.errors.mspId}
                helperText={mspForm.formState.errors.mspId && LENGTH_VALIDATE().message}
              />
              <Input
                label="MSP Full Name"
                startIcon={<User size={INPUT_ICON_SIZE} />}
                required
                {...mspForm.register("mspFullName", LENGTH_VALIDATE().schema)}
                error={!!mspForm.formState.errors.mspFullName}
                helperText={mspForm.formState.errors.mspFullName && LENGTH_VALIDATE().message}
              />
            </div>
            <div className="grid grid-cols-2 gap-4 ">
              <Input
                label="Username"
                startIcon={<Briefcase size={INPUT_ICON_SIZE} />}
                required
                {...mspForm.register("userName", DEFAULT_VALIDATE.schema)}
                error={!!mspForm.formState.errors.userName}
                helperText={mspForm.formState.errors.userName && DEFAULT_VALIDATE.message}
              />
              <Input
                label="Email"
                startIcon={<Mail size={INPUT_ICON_SIZE} />}
                required
                {...mspForm.register("email", EMAIL_VALIDATE.schema)}
                error={!!mspForm.formState.errors.email}
                helperText={mspForm.formState.errors.email && EMAIL_VALIDATE.message}
              />
            </div>
            <Dropdown
              {...mspForm.register("canCreateTenants", DEFAULT_VALIDATE.schema)}
              onChange={(option) => {
                mspForm.setValue("canCreateTenants", option);

                mspForm.trigger("canCreateTenants");
              }}
              value={mspForm.getValues("canCreateTenants") && mspForm.getValues("canCreateTenants")}
              startIcon={<Users size={INPUT_ICON_SIZE} />}
              label="Create Tenant"
              options={["true", "false"]}
              error={!!mspForm.formState.errors.canCreateTenants}
              helperText={mspForm.formState.errors.canCreateTenants && "Field is required"}
              required
            />

            <PasswordInput
              label="Password"
              showProgress
              startIcon={<Lock size={INPUT_ICON_SIZE} />}
              required
              register={mspForm.register("password", PASSWORD_VALIDATE.schema)}
              error={!!mspForm.formState.errors.password}
              helperText={mspForm.formState.errors.password && PASSWORD_VALIDATE.message}
            />

            <PasswordInput
              label="Confirm Password"
              startIcon={<Lock size={INPUT_ICON_SIZE} />}
              required
              type="password"
              register={mspForm.register("confirmPassword", {
                required: true,
                validate: (v) => v === mspForm.watch("password") || "Passwords do not match"
              })}
              error={mspForm.formState.errors.confirmPassword}
              helperText={mspForm.formState.errors.confirmPassword?.message}
            />
          </div>
        )}

        {activeStep === 1 && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 ">
              <Input
                required
                label="Website"
                startIcon={<Globe size={INPUT_ICON_SIZE} />}
                {...mspForm.register("website", DEFAULT_VALIDATE.schema)}
                error={!!mspForm.formState.errors.website}
                helperText={mspForm.formState.errors.website && DEFAULT_VALIDATE.message}
              />

              <PhoneInput
                required
                label="Phone Number"
                //startIcon={<Phone size={INPUT_ICON_SIZE} />}
                // type="number"
                {...mspForm.register("phone", PHONE_VALIDATE.schema)}
                onChange={(e) => {
                  mspForm.setValue("phone", e);
                }}
                value={mspForm.getValues("phone") as RPNInput.Value}
                error={!!mspForm.formState.errors.phone}
                helperText={mspForm.formState.errors.phone && PHONE_VALIDATE.message}
              />
            </div>

            <Input
              required
              label="Address"
              startIcon={<Map size={INPUT_ICON_SIZE} />}
              {...mspForm.register("address", DEFAULT_VALIDATE.schema)}
              error={!!mspForm.formState.errors.address}
              helperText={mspForm.formState.errors.address && DEFAULT_VALIDATE.message}
            />
            <div className="grid grid-cols-1 gap-4 ">
              <Input
                required
                label="Pincode"
                startIcon={<MapPin size={INPUT_ICON_SIZE} />}
                type="number"
                {...mspForm.register("pincode", PINCODE_VALIDATE.schema)}
                error={!!mspForm.formState.errors.pincode}
                helperText={mspForm.formState.errors.pincode && PINCODE_VALIDATE.message}
              />
            </div>
          </div>
        )}
      </form>
    </FormDialog>
  );
};

export default forwardRef(CreateMSPModal);
