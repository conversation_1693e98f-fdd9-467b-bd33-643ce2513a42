import { deleteMsp } from "@api/user";
import Button from "@components/Button";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import useMspDetail from "@hooks/msp/useMspDetail";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE } from "@utils/utilities";
import {
  AtSign, Building2, Calendar, ClipboardCheck, Clock,
  Edit2,
  Globe,
  Map,
  MapPin,
  Phone,
  Trash,
  User,
  Users
} from "lucide-react";
import { useRef, useState } from "react";
import { useParams } from "react-router-dom";
import EditMspModal, { EditRefType } from "./EditMspModal";

const MspDetailPage = () => {
  const editModalRef = useRef<EditRefType>(null);
  const { mspId } = useParams();
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const navigate = useCustomNavigate();
  const { data: mspDetail, isLoading } = useMspDetail(mspId as string);

  const [sortFn, sort] = useTableSort();

  const deleteMutation = useMutation({
    mutationFn: deleteMsp,
    onSuccess: () => {
      // refetchGeoFence();
      showSuccessToast("Msp deleted successfully");
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["msp-list"] });
        setShowDeletePrompt(false);
        navigate(-1);
      }, 500);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

    const handleEditClick = () => {
    if (mspDetail?.mspDetails && editModalRef.current){
      editModalRef.current.openModal(mspDetail?.mspDetails);
    }
  };

  return (
    <main className="space-y-6">
      <Card className="space-y-6">
        {isLoading ? (
          <div className="text-center">
            <CardLoadingSkeleton />
          </div>
        ) : (
          <>
            <div className="between items-center">
              <HeadingIcon Icon={Building2} title="MSPs Details" />

              <div className="flex gap-4">
                <Button
                  startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                  onClick={handleEditClick}
                  color="orange"
                >
                  Edit
                </Button>
                <Button
                  startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                  onClick={() => setShowDeletePrompt(true)}
                  color="red"
                  loading={deleteMutation.isPending}
                >
                  Delete
                </Button>
              </div>
            </div>
            <section className="grid grid-cols-3 gap-4">
              <DetailsCell
                icon={<Building2 size={DATACELL_ICON_SIZE} />}
                title="MSP Id"
                data={mspDetail?.mspDetails.mspId ?? "Not Available"}
              />
              <DetailsCell
                icon={<ClipboardCheck size={DATACELL_ICON_SIZE} />}
                title="MSP Name"
                data={mspDetail?.mspDetails.mspFullName}
              />
              <DetailsCell
                icon={<AtSign size={DATACELL_ICON_SIZE} />}
                title="Email"
                data={mspDetail?.mspDetails.email}
              />
              <DetailsCell
                title="Create Tenant"
                icon={<User size={DATACELL_ICON_SIZE} />}
                data={
                  <Label
                    text={`${mspDetail?.mspDetails.canCreateTenants}`}
                    color={mspDetail?.mspDetails.canCreateTenants ? "green" : "red"}
                  />
                }
              />
              <DetailsCell
                icon={<Phone size={DATACELL_ICON_SIZE} />}
                title="Phone"
                data={mspDetail?.mspDetails.phone}
              />
              <DetailsCell
                icon={<Map size={DATACELL_ICON_SIZE} />}
                title="Address"
                data={mspDetail?.mspDetails.address}
              />
              <DetailsCell
                icon={<MapPin size={DATACELL_ICON_SIZE} />}
                title="Pincode"
                data={mspDetail?.mspDetails.pincode}
              />
              <DetailsCell
                icon={<Globe size={DATACELL_ICON_SIZE} />}
                title="Website"
                data={mspDetail?.mspDetails.website}
              />

              <DetailsCell
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                title="Created At"
                data={convetUTCToLocal(mspDetail?.mspDetails.createdAt)}
              />
              <DetailsCell
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                title="Updated At"
                data={convetUTCToLocal(mspDetail?.mspDetails.updatedAt)}
              />
            </section>
          </>
        )}
      </Card>
      <Card className=" space-y-6">
        <HeadingIcon Icon={Users} title="MSP Tenants" />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
              <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
            </>
          }
          body={
            isLoading ? (
              <TableRowsSkeleton />
            ) : !mspDetail?.tenantDetails.length ? (
              <DataNotFound title="No MSPs Available" isTable />
            ) : (
              <>
                {mspDetail?.tenantDetails.toSorted(sortFn).map((tenant, i) => (
                  <tr
                    key={tenant.email}
                    className="cursor-pointer"
                    onClick={() => navigate(`/TM/tenants/tenantDetails/${tenant.name}`)}
                  >
                    <TableRow>{i + 1}</TableRow>
                    <TableRow>{tenant.name}</TableRow>
                    <TableRow>{tenant.email}</TableRow>
                    <TableRow>{convetUTCToLocal(tenant.createdAt)}</TableRow>
                    <TableRow>{convetUTCToLocal(tenant.updatedAt)}</TableRow>
                  </tr>
                ))}
              </>
            )
          }
        />
      </Card>
      {/* {showUpdateModal && ( */}
        <EditMspModal ref = {editModalRef} />
      {/* )} */}
      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={mspId}
        loading={deleteMutation.isPending}
        onCancel={() => setShowDeletePrompt(false)}
        onConfirm={() => {
          deleteMutation.mutate(mspId);
        }}
      />
    </main>
  );
};

export default MspDetailPage;
