import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useMspList from "@hooks/msp/useMspList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { MspFormData } from "@src/features/features";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Building2, Search, UserPlus } from "lucide-react";
import { useRef, useState } from "react";
import CreateMSPModal, { ModalRefType } from "./CreateMSPModal";

const MspList = () => {
  const CreateMSPModalRef = useRef<ModalRefType|null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();
  const { data: mspList, isLoading: mspLoading } = useMspList({
    page,
    limit,
    search: debouncedSearchQuery
  });

  return (
    <main>
      <section className=" space-y-6">
        <div className="flex gap-4 items-center">
          <HeadingIcon Icon={Building2} title="MSPs" />

          <div className="mx-auto" />
          <Input
            value={searchQuery}
            onChange={(e) => {
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            className="w-[25rem]"
            placeholder="Search"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />
          <Button
            startIcon={<UserPlus size={BUTTON_ICON_SIZE} />}
            onClick={()=> CreateMSPModalRef.current?.openModal()}
          >
            Add MSP
          </Button>
        </div>

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("mspId", order)}>MSP ID</TableHead>
              <TableHead onSort={(order) => sort("mspFullName", order)}>MSP Name</TableHead>
              <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
              <TableHead onSort={(order) => sort("canCreateTenants", order)}>
                Create Tenants
              </TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
            </>
          }
          body={
            mspLoading ? (
              <TableRowsSkeleton />
            ) : !mspList?.mspsList.length ? (
              <DataNotFound title="No MSP Available" isTable />
            ) : (
              <>
                {mspList.mspsList.toSorted(sortFn).map((msp:MspFormData, i:number) => {
                  return (
                    <tr
                      key={msp.mspId}
                      className="cursor-pointer"
                      onClick={() => navigate(msp.mspId)}
                    >
                      <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                      <TableRow title>{msp.mspId}</TableRow>
                      <TableRow>{msp.mspFullName || "N/A"}</TableRow>
                      <TableRow>{msp.email}</TableRow>
                      <TableRow>
                        <Label
                          text={`${msp.canCreateTenants}`}
                          color={msp.canCreateTenants ? "green" : "red"}
                        />
                      </TableRow>

                      <TableRow>{convetUTCToLocal(msp.createdAt)}</TableRow>

                      <TableRow>{convetUTCToLocal(msp.updatedAt)}</TableRow>
                    </tr>
                  );
                })}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: mspList?.pages ?? 1
          }}
        />
      </section>

      {/* {showAddMSPModal &&/>} */}
      <CreateMSPModal ref = {CreateMSPModalRef} />
    </main>
  );
};

export default MspList;
