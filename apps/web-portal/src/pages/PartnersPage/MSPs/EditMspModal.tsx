import {updateMsp } from "@api/user";
import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import FormDialog from "@components/FormDialog";
import { useMutation} from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Check, Globe, Map, MapPin, Phone, Users, X } from "lucide-react";
import { useParams } from "react-router-dom";
import { forwardRef, useImperativeHandle, useState } from "react";
import { MspFormData } from "@src/features/features";
import { useForm } from "react-hook-form";
//import useMspDetail from "@hooks/msp/useMspDetail";
import { queryClient } from "@utils/queryClient";

type Props = unknown;
export type EditRefType = {
  openModal : (mspDetails:MspFormData)=> void
}
const EditMspModal = (props:Props,ref:React.ForwardedRef<EditRefType>) => {
  const { mspId } = useParams();
  const [show, setShow] = useState(false);
   const mspForm = useForm<MspFormData>();

  const mutation = useMutation({
    mutationFn: updateMsp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["msp-details", mspId] });
      showSuccessToast("Msp updated successfully");
      mspForm.reset();
      setShow(false);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });
  const onSubmit = async (values:MspFormData) => {
    const payload = {
      ...values,
      mspId,
      canCreateTenants: values.canCreateTenants || values.canCreateTenants === "true"
    };
    mutation.mutate(payload);
  };
  useImperativeHandle(ref, () => ({ 
    openModal: (mspDetails) => {
       mspForm.reset({
        email: mspDetails.email,
        phone: mspDetails.phone,
        pincode: mspDetails.pincode,
        address: mspDetails.address,
        website: mspDetails.website,
        access: mspDetails.access,
        canCreateTenants: mspDetails.canCreateTenants ?? "false" as "true"| "false"
      });
      setShow(true);
    }
  }),[mspForm]);

  return (
    <FormDialog
      open={show}
      onClose={() => {
        setShow(false);
        mspForm.reset();
      }}
      title="Edit MSP"
      notDismissable
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={() => {
              setShow(false);
              mspForm.reset();
            }}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            type="submit"
            loading={mutation.isPending}
            form="edit-msp-form"
          >
            Update
          </Button>
        </div>
      }
    >
      <form className="space-y-4" onSubmit={mspForm.handleSubmit(onSubmit)} id="edit-msp-form">
        <Dropdown
          onChange={(option) => {
            mspForm.setValue("canCreateTenants", option);
          }}
          value={mspForm.getValues("canCreateTenants") ? "true" : "false"}
          startIcon={<Users size={INPUT_ICON_SIZE} />}
          label="Create Tenant"
          options={["true", "false"]}
          error={!!mspForm.formState.errors.canCreateTenants}
          helperText={mspForm.formState.errors.canCreateTenants && "Field is required"}
          required
        />

        <div className="grid grid-cols-2 gap-4 !my-4">
          <Input
            label=" Website"
            startIcon={<Globe size={INPUT_ICON_SIZE} />}
            required
            {...mspForm.register("website", {
              required: true
            })}
            error={!!mspForm.formState.errors.website}
            helperText={mspForm.formState.errors.website && "Field is required"}
          />

          <Input
            label="Phone Number"
            startIcon={<Phone size={INPUT_ICON_SIZE} />}
            required
            {...mspForm.register("phone", {
              required: true,
              maxLength: 13,
              minLength: 12
            })}
            error={!!mspForm.formState.errors.phone}
            helperText={
              mspForm.formState.errors.phone && "Invalid Phone Number, country code required"
            }
          />
        </div>

        <Input
          label="Address"
          startIcon={<Map size={INPUT_ICON_SIZE} />}
          required
          {...mspForm.register("address")}
        />
        <div className="grid grid-cols-1 gap-4 !my-6">
          <Input
            label="Pincode"
            startIcon={<MapPin size={INPUT_ICON_SIZE} />}
            required
            type="number"
            {...mspForm.register("pincode", {
              maxLength: 6,
              minLength: 6
            })}
            error={!!mspForm.formState.errors.pincode}
            helperText={mspForm.formState.errors.pincode && "Invalid Pincode"}
          />
        </div>
      </form>
    </FormDialog>
  );
};

export default forwardRef(EditMspModal);
