import { fetchUserGroups, updateAllUser } from "@api/user";
import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import FormDialog from "@components/FormDialog";
import useNotificationEscalationGroup from "@hooks/notifications/useNotificationEscalationGroup";
import useDebounce from "@hooks/useDebounce";
import { useMutation, useQuery } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Monitor, Map, MapPin, Phone, Users, X, Check } from "lucide-react";
import { forwardRef, useImperativeHandle, useState } from "react";
import { queryClient } from "@utils/queryClient";
import { useForm } from "react-hook-form";
import { Users as User } from "@src/features/features";

type UserDetailType = {
  address: string;
  email: string;
  monitoringGroups: string[];
  phone: string;
  pincode: string;
  userGroup: {
    id: string;
    name: string;
  };
  createdAt?: string;
  updatedAt?: string;
  disabled?: boolean;
  msp?: string;
  name?: string;
  role?: string;
  tenant?: string;
};

interface OpenModalData {
  userDetail: User;
  type?: string;
  role?: string[];
  email?: string;
}

type UpdateUserModalRef = {
  openModal: (data:OpenModalData) => void;
}
type Props = unknown;
const UpdateUserModal = (props:Props,ref:React.ForwardedRef<UpdateUserModalRef>) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [show ,setShow] = useState(false);
  const [modalData,setModalData] = useState<OpenModalData | undefined>(undefined);;
  const userForm = useForm<UserDetailType>();

  const { data: groupList, isLoading: groupListLoading } = useQuery({
    queryKey: [`user-group-list`, modalData?.type || "tenant"],
    queryFn: fetchUserGroups
  });

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: escalationList, isLoading: isEscalationListLoading } =
    useNotificationEscalationGroup({
      searchQuery: debouncedSearchQuery
    });
  const mutation = useMutation({
    mutationFn: updateAllUser,
    onSuccess: () => {
      queryClient.invalidateQueries({
         queryKey: ["msp-details", modalData?.email, modalData?.type || "tenant"]
      });
      showSuccessToast("User updated successfully");
      userForm.reset();
      setShow(false);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const onSubmit = async (values:UserDetailType) => {
    const payload = {
      ...values,
      userGroup: values.userGroup.id,
       type: modalData?.type || "tenant",
      ...(modalData?.type === "tenant" && {
        monitoringGroups: [...new Set(["default_monitoring_group", ...values.monitoringGroups])]
      })
    };
    mutation.mutate(payload);
  };

  useImperativeHandle(ref, () => ({
       openModal: (data: OpenModalData) => {
        setModalData(data);
        userForm.reset({
          phone: data.userDetail.phone,
          pincode: data.userDetail.pincode,
          address: data.userDetail.address,
          email: data.userDetail.email,
          userGroup: {
            name: data.userDetail.userGroup,
            id: data.userDetail.userGroupId
          },
          ...(data.userDetail.monitoringGroups && {
            monitoringGroups: data.userDetail.monitoringGroups
          })
        });
        setShow(true);
      }}));

  return (
    <FormDialog
      open={show}
      onClose={() => {
        setShow(false);
        userForm.reset();
      }}
      notDismissable
      title="Edit User Details"
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={() => {
              setShow(false);
              userForm.reset();
            }}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            form="update-user-form"
            type="submit"
            small
            loading={mutation.isPending}
          >
            Update
          </Button>
        </div>
      }
    >
      <form className=" space-y-4" onSubmit={userForm.handleSubmit(onSubmit)} id="update-user-form">
        <div className=" space-y-4">
          {!modalData?.role?.includes("SuperUser") && (
            <Dropdown
              label="User Group"
              startIcon={<Users size={INPUT_ICON_SIZE} />}
              onChange={(option) => {
                userForm.setValue("userGroup", option);
              }}
              value={userForm.getValues("userGroup") && userForm.getValues("userGroup")}
              options={groupList?.data?.userGroups || []}
              required
              optionsLoading={groupListLoading}
              isSearchable
              placeHolder="Select User Group"
              newOption={{
                placeHolder: "Create New User Group...",
                target: "/UM/userGroups/createUserGroup"
              }}
              getOptionLabel={"name"}
            />
          )}
          {modalData?.type === "tenant" && (
            <Dropdown
              label="Monitoring Groups "
              {...userForm.register("monitoringGroups")}
              onChange={(option) => {
                userForm.setValue("monitoringGroups", option);
                userForm.trigger("monitoringGroups");
              }}
              options={
                escalationList
                  ? escalationList?.escalationGroups?.map((item) => item.groupName)
                  : []
              }
              defaultValue={["default_monitoring_group"]}
              value={
                userForm.getValues("monitoringGroups") && userForm.getValues("monitoringGroups")
              }
              startIcon={<Monitor size={INPUT_ICON_SIZE} />}
              optionsLoading={isEscalationListLoading}
              isSearchable
              isMulti
              placeHolder="Select Escalations"
              newOption={{
                placeHolder: "Create New Escalations...",
                target: "/notifications/escalations"
              }}
              deepSearch={(value) => setSearchQuery(value)}
              helperText="By Default 'default_monitoring_group' will be selected"
            />
          )}
          {/* <PhoneInput
            required
            label="Phone Number"
            placeholder="Phone Number"
            className="!w-full"
            startIcon={<Phone size={INPUT_ICON_SIZE} />}
            {...userForm.register("phone", PHONE_VALIDATE.schema)}
            onChange={(e) => {
              userForm.setValue("phone", e);
            }}
            error={!!userForm.formState.errors.phone}
            helperText={userForm.formState.errors.phone && PHONE_VALIDATE.message}
          /> */}

          <Input
            label="Phone Number"
            startIcon={<Phone size={INPUT_ICON_SIZE} />}
            required
            {...userForm.register("phone", {
              required: true,
              maxLength: 13,
              minLength: 12
            })}
            error={!!userForm.formState.errors.phone}
            helperText={
              userForm.formState.errors.phone && "Invalid Phone Number, country code required"
            }
          />

          <Input
            label="Address"
            startIcon={<Map size={INPUT_ICON_SIZE} />}
            required
            {...userForm.register("address")}
          />

          <Input
            label="Pincode"
            startIcon={<MapPin size={INPUT_ICON_SIZE} />}
            required
            type="number"
            {...userForm.register("pincode", {
              maxLength: 6,
              minLength: 6
            })}
            error={!!userForm.formState.errors.pincode}
            helperText={userForm.formState.errors.pincode && "Invalid Pincode"}
          />
        </div>
      </form>
    </FormDialog>
  );
};

export default forwardRef(UpdateUserModal);
