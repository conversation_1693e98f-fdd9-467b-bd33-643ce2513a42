import { Modal } from "@mui/material";
import { memo } from "react";
import { Card } from "@components/ui";
import Label from "../../../components/Label";
import { Check, ChevronLeft, Loader, X } from "lucide-react";
import Table, { TableRow } from "../../../components/Table";
import Button from "../../../components/Button";
import { useParams } from "react-router-dom";

import useCustomNavigate from "@hooks/useCustomNavigate";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import useTenantDeletedStatus from "@hooks/tenant/useTenantDeletedStatus";

const PartnerDeleteModal = ({ show }: { show: boolean }) => {
  const partnerName = useParams().partnerName!;

  const navigate = useCustomNavigate();

  const { data: partnerDeleteStatus } = useTenantDeletedStatus({
    tenantName: partnerName,
    enabled: false
  });

  return (
    <Modal
      open={show}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      }}
    >
      <Card className="!p-4 min-w-[550px] space-y-4">
        <div className="center gap-4 mx-8">
          <h2 className="modal-title">Deletion Status for {partnerName}</h2>
          {partnerDeleteStatus?.status === "Success" ? (
            <Label icon={<Check />} text="Success" color="green" />
          ) : (
            <Label icon={<X />} text="Failed" color="red" />
          )}
        </div>
        <div className="max-h-[70vh] overflow-auto ">
          <Table
            body={Object.entries(partnerDeleteStatus?.data || {}).map(
              ([key, value]) =>
                !key.endsWith("Err") &&
                key !== "traceContext" && (
                  <tr className="bg-white border-b" key={key}>
                    <TableRow dense>{key}</TableRow>
                    <TableRow dense>
                      {value === "Success" ? (
                        <Label icon={<Check />} color="green" text="Success" />
                      ) : value === "Failed" ? (
                        <Label icon={<X />} color="red" text="Failed" />
                      ) : value === "Pending" ? (
                        <Label
                          icon={<Loader className="animate-spin " />}
                          text="Pending"
                          color="yellow"
                        />
                      ) : (
                        value
                      )}
                    </TableRow>
                  </tr>
                )
            )}
          />
        </div>
        <Button
          startIcon={<ChevronLeft size={BUTTON_ICON_SIZE} />}
          link
          className="mx-auto"
          onClick={() => {
            navigate(-1);
          }}
        >
          Go to Tenants
        </Button>
      </Card>
    </Modal>
  );
};

export default memo(PartnerDeleteModal);
