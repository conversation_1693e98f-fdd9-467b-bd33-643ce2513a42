import Button from "@components/Button";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import HeadingIcon from "@components/HeadingIcon";
import NoDataFound from "@components/NoDataFound";
import { Progress } from "@components/shadcn/components/progress";
import { Card } from "@components/ui";
import useTenantLimits from "@hooks/tenant/useTenantLimits";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { BarChart2, Calendar, Edit2, MonitorSmartphone, Package } from "lucide-react";
import { useRef} from "react";
import UpdatePartnerLimitsDrawer, { DrawerRef } from "./UpdatePartnerLimitsDrawer";

const PartnerLimits = ({ partnerName, type }:{partnerName : string,type : string}) => {
const updateDrawerRef = useRef<DrawerRef>(null);
  const {
    data: tenantLimits,
    isLoading: tenantLimitsLoading,
   // refetch: refetchLimits
  } =  useTenantLimits(partnerName,type);

  const handleClick = () => {
    if(tenantLimits){
    updateDrawerRef.current?.openModal(tenantLimits,type);
    }
  }
  
  return (
    <>
      <Card className="space-y-6">
        <div className="between">
          <HeadingIcon Icon={BarChart2} title="Account Quota" />

          <Button
            color="gray"
            onClick={handleClick}
            startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
          >
            Edit
          </Button>
        </div>

        {tenantLimitsLoading ? (
          <CardLoadingSkeleton className="w-full" col={2} />
        ) : !tenantLimits ? (
          <NoDataFound />
        ) : (
          <section className="space-y-6">
            <div className="flex gap-4 child:w-full">
              <div className="space-y-2">
                <div className="flex justify-between text-sm items-center">
                  <span className="text-muted-foreground heading-3">{THING_VAR}s Usage</span>
                  <span className="text-muted-foreground font-medium">
                    <span className="heading-2 text-foreground">
                      {tenantLimits.thingsCount}
                    </span>{" "}
                    / {tenantLimits.thingsLimit}
                  </span>
                </div>
                <Progress
                  value={(tenantLimits.thingsCount / tenantLimits.thingsLimit) * 100}
                  className="h-6 rounded-md"
                />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm items-center">
                  <span className="text-muted-foreground heading-3">{PRODUCT_VAR} Usage</span>
                  <span className="text-muted-foreground font-medium">
                    <span className="heading-2 text-foreground">
                      {" "}
                      {tenantLimits.productsCount}
                    </span>{" "}
                    / {tenantLimits.productsLimit}
                  </span>
                </div>
                <Progress
                  value={(tenantLimits.productsCount / tenantLimits.productsLimit) * 100}
                  className="h-6 rounded-md"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Card variant="second">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <MonitorSmartphone className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="">
                    <p className="text-sm font-medium text-muted-foreground ">{THING_VAR}s Count</p>
                    <p className="text-lg font-semibold ">{tenantLimits.thingsCount || 0}</p>
                  </div>
                </div>
              </Card>
              <Card variant="second">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Package className="h-5 w-5 text-yellow-500" />
                  </div>
                  <div className="">
                    <p className="text-sm font-medium text-muted-foreground">
                      {PRODUCT_VAR}s Count
                    </p>
                    <p className="text-lg font-semibold ">{tenantLimits.productsCount || 0}</p>
                  </div>
                </div>
              </Card>
            </div>
            <Card variant="second">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calendar className="h-5 w-5 text-blue-500" />
                </div>
                <div className="">
                  <p className="text-sm font-medium text-muted-foreground ">Account Expiration</p>
                  <p className="text-lg  font-semibold">
                    {convetUTCToLocal(tenantLimits.expiration)}
                  </p>
                </div>
              </div>
            </Card>
          </section>
        )}
      </Card>
      {/* {updateLimitDrawer && ( */}
        <UpdatePartnerLimitsDrawer ref = {updateDrawerRef} />
          {/* tenantLimits={tenantLimits}
          type={type}
          updateLimitDrawer={updateLimitDrawer}
          refetchLimits={refetchLimits}
          setUpdateLimitDrawer={setUpdateLimitDrawer}
      )} */}
    </>
  );
};

export default PartnerLimits;
