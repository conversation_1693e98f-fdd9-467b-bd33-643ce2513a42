import { updatePartner } from "@api/partner";
import Button from "@components/Button";
import Input from "@components/Input";
import { PhoneInput } from "@components/PhoneNumberInput";
import FormDialog from "@components/FormDialog";
import { PHONE_VALIDATE } from "@utils/from_schema";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Check, Globe, Map, MapPin, X } from "lucide-react";
import { queryClient } from "@utils/queryClient";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useForm } from "react-hook-form";
import * as RPNInput from "react-phone-number-input";

type PartnerDetailType  = {
  name ?: string,
  tenant?:string,
  phone : string,
  website : string,
  address : string,
  pincode : string
}
export type TenantModalRef = {
  openModal: ({ partnerDetail, type }: { partnerDetail: PartnerDetailType; type: string }) => void;  
}
type Props = unknown;
const UpdateTenantModal = (props:Props, ref:React.ForwardedRef<TenantModalRef>) => {
  const tenantForm = useForm<PartnerDetailType>();
  const [show,setShow] = useState(false);
  const [Type,setType] = useState("");

  const onSubmit = async (values:PartnerDetailType) => {
    console.log("values", values);
    const resp = await updatePartner({
      ...values,
      userType: Type
      // ...(type === "operator" && {
      //   mspAccess: values.mspAccess.value,
      //   operatorAccess: values.operatorAccess.value
      // })
    });

    if (resp.status === "Success") {
      showSuccessToast("Tenant updated successfully");
     queryClient.invalidateQueries({
      queryKey: [`tenant-details-${values.tenant}`, values.tenant],
});
      setShow(false);
    } else {
      showErrorToast(resp.message);
    }
  };

 useImperativeHandle(ref, () => ({
  openModal: ({partnerDetail, type}) => {
    tenantForm.reset({
      tenant: partnerDetail?.name,
      phone: partnerDetail?.phone,
      website: partnerDetail?.website,
      address: partnerDetail?.address,
      pincode: partnerDetail?.pincode,
    })
    setShow(true);
    setType(type);
  }
}));

  return (
    <FormDialog
      open={show}
      onClose={() => {
        setShow(false);
        tenantForm.reset();
      }}
      notDismissable
      title="Edit Tenant Details"
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={() => {
              setShow(false);
              tenantForm.reset();
            }}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            type="submit"
            small
            loading={tenantForm.formState.isSubmitting}
            form="update-tenant-form"
          >
            Update
          </Button>
        </div>
      }
    >
      <form
        className="space-y-4"
        onSubmit={tenantForm.handleSubmit(onSubmit)}
        id="update-tenant-form"
      >
        {/* {type === "operator" && (
            <div className="grid grid-cols-2 gap-4">
              <Dropdown
                {...tenantForm.register("mspAccess", DEFAULT_VALIDATE.schema)}
                onChange={(option) => {
                  tenantForm.setValue("mspAccess", option);
                  tenantForm.trigger("mspAccess");
                }}
                value={tenantForm.getValues("mspAccess") && tenantForm.getValues("mspAccess")}
                startIcon={<Bookmark size={INPUT_ICON_SIZE} />}
                error={!!tenantForm.formState.errors.mspAccess}
                helperText={tenantForm.formState.errors.mspAccess && DEFAULT_VALIDATE.message}
                label="MSP Access"
                getOptionLabel="label"
                options={ACCESS_VALUES}
                required
              />
              <Dropdown
                {...tenantForm.register("operatorAccess", DEFAULT_VALIDATE.schema)}
                onChange={(option) => {
                  tenantForm.setValue("operatorAccess", option);
                  tenantForm.trigger("operatorAccess");
                }}
                value={
                  tenantForm.getValues("operatorAccess") && tenantForm.getValues("operatorAccess")
                }
                startIcon={<Bookmark size={INPUT_ICON_SIZE} />}
                error={!!tenantForm.formState.errors.operatorAccess}
                helperText={tenantForm.formState.errors.operatorAccess && DEFAULT_VALIDATE.message}
                label="Operator Access"
                getOptionLabel="label"
                options={ACCESS_VALUES}
                required
              />
            </div>
          )} */}
        <PhoneInput
          label="Phone Number"
          required
          {...tenantForm.register("phone", PHONE_VALIDATE.schema)}
          onChange={(e) => {
            tenantForm.setValue("phone", e);
            tenantForm.trigger("phone");
          }}
          value={tenantForm.getValues("phone") as RPNInput.Value}
          error={!!tenantForm.formState.errors.phone}
          helperText={tenantForm.formState.errors.phone && PHONE_VALIDATE.message}
        />

        <Input
          label="Website"
          startIcon={<Globe size={INPUT_ICON_SIZE} />}
          required
          {...tenantForm.register("website")}
        />
        <Input
          label="Address"
          startIcon={<Map size={INPUT_ICON_SIZE} />}
          required
          {...tenantForm.register("address")}
        />

        <Input
          label="Pincode"
          startIcon={<MapPin size={INPUT_ICON_SIZE} />}
          required
          type="number"
          {...tenantForm.register("pincode", {
            maxLength: 6,
            minLength: 6
          })}
          error={!!tenantForm.formState.errors.pincode}
          helperText={tenantForm.formState.errors.pincode && "Invalid Pincode"}
        />
      </form>
    </FormDialog>
  );
};

export default forwardRef(UpdateTenantModal);
