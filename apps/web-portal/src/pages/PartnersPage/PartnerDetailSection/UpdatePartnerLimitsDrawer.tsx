import { updateTenantLimits } from "@api/partner";
import Button from "@components/Button";
import FormDialog from "@components/FormDialog";
import Input from "@components/Input";
import { DateTimePicker } from "@components/shadcn/components/date-time-picker";
import { TenantDetails } from "@hooks/tenant/useTenantLimits";
import { useMutation } from "@tanstack/react-query";
import { THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Archive, Check, Cpu, X } from "lucide-react";
import React, { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { useForm } from "react-hook-form";

export type DrawerRef = {
  openModal : (data:TenantDetails,typee:string) => void;
}
type Props = unknown;

const UpdatePartnerLimitsDrawer = (props:Props, ref : React.ForwardedRef<DrawerRef>) => {
  const [updateLimitDrawer, setUpdateLimitDrawer] = useState(false);
  const [tenantLimits,setTenantLimits] = useState<TenantDetails|null>(null);
  const [type,setType] = useState("");

   const limitForm = useForm({
    defaultValues: {
     thingsLimit: tenantLimits?.thingsLimit || 0,
        productsLimit: tenantLimits?.productsLimit || 0,
        expirationHrs: new Date(tenantLimits?.expiration ?? "")
    }
  });

  useEffect(() => {
    if (tenantLimits) {
      limitForm.reset({
        thingsLimit: tenantLimits.thingsLimit,
        productsLimit: tenantLimits.productsLimit,
        expirationHrs: new Date(tenantLimits.expiration ?? "")
      });
    }
  }, [tenantLimits, limitForm]);

  const mutation = useMutation({
    mutationFn: updateTenantLimits,
    onSuccess: () => {
      setUpdateLimitDrawer(false);
      queryClient.invalidateQueries({queryKey:["tenantLimits", tenantLimits?.tenant, type]});
      showSuccessToast("Limit Updated successfully");
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const handleTenantLimit = (values:{thingLimit?:number|undefined,productsLimit:number|undefined,expirationHrs:Date}) => {
    const payload = { ...values, tenantName: tenantLimits?.tenant, type };
    mutation.mutate(payload);
  };
useImperativeHandle(ref, () => ({
  openModal: (data, typee) => {
    setTenantLimits(data);
    setType(typee as string);
    setUpdateLimitDrawer(true);
  }
}), []);

  return (
    <FormDialog
      open={updateLimitDrawer}
      onClose={() => setUpdateLimitDrawer(false)}
      title="Edit Account Quota"
      notDismissable
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={() => setUpdateLimitDrawer(false)}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            loading={mutation.isPending}
            type="submit"
            form="update-limit-form"
          >
            Update
          </Button>
        </div>
      }
    >
      <form
        className=" space-y-4"
        onSubmit={limitForm.handleSubmit(handleTenantLimit)}
        id="update-limit-form"
      >
        <Input
          defaultValue={tenantLimits?.thingsLimit}
          label={`${THING_VAR}s Limits `}
          startIcon={<Cpu size={INPUT_ICON_SIZE} />}
          required
          {...limitForm.register("thingsLimit", {
            required: true,
            valueAsNumber: true
          })}
          type="number"
          placeholder={`${THING_VAR}s Limit`}
        />
        <Input
          label="Products Limit"
          {...limitForm.register("productsLimit", {
            required: true,
            valueAsNumber: true
          })}
          defaultValue={tenantLimits?.productsLimit}
          startIcon={<Archive size={INPUT_ICON_SIZE} />}
          type="number"
          required
          placeholder="Product Limits"
        />
        <div>
          <p className="input-label-text">Expiration Date *</p>
          <DateTimePicker
            value={limitForm.watch("expirationHrs")}
            onChange={(newValue) => {
              if (!newValue) return;
              limitForm.setValue("expirationHrs", newValue);
              // setStartTime(newValue);
            }}
          />
        </div>
      </form>
    </FormDialog>
  );
};

export default forwardRef(UpdatePartnerLimitsDrawer);
