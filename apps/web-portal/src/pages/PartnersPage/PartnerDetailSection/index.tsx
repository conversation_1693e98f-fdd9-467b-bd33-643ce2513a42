import Alert from "@components/Alert";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import { Card } from "@components/ui";
import usePartnerDetail from "@hooks/tenant/usePartnerDetail";
import useTenantDeletedStatus from "@hooks/tenant/useTenantDeletedStatus";
import UsagePage from "@src/pages/UsagePage";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE } from "@utils/utilities";
import {
  AtSign,
  Briefcase,
  Edit2,
  ExternalLink,
  Globe,
  Info,
  ListChecks,
  Map,
  MapPin,
  MoreVertical,
  Phone,
  Trash,
  UserRoundCog,
  Users
} from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { deletePartner } from "../../../api/partner";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import { showErrorToast, showSuccessToast } from "../../../utils";
import PartnerDeleteModal from "./PartnerDeleteModal";
import PartnerLimits from "./PartnerLimits";
import UpdateTenantModal from "./UpdateTenantModal";

function PartnerDetailPage({ type }: { type: "msp" | "operator" }) {

  const updateTenantModalRef = useRef<ElementRef<typeof UpdateTenantModal>>(null);
  const partnerName = useParams().partnerName!;

  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [showDeleteStatusPrompt, setShowDeleteStatusPrompt] = useState(false);

  const [loadingBtn, setLoadingBtn] = useState(false);

  const {
    data: partnerDetail,
    isLoading,
  } = usePartnerDetail(partnerName);
  

  const { refetch: refetchPartnerDeleteStatus } = useTenantDeletedStatus({
    tenantName: partnerName,
    enabled: false
  });

  const handleDelete = async () => {
    setLoadingBtn(true);
    const resp = await deletePartner({ type, name: partnerName });
    if (resp.status === "Success") {
      showSuccessToast("Tenant deleted successfully");
      setShowDeletePrompt(false);
      refetchPartnerDeleteStatus();
      setShowDeleteStatusPrompt(true);
    } else {
      showErrorToast(resp.message);
    }
    setLoadingBtn(false);
  };

  return (
    <main className="space-y-6">
      {!isLoading && partnerDetail?.setupStatus !== "COMPLETED" && (
        <Alert
          title="Warning"
          description="    You cannot visit or access the details without setting up the tenant. Once the setup is
            complete, you will get metrics and messages for this Tenant."
          Icon={Info}
        />
      )}
      <div className="grid grid-cols-2 gap-4">
        <Card className="">
          {isLoading || !partnerDetail ? (
            <CardLoadingSkeleton />
          ) : (
            <div className="space-y-4">
              <div className="between items-center">
                <div className="flex gap-4 items-center">
                  <HeadingIcon Icon={Users} title="Tenant Details" />
                </div>
                <div className="flex gap-4 ">
                  {partnerDetail?.setupStatus === "COMPLETED" && (
                    <Link
                      to={`/TM/tenants/${partnerDetail.featureType}/${partnerDetail.name}`}
                      target="_blank"
                    >
                      <ShadcnButton className="h-10">
                        <ExternalLink size={BUTTON_ICON_SIZE} className="mr-2" />
                        Visit Tenant
                      </ShadcnButton>
                    </Link>
                  )}
                  <Menubar className="!bg-transparent border-none p-0 ">
                    <MenubarMenu>
                      <MenubarTrigger className="bg-transparent dark:bg-transparent cursor-pointer">
                        <MoreVertical size={BUTTON_ICON_SIZE} />
                      </MenubarTrigger>
                      <MenubarContent className="">
                        <MenubarItem
                          startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                          onClick={() => { updateTenantModalRef.current?.openModal({partnerDetail, type});
                              // mspAccess: ACCESS_VALUES.find(
                              //   (access) => access.value === partnerDetail.data.mspAccess
                              // ),
                              // operatorAccess: ACCESS_VALUES.find(
                              //   (access) => access.value === partnerDetail.data.operatorAccess
                              // )
                           
                          }}
                        >
                          Edit
                        </MenubarItem>
                        <MenubarItem
                          startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                          onClick={() => setShowDeletePrompt(true)}
                          className="text-red-500 hover:!text-red-600"
                        >
                          Delete
                        </MenubarItem>
                      </MenubarContent>
                    </MenubarMenu>
                  </Menubar>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <DetailsCell
                  title="Tenant Name"
                  data={partnerDetail.name}
                  icon={<Users size={DATACELL_ICON_SIZE} />}
                />
                <DetailsCell
                  title="Email"
                  data={partnerDetail.email}
                  icon={<AtSign size={DATACELL_ICON_SIZE} />}
                />

                <DetailsCell
                  title="Company"
                  data={partnerDetail.tenantName}
                  icon={<Briefcase size={DATACELL_ICON_SIZE} />}
                />
                <DetailsCell
                  title="Phone"
                  data={partnerDetail.phone}
                  icon={<Phone size={DATACELL_ICON_SIZE} />}
                />
                <DetailsCell
                  title="Website"
                  data={partnerDetail.website}
                  icon={<Globe size={DATACELL_ICON_SIZE} />}
                />

                <DetailsCell
                  title="MSP"
                  data={partnerDetail.mspId}
                  icon={<UserRoundCog size={DATACELL_ICON_SIZE} />}
                />

                <DetailsCell
                  title="Address"
                  data={partnerDetail.address}
                  icon={<Map size={DATACELL_ICON_SIZE} />}
                />

                <DetailsCell
                  title="Pincode"
                  data={partnerDetail.pincode}
                  icon={<MapPin size={DATACELL_ICON_SIZE} />}
                />
                <DetailsCell
                  title="Operator Access"
                  data={partnerDetail.operatorAccess}
                  icon={<ListChecks size={DATACELL_ICON_SIZE} />}
                />
                <DetailsCell
                  title="MSP Access"
                  data={partnerDetail.mspAccess}
                  icon={<UserRoundCog size={DATACELL_ICON_SIZE} />}
                />
              </div>
            </div>
          )}
        </Card>
        <PartnerLimits partnerName={partnerName} type={type} />
      </div>

      {partnerDetail?.setupStatus === "COMPLETED" && (
        <UsagePage setupStatus={partnerDetail?.setupStatus} />
      )}
      <PartnerDeleteModal show={showDeleteStatusPrompt} />
      
        <UpdateTenantModal ref = {updateTenantModalRef}  />

      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={partnerName}
        onCancel={() => setShowDeletePrompt(false)}
        onConfirm={handleDelete}
        loading={loadingBtn}
      />
    </main>
  );
}

export default PartnerDetailPage;
