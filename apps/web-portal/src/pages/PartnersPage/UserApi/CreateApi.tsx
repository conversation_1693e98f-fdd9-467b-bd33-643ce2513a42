import React, { useEffect, useState } from "react";

import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import HeadingIcon from "@components/HeadingIcon";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@components/shadcn/components/alert-dialog";
import { Checkbox } from "@components/shadcn/components/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@components/shadcn/components/tooltip";
import Table, { TableHead, TableRow } from "@components/Table";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useAccessTemplateList from "@hooks/tenant/useAccessTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { IconButton } from "@mui/material";
import { AuthorizationData, FeatureItem } from "@src/features/features";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Copy, KeyRound, ListTodo, MessageCircleWarning, Plus } from "lucide-react";
import { useForm } from "react-hook-form";
import { createUserApi } from "../../../api/user";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import { showErrorToast, showSuccessToast } from "../../../utils";
import { accessListModifier, allServiceChange, handleAPIServiceChange } from "../../../utils/misc";
import { geoServiceNameChange } from "../UserGroups/CreateUserGroup";

type FormValues = {
  name: string;
  description: string;
  tokenName: string;
};
type UserAPiDetails = {
  encodedKey: string | undefined;
  keyId: string;
  message: string;
  name: string;
  status: string;
};

const handleCopyClick = async (textToCopy: string | undefined) => {
  try {
    await navigator.clipboard.writeText(textToCopy as string);
    showSuccessToast("API key copied to clipboard!");
  } catch {
    showErrorToast("Failed to copy API key.");
  }
};

const CreateApi = ({ type = "tenant" }) => {
  const [authorizationList, setAuthorizationList] = useState<Record<string, FeatureItem[]>>({});
  const [showApiModal, setShowApiModal] = useState(false);
  const [userApiDetails, setUserApiDetails] = useState<UserAPiDetails | null>(null);
  const [selectAllService, setSelectAllService] = useState(false);

  const navigate = useCustomNavigate();
  const userApiForm = useForm<FormValues>();
  const { data: permissions } = useUserGroupPermissions();

  const { data: accessList, isLoading: accessListLoading } = useAccessTemplateList({
    type: type as "tenant" | "operator" | "msp" | "platform-operator"
  });

  useEffect(() => {
    if (!accessList?.data) return;
    const modifiedTemplate = accessListModifier(accessList.data);
    setAuthorizationList(modifiedTemplate as Record<string, FeatureItem[]>);
  }, [accessList]);

  const onSubmit = async (values: FormValues) => {
    const authorization = Object.values(authorizationList).reduce((acc, groupItems) => {
      return groupItems.reduce((serviceAcc: AuthorizationData, serviceItem) => {
        const subServiceData = serviceItem.subServices.reduce(
          (
            subAcc: { [feature: string]: string | { [subFeature: string]: string } },
            subService
          ) => {
            // Case 1: subService has direct actions
            if (subService.actions) {
              const accessLevel = subService.actions.find(
                (a) => a.actionLabel === "write" && a.isChecked
              )
                ? "write"
                : subService.actions.find((a) => a.actionLabel === "read" && a.isChecked)
                  ? "read"
                  : null;

              if (accessLevel) {
                subAcc[subService.feature] = accessLevel;
              }

              return subAcc;
            }

            // Case 2: subService has subFeatures
            const subFeatureAccess = subService.subFeatures.reduce(
              (sfAcc: { [subFeature: string]: string }, sf) => {
                const accessLevel = sf.actions.find((a) => a.actionLabel === "write" && a.isChecked)
                  ? "write"
                  : sf.actions.find((a) => a.actionLabel === "read" && a.isChecked)
                    ? "read"
                    : null;

                if (accessLevel) {
                  sfAcc[sf.subFeature] = accessLevel;
                }

                return sfAcc;
              },
              {}
            );

            if (Object.keys(subFeatureAccess).length) {
              // Merge if feature already exists
              subAcc[subService.feature] = {
                ...(typeof subAcc[subService.feature] === "object"
                  ? (subAcc[subService.feature] as object)
                  : {}),
                ...subFeatureAccess
              };
            }

            return subAcc;
          },
          {}
        );

        if (Object.keys(subServiceData).length) {
          serviceAcc[serviceItem.service] = {
            ...(serviceAcc[serviceItem.service] || {}),
            ...Object.entries(subServiceData).reduce(
              (merged, [feature, value]) => {
                if (
                  typeof value === "object" &&
                  typeof serviceAcc[serviceItem.service]?.[feature] === "object"
                ) {
                  // Merge subFeature objects
                  merged[feature] = {
                    ...(serviceAcc[serviceItem.service][feature] as object),
                    ...(value as object)
                  };
                } else {
                  merged[feature] = value;
                }

                return merged;
              },
              {} as { [feature: string]: string | { [subFeature: string]: string } }
            )
          };
        }

        return serviceAcc;
      }, acc);
    }, {});

    const resp = await createUserApi(
      {
        ...values,
        authorization
      },
      type
    );

    if (resp.status === "Success") {
      showSuccessToast(resp.message);
      handleCopyClick(resp?.encodedKey);
      setUserApiDetails(resp);
      setShowApiModal(true);
    } else {
      showErrorToast(resp.message);
    }
  };

  return (
    <main>
      <Card>
        <form className=" space-y-4" onSubmit={userApiForm.handleSubmit(onSubmit)}>
          <div className="flex justify-between items-center">
            <HeadingIcon Icon={KeyRound} title="Create User API" />

            <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              type="submit"
              loading={userApiForm.formState.isSubmitting}
              noAccess={
                type === "msp"
                  ? permissions.mspKey !== "write"
                  : type === "tenant"
                    ? permissions.tntKey !== "write"
                    : false
              }
            >
              Submit
            </Button>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <Input
              label="Token Name"
              required
              {...userApiForm.register("tokenName")}
              error={!!userApiForm.formState.errors.tokenName}
              helperText={userApiForm.formState.errors.tokenName && "Field is required"}
            />
            <Input
              label="Description"
              required
              className="col-span-2"
              {...userApiForm.register("description")}
              error={!!userApiForm.formState.errors.description}
              helperText={userApiForm.formState.errors.description && "Field is required"}
            />
          </div>
          <hr className="hr !my-8" />
          <HeadingIcon Icon={ListTodo} title="User API Permissions" />

          {accessListLoading ? (
            <CardLoadingSkeleton />
          ) : (
            <div className=" w-full">
              <div className="flex gap-4  items-center my-4">
                <Checkbox
                  checked={selectAllService}
                  onCheckedChange={(value) => {
                    if (typeof value === "boolean") {
                      setSelectAllService(value);
                      allServiceChange(value, setAuthorizationList);
                    }
                  }}
                />
                <h3 className="heading-3">Administrator Access</h3>
                <p className="helper-text">( This will select all the services )</p>
              </div>

              <Table
                resizable={false}
                head={
                  <>
                    <TableHead>Service Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Write</TableHead>
                    <TableHead>Read</TableHead>
                  </>
                }
                body={
                  <>
                    {Object.entries(authorizationList).map(([groupName, services]) => (
                      <React.Fragment key={groupName}>
                        <tr>
                          <TableRow className="font-medium text-base text-foreground" colSpan={100}>
                            {groupName}
                          </TableRow>
                        </tr>

                        {services.map((item) => (
                          <React.Fragment key={item.service}>
                            {item.subServices.map((subService) => {
                              return subService.actions ? (
                                <tr
                                  className="cursor-pointer"
                                  // key={thing.thingName}
                                >
                                  <TableRow>{geoServiceNameChange(subService.name)}</TableRow>
                                  <TableRow className="text-muted-foreground">
                                    {subService.desc}
                                  </TableRow>

                                  {subService.actions.map((action) => (
                                    <TableRow>
                                      <Checkbox
                                        checked={action.isChecked}
                                        onCheckedChange={(value) => {
                                          handleAPIServiceChange(
                                            value,
                                            action.actionLabel,
                                            setAuthorizationList,
                                            item.service,
                                            subService.feature
                                          );
                                        }}
                                      />
                                    </TableRow>
                                  ))}
                                </tr>
                              ) : (
                                <>
                                  {subService.subFeatures.map((basic) => {
                                    return (
                                      <tr
                                        className="cursor-pointer"
                                        // key={thing.thingName}
                                      >
                                        <TableRow>{geoServiceNameChange(basic.name)}</TableRow>
                                        <TableRow className="text-muted-foreground">
                                          {basic.desc}
                                        </TableRow>

                                        {basic.actions.map((action) => (
                                          <TableRow>
                                            <Checkbox
                                              checked={action.isChecked}
                                              onCheckedChange={(value) => {
                                                handleAPIServiceChange(
                                                  value,
                                                  action.actionLabel,
                                                  setAuthorizationList,
                                                  item.service,
                                                  subService.feature,
                                                  basic.subFeature
                                                );
                                              }}
                                            />
                                          </TableRow>
                                        ))}
                                      </tr>
                                    );
                                  })}
                                </>
                              );
                            })}
                          </React.Fragment>
                        ))}
                      </React.Fragment>
                    ))}
                  </>
                }
              />
            </div>
          )}
        </form>
      </Card>
      <AlertDialog open={showApiModal}>
        <AlertDialogContent className="min-w-[35rem]">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <MessageCircleWarning size="1.75rem" className="text-yellow-600 mr-2" />
              User API Key
            </AlertDialogTitle>
            <AlertDialogDescription>
              This is your only chance to obtain the API key—act now and secure it before it's too
              late!
            </AlertDialogDescription>
          </AlertDialogHeader>
          <Input
            small
            value={userApiDetails?.encodedKey}
            endIcon={
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <IconButton
                      onClick={() => {
                        handleCopyClick(userApiDetails?.encodedKey);
                      }}
                    >
                      <Copy size={INPUT_ICON_SIZE} />
                    </IconButton>
                  </TooltipTrigger>
                  <TooltipContent>Copy API</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            }
          />
          <AlertDialogFooter>
            <Button
              onClick={() => {
                navigate(-1);
                queryClient.invalidateQueries({ queryKey: ["single-user-api-list"] });
              }}
            >
              Done
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  );
};

export default CreateApi;
