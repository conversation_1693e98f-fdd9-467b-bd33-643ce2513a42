import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Table, { TableHead, TableRow } from "@components/Table";
import { Checkbox } from "@components/shadcn/components/checkbox";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useAccessTemplateList from "@hooks/tenant/useAccessTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE, MSP_ROLES, TENANT_ROLES } from "@utils/utilities";
import { KeyRound, ListTodo } from "lucide-react";
import React, { useEffect, useState } from "react";
import { AtSign, Calendar, FileText, Trash, User, Users } from "lucide-react";
import { useLocation } from "react-router-dom";
import { deleteUserApi } from "../../../api/user";
import Button from "../../../components/Button";
import { Card } from "@components/ui";
import { useAppSelector } from "../../../store";
import { showErrorToast, showSuccessToast } from "../../../utils";
import { accessListModifier } from "../../../utils/misc";
import { geoServiceNameChange } from "../UserGroups/CreateUserGroup";
import { FeatureItem } from "@src/features/features";

const ApiDetailPage = ({
  type
}: {
  type?: "tenant" | "operator" | "msp" | "platform-operator";
}) => {
  const { state } = useLocation();
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [authorizationList, setAuthorizationList] = useState<Record<string, FeatureItem[]>>({});

  const apiDetails = state || {};
  const { user } = useAppSelector((state) => state.user);
  const { data: accessList, isLoading: isAccessListLoading } = useAccessTemplateList({
    type: type as "tenant" | "operator" | "msp" | "platform-operator"
  });
  const navigate = useCustomNavigate();
  const userType = MSP_ROLES.includes(user?.role ?? "") ? "msp" : "tenant";
  const { data: permissions } = useUserGroupPermissions();
  useEffect(() => {
    if (!accessList?.data) return;
    const modifiedTemplate = accessListModifier(accessList?.data);
    setAuthorizationList(modifiedTemplate as Record<string, FeatureItem[]>);
  }, [accessList]);

  const submitHandler = async () => {
    setLoadingBtn(true);
    const resp = await deleteUserApi({
      userType,
      //email: apiDetails?.email,
      keyId: apiDetails?.keyId
    });
    if (resp.status === "Success") {
      showSuccessToast(resp.message);

      queryClient.invalidateQueries({ queryKey: ["user-api-list"] });
      navigate(-1);
    } else {
      showErrorToast(resp.message);
    }
    setLoadingBtn(false);
  };
  return (
    <main className="space-y-6">
      <Card className=" space-y-6">
        <div className="flex justify-between gap-2 items-center ">
          <HeadingIcon Icon={KeyRound} title="API key Details" />
          {(type === "msp" || TENANT_ROLES.includes(user?.role ?? "")) && (
            <Button
              onClick={() => setShowDeletePrompt(true)}
              startIcon={<Trash size={BUTTON_ICON_SIZE} />}
              color="red"
              noAccess={
                type === "msp"
                  ? permissions.mspKey !== "write"
                  : type === "tenant"
                    ? permissions.tntKey !== "write"
                    : false
              }
            >
              Delete
            </Button>
          )}
        </div>

        <section className="grid grid-cols-2 gap-4">
          <DetailsCell
            title="Token Name"
            icon={<User size={DATACELL_ICON_SIZE} />}
            data={apiDetails.tokenName}
          />

          <DetailsCell
            icon={<KeyRound size={DATACELL_ICON_SIZE} />}
            title="Key ID"
            data={apiDetails.keyId}
          />

          <DetailsCell
            title="Created"
            icon={<Calendar size={DATACELL_ICON_SIZE} />}
            data={convetUTCToLocal(apiDetails.createdAt)}
          />

          <DetailsCell
            icon={<AtSign size={DATACELL_ICON_SIZE} />}
            title="Email"
            data={apiDetails.email}
          />

          <DetailsCell
            title="Role"
            icon={<Users size={DATACELL_ICON_SIZE} />}
            data={apiDetails.role}
          />

          <DetailsCell
            icon={<FileText size={DATACELL_ICON_SIZE} />}
            title="Description"
            data={apiDetails.description}
          />
        </section>
      </Card>
      <Card className=" space-y-4">
        <HeadingIcon Icon={ListTodo} title="API key Permissions" />

        {isAccessListLoading ? (
          <CardLoadingSkeleton />
        ) : (
          <div className=" w-full ">
            <Table
              head={
                <>
                  <TableHead>Service Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Write</TableHead>
                  <TableHead>Read</TableHead>
                </>
              }
              body={
                <>
                  {Object.entries(authorizationList).map(([groupName, services]) => (
                    <React.Fragment key={groupName}>
                      <tr>
                        <TableRow className="font-medium text-base text-foreground" colSpan={100}>
                          {groupName}
                        </TableRow>
                      </tr>
                      {services.map((item) =>
                        item.subServices.map((subService) =>
                          subService.actions ? (
                            <tr
                              className="cursor-pointer"
                              // key={thing.thingName}
                            >
                              <TableRow>{geoServiceNameChange(subService.name)}</TableRow>
                              <TableRow className="text-muted-foreground">
                                {subService.desc}
                              </TableRow>
                              <TableRow>
                                <Checkbox
                                  checked={
                                    apiDetails.keyAuthorization.authorizationData?.[item.service]?.[
                                      subService.feature
                                    ] === "write"
                                  }
                                />
                              </TableRow>
                              <TableRow>
                                <Checkbox
                                  checked={
                                    apiDetails.keyAuthorization.authorizationData?.[item.service]?.[
                                      subService.feature
                                    ] === "write" ||
                                    apiDetails.keyAuthorization.authorizationData?.[item.service]?.[
                                      subService.feature
                                    ] === "read"
                                  }
                                />
                              </TableRow>
                            </tr>
                          ) : (
                            <>
                              {subService.subFeatures.map((basic) => {
                                return (
                                  <tr
                                    className="cursor-pointer"
                                    // key={thing.thingName}
                                  >
                                    <TableRow>{geoServiceNameChange(basic.name)}</TableRow>
                                    <TableRow className="text-muted-foreground">
                                      {basic.desc}
                                    </TableRow>
                                    <TableRow>
                                      <Checkbox
                                        checked={
                                          apiDetails.keyAuthorization.authorizationData?.[
                                            item.service
                                          ]?.[subService.feature]?.[basic.subFeature] === "write"
                                        }
                                      />
                                    </TableRow>
                                    <TableRow>
                                      <Checkbox
                                        checked={
                                          apiDetails.keyAuthorization.authorizationData?.[
                                            item.service
                                          ]?.[subService.feature]?.[basic.subFeature] === "write" ||
                                          apiDetails.keyAuthorization.authorizationData?.[
                                            item.service
                                          ]?.[subService.feature]?.[basic.subFeature] === "read"
                                        }
                                      />
                                    </TableRow>
                                  </tr>
                                );
                              })}
                            </>
                          )
                        )
                      )}
                    </React.Fragment>
                  ))}
                </>
              }
            />
          </div>
        )}
      </Card>
      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={apiDetails?.tokenName}
        onCancel={() => setShowDeletePrompt(false)}
        loading={loadingBtn}
        onConfirm={() => {
          submitHandler();
        }}
      />
    </main>
  );
};

export default ApiDetailPage;
