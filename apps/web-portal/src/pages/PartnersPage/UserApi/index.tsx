import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useUserApiList from "@hooks/tenant/useUserApiList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import {
  BUTTON_ICON_SIZE,
  DEFAULT_PAGE_COUNT,
  INPUT_ICON_SIZE,
  TENANT_ROLES
} from "@utils/utilities";
import { Plus, Search } from "lucide-react";
import { useState } from "react";
import Button from "../../../components/Button";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import { useAppSelector } from "../../../store";

const UserApiPage = ({ type = "tnt" }: { type: "msp" | "tnt" }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const navigate = useCustomNavigate();

  const [sortFn, sort] = useTableSort();

  const activeUser = useAppSelector((state) => state.user.user);

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();

  const { data: userApiList, isLoading: userApiLoading } = useUserApiList({
    page,
    limit,
    search: debouncedSearchQuery,
    type
  });

  return (
    <main className="space-y-4">
      <HeaderSection
        title="API Keys"
        description="Manage your API Keys"
        actions={
          (type === "msp" || TENANT_ROLES.includes(activeUser?.role || "")) && (
            <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              onClick={() => navigate("createApi")}
              noAccess={
                type === "msp"
                  ? permissions.mspKey !== "write"
                  : type === "tnt"
                    ? permissions.tntKey !== "write"
                    : false
              }
            >
              Create API
            </Button>
          )
        }
      />
      <Card className="space-y-4">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setPage(1);
            setSearchQuery(e.target.value);
          }}
          className="min-w-[25rem]"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("tokenName", order)}>Token Name</TableHead>
              <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
              <TableHead onSort={(order) => sort("role", order)}>Role</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            </>
          }
          body={
            userApiLoading ? (
              <TableRowsSkeleton />
            ) : !userApiList?.apiKeys?.length ? (
              <DataNotFound title="No User Api Available" isTable />
            ) : (
              <>
                {userApiList.apiKeys.toSorted(sortFn).map((userApi, i) => (
                  <tr
                    className="cursor-pointer"
                    onClick={() => navigate("apiDetails", { state: userApi })}
                    key={userApi.encryptedKey}
                  >
                    <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                    <TableRow title>{userApi.tokenName}</TableRow>
                    <TableRow>{userApi.email}</TableRow>
                    <TableRow>{userApi.role}</TableRow>
                    <TableRow>{convetUTCToLocal(userApi.createdAt)}</TableRow>
                  </tr>
                ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: userApiList?.pages || 1
          }}
        />
      </Card>
    </main>
  );
};

export default UserApiPage;
