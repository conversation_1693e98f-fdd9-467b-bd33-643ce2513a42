import { deleteTenantAccessTemplate } from "@api/partner";
import Button from "@components/Button";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useTenantAccessDetails from "@hooks/tenant/useTenantAccessDetails";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { Cog, Edit2, List, Trash, UserPen } from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";

const TenantAccessDetails = () => {
  const { templateId } = useParams();
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);

  const navigate = useCustomNavigate();
  const { data: templateDetails, isLoading } = useTenantAccessDetails(templateId as string)

  

  const deleteTemplateMutation = useMutation({
    mutationFn: deleteTenantAccessTemplate,
    onSuccess: () => {
      showSuccessToast("Successfully deleted Tenant access template");
      queryClient.invalidateQueries({ queryKey: ["tenant-access-list"] });
      setTimeout(() => {
        navigate(-1);
      }, 500);
    },
    onError: (error) => {
      showErrorToast(error?.message);
    }
  });

  return (
    <section className="space-y-6">
      <Card className="space-y-6">
        <div className="flex justify-between gap-2 items-center ">
          <HeadingIcon Icon={UserPen} title="Template Details" />

          <div className="flex gap-4">
            <Button
              onClick={() => navigate(`edit`, { state: templateDetails })}
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
              color="gray"
              // noAccess={permissions.policy !== "write"}
            >
              Edit
            </Button>
            <Button
              startIcon={<Trash size={BUTTON_ICON_SIZE} />}
              color="red"
              onClick={() => setShowDeletePrompt(true)}
              // noAccess={permissions.policy !== "write"}
            >
              Delete
            </Button>
          </div>
        </div>

        {isLoading || !templateDetails ? (
          <CardLoadingSkeleton />
        ) : (
          <div className="grid grid-cols-2 gap-4">
            <DetailsCell
              // icon={<Cpu size={DATACELL_ICON_SIZE} />}
              title="Id"
              data={templateDetails.id}
            />
            <DetailsCell
              // icon={<Clock size={DATACELL_ICON_SIZE} />}
              title="Tenant"
              data={templateDetails.tenant}
            />

            <DetailsCell
              // icon={<Sliders size={DATACELL_ICON_SIZE} />}
              title="Template Name"
              data={templateDetails.name}
            />
          </div>
        )}
      </Card>
      <Card className="space-y-6">
        <HeadingIcon Icon={List} title="Features" />

        {isLoading || !templateDetails ? (
          <CardLoadingSkeleton />
        ) : templateDetails.features?.length ? (
          <div className="grid grid-cols-4 gap-4">
            {templateDetails.features?.map((feature:string[],i:number) => (
              <Card key={i} className=" p-4">
                <div className="flex items-center gap-4">
                  <Cog size={BUTTON_ICON_SIZE} className="text-blue-500" />
                  <h4 className="heading-3">{feature}</h4>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <DataNotFound title="No Features Found" />
        )}
      </Card>
      <ConfirmPrompt
        show={showDeletePrompt}
        item={templateDetails?.name}
        validate
        onCancel={() => setShowDeletePrompt(false)}
        loading={deleteTemplateMutation.isPending}
        onConfirm={() => {
          deleteTemplateMutation.mutate(templateId);
        }}
      />
    </section>
  );
};

export default TenantAccessDetails;
