import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import { Tabs, TabsList, TabsTrigger } from "@components/shadcn/components/tabs";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import {
  BUTTON_ICON_SIZE,
  DEFAULT_PAGE_COUNT,
  INPUT_ICON_SIZE
} from "@frontend/shared/config/defaults";
import useTenantAccessList from "@hooks/tenant/useTenantAccessList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { getTableIndex } from "@utils/tableUtils";
import { Plus, Search } from "lucide-react";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";

const TenantAccessList = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchParams, setSearchParams] = useSearchParams();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();
  const [currentView, setCurrentView] = useState(searchParams.get("tab") || "msp");

  const { data: tenantAccessList, isLoading } =  useTenantAccessList({page,limit,view:currentView})
  

  const handleTabChange = (newValue : string) => {
    setCurrentView(newValue);
    setSearchParams({ tab: newValue });
  };

  return (
    <section className=" space-y-6">
      <div className="between items-center">
        <Tabs defaultValue={currentView}>
          <TabsList className="grid  grid-cols-2 ">
            <TabsTrigger
              value="msp"
              onClick={() => {
                handleTabChange("msp");
              }}
            >
              MSP Templates
            </TabsTrigger>
            <TabsTrigger value="tenant" onClick={() => handleTabChange("tenant")}>
              Tenant Templates
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex gap-4 items-center">
          <Input
            value={searchQuery}
            onChange={(e) => {
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            placeholder="Search"
            className="w-[25rem]"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("createTenantAccess")}
          >
            Add Template
          </Button>
        </div>
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            {currentView === "tenant" && (
              <TableHead onSort={(order) => sort("tenant", order)}>Tenant</TableHead>
            )}

            <TableHead>Features</TableHead>
          </>
        }
        body={
          isLoading ? (
            <TableRowsSkeleton />
          ) : !tenantAccessList?.result?.length ? (
            <DataNotFound title="No Access Template Available" isTable />
          ) : (
            <>
              {tenantAccessList.result.toSorted(sortFn).map((access:{id:string,name:string, tenant : string,features:string}, i:number) => (
                <tr
                  className="cursor-pointer"
                  onClick={() => navigate(`${access.id}`)}
                  key={access.id}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{access.name}</TableRow>
                  {currentView === "tenant" && <TableRow>{access.tenant}</TableRow>}

                  <TableRow>{access.features?.length}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: tenantAccessList?.pages
        }}
      />
    </section>
  );
};

export default TenantAccessList;
