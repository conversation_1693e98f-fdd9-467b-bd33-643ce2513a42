import { createTenantAccessTemplate, editTenantAccessTemplate } from "@api/partner";
import Button from "@components/Button";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import { Checkbox } from "@components/shadcn/components/checkbox";
import Table, { TableHead, TableRow } from "@components/Table";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useAccessTemplateList from "@hooks/tenant/useAccessTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { useMutation } from "@tanstack/react-query";
import { DEFAULT_VALIDATE } from "@utils/from_schema";
import { showErrorToast, showSuccessToast } from "@utils/index";
import {
  accessModifierTenantAccess,
  enableTemplateService,
  filterAndCleanAccessData
} from "@utils/misc";
import { queryClient } from "@utils/queryClient";
import { ListTodo, Plus, UserPen } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useLocation } from "react-router-dom";
import { geoServiceNameChange } from "../UserGroups/CreateUserGroup";
import { FeatureItem } from "@src/features/features";

const CreateTenantAccessTemplate = ({ mode }: { mode: "new" | "edit" }) => {
  const [authorizationList, setAuthorizationList] = useState<Record<string, FeatureItem[]>>({});
  const { state: tenantTemplate } = useLocation();

  const navigate = useCustomNavigate();
  const templateForm = useForm({
    defaultValues: {
      name: mode === "edit" ? tenantTemplate.name : ""
    }
  });
  const { data: accessList, isLoading: accessListLoading } = useAccessTemplateList({ type: "msp" });

  useEffect(() => {
    let updatedData;
    if (!accessList) {
      return;
    }
    if (mode === "edit") {
      function addEnableFlag(firstArray: FeatureItem[], secondArray: FeatureItem[]) {
        // Create a Set of identifiers for quick lookup
        const enabledIdentifiers = new Set(
          secondArray.flatMap((item) =>
            item.subServices.flatMap((subService) => [
              `${item.service}:${subService.name}`,
              ...(subService.subFeatures || []).map(
                (subFeature) => `${item.service}:${subService.name}:${subFeature.name}`
              )
            ])
          )
        );

        const grouped: { [key: string]: FeatureItem[] } = {};

        firstArray.forEach((item) => {
          item.subServices.forEach((subService) => {
            if (subService.actions) {
              const group = subService.group || "Others";
              const modifiedSubService = {
                ...subService,
                enabled: enabledIdentifiers.has(`${item.service}:${subService.name}`)
              };

              if (!grouped[group]) grouped[group] = [];
              let serviceGroup = grouped[group].find((g) => g.service === item.service);
              if (!serviceGroup) {
                serviceGroup = { ...item, subServices: [] };
                grouped[group].push(serviceGroup);
              }
              serviceGroup.subServices.push(modifiedSubService);
            } else if (subService.subFeatures) {
              subService.subFeatures.forEach((sf) => {
                const group = sf.group || "Others";
                const modifiedSubFeature = {
                  ...sf,
                  enabled: enabledIdentifiers.has(`${item.service}:${subService.name}:${sf.name}`)
                };

                if (!grouped[group]) grouped[group] = [];
                let serviceGroup = grouped[group].find((g) => g.service === item.service);
                if (!serviceGroup) {
                  serviceGroup = { ...item, subServices: [] };
                  grouped[group].push(serviceGroup);
                }
                serviceGroup.subServices.push({
                  ...subService,
                  subFeatures: [modifiedSubFeature]
                });
              });
            }
          });
        });

        return grouped;
      }
      updatedData = addEnableFlag(accessList?.data, tenantTemplate.accessTemplate);
    } else {
      updatedData = accessModifierTenantAccess(accessList?.data);
    }
    setAuthorizationList(updatedData as Record<string, FeatureItem[]>);

    // setAuthorizationList(mode === "edit" ? editModifiedTemplate : modifiedTemplate);
  }, [accessList]);

  const templateMutation = useMutation({
    mutationFn: createTenantAccessTemplate,
    onSuccess: () => {
      showSuccessToast("Successfully created Tenant Access Template");
      queryClient.invalidateQueries({ queryKey: ["tenant-access-list"] });
      navigate(-1);
    },
    onError: (error) => {
      showErrorToast(error?.message);
    }
  });

  const templateEditMutation = useMutation({
    mutationFn: editTenantAccessTemplate,
    onSuccess: () => {
      showSuccessToast("Tenant Access Template updated successfully");
      queryClient.invalidateQueries({ queryKey: ["tenant-access-details", tenantTemplate.id] });
      navigate(-1);
    },
    onError: (error) => {
      showErrorToast(error?.message);
    }
  });

  const onSubmit = async (values: Record<string, string>) => {
    const payload = {
      name: values.name,
      template: filterAndCleanAccessData(authorizationList)
    };
    if (mode === "edit") {
      templateEditMutation.mutate({ templateId: tenantTemplate.id, payload });
    } else {
      templateMutation.mutate(payload);
    }
  };

  return (
    <Card>
      <form className=" space-y-4" onSubmit={templateForm.handleSubmit(onSubmit)}>
        <div className="flex justify-between items-center">
          <HeadingIcon Icon={UserPen} title={`Tenant Access Template`} />

          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            loading={templateForm.formState.isSubmitting}
            // noAccess={
            //   type === "msp"
            //     ? permissions.mspUSers !== "write"
            //     : type === "tenant"
            //       ? permissions.tntUsers !== "write"
            //       : false
            // }
          >
            Submit
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <Input
            required
            label="Template Name"
            // disabled={mode === "edit"}
            {...templateForm.register("name", DEFAULT_VALIDATE.schema)}
            error={!!templateForm.formState.errors.name}
            helperText={templateForm.formState.errors.name && DEFAULT_VALIDATE.message}
          />
        </div>
        <hr className="hr !my-8" />
        <HeadingIcon Icon={ListTodo} title="Template Permissions" />

        {accessListLoading ? (
          <CardLoadingSkeleton />
        ) : (
          <div className=" w-full rounded-md">
            <Table
              resizable={false}
              head={
                <>
                  <TableHead>Service Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Enabled</TableHead>
                </>
              }
              body={
                <>
                  {Object.entries(authorizationList).map(([groupName, services]) => (
                    <React.Fragment key={groupName}>
                      <tr>
                        <TableRow className="font-medium text-base text-foreground" colSpan={100}>
                          {groupName}
                        </TableRow>
                      </tr>
                      {services.map((item) => (
                        <React.Fragment key={item.service}>
                          {item.subServices.map((subService, subIdx) =>
                            subService.actions ? (
                              <tr key={`${item.service}-${subIdx}`} className="cursor-pointer">
                                <TableRow>{geoServiceNameChange(subService.name)}</TableRow>
                                <TableRow className="text-muted-foreground">
                                  {subService.desc}
                                </TableRow>
                                <TableRow className="text-muted-foreground">
                                  <Checkbox
                                    checked={subService.enabled}
                                    onCheckedChange={(value) => {
                                      enableTemplateService(
                                        value,
                                        setAuthorizationList,
                                        item.service,
                                        subService.feature
                                      );
                                    }}
                                  />
                                </TableRow>
                              </tr>
                            ) : (
                              subService.subFeatures.map((basic, basicIdx) => (
                                <tr
                                  key={`${item.service}-${subIdx}-basic-${basicIdx}`}
                                  className="cursor-pointer"
                                >
                                  <TableRow>{geoServiceNameChange(basic.name)}</TableRow>
                                  <TableRow className="text-muted-foreground">
                                    {basic.desc}
                                  </TableRow>
                                  <TableRow className="text-muted-foreground">
                                    <Checkbox
                                      checked={basic.enabled}
                                      onCheckedChange={(value) => {
                                        enableTemplateService(
                                          value,
                                          setAuthorizationList,
                                          item.service,
                                          subService.feature,
                                          basic.subFeature
                                        );
                                      }}
                                    />
                                  </TableRow>
                                </tr>
                              ))
                            )
                          )}
                        </React.Fragment>
                      ))}
                    </React.Fragment>
                  ))}
                </>
              }
            />
          </div>
        )}
      </form>
    </Card>
  );
};

export default CreateTenantAccessTemplate;
