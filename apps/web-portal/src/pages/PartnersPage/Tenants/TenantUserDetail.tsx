import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useAllUserDetails from "@hooks/tenant/useAllUserDetails";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { useMutation } from "@tanstack/react-query";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE, ROLES_Mapped } from "@utils/utilities";
import {
  AtSign,
  Calendar,
  Clock,
  Edit2,
  Map,
  MapPin,
  Monitor,
  Phone,
  Trash,
  User,
  UserCheck,
  Users
} from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import colors from "tailwindcss/colors";
import { deleteUser } from "../../../api/user";
import Button from "../../../components/Button";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import Label from "../../../components/Label";
import { showErrorToast, showSuccessToast } from "../../../utils";
import { convetUTCToLocal } from "../../UserTypes/Tracking/HomeSection/utils";
import UpdateUserModal from "../UpdateUserModal";

function TenantUserDetail() {
  const updateUserModalRef = useRef<ElementRef<typeof UpdateUserModal>>(null);
  const { userEmail: email } = useParams();
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);

  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();

  const {
    data: userDetail,
    isLoading
  } =  useAllUserDetails(email as string, "tenant");

  console.log("User Detail", userDetail);

  const deleteMutation = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      showSuccessToast("User deleted successfully");
      setTimeout(() => {
        setShowDeletePrompt(false);
        navigate(-1);
      }, 500);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

   const handleEditClick = () => {
    if (updateUserModalRef.current && userDetail) {
      updateUserModalRef.current.openModal({
        userDetail,
        type: "tenant",
        role: userDetail.role ? [userDetail.role] : [],
        email: email
      });
    }
  };
  return (
    <main>
      <Card className=" space-y-6">
        {isLoading ? (
          <CardLoadingSkeleton />
        ) : !userDetail ? <DataNotFound title = "User Not Found"/>: (
          <>
            <div className="between items-center">
              <HeadingIcon Icon={User} title="Users Details" />

              <div className="flex gap-4">
                <Button
                  startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                  onClick={handleEditClick}
                  color="orange"
                  noAccess={permissions.tntUsers !== "write"}
                >
                  Edit
                </Button>
                <Button
                  startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                  onClick={() => setShowDeletePrompt(true)}
                  color="red"
                  loading={deleteMutation.isPending}
                  noAccess={permissions.tntUsers !== "write"}
                >
                  Delete
                </Button>
              </div>
            </div>
            <section className="grid grid-cols-3 gap-4">
              <DetailsCell
                icon={<User size={DATACELL_ICON_SIZE} />}
                title="Name"
                data={userDetail.name}
              />
              <DetailsCell
                title="Email"
                icon={<AtSign size={DATACELL_ICON_SIZE} />}
                data={userDetail.email}
              />

              <DetailsCell
                title="User Role"
                icon={<UserCheck size={DATACELL_ICON_SIZE} />}
                data={
                  <Label
                    text={ROLES_Mapped.find((role) => role.key === userDetail.role)?.value}
                    color={ROLES_Mapped.find((role) => role.key === userDetail.role)?.color as keyof typeof colors}
                  />
                }
              />
              {userDetail.userGroup && (
                <DetailsCell
                  title="User Group"
                  icon={<Users size={DATACELL_ICON_SIZE} />}
                  data={
                    <span
                      onClick={() => navigate(`/UM/userGroups/${userDetail.userGroupId}`)}
                      className="font-semibold text-foreground cursor-pointer hover:underline"
                    >
                      {userDetail.userGroup}
                    </span>
                  }
                />
              )}
              <DetailsCell
                title="Tenant"
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                data={userDetail.tenant}
              />
              <DetailsCell
                title="Monitoring Groups"
                flexDirectionRow
                icon={<Monitor size={DATACELL_ICON_SIZE} />}
                data={userDetail.monitoringGroups?.map((group : string) => (
                  <Label text={group} color="green" />
                ))}
              />

              <DetailsCell
                title="Phone"
                icon={<Phone size={DATACELL_ICON_SIZE} />}
                data={userDetail.phone}
              />
              <DetailsCell
                title="Address"
                icon={<Map size={DATACELL_ICON_SIZE} />}
                data={userDetail.address}
              />
              <DetailsCell
                title="Pincode"
                icon={<MapPin size={DATACELL_ICON_SIZE} />}
                data={userDetail.pincode}
              />
              <DetailsCell
                title="Created At"
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(userDetail.createdAt)}
              />
              <DetailsCell
                title="Updated At"
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(userDetail.updatedAt)}
              />
            </section>
          </>
        )}
      </Card>

        <UpdateUserModal ref = {updateUserModalRef} />
    
      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={userDetail?.name}
        onCancel={() => setShowDeletePrompt(false)}
        loading={deleteMutation.isPending}
        onConfirm={() => {
          deleteMutation.mutate({ type: "tenant", userEmail: email });
        }}
      />
    </main>
  );
}

export default TenantUserDetail;
