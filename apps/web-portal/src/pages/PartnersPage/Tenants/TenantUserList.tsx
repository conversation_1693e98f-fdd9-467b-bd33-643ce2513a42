import { fetchTenantUsers } from "@api/user";
import DataNotFound from "@components/DataNotFound";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { Users } from "@src/features/features";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { getTableIndex } from "@utils/tableUtils";
import {
  BUTTON_ICON_SIZE,
  DEFAULT_PAGE_COUNT,
  INPUT_ICON_SIZE,
  ROLES_Mapped
} from "@utils/utilities";
import { Plus, Search } from "lucide-react";
import { useState } from "react";
import colors from "tailwindcss/colors";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import Label from "../../../components/Label";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
/**
 * @type {React.FC<{type:'ent'|'pfm'}>}
 */
function TenantUserList({ type = "tenant" }: { type?: "tenant" | "msp" }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();

  const {
    data: tenantUserList,
    isLoading,
    isRefetching
  } = useQuery({
    queryKey: ["tenant-users-list", page, limit, debouncedSearchQuery],
    queryFn: fetchTenantUsers
  });

  return (
    <main className=" space-y-4">
      <HeaderSection
        title="Users"
        description="Manage your Users"
        actions={
          type === "tenant" && (
          <Button
            noAccess={permissions.tntUsers !== "write"}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("createUser")}
          >
            Add User
          </Button>
        )
        }
      />
      <Card className = "space-y-4">
        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search by Name"
          className="flex-1"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
            <TableHead onSort={(order) => sort("role", order)}> Role</TableHead>
            <TableHead onSort={(order) => sort("userGroup", order)}>User Group</TableHead>
            <TableHead onSort={(order) => sort("phone", order)}>Phone</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading || isRefetching ? (
            <TableRowsSkeleton />
          ) : !tenantUserList.data?.users || !tenantUserList.data?.users.length ? (
            <DataNotFound title="No Users Found" isTable />
          ) : (
            <>
              {tenantUserList.data?.users.toSorted(sortFn).map((user:Users, i:number) => (
                <tr
                  className="cursor-pointer"
                  onClick={() => navigate(user.email)}
                  // onClick={() => navigate(user.email)}
                  key={user.email}
                  // ref={
                  //   Math.max(arr.length - 5, 0) === i ? lastItemRef : null
                  // }
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{user.name}</TableRow>
                  <TableRow>{user.email}</TableRow>

                  <TableRow>
                    <Label
                      text={ROLES_Mapped.find((role) => role.key === user.role)?.value}
                      color={ROLES_Mapped.find((role) => role.key === user.role)?.color as keyof typeof colors}
                    />
                  </TableRow>
                  <TableRow>{user.userGroup || "N/A"}</TableRow>

                  <TableRow>{user.phone || "N/A"}</TableRow>
                  <TableRow>{convetUTCToLocal(user.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: tenantUserList?.data?.pages || 1
        }}
      />
      </Card>
    </main>
  );
}

export default TenantUserList;
