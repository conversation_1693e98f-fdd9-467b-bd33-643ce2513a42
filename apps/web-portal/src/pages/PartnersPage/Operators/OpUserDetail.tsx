import { deleteUser, fetchAllUserDetails } from "@api/user";
import Button from "@components/Button";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import { Card } from "@components/ui";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { Type } from "@src/features/features";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import {
  BUTTON_ICON_SIZE,
  DATACELL_ICON_SIZE,
  OPERATOR_ROLES,
  ROLES_Mapped
} from "@utils/utilities";
import {
  AtSign,
  Calendar,
  Clock,
  Edit2,
  Map,
  MapPin,
  Phone,
  Trash,
  User,
  UserCheck,
  Users
} from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import colors from "tailwindcss/colors";
import UpdateUserModal from "../UpdateUserModal";

const OpUserDetail = ({ type }:{type:Type}) => {
   const updateUserModalRef = useRef<ElementRef<typeof UpdateUserModal>>(null);
  const { userEmail } = useParams();
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const {
    data: userDetails,
    isLoading,
  } = useQuery({
    queryKey: [`msp-details-${type}`, userEmail, "operator"],
    queryFn: fetchAllUserDetails
  });
 console.log("UserDetails", userDetails);
  const navigate = useCustomNavigate();

  const deleteMutation = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      showSuccessToast("Operator user deleted successfully");
      setTimeout(() => {
        setShowDeletePrompt(false);
        navigate(-1);
      }, 500);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });
  const handleEditClick = () => {
    if (updateUserModalRef.current && userDetails) {
      updateUserModalRef.current.openModal({
        userDetail : userDetails.data,
        type: "tenant",
        role: userDetails.data.role ? [userDetails.data.role] : [],
        email: userDetails.data.email
      });
    }
  };

  return (
    <main>
      <Card className="space-y-6">
        {isLoading ? (
          <CardLoadingSkeleton />
        ) : (
          <>
            <div className="between items-center">
              <HeadingIcon Icon={Users} title={`${type} Users Details`} />

              {OPERATOR_ROLES.includes(userDetails.data.role) && (
                <div className="flex gap-4">
                  <Button
                    startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
                    onClick={handleEditClick}
                    color="orange"
                  >
                    Edit
                  </Button>
                  <Button
                    startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                    onClick={() => setShowDeletePrompt(true)}
                    loading={deleteMutation.isPending}
                    color="red"
                  >
                    Delete
                  </Button>
                </div>
              )}
            </div>
            <section className="grid grid-cols-3 gap-4">
              <DetailsCell
                icon={<User size={DATACELL_ICON_SIZE} />}
                title="Name"
                data={userDetails.data.name}
              />
              <DetailsCell
                title="Email"
                icon={<AtSign size={DATACELL_ICON_SIZE} />}
                data={userDetails.data.email}
              />

              <DetailsCell
                title="User Role"
                icon={<UserCheck size={DATACELL_ICON_SIZE} />}
                data={
                  <Label
                    text={ROLES_Mapped.find((role) => role.key === userDetails.data.role)?.value}
                    color={ROLES_Mapped.find((role) => role.key === userDetails.data.role)?.color as keyof typeof colors}
                  />
                }
              />
              {userDetails.data.userGroup && (
                <DetailsCell
                  title="User Group"
                  icon={<Users size={DATACELL_ICON_SIZE} />}
                  data={<Label text={userDetails.data.userGroup} />}
                />
              )}
              <DetailsCell
                title="Tenant"
                icon={<User size={DATACELL_ICON_SIZE} />}
                data={userDetails.data.tenant}
              />
              <DetailsCell
                title="Phone"
                icon={<Phone size={DATACELL_ICON_SIZE} />}
                data={userDetails.data.phone}
              />
              <DetailsCell
                title="Address"
                icon={<Map size={DATACELL_ICON_SIZE} />}
                data={userDetails.data.address}
              />
              <DetailsCell
                title="Pincode"
                icon={<MapPin size={DATACELL_ICON_SIZE} />}
                data={userDetails.data.pincode}
              />
              <DetailsCell
                title="Created At"
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(userDetails.data.createdAt)}
              />
              <DetailsCell
                title="Updated At"
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(userDetails.data.updatedAt)}
              />
            </section>
          </>
        )}
      </Card>
        <UpdateUserModal ref = {updateUserModalRef} />

      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={userDetails?.data?.name}
        loading={deleteMutation.isPending}
        onCancel={() => setShowDeletePrompt(false)}
        onConfirm={() => {
          deleteMutation.mutate({ type: "operator", userEmail });
        }}
      />
    </main>
  );
};

export default OpUserDetail;
