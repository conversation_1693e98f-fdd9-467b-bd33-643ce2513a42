import { fetchOperatorViewUsers } from "@api/user";
import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useQuery } from "@tanstack/react-query";
import { getTableIndex } from "@utils/tableUtils";
import {
  BUTTON_ICON_SIZE,
  DEFAULT_PAGE_COUNT,
  INPUT_ICON_SIZE,
  ROLES_Mapped
} from "@utils/utilities";
import { useState } from "react";
import { Search, UserPlus, Users } from "lucide-react";
import { Type,Users as UserType} from "@src/features/features";
import colors from "tailwindcss/colors";

const OpUserList = ({ type }:{type : Type}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const {
    data: operatorUsers,
    isLoading,
    isRefetching
  } = useQuery({
    queryKey: [
      `operator-user-list-${type}`,
      page,
      limit,
      debouncedSearchQuery,
      {
        onlyMspUsers: type === "MSP",
        onlyTenantUsers: type === "Tenant",
        onlyOpUsers: type === "Operator"
      }
    ],
    queryFn: fetchOperatorViewUsers
  });

  return (
    <section className="space-y-6">
      <div className="between items-center">
        <HeadingIcon Icon={Users} title={`${type} Users`} />

        <div className="flex gap-4">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search by Name"
            className="w-[25rem]"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />
          {type === "Operator" && (
            <Button
              startIcon={<UserPlus size={BUTTON_ICON_SIZE} />}
              onClick={() => navigate("createUser")}
            >
              Add Operator User
            </Button>
          )}
        </div>
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            {type === "Tenant" && (
              <TableHead onSort={(order) => sort("Tenant", order)}>Tenant Name</TableHead>
            )}
            <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
            <TableHead onSort={(order) => sort("role", order)}>Role</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isLoading || isRefetching ? (
            <TableRowsSkeleton />
          ) : !operatorUsers.data.users.length || !operatorUsers ? (
            <DataNotFound title="No Users Available" isTable />
          ) : (
            <>
              {operatorUsers.data.users.toSorted(sortFn).map((user:UserType, i:number) => (
                <tr
                  className="cursor-pointer"
                  onClick={() => navigate(user.email)}
                  key={user.email}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{user.name}</TableRow>
                  {type === "Tenant" && <TableRow>{user.tenant}</TableRow>}

                  <TableRow>{user.email}</TableRow>

                  <TableRow>
                    <Label
                      text={ROLES_Mapped.find((role) => role.key === user.role)?.value}
                      color={ROLES_Mapped.find((role) => role.key === user.role)?.color as keyof typeof colors}
                    />
                  </TableRow>
                  <TableRow>{convetUTCToLocal(user.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(user.updatedAt)}</TableRow>
                </tr>
              ))}
            </>
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: operatorUsers?.data.pages
        }}
      />
    </section>
  );
};

export default OpUserList;
