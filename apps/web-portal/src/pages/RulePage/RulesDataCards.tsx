import { useEffect, useState } from "react";
import { <PERSON><PERSON>ontaine<PERSON>, ArcherElement } from "react-archer";
import { GitBranch, Server, Terminal } from "lucide-react";
import { Card } from "@components/ui";
import Label from "../../components/Label";

const ArrowItemContainer = ({ targetIds = [], id, children }) => {
  const [visibility, setVisibility] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setVisibility(true);
    }, 500);
  }, []);

  return (
    <ArcherElement
      id={id}
      relations={
        !visibility
          ? []
          : targetIds.map((targetId) => ({
              targetId,
              targetAnchor: "left",
              sourceAnchor: "right",
              style: {
                strokeWidth: 2,
                endShape: {
                  arrow: {
                    arrowLength: 6,
                    arrowThickness: 6
                  }
                }
              }
            }))
      }
    >
      {children}
    </ArcherElement>
  );
};

const RulesDataCards = ({ ruleDetail }) => {
  return (
    <ArcherContainer strokeColor="grey" offset={6}>
      <div className="flex items-center justify-evenly">
        <ArrowItemContainer id="input-card" targetIds={["trigger-card"]}>
          <Card className=" w-[250px]  ring-[.3px] ring-blue-400  !p-0">
            <div className="flex gap-2 items-center px-4 py-3 border-b border-gray-500">
              <Terminal size={20} className="text-blue-500" />
              <h3 className="content-base">
                {ruleDetail.ruleType === "event"
                  ? "Event"
                  : ruleDetail.alias?.thingName
                    ? "Thing"
                    : "Product"}{" "}
                Inputs
              </h3>
            </div>

            <div className="p-3 space-y-3">
              {ruleDetail.ruleType === "event" ? (
                <div>
                  <span className="content-heading ">{ruleDetail.events.length}</span> Events
                </div>
              ) : (
                <Label
                  className="text-md"
                  text={
                    ruleDetail?.alias?.thingName
                      ? ruleDetail?.alias?.thingName
                      : ruleDetail?.alias?.productName
                  }
                  color="blue"
                  linear
                />
              )}
            </div>
          </Card>
        </ArrowItemContainer>

        <ArrowItemContainer
          id="trigger-card"
          targetIds={ruleDetail?.destinations.map((item, index) => `action-${index}-card`)}
        >
          <Card className=" w-[300px]  ring-[.3px] ring-yellow-400  !p-0">
            <div className="flex gap-2 items-center px-4 py-3 border-b border-gray-500">
              <GitBranch size={20} className="text-yellow-500" />
              <h3 className="content-base">Logics</h3>
            </div>

            <div className="grid grid-cols-2 p-3">
              <div>
                <span className="content-heading ">
                  {Object.entries(ruleDetail?.payloadTransform || {}).length}{" "}
                </span>{" "}
                Transform
              </div>

              <div>
                <span className="content-heading ">
                  {Object.entries(ruleDetail?.conditions || {}).length}
                </span>{" "}
                Conditions
              </div>
            </div>
          </Card>
        </ArrowItemContainer>

        <div className="flex gap-8 items-center justify-center flex-col">
          {ruleDetail?.destinations.map((action, index) => (
            <ArcherElement id={`action-${index}-card`}>
              <Card className=" w-[300px]  ring-[.3px] ring-green-400  !p-0">
                <div className="flex gap-2 items-center px-4 py-3 border-b border-gray-500">
                  <Server size={20} className="text-green-500" />
                  <h3 className="content-base">Action</h3>
                  <Label className="ml-auto text-md" text={action.type} color="green" linear />
                </div>

                <div className=" p-3">
                  {action.type === "republish" && (
                    <div className="content">
                      Topic :<span className="title-heading-6"> {action.args.topic} </span>
                    </div>
                  )}
                  {action.type === "webhook" && (
                    <div>
                      <span className="content-heading ">{action.list.length} </span> Webhooks
                    </div>
                  )}
                  {action.type === "mqtt" && (
                    <div>
                      <span className="content-heading ">{action.list.length} </span> MQTT
                    </div>
                  )}
                  {action.type === "teams" && (
                    <p className="content truncate ">
                      URL: <span className="title-heading-6"> {action.args.webhookUrl}</span>
                    </p>
                  )}
                  {action.type === "incidents" && (
                    <p className="content truncate ">
                      Incident Type:{" "}
                      <span className="title-heading-6"> {action.args.ticketType}</span>
                    </p>
                  )}
                  {action.type === "notification" && (
                    <div>
                      <span className="content-heading ">
                        {Object.values(action.args).filter((value) => value === true).length}{" "}
                      </span>{" "}
                      Notification Enabled
                    </div>
                  )}
                </div>
              </Card>
            </ArcherElement>
          ))}
        </div>
      </div>
    </ArcherContainer>
  );
};

export default RulesDataCards;
