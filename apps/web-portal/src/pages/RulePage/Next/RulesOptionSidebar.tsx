import { Accordion } from "@components/shadcn/components/accordian";
import { <PERSON>u, ChevronFirst } from "lucide-react";
import { IconButton } from "@mui/material";
import clsx from "clsx";
import { DragEvent } from "react";
import { AllNames, NodeType } from "./RuleConfig";
import RuleSideBarInputList from "./SideBarOptions/RuleSideBarInputList";
import RuleSidebarActions from "./SideBarOptions/RuleSidebarActions";
import RuleSidebarTriggers from "./SideBarOptions/RuleSidebarTransforms";
import useRuleSideBarOpen from "./hooks/useRuleSideBarOpen";

const RulesOptionSidebar = () => {
  const onDragStart = (
    event: DragEvent<HTMLDivElement>,
    data: { name: AllNames; type: NodeType; executor: string[]; color: string }
  ) => {
    event.dataTransfer.setData("application/reactflow", JSON.stringify(data));
    event.dataTransfer.effectAllowed = "move";
  };

  const isRuleSideBarOpen = useRuleSideBarOpen((state) => state.isRuleSideBarOpen);
  const setRuleSideBarOpen = useRuleSideBarOpen((state) => state.setIsRuleSideBarOpen);

  return (
    <aside
      style={{ height: "82vh" }}
      className={clsx(
        "card p-4 transition-all overflow-y-auto  shadow-md rounded-md select-none space-y-2",
        "flex flex-col   w-[28rem]    ",
        !isRuleSideBarOpen && "!h-12 !w-12 !p-2"
      )}
    >
      <div className="flex items-start justify-between gap-2">
        {isRuleSideBarOpen && (
          <div className="space-y-1">
            <p className="heading-2">Connector List</p>
            <p className="heading-4 text-muted-foreground">
              You can drag these nodes to the pane on the right
            </p>
          </div>
        )}
        <IconButton
          className="!p-1 dark:!bg-white  !bg-gray-700 !text-background"
          onClick={() => setRuleSideBarOpen(!isRuleSideBarOpen)}
        >
          {isRuleSideBarOpen ? <ChevronFirst size="1.2rem" /> : <Menu size="1.2rem" />}
        </IconButton>
      </div>

      {isRuleSideBarOpen && (
        <Accordion type="single" collapsible className="w-full mt-2 flex flex-col gap-3">
          <RuleSideBarInputList onDragStart={onDragStart} collapsed={!isRuleSideBarOpen} />
          <RuleSidebarTriggers onDragStart={onDragStart} collapsed={!isRuleSideBarOpen} />
          <RuleSidebarActions onDragStart={onDragStart} collapsed={!isRuleSideBarOpen} />
        </Accordion>
      )}
    </aside>
  );
};

export default RulesOptionSidebar;
