import Label from "@components/Label";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { Check, CircleAlert, MoreVertical, Pencil, Trash } from "lucide-react";
import React from "react";
import { useLocation, useParams } from "react-router-dom";
import { useNodeId, useReactFlow } from "reactflow";
import { CustomNodeData, NodeType } from "../RuleConfig";
import { validateRuleNodeCompleted } from "../utils/validateNode";

const DefaultNodeHeader = ({
  color,
  icon,
  onEditClick
}: {
  color: string;
  icon: React.ReactNode;
  onEditClick: () => void;
}) => {
  const { getNode, setEdges, setNodes } = useReactFlow<CustomNodeData>();
  const nodeId = useNodeId()!;
  const { ruleId } = useParams();
  const { workflowId } = useParams();

  const pathName = useLocation().pathname;

  const isRuleDetail = pathName.endsWith(`rules/${ruleId}`);
  const isWorkFlowDetail = pathName.endsWith(`workflow/${workflowId}`);
  const node = getNode(nodeId);

  if (!node) return null;

  const { name, type } = node.data;

  const colorClasses =
    type === NodeType.INPUT ? "emerald" : type === NodeType.TRANSFORM ? "sky" : "purple";

  const isRuleValid = validateRuleNodeCompleted(node.data);
  const handleDeleteNode = () => {
    setNodes((nodes) => nodes.filter((_node) => _node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
  };

  return (
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center gap-2">
        {isRuleValid && !isRuleDetail && !isWorkFlowDetail ? (
          <div className="bg-green-500  text-white rounded-full p-1">
            <Check size={INPUT_ICON_SIZE} />
          </div>
        ) : !isRuleDetail && !isWorkFlowDetail ? (
          <div className="bg-amber-300 text-black rounded-full p-1">
            <CircleAlert size={INPUT_ICON_SIZE} />
          </div>
        ) : null}

        <div
          className={clsx(
            "flex gap-2 text-foreground items-center rounded-md  py-0.5 px-4 border ",
            color.replace(/(bg-\w+-\d+)/, "$1/20"),
            color.replace("bg-", "border-")
          )}
        >
          <div className={clsx(" rounded-md   bg-transparent")}>{icon}</div>

          <p className="heading-4-bold">{name}</p>
        </div>
      </div>
      {/* {isRuleValid && !isRuleDetail ? (
        <IconButton onClick={onEditClick} className="!p-1.5 !bg-brandColor/20 !text-brandColor">
          <Edit size={INPUT_ICON_SIZE} />
        </IconButton>
      ) : !isRuleDetail ? (
        <IconButton onClick={onEditClick} className="!p-1.5 !bg-orange-500/20 !text-orange-500">
          <TriangleAlert size={INPUT_ICON_SIZE} />
        </IconButton>
      ) : null} */}
      <div className="flex gap-2 items-center">
        <Label
          color={colorClasses}
          text={
            type === NodeType.INPUT
              ? "Source"
              : type === NodeType.TRANSFORM
                ? "Transform"
                : "Action"
          }
        />

        {!isRuleDetail && !isWorkFlowDetail && (
          <Menubar className="!bg-transparent border-none p-0 ">
            <MenubarMenu>
              <MenubarTrigger className="bg-transparent dark:bg-transparent cursor-pointer">
                <MoreVertical size={BUTTON_ICON_SIZE} />
              </MenubarTrigger>
              <MenubarContent>
                <MenubarItem startIcon={<Pencil size={BUTTON_ICON_SIZE} />} onClick={onEditClick}>
                  Edit
                </MenubarItem>

                <MenubarItem
                  startIcon={<Trash size={BUTTON_ICON_SIZE} />}
                  onClick={handleDeleteNode}
                >
                  Remove
                </MenubarItem>
              </MenubarContent>
            </MenubarMenu>
          </Menubar>
        )}
      </div>
    </div>
  );
};

export default DefaultNodeHeader;
