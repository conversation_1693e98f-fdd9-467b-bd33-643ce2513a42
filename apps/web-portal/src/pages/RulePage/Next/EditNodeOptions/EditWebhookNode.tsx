/* eslint-disable no-use-before-define */
import { COMM_URL } from "@api/index";
import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import GenerateInputValue from "@components/GenerateInputValue";
import Input from "@components/Input";
import FormDialog from "@components/FormDialog";
import useN8nCredentials from "@hooks/n8n/useN8NCredentails";
import { fetchCredentialsDetails } from "@hooks/n8n/useN8NCredentailsDetails";
import useResourceList from "@hooks/rules/useResourceList";
import { generateRandomString } from "@src/pages/MonitorPage/utils";
import { useAppDispatch } from "@src/store";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Plus, Save, Edit2, X, Check } from "lucide-react";
import React, {
  ChangeEventHandler,
  ElementRef,
  ForwardedRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from "react";

import CreateDestinationSideBar from "../../DestinationPage/CreateDestinationSideBar";
import useCreateCredentails from "../hooks/mutations/useCreateCredentails";
import useUpdateN8nCredentails from "../hooks/mutations/useUpdateN8nCredentails";
import useRuleType from "../hooks/useRuleType";
import { CustomNodeData } from "../RuleConfig";

const defaultState = {
  path: "",
  fullPath: "",
  credentials: undefined
} as CustomNodeData;

const getUrlFromPath = (path: string) => {
  return `${COMM_URL}/brk/automation/execute/${path}`;
};

const EditWebhookNode = ({
  onSave,
  initialState
}: {
  onSave: (data: Partial<CustomNodeData>) => void;
  initialState: typeof defaultState;
}) => {
  const [formData, setFormData] = React.useState(initialState);
  const dispatch = useAppDispatch();
  const { data: credentials } = useN8nCredentials({ type: "httpBasicAuth" });
  const ref = React.useRef<ElementRef<typeof AddWebhookCredentials>>(null);
  const destinationSideBarRef = useRef<ElementRef<typeof CreateDestinationSideBar>>(null);

  const ruleType = useRuleType();

  const { data: resourcesList, isLoading: resourceListLoading } = useResourceList({
    type: "bridge"
  });

  const resources = useMemo(() => {
    if (resourceListLoading || !resourcesList?.finalData) {
      return [];
    }
    return resourcesList.finalData.filter((item) => item.type === "webhook");
  }, [resourcesList, resourceListLoading]);

  if (ruleType === "emqx") {
    return (
      <>
        <form
          className="space-y-6"
          onSubmit={(e) => {
            e.preventDefault();
            onSave({
              webhookName: formData.webhookName
            });
          }}
        >
          <div className="flex w-full flex-col gap-4">
            <Dropdown
              options={resources || []}
              getOptionLabel="name"
              className="flex-grow"
              label="Select webhook"
              value={{ name: formData.webhookName, id: formData.webhookId }}
              placeHolder="Select webhook"
              onChange={(item) => {
                setFormData((prev) => ({ ...prev, webhookName: item.name, webhookId: item.id }));
              }}
            />
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t dark:border-gray-600" />
              </div>
              <div className="relative flex justify-center text-sm uppercase">
                <span className="bg-background dark:bg-card px-2 text-muted-foreground">Or</span>
              </div>
            </div>
            <div
              onClick={() => destinationSideBarRef.current?.openDrawer("webhook")}
              className="p-2 cursor-pointer rounded-md border border-dashed border-gray-500 flex gap-4 justify-center items-center"
            >
              <Plus size={BUTTON_ICON_SIZE} /> <span>Create Webhook</span>
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            <Button small startIcon={<Edit2 size={BUTTON_ICON_SIZE} />} type="submit">
              Save
            </Button>
            <Button
              small
              startIcon={<X size={BUTTON_ICON_SIZE} />}
              color="gray"
              type="button"
              onClick={() => onSave({})}
            >
              Close
            </Button>
          </div>
        </form>
        <CreateDestinationSideBar ref={destinationSideBarRef} />
      </>
    );
  }

  return (
    <>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          onSave({
            path: formData.path,
            method: formData.method,
            headers: formData.headers,
            credentials: formData.credentials
          });
        }}
        className="flex flex-col gap-4"
      >
        <Input
          required
          label="Webhook path"
          value={formData.path}
          placeholder="Enter webhook path"
          onChange={(e) => {
            setFormData((prev) => ({
              ...prev,
              path: e.target.value
            }));
          }}
          endIcon={
            <GenerateInputValue
              title="Generate path"
              onClick={() => {
                const newPath = `/webhook/path/${generateRandomString(8)}`;
                setFormData((prev) => ({
                  ...prev,
                  path: newPath
                }));
              }}
            />
          }
        />
        <Dropdown
          options={["get", "post", "put", "delete"]}
          value={formData.method}
          required
          onChange={(value) => {
            setFormData((prev) => ({
              ...prev,
              method: value
            }));
          }}
          placeHolder="Select method"
          label="Method"
        />
        <div>
          <p className="input-label-text">Full Path</p>

          <p className="input-value-text ml-1">{formData.fullPath || "N/A"}</p>
        </div>
        <div className=" flex gap-4 items-end">
          <Dropdown
            options={credentials || []}
            getOptionLabel="name"
            className=" flex-grow"
            label="Select credentials"
            required
            value={formData.credentials}
            placeHolder="Select credentials"
            onChange={(item) => {
              setFormData((prev) => ({
                ...prev,
                credentials: item
              }));
            }}
          />
        </div>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-sm uppercase">
            <span className="bg-background dark:bg-card px-2 text-muted-foreground">Or</span>
          </div>
        </div>
        <div
          onClick={() => ref.current?.showModal()}
          className="p-2 cursor-pointer rounded-md border border-dashed border-gray-500 flex gap-4 justify-center items-center"
        >
          <Plus size={BUTTON_ICON_SIZE} /> <span>Create Credentials</span>
        </div>

        <div className="flex gap-4 justify-end">
          <Button small startIcon={<Edit2 size={BUTTON_ICON_SIZE} />} type="submit">
            Save
          </Button>
          <Button
            small
            startIcon={<X size={BUTTON_ICON_SIZE} />}
            color="gray"
            type="button"
            onClick={() => onSave({})}
          >
            Close
          </Button>
        </div>
      </form>
      <AddWebhookCredentials ref={ref} onSave={() => {}} type="n8n" />
    </>
  );
};

const AddNewWebhookCredentialsWoRef = (
  Props: {
    type: "n8n" | "emqx";
    onSave: () => void;
  },
  ref: ForwardedRef<{
    showModal: (details?: any) => void;
    closeModal: () => void;
  }>
) => {
  const [open, setOpen] = React.useState(false);
  const [editingCredentials, setEditingCredentials] = useState<{
    id: string;
    name: string;
    data: {
      user: string;
      password: string;
      headers: string[];
    };
  }>();

  const [formData, setFormData] = React.useState({
    name: "",
    headers: [],
    username: "",
    password: ""
  });
  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value
    });
  };
  const showModal = useCallback(async (item?: any) => {
    setOpen(true);
    if (item) {
      const details = await fetchCredentialsDetails(item.id);
      setEditingCredentials(details);
      console.log(details);
      setFormData({
        name: details.name,
        host: details.data.host,
        port: details.data.port,
        username: details.data.username || details.data.user,
        password: details.data.password,
        id: details.id,
        ssl: details.data.ssl,
        clientId: details.data.clientId
      });
    }
  }, []);

  const closeModal = useCallback(() => {
    setOpen(false);
    setEditingCredentials(undefined);
    setFormData({
      name: "",
      headers: [],
      username: "",
      password: ""
    });
  }, []);

  const createCredentailsMutation = editingCredentials
    ? useUpdateN8nCredentails(() => {
        closeModal();
      })
    : useCreateCredentails(() => {
        closeModal();
      });

  useImperativeHandle(ref, () => ({ showModal, closeModal }));

  return (
    <FormDialog
      open={open}
      onClose={() => closeModal()}
      title={editingCredentials ? "Edit Credentials" : "Create Credentials"}
      description={
        editingCredentials
          ? "Edit the credentials to connect to your desired destination."
          : "Create a new webhook to connect to your desired destination."
      }
      footer={
        <Button
          loading={createCredentailsMutation.isPending}
          type="submit"
          form="add-http-credentials-form"
          small
          startIcon={
            editingCredentials ? (
              <Check size={BUTTON_ICON_SIZE} />
            ) : (
              <Plus size={BUTTON_ICON_SIZE} />
            )
          }
        >
          {editingCredentials ? "Update" : "Create"}
        </Button>
      }
    >
      <form
        onSubmit={(e) => {
          e.preventDefault();
          createCredentailsMutation.mutate({
            name: formData.name,
            id: formData.id,
            data: {
              user: formData.username,
              password: formData.password
            },
            type: "httpBasicAuth",
            ...(editingCredentials && { id: editingCredentials.id })
          });
        }}
        className="flex flex-col gap-4"
        id="add-http-credentials-form"
      >
        <Input id="name" required label="Name" value={formData.name} onChange={handleChange} />
        <Input
          id="username"
          label="Username"
          required
          value={formData.username}
          onChange={handleChange}
        />

        <Input
          id="password"
          label="Password"
          type="password"
          required
          value={formData.password}
          onChange={handleChange}
        />
      </form>
    </FormDialog>
  );
};

export const AddWebhookCredentials = React.forwardRef(AddNewWebhookCredentialsWoRef);

export default EditWebhookNode;
