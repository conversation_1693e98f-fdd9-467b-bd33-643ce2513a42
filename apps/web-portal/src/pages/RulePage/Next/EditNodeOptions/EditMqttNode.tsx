import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import GenerateInputValue from "@components/GenerateInputValue";
import Input from "@components/Input";
import useN8nCredentials from "@hooks/n8n/useN8NCredentails";
import { generateRandomString } from "@src/pages/MonitorPage/utils";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Plus, Edit2, X } from "lucide-react";
import React, { ElementRef } from "react";
import { CustomNodeData } from "../RuleConfig";
import { AddNewMqttTriggerCredentials } from "./EditMqttTrigger";
const defaultState = {
  mqtt: {
    topic: ""
  },
  credentials: undefined
} as CustomNodeData;

const EditMqttNode = ({
  onSave,
  initialState
}: {
  onSave: (data: Partial<CustomNodeData>) => void;
  initialState: typeof defaultState;
}) => {
  const [formValues, setFormValues] = React.useState<typeof defaultState>(
    initialState.mqtt ? initialState : defaultState
  );
  const ref = React.useRef<ElementRef<typeof AddNewMqttTriggerCredentials>>(null);
  const { data: credentials } = useN8nCredentials({ type: "mqtt" });

  return (
    <>
      <form
        className="flex flex-col gap-4"
        onSubmit={(e) => {
          e.preventDefault();
          onSave({
            mqtt: formValues.mqtt,
            credentials: formValues.credentials
          });
        }}
      >
        <div
          onClick={() => ref.current?.showModal()}
          className="p-2 cursor-pointer rounded-md border border-dashed border-gray-500 flex gap-4 justify-center items-center"
        >
          <Plus size={BUTTON_ICON_SIZE} /> <span>Create Credentials</span>
        </div>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-sm uppercase">
            <span className="bg-background dark:bg-card px-2 text-muted-foreground">Or</span>
          </div>
        </div>

        <div className="flex gap-3 items-end">
          <Dropdown
            options={credentials || []}
            getOptionLabel="name"
            className="flex-grow"
            label="Select credentials"
            required
            value={formValues.credentials}
            placeHolder="Select credentials"
            onChange={(item) => {
              setFormValues((prev) => ({
                ...prev,
                credentials: item
              }));
            }}
          />
        </div>

        <Input
          label="Topic"
          required
          placeholder="Enter topic"
          value={formValues.mqtt?.topic}
          onChange={(e) =>
            setFormValues((prev) => ({
              ...prev,
              mqtt: { ...formValues.mqtt, topic: e.target.value }
            }))
          }
          endIcon={
            <GenerateInputValue
              title="Generate topic"
              onClick={() => {
                setFormValues((prev) => ({
                  ...prev,
                  mqtt: {
                    ...formValues.mqtt,
                    topic: `/mqtt/${generateRandomString(8)}`
                  }
                }));
              }}
            />
          }
        />
        <div className="flex gap-4 justify-end">
          <Button small startIcon={<Edit2 size={BUTTON_ICON_SIZE} />} type="submit">
            Save
          </Button>
          <Button
            small
            startIcon={<X size={BUTTON_ICON_SIZE} />}
            color="gray"
            type="button"
            onClick={() => onSave({})}
          >
            Close
          </Button>
        </div>
      </form>
      <AddNewMqttTriggerCredentials ref={ref} type="n8n" />
    </>
  );
};

export default EditMqttNode;
