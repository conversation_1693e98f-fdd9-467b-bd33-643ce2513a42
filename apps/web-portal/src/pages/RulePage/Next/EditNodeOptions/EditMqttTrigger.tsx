/* eslint-disable no-use-before-define */

import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import GenerateInputValue from "@components/GenerateInputValue";
import Input from "@components/Input";
import FormDialog from "@components/FormDialog";
import useN8nCredentials from "@hooks/n8n/useN8NCredentails";
import { fetchCredentialsDetails } from "@hooks/n8n/useN8NCredentailsDetails";
import { generateRandomString } from "@src/pages/MonitorPage/utils";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Check, Edit2, Plus, X } from "lucide-react";
import * as React from "react";
import { ElementRef, useState } from "react";
import { CustomNodeData } from "../RuleConfig";
import useCreateCredentails from "../hooks/mutations/useCreateCredentails";
import useUpdateN8nCredentails from "../hooks/mutations/useUpdateN8nCredentails";

const defaultState = {
  mqtt: {
    topic: ""
  },
  credentials: {}
} as CustomNodeData;

const EditMqttTrigger = ({
  onSave,
  initialState
}: {
  onSave: (data: Partial<CustomNodeData>) => void;
  initialState?: typeof defaultState;
}) => {
  const [formValues, setFormValues] = useState<typeof defaultState>(initialState || defaultState);

  const ref = React.useRef<ElementRef<typeof AddNewMqttTriggerCredentials>>(null);

  const { data: credentials } = useN8nCredentials({ type: "mqtt" });

  if (!formValues) return null;

  return (
    <>
      <form
        className="flex flex-col gap-4"
        onSubmit={(e) => {
          e.preventDefault();
          onSave({
            mqtt: {
              topic: formValues.topic
            },
            credentials: formValues.credentials
          });
        }}
      >
        <div
          onClick={() => ref.current?.showModal()}
          className="p-2 cursor-pointer rounded-md border border-dashed border-gray-500 flex gap-4 justify-center items-center"
        >
          <Plus size={BUTTON_ICON_SIZE} /> <span>Create Credentials</span>
        </div>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-sm uppercase">
            <span className="bg-background dark:bg-card px-2 text-muted-foreground">Or</span>
          </div>
        </div>
        <div className="flex items-end gap-3">
          <Dropdown
            options={credentials || []}
            getOptionLabel="name"
            className="flex-grow"
            label="Select credentials"
            value={formValues.credentials}
            placeHolder="Select credentials"
            onChange={(option) => {
              setFormValues((prev) => ({ ...prev, credentials: option }));
            }}
            required
          />
        </div>

        <Input
          label="Topic"
          required
          placeholder="select topic"
          value={formValues.topic}
          onChange={(e) => setFormValues((prev) => ({ ...prev, topic: e.target.value }))}
          endIcon={
            <GenerateInputValue
              title="Generate topic"
              onClick={() => {
                setFormValues((prev) => ({
                  ...prev,
                  topic: `/mqtt-trigger/${generateRandomString(8)}`
                }));
              }}
            />
          }
        />
        <div className="flex gap-4 justify-end">
          <Button small startIcon={<Edit2 size={BUTTON_ICON_SIZE} />} type="submit">
            Save
          </Button>
          <Button
            small
            startIcon={<X size={BUTTON_ICON_SIZE} />}
            color="gray"
            type="button"
            onClick={() => onSave({})}
          >
            Close
          </Button>
        </div>
      </form>
      <AddNewMqttTriggerCredentials ref={ref} type="n8n" />
    </>
  );
};

const AddNewMqttTriggerCredentialsWithRef = (
  Props: {
    type: "n8n" | "emqx";
  },
  ref: React.ForwardedRef<{
    showModal: (details?: any) => void;
    closeModal: () => void;
  }>
) => {
  const [open, setOpen] = React.useState(false);
  const [editingCredentials, setEditingCredentials] = useState<{
    id: string;
    name: string;
    data: {
      host: string;
      port: string;
      username: string;
      password: string;
      ssl: boolean;
      clientId: string;
    };
  }>();

  const [formData, setFormData] = React.useState({
    name: "",
    host: "",
    port: "",
    username: "",
    password: "",
    ssl: false,
    clientId: ""
  });
  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value
    });
  };

  const showModal = React.useCallback(async (item) => {
    setOpen(true);

    if (item) {
      const details = await fetchCredentialsDetails(item.id);
      setEditingCredentials(details);
      setFormData({
        name: details.name,
        host: details.data.host,
        port: details.data.port,
        username: details.data.username || details.data.user,
        password: details.data.password,
        ssl: details.data.ssl,
        clientId: details.data.clientId
      });
    }
  }, []);

  const closeModal = React.useCallback(() => {
    setOpen(false);
    setFormData({
      name: "",
      host: "",
      port: "",
      username: "",
      password: "",
      ssl: false,
      clientId: ""
    });
  }, []);

  const createCredentailsMutation = editingCredentials
    ? useUpdateN8nCredentails(() => {
        closeModal();
      })
    : useCreateCredentails(() => {
        closeModal();
      });

  React.useImperativeHandle(ref, () => ({ showModal, closeModal }));

  return (
    <FormDialog
      open={open}
      title={editingCredentials ? "Edit Credentials" : "Create Credentials"}
      description={
        editingCredentials
          ? "Edit the credentials to connect to your desired destination."
          : "Create a new credentials to connect to your desired destination."
      }
      onClose={closeModal}
      footer={
        <Button
          loading={createCredentailsMutation.isPending}
          className=" col-span-2"
          small
          type="submit"
          form="add-mqtt-credentials-form"
          startIcon={<Check size={BUTTON_ICON_SIZE} />}
        >
          {editingCredentials ? "Update" : "Create"}
        </Button>
      }
    >
      <form
        onSubmit={(e) => {
          e.preventDefault();
          createCredentailsMutation.mutate({
            name: formData.name,
            data: {
              host: formData.host,
              port: Number(formData.port),
              username: formData.username,
              password: formData.password,
              ssl: formData.ssl,
              clientId: formData.clientId
            },
            type: "mqtt",
            ...(editingCredentials && { id: editingCredentials.id })
          });
        }}
        className="grid grid-cols-2 gap-4"
        id="add-mqtt-credentials-form"
      >
        <Input id="name" required label="Name" value={formData.name} onChange={handleChange} />
        <Input id="host" required label="Host" value={formData.host} onChange={handleChange} />
        <Input
          id="port"
          required
          label="Port"
          type="number"
          value={formData.port}
          onChange={handleChange}
        />
        <Input id="clientId" label="Client Id" value={formData.clientId} onChange={handleChange} />
        <Input
          id="username"
          label="Username"
          required
          value={formData.username}
          onChange={handleChange}
        />
        <Input
          id="password"
          label="Password"
          type="password"
          required
          value={formData.password}
          onChange={handleChange}
        />
      </form>
    </FormDialog>
  );
};

export const AddNewMqttTriggerCredentials = React.forwardRef(AddNewMqttTriggerCredentialsWithRef);

export default EditMqttTrigger;
