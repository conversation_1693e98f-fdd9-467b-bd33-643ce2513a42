import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import useResourceList from "@hooks/rules/useResourceList";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { Plus, Edit2, X } from "lucide-react";
import React, { ElementRef, useRef } from "react";
import CreateDestinationSideBar from "../../DestinationPage/CreateDestinationSideBar";
import { CustomNodeData } from "../RuleConfig";

const EditMqttBridgeNode = ({
  onSave,
  initialState
}: {
  onSave: (data: Partial<CustomNodeData>) => void;
  initialState: { mqttBridge: CustomNodeData["mqttBridge"] };
}) => {
  const [formValues, setFormValues] = React.useState(initialState);

  const destinationSideBarRef = useRef<ElementRef<typeof CreateDestinationSideBar>>(null);

  const { data: resourcesList, isLoading: resourceListLoading } = useResourceList({
    type: "bridge"
  });

  return (
    <>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          onSave({
            mqttBridge: formValues.mqttBridge
          });
        }}
        className="space-y-6"
      >
        <div className="flex gap-4 flex-col  w-full">
          <Dropdown
            options={resourcesList?.finalData || []}
            optionsLoading={resourceListLoading}
            getOptionLabel="name"
            label="Select Mqtt Bridge"
            className="flex-grow"
            required
            onChange={(item) => {
              setFormValues({ mqttBridge: item });
            }}
          />
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t dark:border-gray-600" />
            </div>
            <div className="relative flex justify-center text-sm uppercase">
              <span className="bg-background dark:bg-card px-2 text-muted-foreground">Or</span>
            </div>
          </div>
          <div
            onClick={() => destinationSideBarRef.current?.openDrawer("mqtt")}
            className="p-2 cursor-pointer rounded-md border border-dashed border-gray-500 flex gap-4 justify-center items-center"
          >
            <Plus size={BUTTON_ICON_SIZE} /> <span>Create MQTT Bridge</span>
          </div>
        </div>
        <div className="flex gap-4 justify-end">
          <Button small startIcon={<Edit2 size={BUTTON_ICON_SIZE} />} type="submit">
            Save
          </Button>
          <Button
            small
            startIcon={<X size={BUTTON_ICON_SIZE} />}
            color="gray"
            type="button"
            onClick={() => onSave({})}
          >
            Close
          </Button>
        </div>
      </form>
      <CreateDestinationSideBar ref={destinationSideBarRef} />
    </>
  );
};

export default EditMqttBridgeNode;
