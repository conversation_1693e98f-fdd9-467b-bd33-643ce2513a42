import ActionButton from "@components/ActionButton";
import HeadingIcon from "@components/HeadingIcon";
import RadioSelect from "@components/RadioSelect";
import FormDialog from "@components/FormDialog";
import { getPartnerAndFeature } from "@frontend/shared/utils/common";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useN8nCredentials from "@hooks/n8n/useN8NCredentails";
import useResourceList from "@hooks/rules/useResourceList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { queryClient } from "@utils/queryClient";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT } from "@utils/utilities";
import clsx from "clsx";
import { Check, Edit3, GitBranch<PERSON><PERSON>, KeyRound, Plus } from "lucide-react";
import { ElementRef, useEffect, useRef, useState } from "react";
import { disableIntegration, enableIntegration } from "../../../api/iot";
import Button from "../../../components/Button";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import Input from "../../../components/Input";
import Label from "../../../components/Label";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import { useAppSelector } from "../../../store";
import { showErrorToast, showSuccessToast } from "../../../utils";
import { AddNewMqttTriggerCredentials } from "../Next/EditNodeOptions/EditMqttTrigger";
import { AddWebhookCredentials } from "../Next/EditNodeOptions/EditWebhookNode";
import useDeleteCredentials from "../Next/hooks/mutations/useDeleteCredentials";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

const INTEGRATIONS_DATA = (partnerName: string) => {
  return [
    // { name: `${partnerName}_TEAMS`, type: "webhook", enable: false },
    { name: `${partnerName}_INCIDENTS`, type: "webhook", enable: false }
  ];
};
const convertIntegrationName = (name: string, partnerName: string) => {
  const regex = new RegExp(`^${partnerName}_`);
  const result = name.replace(regex, "");

  return result;
};

const IntegraionPage = () => {
  const [showEnablePrompt, setShowEnablePrompt] = useState(false);
  const [encodedKey, setEncodedKey] = useState("");
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [showDisablePrompt, setShowDisablePrompt] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState("");
  const editMqttCredentialsRef = useRef<ElementRef<typeof AddNewMqttTriggerCredentials>>(null);
  const editWebhookCredentialsRef = useRef<ElementRef<typeof AddWebhookCredentials>>(null);
  const [createIntegrationModalOpen, setCreateIntegrationModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const user = useAppSelector((state) => state.user.user);
  const { partnerName } = getPartnerAndFeature();
  const [initialIntegrations, setInitialIntegrations] = useState(
    INTEGRATIONS_DATA(user?.tenant || partnerName)
  );
  const [deleteCredentialId, setDeleteCredentialId] = useState<{ id: string; name: string }>();
  const deleteCredentialMutation = useDeleteCredentials(() => {
    setDeleteCredentialId(undefined);
  });

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const navigate = useCustomNavigate();
  const { data: credentials } = useN8nCredentials({});
  const { data: permissions } = useUserGroupPermissions();

  const { data: integrationList, isLoading: integrationsLoading } = useResourceList({
    type: "integration",
    page,
    limit,
    search: debouncedSearchQuery
  });

  useEffect(() => {
    const updatedArray = [...initialIntegrations];

    updatedArray.forEach((firstObj, index) => {
      const matchingItem = integrationList?.finalData.find((int) => int.name === firstObj.name);

      if (matchingItem) {
        updatedArray[index] = { ...firstObj, enable: true };
      } else {
        updatedArray[index] = { ...firstObj, enable: false };
      }
    });

    setInitialIntegrations(updatedArray);
  }, [integrationList]);

  const toggleIntegration = async (e) => {
    if (e) {
      setShowDisablePrompt(true);
    } else {
      setShowEnablePrompt(true);
    }
  };
  const integrationEnableHandler = async (e) => {
    e.preventDefault();
    setLoadingBtn(true);
    const resp = await enableIntegration({
      destination: selectedIntegration,
      apiKey: encodedKey
    });
    if (resp.status === "Success") {
      showSuccessToast("Integration Enabled Successfully.");
      setShowEnablePrompt(false);
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["resources-list", "integration"] });
      }, 500);
    } else {
      showErrorToast(resp.message);
    }
    setLoadingBtn(false);
  };

  const handleDisableIntegration = async () => {
    setLoadingBtn(true);
    const resp = await disableIntegration({
      integrationId: selectedIntegration,
      type: "webhook",
      integration: true
    });
    if (resp.status === "Success") {
      showSuccessToast("Integration disabled  successfully");

      setTimeout(() => {
        setLoadingBtn(false);
        setShowDisablePrompt(false);
        queryClient.invalidateQueries({ queryKey: ["resources-list", "integration"] });
      }, 500);
    } else {
      setLoadingBtn(false);

      showErrorToast(resp.message);
    }
  };

  return (
    <main className="space-y-4">
        <HeaderSection
                title="Credentials"
                description="Manage your Credentials"
                actions={
                  <Button
                   startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                   onClick={() => { setCreateIntegrationModalOpen(true) }}
                   noAccess={permissions.destinations !== "write"}
                  >
                     Add Credentials
                  </Button>
                }
              />
            <Card className="space-y-4">
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Mode</TableHead>
              <TableHead>Action</TableHead>
              <TableHead>Delete</TableHead>
            </>
          }
          body={
            integrationsLoading ? (
              <TableRowsSkeleton />
            ) : (
              <>
                {[...initialIntegrations, ...(credentials || [])].map((r, i) => (
                  <tr
                    className={clsx(
                      "bg-white",
                      r.enable || r.id ? "cursor-pointer" : "cursor-not-allowed"
                    )}
                    key={r.name}
                  >
                    <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                    <TableRow title data-testid={r.name}>
                      {convertIntegrationName(r.name, user?.tenant)}
                    </TableRow>

                    <TableRow>
                      <Label text={r.type} color={r.type === "webhook" ? "blue" : "yellow"} />
                    </TableRow>

                    <TableRow>
                      {r.id ? (
                        <Label text="Advance" color="gray" />
                      ) : (
                        <Label text="basic" color="purple" />
                      )}
                    </TableRow>

                    <TableRow>
                      <Button
                        small
                        noAccess={permissions.destinations !== "write"}
                        className="min-w-[6rem] !font-semibold"
                        color={r.id ? undefined : r.enable ? "red" : undefined}
                        onClick={() => {
                          if (!r.id) {
                            setSelectedIntegration(r.name);
                            toggleIntegration(r.enable);
                            return;
                          }
                          if (r.type === "mqtt") {
                            editMqttCredentialsRef.current?.showModal(r);
                            return;
                          }
                          editWebhookCredentialsRef.current?.showModal(r);
                        }}
                        startIcon={<Edit3 size={BUTTON_ICON_SIZE} />}
                      >
                        {r.id ? "Edit" : r.enable ? "Disable" : "Enable"}
                      </Button>
                    </TableRow>

                    <TableRow className=" ">
                      <ActionButton
                        disabled={!r.id || permissions.destinations !== "write"}
                        onClick={(e) => {
                          e.stopPropagation();
                          setDeleteCredentialId({
                            id: r.id,
                            name: r.name
                          });
                        }}
                        type="delete"
                      />
                    </TableRow>
                  </tr>
                ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: integrationList?.pages || 0
          }}
        />
      </Card>
      <FormDialog
        open={showEnablePrompt}
        onClose={() => {
          setEncodedKey("");
          setShowEnablePrompt(false);
        }}
        description="Enter existing API key or generate new one"
        title={`Enable ${convertIntegrationName(selectedIntegration, user?.tenant)}`}
      >
        <form className="w-full mt-4 mb-2 space-y-4" onSubmit={integrationEnableHandler}>
          {/* <h4 className="sidebar-sub-heading">Enter API key to Enable Integration.</h4> */}
          <Input
            inputType="textarea"
            label="API key"
            required
            onChange={(e) => setEncodedKey(e.target.value)}
            value={encodedKey}
          />
          <Button
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            loading={loadingBtn}
            type="submit"
            small
          >
            Submit
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t dark:border-gray-600" />
            </div>
            <div className="relative flex justify-center text-sm uppercase">
              <span className="bg-background dark:bg-card px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>

          <div
            onClick={() => navigate("/UM/apiKeys/createApi")}
            className="p-2 cursor-pointer rounded-md border border-dashed border-gray-500 flex gap-4 justify-center items-center"
          >
            <KeyRound size={BUTTON_ICON_SIZE} /> <span> Generate New API Key</span>
          </div>
        </form>
      </FormDialog>

      <ConfirmPrompt
        show={showDisablePrompt}
        item={convertIntegrationName(selectedIntegration, user?.tenant)}
        validate
        onCancel={() => setShowDisablePrompt(false)}
        loading={loadingBtn}
        onConfirm={() => {
          handleDisableIntegration();
        }}
      />

      <ConfirmPrompt
        show={Boolean(deleteCredentialId)}
        item={deleteCredentialId?.name}
        validate
        onCancel={() => setDeleteCredentialId(undefined)}
        loading={deleteCredentialMutation.isPending}
        onConfirm={() => {
          deleteCredentialMutation.mutate(deleteCredentialId?.id || "");
        }}
      />

      {/* <EditIngerationsSideBar ref={editIntegrationRef} /> */}
      <AddNewMqttTriggerCredentials ref={editMqttCredentialsRef} type="n8n" />
      <AddWebhookCredentials onSave={() => {}} ref={editWebhookCredentialsRef} type="n8n" />
      <FormDialog
        title="Select Integration Type"
        open={createIntegrationModalOpen}
        onClose={() => setCreateIntegrationModalOpen(false)}
      >
        <div className="space-y-4">
          <div className="flex flex-col gap-2">
            <RadioSelect
              data="MQTT Credentials"
              onClick={() => {
                setCreateIntegrationModalOpen(false);
                editMqttCredentialsRef.current?.showModal();
              }}
              description="Create mqtt credentials."
            />
            <RadioSelect
              data="HttpAuthorization"
              onClick={() => {
                setCreateIntegrationModalOpen(false);
                editWebhookCredentialsRef.current?.showModal();
              }}
              description="Create http authorization credentials."
            />
          </div>
        </div>
      </FormDialog>
    </main>
  );
};

export default IntegraionPage;
