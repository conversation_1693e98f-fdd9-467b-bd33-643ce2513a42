import { Card, StatCard } from "@components/ui";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useRulesList from "@hooks/rules/useRulesList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { IconButton } from "@mui/material";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { useState } from "react";
import { Check, Cloud, CloudOff, Plus, Search, Wrench, Trash, X } from "lucide-react";
import { deleteIotRule } from "../../api/iot";
import Button from "../../components/Button";
import ConfirmPrompt from "../../components/ConfirmPrompt";
import Input from "../../components/Input";
import Label from "../../components/Label";
import Table, { TableHead, TableRow } from "../../components/Table";
import TableRowsSkeleton from "../../components/Table/TableRowsSkeleton";
import useTableSort from "../../hooks/useTableSort";
import { showErrorToast, showSuccessToast } from "../../utils";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import useActionMode from "./Next/hooks/useActionMode";
import useRuleStats from "./Next/hooks/useRuleStats";
import HeaderSection from "@components/layout/HeaderSection";

export default function RulePage({ mode = "rules" }: { mode?: "workflows" | "rules" }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteRuleId, setDeleteRuleId] = useState<string | null>(null);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [ruleSortFn, ruleSort] = useTableSort();
  const navigate = useCustomNavigate();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: permissions } = useUserGroupPermissions();
  const {
    data: rules,
    isLoading: rulesLoading,
    refetch: refetchRules
  } = useRulesList({
    page,
    limit,
    search: debouncedSearchQuery,
    mode
  });

  const actionMode = useActionMode();

  const { data: ruleStat } = useRuleStats(actionMode);

  const handleDeleteRule = async () => {
    setLoadingBtn(true);
    if (!deleteRuleId) {
      return;
    }
    const resp = await deleteIotRule(deleteRuleId);

    if (resp.status === "Success") {
      showSuccessToast(`${mode === "rules" ? "Rule" : "Workflow"} Deleted successfully`);

      setTimeout(() => {
        refetchRules();
        setDeleteRuleId(null);
        setLoadingBtn(false);
      }, 1000);
    } else {
      setLoadingBtn(false);
      showErrorToast(resp.message);
    }
  };
  return (
    <main className="space-y-4">
       <HeaderSection
              title={mode.charAt(0).toUpperCase() + mode.slice(1)}
              description={`Manage your ${mode} `}
              actions={
                <Button
                   onClick={() => {
              if (mode === "workflows") {
                navigate("/actions/workflow/create");
                return;
              }
              navigate("create-next");
            }}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            noAccess={permissions.rules !== "write"}
                >
                  Add {mode}
                </Button>
              }
            />
      <section className="flex w-full flex-wrap gap-4">
        <StatCard
          title={`${mode} Executed`}
          value={ruleStat.success}
          className=" capitalize"
          colorScheme="success"
          icon={Cloud}
          description={`${((ruleStat.success / ruleStat.total) * 100).toFixed(2)}%`}
        />
        <StatCard
          title={`${mode} Failed`}
          value={ruleStat.failed}
          className=" capitalize"
          colorScheme="danger"
          icon={CloudOff}
          description={`${((ruleStat.failed / ruleStat.total) * 100).toFixed(2)}%`}
        />
        <StatCard
          title={`Total ${mode}`}
          value={ruleStat.total}
          className=" capitalize"
          colorScheme="info"
          icon={Wrench}
        />
      </section>
      <Card className=" space-y-4">
         <h3 className="heading-2">{mode.charAt(0).toUpperCase() + mode.slice(1)} List</h3>
          <Input
            value={searchQuery}
            onChange={(e) => {  
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            placeholder="Search"
            className="flex-1"
            innerClasses="ml-auto"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => ruleSort("id", order)}>ID</TableHead>

              <TableHead onSort={(order) => ruleSort("enabled", order)}>Status</TableHead>
              <TableHead onSort={(order) => ruleSort("created_at", order)}>Created</TableHead>
              <TableHead onSort={(order) => ruleSort("updated_at", order)}>Updated</TableHead>
              <TableHead>Operations</TableHead>
            </>
          }
          body={
            rulesLoading ? (
              <TableRowsSkeleton />
            ) : !rules?.dbRules.length ? (
              <DataNotFound title={`No ${mode} Available`} isTable />
            ) : (
              <>
                {rules.dbRules.toSorted(ruleSortFn).map((r, i) => (
                  <tr
                    className="cursor-pointer"
                    key={r.id}
                    data-testid={r.ruleName}
                    onClick={() => {
                      if (mode === "rules") {
                        navigate(r.ruleName);
                        return;
                      }
                      navigate(`/actions/workflow/${r.ruleName}`);
                    }}
                  >
                    <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                    <TableRow title>{r.ruleName}</TableRow>
                    {/* <TableRow>{r.from.join()}</TableRow> */}
                    {/* <TableRow >{r.from.join()}</TableRow> */}
                    {/* <TableRow>
                          <pre>{r.actions.map((a) => a).join("\n")}</pre>
                        </TableRow> */}
                    <TableRow>
                      <Label
                        icon={r.enable ? <Check size="1rem" /> : <X size="1rem" />}
                        text={r.enable ? "Enabled" : "Disabled"}
                        color={r.enable ? "green" : "red"}
                      />
                    </TableRow>
                    <TableRow>{convetUTCToLocal(r.created_at)}</TableRow>
                    <TableRow>{convetUTCToLocal(r.updated_at)}</TableRow>
                    <TableRow dense>
                      <IconButton
                        disabled={permissions.rules !== "write"}
                        onClick={(e) => {
                          e.stopPropagation();
                          setDeleteRuleId(r.ruleName);
                        }}
                        className="!p-2 !mr-2"
                      >
                        <Trash size={INPUT_ICON_SIZE} />
                      </IconButton>
                    </TableRow>
                  </tr>
                ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: rules?.pages || 0
          }}
        />
      </Card>
      <ConfirmPrompt
        show={deleteRuleId !== null}
        validate
        item={deleteRuleId}
        onCancel={() => {
          setDeleteRuleId(null);
          setLoadingBtn(false);
        }}
        onConfirm={handleDeleteRule}
        loading={loadingBtn}
      />
    </main>
  );
}
