import { Card, StatCard } from "@components/ui";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useDestinationStats from "@hooks/rules/useDestinationStats";
import useResourceList from "@hooks/rules/useResourceList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Cloud, CloudOff, GitCompareArrows, Plus, Search, Wrench } from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import Label from "../../../components/Label";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import useDebounce from "../../../hooks/useDebounce";
import useTableSort from "../../../hooks/useTableSort";
import CreateDestinationSideBar from "./CreateDestinationSideBar";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import HeaderSection from "@components/layout/HeaderSection";

const DestinationPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const destinationSideBarRef = useRef<ElementRef<typeof CreateDestinationSideBar>>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const [sortFn, sort] = useTableSort();
  const navigate = useCustomNavigate();

  const { data: permissions } = useUserGroupPermissions();

  const { data: resourceStats, isLoading: resourceStatsLoading } = useDestinationStats();

  const { data: resourcesList, isLoading: resourceListLoading } = useResourceList({
    search: debouncedSearchQuery,
    type: "bridge",
    limit,
    page
  });

  return (
    <main className="space-y-4">
       <HeaderSection
        title="Destinations"
        description="Manage your Destinations"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => destinationSideBarRef.current?.openDrawer()}
            noAccess={permissions.destinations !== "write"}
          >
             Create Destination
          </Button>
        }
      />
      <section className="flex w-full flex-wrap gap-4 ">
        <StatCard
          title="Rules Execution Success"
          value={resourceStats?.ruleExeSuccess}
          colorScheme="success"
          loading={resourceStatsLoading}
          icon={Cloud}
          description={
            ((resourceStats.ruleExeSuccess / resourceStats.totalRuleExe) * 100).toFixed(1) + "%"
          }
        />
        <StatCard
          title="Rules execution Failed"
          value={resourceStats.ruleExeFailed}
          loading={resourceStatsLoading}
          colorScheme="danger"
          icon={CloudOff}
          description={
            ((resourceStats.ruleExeFailed / resourceStats.totalRuleExe) * 100).toFixed(1) + "%"
          }
        />
        <StatCard
          title="Total Rules"
          value={resourceStats?.totalRuleExe}
          colorScheme="info"
          loading={resourceStatsLoading}
          icon={Wrench}
        />
      </section>
      <Card className=" space-y-4">
 <h3 className="heading-2">Destinations List</h3>
          <Input
            value={searchQuery}
            onChange={(e) => {
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            placeholder="Search"
            className="flex-1 "
            innerClasses="ml-auto"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
/>
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("name", order)}>Destination ID</TableHead>
              <TableHead>Enable</TableHead>
              <TableHead onSort={(order) => sort("type", order)}>Type</TableHead>
              <TableHead onSort={(order) => sort("type", order)}>Create Date </TableHead>
              <TableHead onSort={(order) => sort("type", order)}>Update Date</TableHead>
            </>
          }
          body={
            resourceListLoading ? (
              <TableRowsSkeleton />
            ) : !resourcesList?.finalData.length ? (
              <DataNotFound title="No Destination Available" isTable />
            ) : (
              <>
                {resourcesList.finalData.toSorted(sortFn).map((r, i) => (
                  <tr
                    className="cursor-pointer"
                    key={r.name}
                    onClick={() =>
                      navigate(r.name, {
                        state: {
                          type: r.type
                        }
                      })
                    }
                  >
                    <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                    <TableRow title data-testid={r.name}>
                      {r.name}
                    </TableRow>
                    <TableRow>
                      <Label
                        text={r.enable ? "Enabled" : "Disabled"}
                        color={r.enable ? "green" : "red"}
                      />
                    </TableRow>

                    <TableRow>
                      <Label text={r.type} color={r.type === "webhook" ? "blue" : "yellow"} />
                    </TableRow>
                    <TableRow>{convetUTCToLocal(r.createdAt)}</TableRow>
                    <TableRow>{convetUTCToLocal(r.updatedAt)}</TableRow>
                  </tr>
                ))}
              </>
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: resourcesList?.pages || 0
          }}
        />
      </Card>
      <CreateDestinationSideBar ref={destinationSideBarRef} />
    </main>
  );
};

export default DestinationPage;
