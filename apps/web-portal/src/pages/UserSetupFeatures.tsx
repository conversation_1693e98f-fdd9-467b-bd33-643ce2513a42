import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@components/shadcn/components/card";
import { Bell, History, LayoutDashboard, Map, Package, Settings } from "lucide-react";
import { useState } from "react";
import { UserSetupFeatureModal } from "./UserSetupFeatureModal";

const features = [
  {
    id: "product",
    title: "Product Management",
    description: "Manage your products, inventory, and data policies.",
    icon: Package,
    slides: [
      {
        title: "Centralized Product Control",
        description:
          "Take control of your entire product lifecycle from a single, intuitive interface.",
        keyPoint: "Streamline your product management workflow with our comprehensive dashboard.",
        benefits: [
          "Unified product catalog management",
          "Real-time inventory tracking",
          "Automated stock alerts and notifications",
          "Bulk product updates and changes"
        ]
      },
      {
        title: "Smart Inventory Management",
        description: "Let data-driven insights guide your inventory decisions.",
        keyPoint: "Optimize stock levels and reduce carrying costs with predictive analytics.",
        benefits: [
          "Predictive stock level management",
          "Automated reorder point calculations",
          "Seasonal demand forecasting",
          "Inventory value tracking"
        ]
      },
      {
        title: "Advanced Analytics Integration",
        description: "Make informed decisions with comprehensive product analytics.",
        keyPoint: "Transform raw data into actionable insights for your business.",
        benefits: [
          "Sales performance tracking",
          "Product trend analysis",
          "Customer behavior insights",
          "ROI calculation per product"
        ]
      }
    ]
  },
  {
    id: "things",
    title: "Things Management",
    description: "Manage your things, inventory, and data policies.",
    icon: Settings,
    slides: [
      {
        title: "IoT Device Management",
        description: "Manage all your connected devices from a single platform.",
        keyPoint: "Maintain complete control over your IoT ecosystem.",
        benefits: [
          "Real-time device status monitoring",
          "Remote device configuration",
          "Automated firmware updates",
          "Device health analytics"
        ]
      },
      {
        title: "Asset Tracking",
        description: "Keep track of all your assets in real-time.",
        keyPoint: "Never lose sight of your valuable assets with our tracking system.",
        benefits: [
          "Live location tracking",
          "Movement history",
          "Maintenance scheduling",
          "Usage analytics"
        ]
      },
      {
        title: "Preventive Maintenance",
        description: "Stay ahead of potential issues with predictive maintenance.",
        keyPoint: "Reduce downtime and extend asset lifetime with proactive maintenance.",
        benefits: [
          "Predictive maintenance alerts",
          "Maintenance history tracking",
          "Service life optimization",
          "Cost reduction analysis"
        ]
      }
    ]
  },
  {
    id: "geofence",
    title: "Geofence Management",
    description: "Manage your geofences, inventory, and data policies.",
    icon: Map,
    slides: [
      {
        title: "Dynamic Geofencing",
        description: "Create and manage virtual perimeters for your locations.",
        keyPoint: "Set up intelligent boundaries that work for your business.",
        benefits: [
          "Easy geofence creation and editing",
          "Multiple zone management",
          "Custom shape drawing",
          "Zone overlap detection"
        ]
      },
      {
        title: "Real-time Monitoring",
        description: "Monitor activity within your geofenced areas instantly.",
        keyPoint: "Stay informed about all movements within your defined zones.",
        benefits: [
          "Instant breach notifications",
          "Activity logging",
          "Multiple device tracking",
          "Custom alert rules"
        ]
      },
      {
        title: "Advanced Rules Engine",
        description: "Create sophisticated rules for your geofenced areas.",
        keyPoint: "Automate actions based on geofence interactions.",
        benefits: [
          "Time-based rules",
          "Device-specific policies",
          "Automated responses",
          "Integration capabilities"
        ]
      }
    ]
  },
  {
    id: "history",
    title: "Geofence History",
    description: "View your geofence history, inventory, and data policies.",
    icon: History,
    slides: [
      {
        title: "Historical Data",
        description: "View detailed history of geofence activities",
        imageUrl: "/placeholder.svg?height=400&width=600",
        benefits: [
          "Instant breach notifications",
          "Activity logging",
          "Multiple device tracking",
          "Custom alert rules"
        ]
      },
      {
        title: "Activity Timeline",
        description: "Chronological view of all geofence events",
        imageUrl: "/placeholder.svg?height=400&width=600",
        benefits: [
          "Instant breach notifications",
          "Activity logging",
          "Multiple device tracking",
          "Custom alert rules"
        ]
      },
      {
        title: "Report Generation",
        description: "Generate comprehensive reports of historical data",
        imageUrl: "/placeholder.svg?height=400&width=600",
        benefits: [
          "Instant breach notifications",
          "Activity logging",
          "Multiple device tracking",
          "Custom alert rules"
        ]
      }
    ]
  },
  {
    id: "dashboard",
    title: "Dashboard",
    description: "Customize your dashboard, inventory, and data policies.",
    icon: LayoutDashboard,
    slides: [
      {
        title: "Customizable Widgets",
        description: "Build your perfect dashboard with drag-and-drop widgets.",
        keyPoint: "Create a dashboard that shows exactly what you need to see.",
        benefits: [
          "Drag-and-drop customization",
          "Real-time data updates",
          "Multiple layout options",
          "Widget preferences saving"
        ]
      },
      {
        title: "Data Visualization",
        description: "Transform complex data into clear, actionable insights.",
        keyPoint: "Understand your data at a glance with intuitive visualizations.",
        benefits: [
          "Interactive charts and graphs",
          "Custom reporting tools",
          "Export capabilities",
          "Trend analysis"
        ]
      },
      {
        title: "Performance Metrics",
        description: "Track all your important KPIs in one place.",
        keyPoint: "Monitor and improve your business performance effectively.",
        benefits: [
          "Real-time KPI tracking",
          "Custom metric creation",
          "Goal setting and monitoring",
          "Performance comparisons"
        ]
      }
    ]
  },
  {
    id: "notifications",
    title: "Notifications",
    description: "Manage your notifications, inventory, and data policies.",
    icon: Bell,
    slides: [
      {
        title: "Smart Alerts",
        description: "Stay informed with intelligent notification system.",
        keyPoint: "Never miss important updates with customizable alerts.",
        benefits: [
          "Priority-based notifications",
          "Custom alert rules",
          "Multiple delivery channels",
          "Alert grouping"
        ]
      },
      {
        title: "Notification Preferences",
        description: "Customize how and when you receive notifications.",
        keyPoint: "Get notifications your way, when you want them.",
        benefits: [
          "Channel selection",
          "Time-based rules",
          "Category filtering",
          "Quiet hours setting"
        ]
      },
      {
        title: "Alert Analytics",
        description: "Understand and optimize your notification patterns.",
        keyPoint: "Improve communication efficiency with notification insights.",
        benefits: [
          "Response time tracking",
          "Notification effectiveness",
          "Pattern analysis",
          "Optimization suggestions"
        ]
      }
    ]
  }
];

function FeatureCard({ title, description, icon: Icon, onClick }) {
  return (
    <Card onClick={onClick} className={`transition-all duration-300 card-border cursor-pointer`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-5 w-5 text-primary }`} />
      </CardHeader>
      <CardContent>
        <p className="text-sm text-left text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}

export function UserSetupFeatures() {
  const [selectedFeature, setSelectedFeature] = useState(null);

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        {features.map((feature, index) => (
          <FeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
            onClick={() => setSelectedFeature(feature)}
          />
        ))}
      </div>
      {selectedFeature && (
        <UserSetupFeatureModal
          isOpen={!!selectedFeature}
          onClose={() => setSelectedFeature(null)}
          feature={selectedFeature}
        />
      )}
    </>
  );
}
