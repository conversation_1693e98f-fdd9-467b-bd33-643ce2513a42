import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import { Tabs, TabsList, TabsTrigger } from "@components/shadcn/components/tabs";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useSimulatorTemplateList from "@hooks/product/useSimulatorTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { useState } from "react";
import { Plus, Search } from "lucide-react";
type TabsType = "default" | "geo";

const SimulatorTemplateList = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [tabValue, setTabValue] = useState<"default" | "geo">("default");

  const navigate = useCustomNavigate();
  const { data: simulatorListData, isLoading: simulatorListLoading } = useSimulatorTemplateList({
    featureType: tabValue
  });

  const [sortFn, sort] = useTableSort();
  const handleChange = (val: TabsType) => {
    setTabValue(val);
  };
  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <Tabs value={tabValue}>
          <TabsList className=" ">
            <TabsTrigger value="default" onClick={() => handleChange("default")}>
              Default Templates
            </TabsTrigger>

            <TabsTrigger value="geo" onClick={() => handleChange("geo")}>
              Geo Templates
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="flex gap-4">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search"
            className="w-[25rem]"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />

          <Button onClick={() => navigate("new")} startIcon={<Plus size={BUTTON_ICON_SIZE} />}>
            Add Simulator Template
          </Button>
        </div>
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Template Name</TableHead>
            <TableHead>Description</TableHead>
          </>
        }
        body={
          simulatorListLoading ? (
            <TableRowsSkeleton />
          ) : !simulatorListData?.length ? (
            <DataNotFound title="No Template Available" isTable />
          ) : (
            simulatorListData?.toSorted(sortFn).map((template, i) => (
              <tr
                className="cursor-pointer"
                key={template.name}
                onClick={() => navigate(template.name)}
              >
                <TableRow>{i + 1}</TableRow>
                <TableRow title>{template.name}</TableRow>
                <TableRow>{template.description || "N/A"}</TableRow>
              </tr>
            ))
          )
        }
      />
    </section>
  );
};

export default SimulatorTemplateList;
