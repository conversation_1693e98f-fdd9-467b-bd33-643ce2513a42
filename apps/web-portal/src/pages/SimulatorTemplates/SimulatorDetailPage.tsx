import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DetailsCell from "@components/DetailsCell";
import Dropdown from "@components/Dropdown";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import NoDataFound from "@components/NoDataFound";
import { Card } from "@components/ui";
import useSimulatorDetails, { ParametersType } from "@hooks/Simulator/useSimulatorDetails";
import { LayoutTemplate, ListTodo } from "lucide-react";
import { useParams } from "react-router-dom";




const SimulatorDetailPage = () => {
  const { templateName } = useParams();
  const { data: simulatorDetails, isLoading: simulatorDetailsLoading } = useSimulatorDetails({templateName})
  

  return (
    <section className="space-y-6">
      <Card className="space-y-6">
        <HeadingIcon Icon={LayoutTemplate} title="Simulator Details" />

        {simulatorDetailsLoading ? (
          <div className="text-center">
            <CardLoadingSkeleton />
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4">
            <DetailsCell title="Template Name" data={simulatorDetails?.name} />
            <DetailsCell title="Description" data={simulatorDetails?.description} />
          </div>
        )}
      </Card>
      <Card className="space-y-4">
        <HeadingIcon Icon={ListTodo} title="Simulator Properties" />
        {simulatorDetailsLoading ? (
          <div className="text-center">
            <CardLoadingSkeleton />
          </div>
        ) : !simulatorDetails?.parameters.length ? (
          <NoDataFound content="No Property Added" />
        ) : (
          <div className="grid grid-cols-3 gap-4 ">
            {simulatorDetails.parameters.map((property) => (
              <SimulatorCard
                key={property.id}
                property={property}
                //   setProperties={setProperties}
              />
            ))}
          </div>
        )}
      </Card>
    </section>
  );
};

const SimulatorCard = ({property}:{property:ParametersType}) => {
  return (
    <Card variant="second" className="space-y-3">
      <h3 className="heading-3">Property</h3>

      <div className="flex gap-3 items-center">
        <Input disabled value={property.name} medium className=" flex-1" />
        <Dropdown
          disabled
          options={["string", "boolean", "number"]}
          value={property.type}
          className="w-28 ml-1"
          onChange={() => {}}
        />
      </div>

      <div className="flex gap-4 items-end ">
        {property.type === "number" && (
          <>
            <Input
              value={property.min}
              type="number"
              className=" flex-1"
              label="Min"
              medium
              disabled
            />
            <Input
              value={property.max}
              type="number"
              medium
              label="Max"
              className=" flex-1"
              disabled
            />
          </>
        )}
        {property.type === "string" && (
          <Dropdown
            value={property.values || []}
            className=" flex-1"
            label="Values"
            isMulti
            isAddField
            disabled
            options={[...property.values]}
            onChange={() => {}}
          />
        )}
        {property.type === "boolean" && (
          <Dropdown
            label="values"
            className=" flex-1"
            value={["true", "false"]}
            disabled
            options={["true", "false"]}
            isMulti
            onChange={() => {}}
          />
        )}
      </div>
    </Card>
  );
};

export default SimulatorDetailPage;
