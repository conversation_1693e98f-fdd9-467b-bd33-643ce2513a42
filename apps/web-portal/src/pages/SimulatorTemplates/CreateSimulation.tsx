import { createSimulatorTemplate } from "@api/product";
import Button from "@components/Button";
import { Card } from "@components/ui";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { useMutation } from "@tanstack/react-query";
import { DEFAULT_VALIDATE, LENGTH_VALIDATE } from "@utils/from_schema";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { LayoutTemplate, ListTodo, Plus } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { generateRandomString } from "../MonitorPage/utils";
import {
  PropertiesItem,
  initialSimulatedProperty
} from "../ProductPage/AddProductSection/Steps/SimulatedProduct/SimulatedAdditional";
import SimulatorParameterItem from "../ProductPage/SimulatorParameterItem";

type FormaValues = {
  simulatorName : string;
  description: string;
}
const CreateSimulation = () => {
  const [properties, setProperties] = useState<PropertiesItem[]>([initialSimulatedProperty]);

  const simulatorForm = useForm<FormaValues>();

  const navigate = useCustomNavigate();
  const mutation = useMutation({
    mutationFn: createSimulatorTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["simulator-template-list"] });
      showSuccessToast("Simulation Template created successfully");
      navigate("/templates/simulator-templates");
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });
  const onSubmit = async (values:FormaValues) => {
    const payload = { ...values, parameters: properties };
    mutation.mutate(payload);
  };

  return (
    <Card>
      <form className=" space-y-4" onSubmit={simulatorForm.handleSubmit(onSubmit)}>
        <div className="flex justify-between items-center">
          <HeadingIcon Icon={LayoutTemplate} title="Create Simulator Template" />

          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            loading={mutation.isPending}
          >
            Submit
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <Input
            label="Template Name"
            {...simulatorForm.register("simulatorName", LENGTH_VALIDATE().schema)}
            error={!!simulatorForm.formState.errors.simulatorName}
            helperText={simulatorForm.formState.errors.simulatorName && LENGTH_VALIDATE().message}
          />
          <Input
            label="Description"
            required
            className="col-span-2"
            {...simulatorForm.register("description", DEFAULT_VALIDATE.schema)}
            error={!!simulatorForm.formState.errors.description}
            helperText={simulatorForm.formState.errors.description && DEFAULT_VALIDATE.message}
          />
        </div>
        <hr className="hr !my-8" />

        <div className="between">
          <HeadingIcon Icon={ListTodo} title="Simulator Properties Permissions" />

          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="button"
            small
            onClick={() => {
              setProperties([
                ...properties,
                { ...initialSimulatedProperty, id: generateRandomString(8) }
              ]);
            }}
          >
            Add New Property
          </Button>
        </div>

        <div className="grid grid-cols-3 gap-4 ">
          {properties.map((property) => (
            <SimulatorParameterItem
              key={property.id}
              property={property}
              setProperties={setProperties}
            />
          ))}
        </div>
      </form>
    </Card>
  );
};

export default CreateSimulation;
