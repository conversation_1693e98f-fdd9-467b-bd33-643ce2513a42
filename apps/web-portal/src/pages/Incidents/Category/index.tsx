import { deleteIncidentCategory } from "@api/ticket";
import ActionButton from "@components/ActionButton";
import Button from "@components/Button";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import HeaderSection from "@components/layout/HeaderSection";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useTablePagination from "@hooks/classic/useTablePagination";
import useCategoryList from "@hooks/incidents/useCategoryList";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { INPUT_ICON_SIZE } from "@utils/utilities";
import { Plus, Search } from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import CreateCategorySidebar from "./CreateCategorySidebar";

const IncidentCategoryList = () => {
  const { searchQuery, setSearchQuery, setPage, page, setLimit, limit } = useTablePagination();
  const [deleteCategory, setDeleteCategory] = useState<{ id: string; name: string } | null>();
  const [sortFn, sort] = useTableSort();
  const createCategoryRef = useRef<ElementRef<typeof CreateCategorySidebar>>(null);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: categoryList, isLoading: isCategoryLoading } = useCategoryList({
    search: debouncedSearchQuery,
    page,
    limit
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: deleteIncidentCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["incident-categories"] });
      showSuccessToast("Category deleted successfully");
      setDeleteCategory(null);
    },
    onError(error) {
      showErrorToast(error.message);
    }
  });
  const deleteCategoryHandler = () => {
    if (!deleteCategory) return;
    deleteCategoryMutation.mutate({ id: deleteCategory.id });
  };

  return (
    <main className=" space-y-4">
         <HeaderSection
        title="Categories"
        description="Manage your Categories"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              type="submit"
              onClick={() => createCategoryRef?.current?.openDrawer()}
              //   noAccess={permissions.policy !== "write"}
          >
            Add Category
          </Button>
        }
      />
      <Card className= "space-y-4">
            <Input
              value={searchQuery}
              onChange={(e) => {
                setPage(1);
                setSearchQuery(e.target.value);
              }}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("name", order)}>Category Name</TableHead>
              <TableHead onSort={(order) => sort("description", order)}>Description</TableHead>
              <TableHead>Actions</TableHead>
            </>
          }
          body={
            isCategoryLoading ? (
              <TableRowsSkeleton />
            ) : !categoryList || !categoryList?.categoryList?.length ? (
              <DataNotFound title="No Category Available" isTable />
            ) : (
              categoryList?.categoryList.toSorted(sortFn).map((category, i) => (
                <tr key={category.id}>
                  {/* <TableRow>{getTableIndex(page, limit, i)}</TableRow> */}
                  <TableRow>{i + 1}</TableRow>
                  <TableRow title>{category.name}</TableRow>
                  <TableRow>{category.description}</TableRow>

                  <TableRow>
                    <ActionButton
                      onClick={() => createCategoryRef?.current?.onEditCategory(category)}
                      className="!mr-2"
                      type="edit"
                    />
                    <ActionButton
                      onClick={() => setDeleteCategory({ id: category.id, name: category.name })}
                      type="delete"
                    />
                  </TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: categoryList?.pages || 1
          }}
        />
    </Card>
      
      <CreateCategorySidebar ref={createCategoryRef} />
      <ConfirmPrompt
        show={Boolean(deleteCategory)}
        validate
        item={deleteCategory?.name}
        onCancel={() => setDeleteCategory(null)}
        loading={deleteCategoryMutation.isPending}
        onConfirm={() => {
          deleteCategoryHandler();
        }}
      />
      
  
    </main>
  );
};

export default IncidentCategoryList;
