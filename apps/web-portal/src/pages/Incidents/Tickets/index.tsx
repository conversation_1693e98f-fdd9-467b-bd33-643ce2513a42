import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import { FilterData } from "@components/FilterData";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import useTablePagination from "@hooks/classic/useTablePagination";
import useIncidentList from "@hooks/incidents/useIncidentList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { TICKET_PRIORITY, TICKET_STATUS } from "@utils/utilities";
import { AlertTriangle, Clock, Plus, Search } from "lucide-react";
import { useState } from "react";
import IncidentCardSection from "./IncidentCardSection";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

export type previewUrlsType = {
  url: string | undefined;
  name: string | undefined;
  extension: string | undefined;
};

export const getStatusBadge = (status: string) => {
  switch (status) {
    case "new":
      return <Label text="New" color="blue" />;
    case "in_progress":
      return <Label text="In Progress" color="amber" />;
    case "under_review":
      return <Label text="Under Review" color="purple" />;
    case "resolved":
      return <Label text="Resolved" color="green" />;
    default:
      return <Label text={status} color="gray" />;
  }
};

export const getPriorityBadge = (priority: string) => {
  switch (priority) {
    case "4-low":
      return <Label text="Low" color="blue" />;

    case "3-medium":
      return <Label text="Medium" color="amber" />;

    case "2-high":
      return <Label text="High" color="orange" />;
    case "1-critical":
      return <Label text="Critical" color="red" />;
    default:
      return <Label text={priority} color="gray" />;
  }
};

const TicketsList = () => {
  const { limit, page, setPage, setLimit, searchQuery, setSearchQuery } = useTablePagination();
  const debouncedSearch = useDebounce(searchQuery, 500);
  const [priority, setPriority] = useState<string[]>([]);
  const [status, setStatus] = useState<string[]>([]);

  const {
    data: ticketsList,
    isLoading: isTicketListLoading,
    isRefetching
  } = useIncidentList({
    page,
    limit,
    search: debouncedSearch,
    priority: priority,
    status: status
  });

  const [sortFn, sort] = useTableSort();
  const navigate = useCustomNavigate();

  return (
    <section className=" space-y-4">
         <HeaderSection
              title="Tickets"
              description="Manage your Tickets"
              actions={
                <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("addTicket")}
            //   noAccess={permissions.policy !== "write"}
          >
            Add Ticket
          </Button>
              }
            />
      <IncidentCardSection />
      <Card className="space-y-4">
        <div className="flex gap-4 items-center">
          <Input
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setPage(1);
            }}
            className="flex-1"
            placeholder="Search"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />
            <FilterData
              title={`Priority`}
              filters={priority}
              setFilters={setPriority}
              options={TICKET_PRIORITY}
            />
            <FilterData
              title={`Status`}
              filters={status}
              setFilters={setStatus}
              options={TICKET_STATUS}
            />
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Title</TableHead>
            <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
            <TableHead onSort={(order) => sort("type", order)}>Type</TableHead>
            <TableHead onSort={(order) => sort("category", order)}>Category</TableHead>
            <TableHead onSort={(order) => sort("status", order)}>Status</TableHead>
            <TableHead onSort={(order) => sort("priority", order)}>Priority</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Create Date</TableHead>
          </>
        }
        body={
          isTicketListLoading || isRefetching ? (
            <TableRowsSkeleton />
          ) : !ticketsList || !ticketsList?.data?.length ? (
            <DataNotFound title="No Tickets Available" isTable />
          ) : (
            ticketsList.data.toSorted(sortFn).map((ticket, i: number) => (
              <tr key={ticket.id} className="cursor-pointer" onClick={() => navigate(ticket.id)}>
                <TableRow>{getTableIndex(page, limit, i)}</TableRow>
                <TableRow title>{ticket.title}</TableRow>
                <TableRow>{ticket.thingName}</TableRow>
                <TableRow>
                  <Label
                    color={ticket.type === "incident" ? "red" : "blue"}
                    className="uppercase"
                    text={
                      <div className="flex items-center gap-2">
                        {ticket.type === "incident" ? (
                          <AlertTriangle className="h-3 w-3" />
                        ) : (
                          <Clock className="h-3 w-3" />
                        )}
                        {ticket.type}
                      </div>
                    }
                  />
                </TableRow>
                <TableRow>{ticket.category}</TableRow>
                <TableRow>{getStatusBadge(ticket.status)}</TableRow>
                <TableRow>{getPriorityBadge(ticket.priority)}</TableRow>
                <TableRow>{convetUTCToLocal(ticket.createdAt)}</TableRow>
              </tr>
            ))
          )
        }
        pagination={{ page, setPage, setLimit, totalPages: ticketsList?.pages || 1 }}
      />
      </Card>
    </section>
  );
};

export default TicketsList;
