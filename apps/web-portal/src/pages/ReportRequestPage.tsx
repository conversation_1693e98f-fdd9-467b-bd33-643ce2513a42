import { Card, StatCard } from "@components/ui";
import useReportList from "@hooks/reports/useReportList";
import useReportStats from "@hooks/reports/useReportStats";
import useDebounce from "@hooks/useDebounce";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { CheckCircle, Clock, Cpu, DownloadCloud, Search } from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import HeadingIcon from "@components/HeadingIcon";
import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "./UserTypes/Tracking/HomeSection/utils";
import ActionButton from "@components/ActionButton";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import Input from "@components/Input";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import Label from "@components/Label";
import parser from "cron-parser";
import ConfirmPrompt from "@components/ConfirmPrompt";
import useDeleteScheduledReport from "@hooks/reports/useDeleteScheduledReport";
import { showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import AddReportRequestSidebar from "@components/AddReportRequestSidebar";
import { Plus } from "lucide-react";
import HeaderSection from "@components/layout/HeaderSection";

function getNextRunFormatted(cronExpr: string) {
  try {
    const interval = parser.parseExpression(cronExpr);
    const nextDate = interval.next().toDate();

    // Format to something like "Monday 12:10 PM"
    const formatted = convetUTCToLocal(nextDate);
    return formatted;
  } catch (err) {
    console.error("Invalid cron expression:", err);
    return null;
  }
}

const ReportRequestPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: stats, isLoading: statsLoading } = useReportStats();
  const { data, isLoading: reportLoading } = useReportList({
    search: debouncedSearchQuery
  });
  const addReportRequestSidebarRef = useRef<ElementRef<typeof AddReportRequestSidebar>>(null);
  const [sortFn, sort] = useTableSort();
  const { data: permissions } = useUserGroupPermissions();
  const [selectDeletingReport, setSelectDeletingReport] = useState<{
    id: number;
    title: string;
  }>();
  const deleteMutation = useDeleteScheduledReport({
    onSuccess: () => {
      showSuccessToast("Report request deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["reportList"] });
      setSelectDeletingReport(undefined);
    }
  });

  return (
    <main className="space-y-4">
       <HeaderSection
        title={`Report Requests`}
        description="Manage your Report Requests"
        actions={
          <Button
           onClick={() => addReportRequestSidebarRef.current?.openDrawer()}
           startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          >
            Generate Report
          </Button>
        }
      />
      <section className="flex w-full flex-wrap gap-4">
        <StatCard
          title={"Scheduled Reports"}
          value={stats.scheduleCount}
          loading={statsLoading}
          colorScheme="success"
          icon={CheckCircle}
          description={`${((stats.scheduleCount / stats.totalCount) * 100).toFixed(1)}%`}
        />
        <StatCard
          title={"Completed Reports"}
          value={stats.totalCount - stats.scheduleCount}
          loading={statsLoading}
          colorScheme="danger"
          icon={Clock}
          description={`${(((stats.totalCount - stats.scheduleCount) / stats.totalCount) * 100).toFixed(1)}%`}
        />

        <StatCard
          title={`Total Reports`}
          loading={statsLoading}
          value={stats.totalCount}
          colorScheme="info"
          icon={Cpu}
        />
      </section>

      <Card className="space-y-4">
         <h3 className="heading-2"> Reports List</h3>
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead>Job Name</TableHead>
              <TableHead onSort={(order) => sort("status", order)}>status</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Ran At</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead>Action</TableHead>
            </>
          }
          body={
            reportLoading ? (
              <TableRowsSkeleton />
            ) : !data?.reportsList?.length ? (
              <DataNotFound title="No Reports Found" isTable />
            ) : (
              <>
                {/* FILTER NEED TO BE CHANGE  */}
                {data.reportsList.toSorted(sortFn).map((reportItem, i) => (
                  <tr key={reportItem.id} className="cursor-pointer">
                    <TableRow>{i + 1}</TableRow>

                    <TableRow title>{reportItem.title}</TableRow>

                    <TableRow>
                      <Label
                        className="!capitalize"
                        color={
                          reportItem.status === "success"
                            ? "green"
                            : reportItem.status === "failed"
                              ? "red"
                              : reportItem.status === "pending"
                                ? "gray"
                                : "blue"
                        }
                        text={reportItem.status}
                      />
                    </TableRow>

                    <TableRow className=" capitalize">{reportItem.type}</TableRow>

                    <TableRow>
                      {reportItem.schedule ? (
                        <p>
                          Next Run{" "}
                          <span className="font-semibold mr-1">
                            {getNextRunFormatted(reportItem.schedule)}
                          </span>
                        </p>
                      ) : (
                        <p>
                          One Time{" "}
                          <span className="font-semibold mr-1">
                            {convetUTCToLocal(reportItem.createdAt)}
                          </span>
                        </p>
                      )}
                    </TableRow>

                    <TableRow>{convetUTCToLocal(reportItem.createdAt)}</TableRow>
                    <TableRow className="flex gap-2">
                      <ActionButton
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectDeletingReport({
                            id: reportItem.id,
                            title: reportItem.title
                          });
                        }}
                        disabled={permissions.reports !== "write"}
                        type="delete"
                      />
                      <ActionButton
                        onClick={(e) => {
                          e.stopPropagation();
                          addReportRequestSidebarRef.current?.openDrawer(reportItem);
                        }}
                        disabled={
                          permissions.reports !== "write" ||
                          reportItem.status === "running" ||
                          !reportItem.schedule
                        }
                        type="edit"
                      />
                    </TableRow>
                  </tr>
                ))}
              </>
            )
          }
        />
      </Card>
      <ConfirmPrompt
        validate
        show={Boolean(selectDeletingReport)}
        onCancel={() => setSelectDeletingReport(undefined)}
        loading={deleteMutation.isPending}
        item={selectDeletingReport?.title || ""}
        onConfirm={() => {
          if (selectDeletingReport?.id) {
            deleteMutation.mutate({ id: selectDeletingReport?.id });
          }
        }}
      />
      <AddReportRequestSidebar ref={addReportRequestSidebarRef} />
    </main>
  );
};

export default ReportRequestPage;
