.leaflet-container {
  /* height: 100%; */
  z-index: 2;
  height: 100%;
  width: 100%;
}
.leaflet-popup-content {
  margin: 0 !important;
}
.leaflet-popup {
  margin-bottom: 0.8rem !important;
}

.leaflet-popup-content-wrapper {
  padding: 0;
  background: transparent !important;
}
.leaflet-popup-content p {
  margin: 0.25rem 0 !important;
}
.leaflet-popup-tip {
  @apply bg-white dark:bg-secondary;
}

.leaflet-popup-close-button {
  top: 0.25rem !important;
  right: 0.25rem !important;
  font-size: 1.25rem !important;
  height: 1.5rem !important;
  width: 1.5rem !important;
  line-height: 1.25rem !important;
}

.geofence-draw-custom-icon {
  background-color: #ff0000;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid white;
  box-shadow: 0 0 2px black;
}

.leaflet-control-sidebar-button,
.leaflet-control-custom-button {
  display: block;
  width: 26px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  text-decoration: none;
  color: black;
  background-color: white;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
}

.leaflet-control-sidebar-button:hover,
.leaflet-control-custom-button:hover {
  background-color: #f4f4f4;
}

.leaflet-control-custom-button {
  border-top: 1px solid #ccc;
}

.leaflet-control-sidebar-button:before {
  content: "\2630"; /* Unicode for hamburger menu icon */
  font-size: 16px;
}

.leaflet-control-custom-button:before {
  content: "B"; /* Custom button content */
  font-size: 16px;
}

.leaflet-retina .leaflet-control-layers-toggle {
  background-size: 1rem 1rem !important;
}

.map-icon {
  width: 30px;
  height: 45px;
  background-color: #dd4b3e;
  border-radius: 100% 100% 0 100%;
  transform: rotate(-45deg);
  position: relative;
  overflow: hidden;
}

.map-icon:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #dd4b3e;
  border-radius: 100% 0 0 0;
}

.map-icon-inner {
  width: 12px;
  height: 12px;
  background-color: #9e3329;
  border-radius: 50%;
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

.leaflet-tooltip {
  border-radius: 2rem;
}

.voyager-inverted.leaflet-layer,
.voyager-inverted .leaflet-control-attribution {
  filter: invert(100%) hue-rotate(180deg) brightness(95%) contrast(90%);
}
.voyager-inverted.leaflet-layer img {
  filter: inherit;
}
