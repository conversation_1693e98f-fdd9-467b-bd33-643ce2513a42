import { GEO_URL } from "@api/index";
import { fetchApi } from "@api/_helpers";
import Button from "@components/Button";
import CustomTooltip from "@components/CustomTooltip";
import { useQuery } from "@tanstack/react-query";
import { getUrlParams } from "@utils/url";
import { useState } from "react";

const ShowAddressButton = ({ lat, long }: { lat: number; long: number }) => {
  const [enabled, setEnabled] = useState(false);

  const { data: address, isLoading } = useQuery({
    queryKey: ["geo-address", enabled, lat, long],
    enabled,
    queryFn: async () => {
      const fetchResponse = await fetchApi(
        `/report/address/?${getUrlParams({ lat, long })}`,
        {},
        GEO_URL
      );
      const res = await fetchResponse.json();
      if (res.status === "Success") {
        return res.data;
      }
      if (res.status === "Failure") {
        throw new Error(res.message);
      }
      throw new Error("Something went wrong");
    }
  });

  if (address) {
    return (
      <CustomTooltip title={address}>
        <p className="max-w-[12rem] truncate">{typeof address === "string" ? address : "N/A"}</p>
      </CustomTooltip>
    );
  }

  return (
    <Button
      small
      color="gray"
      loading={isLoading}
      loadingText=" "
      className="w-28 h-6"
      onClick={() => {
        setEnabled(true);
      }}
    >
      Show address
    </Button>
  );
};

export default ShowAddressButton;
