import {
  <PERSON><PERSON>r,
  MenubarCheckboxItem,
  MenubarContent,
  MenubarLabel,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { SlidersHorizontal } from "lucide-react";

const Filter = ({
  options,
  onChange,
  title = "Filters",
  value
}: {
  options: { label: string; value: string }[];
  onChange: (a: string) => void;
  title?: string;
  value: string;
}) => {
  return (
    <Menubar className="relative !bg-transparent border-none p-0 ">
      {value !== options[0]?.value && (
        <span className="absolute top-1 -right-1 flex h-2 w-2 rounded-full bg-brandColor" />
      )}
      <MenubarMenu>
        <MenubarTrigger className="bg-transparent dark:bg-transparent cursor-pointer btre">
          <SlidersHorizontal size={BUTTON_ICON_SIZE} />
        </MenubarTrigger>
        <MenubarContent>
          <MenubarLabel>{title}</MenubarLabel>
          <MenubarSeparator />
          {options.map((option) => (
            <MenubarCheckboxItem
              onClick={() => onChange(option.value)}
              checked={option.value === value}
              key={option.value}
            >
              {option.label}
            </MenubarCheckboxItem>
          ))}
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  );
};

export default Filter;
