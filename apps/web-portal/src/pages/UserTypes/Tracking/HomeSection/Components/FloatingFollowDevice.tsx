import ActionButton from "@components/ActionButton";
import useGeoThingDetails from "@hooks/geo/useGeoThingDetails";
import { connectToSocket } from "@utils/socket";
import { X } from "lucide-react";
import React, { ElementRef, useEffect, useRef } from "react";
import Draggable from "react-draggable";
import { MapContainer, Marker } from "react-leaflet";
import { useDispatch } from "react-redux";
import { io } from "socket.io-client";
import { stopFollowDevice } from "../../../../../features/geofenceSlice";
import { useAppSelector } from "../../../../../store";
import { customVehicleIcon } from "../mapUtils";
import CustomMapTile from "./CustomMapTile";
import { Card } from "@components/ui";

let socket: ReturnType<typeof io>;

const FloatingFollowDevice = () => {
  const followedDevice = useAppSelector((state) => state.geofence.followedDevice);
  const mapRef = useRef<ElementRef<typeof MapContainer>>(null);
  const markerRef = useRef<ElementRef<typeof Marker>>(null);
  const followedThingName = followedDevice?.thingName;
  const dispatch = useDispatch();

  const { data: thingDetails } = useGeoThingDetails({ thingName: followedThingName! });
  console.log(followedThingName);
  useEffect(() => {
    if (followedThingName) {
      socket = connectToSocket();

      socket.on(`position_update/${followedThingName}`, (newPosition) => {
        if (newPosition.thingName === followedThingName) {
          const { long, lat, altitude } = newPosition;
          mapRef.current?.panTo([lat, long, altitude]);
          markerRef.current?.setLatLng([lat, long, altitude]);
        }
      });
    }

    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [followedThingName]);

  if (!followedDevice) return null;

  const { lat, long, altitude } = followedDevice;

  return (
    <Draggable bounds="parent">
      <Card
        className="floating-window h-[27vw] w-[27vw] min-h-[20rem] min-w-[20rem]  max-h-[27rem] max-w-[27rem] p-0  flex overflow-hidden flex-col z-[100]"
        style={{ position: "absolute", left: 10, top: 10 }}
      >
        <div className="items-center text-center flex justify-between w-full p-2 border-b">
          <p className="heading-3  ml-2">{followedThingName}</p>
          <ActionButton Icon={X} onClick={() => dispatch(stopFollowDevice())} />
        </div>
        <div className="flex-1">
          <MapContainer maxZoom={20} ref={mapRef} center={[lat, long]} zoom={16} dragging={false}>
            <CustomMapTile />
            <Marker
              ref={markerRef}
              position={[lat, long, altitude]}
              icon={customVehicleIcon(thingDetails?.imgURL, thingDetails?.status)}
            />
          </MapContainer>
        </div>
      </Card>
    </Draggable>
  );
};

export default React.memo(FloatingFollowDevice);
