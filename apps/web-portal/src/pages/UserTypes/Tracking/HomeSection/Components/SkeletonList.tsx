import clsx from "clsx";
import React from "react";

const SkeletonList = ({
  count = 9,
  className,
  childrenClassName
}: {
  count?: number;
  className?: string;
  childrenClassName?: string;
}) => {
  return (
    <div className={clsx("animate-pulse space-y-3", className)}>
      {[...new Array(count).keys()].map((i) => (
        <div
          className={clsx("bg-secondary h-7 rounded border border-card-border", childrenClassName)}
          key={i}
        />
      ))}
    </div>
  );
};

export default SkeletonList;
