import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { Locate, LocateFixed, Link2Off } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { unlinkDeviceGeofence } from "../../../../../api/geoLocation";
import { setMapCurrentPoint } from "../../../../../features/geoThingDetailSlice";
import useGeoFenceList from "../../../../../hooks/geo/useGeoFenceList";
import { useAppDispatch, useAppSelector } from "../../../../../store";
import { showErrorToast, showSuccessToast } from "../../../../../utils";
import { convetUTCToLocal } from "../utils";
import ConfirmPrompt from "@components/ConfirmPrompt";

const GeofenceTab = () => {
  const [showGeoDeletePrompt, setShowGeoDeletePrompt] = useState(false);
  const [selectedGeofence, setSelectedGeofence] = useState<{
    name: string;
    id: number;
  } | null>(null);
  const selectedDevice = useParams().thingName!;
  const [sortFn, sort] = useTableSort();
  const mapCurrentPoint = useAppSelector((state) => state.geoDevice.mapCurrentPoint);

  const navigate = useCustomNavigate();
  const dispatch = useAppDispatch();
  const {
    data: geoFenceList,
    isLoading: geoFenceLoading,
    refetch: refetchGeoFence
  } = useGeoFenceList({ thingName: selectedDevice });

  const { mutate, isPending } = useMutation({
    mutationFn: unlinkDeviceGeofence,
    onSuccess: () => {
      showSuccessToast("Geofence unlinked successfully");
      setShowGeoDeletePrompt(false);
      refetchGeoFence();
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  return (
    <>
      <Table
        resizable
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            <TableHead onSort={(order) => sort("lineType", order)}>Type</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Unlink </TableHead>
            <TableHead>Locate</TableHead>
            <TableHead>Visit</TableHead>
          </>
        }
        body={
          geoFenceLoading ? (
            <TableRowsSkeleton />
          ) : !geoFenceList?.length ? (
            <DataNotFound title="No Geofence Available" isTable />
          ) : (
            geoFenceList.toSorted(sortFn).map((geofence, index) => (
              <tr key={geofence.id}>
                <TableRow>{index + 1}</TableRow>
                <TableRow>{geofence.name}</TableRow>
                <TableRow>{geofence.lineType}</TableRow>
                <TableRow>{geofence.description}</TableRow>
                <TableRow>{geofence?.attributes?.email || "N/A"}</TableRow>
                <TableRow>{convetUTCToLocal(geofence?.attributes?.createdAt)}</TableRow>
                <TableRow>
                  {!geofence.routePointId && (
                    <Link2Off
                      onClick={() => {
                        setShowGeoDeletePrompt(true);
                        setSelectedGeofence({ name: geofence.name, id: geofence.id });
                      }}
                      className="!h-4 cursor-pointer"
                    />
                  )}
                </TableRow>
                <TableRow>
                  {mapCurrentPoint.identifier === geofence.id ? (
                    <LocateFixed
                      onClick={() =>
                        dispatch(
                          setMapCurrentPoint({
                            identifier: null,
                            points: []
                          })
                        )
                      }
                      className="!h-4 cursor-pointer"
                    />
                  ) : (
                    <Locate
                      onClick={() =>
                        dispatch(
                          setMapCurrentPoint({
                            identifier: geofence.id,
                            points: geofence.line,
                            extraInfo: { geofenceType: geofence.lineType }
                          })
                        )
                      }
                      className="!h-4 cursor-pointer"
                    />
                  )}
                </TableRow>
                <TableRow>
                  <Button
                    small
                    outlined
                    onClick={() => navigate(`/geofences?fence=${geofence.id}`)}
                  >
                    Visit
                  </Button>
                </TableRow>
              </tr>
            ))
          )
        }
      />
      <ConfirmPrompt
        show={showGeoDeletePrompt}
        validate
        message="Are  you sure you want to unlink "
        loading={isPending}
        item={selectedGeofence?.name}
        onCancel={() => {
          setShowGeoDeletePrompt(false);
          setSelectedGeofence(null);
        }}
        onConfirm={() =>
          mutate({ thingName: selectedDevice, geofenceId: selectedGeofence?.id || 0 })
        }
      />
    </>
  );
};

export default GeofenceTab;
