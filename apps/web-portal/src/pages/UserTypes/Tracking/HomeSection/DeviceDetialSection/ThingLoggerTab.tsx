import { getLoggerTable } from "@api/logger";
import DataNotFound from "@components/DataNotFound";
import Table, { TableHead } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useGeoThingList from "@hooks/geo/useGeoThingList";
import { CircularProgress } from "@mui/material";
import LoggerListItem from "@src/pages/Logger/layout/LoggerListItem";
import { useAppSelector } from "@src/store";
import { useInfiniteQuery } from "@tanstack/react-query";
import { Hash } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";
import { getDateFromDurationString } from "../utils";

const LIMIT = 50;

const ThingLoggerTab = ({
  thingName,
  productName
}: {
  thingName?: string;
  productName?: string;
}) => {
  // const [selectedItem, setSelectedItem] = useState<any>(undefined);

  const duration = useAppSelector((state) => state.geoDevice.duration);

  const { startDate, endDate } = useMemo(() => {
    return getDateFromDurationString(duration);
  }, [duration]);

  const { data: productThingList } = useGeoThingList({
    productName,
    enabled: Boolean(productName)
  });

  const thingList = useMemo(() => {
    return productThingList?.things?.map((item) => item.thingName);
  }, [productThingList]);

  const { ref, inView } = useInView();
  const { isLoading, data, isSuccess, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ["thing-product-logs", thingName, endDate, startDate, thingList],
      queryFn: ({ pageParam = 1 }) =>
        getLoggerTable(
          `${startDate}TO${endDate}`,
          {
            clientId: productName ? thingList : [thingName],
            event: []
          },
          pageParam,
          "mqtt"
        ),
      getNextPageParam: (lastPage, allPages) => {
        const nextPage = lastPage.length === LIMIT ? allPages.length + 1 : undefined;
        return nextPage;
      },
      initialPageParam: 1
    });

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);

  const isEmpty = data?.pages[0]?.length === 0 && isSuccess && !isLoading;

  return (
    <div>
      <Table
        head={
          <>
            <TableHead>Timestamp</TableHead>
            <TableHead>Event</TableHead>
            <TableHead>Client ID</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Protocol</TableHead>
            <TableHead>Details</TableHead>
            <TableHead>Actions</TableHead>
          </>
        }
        body={
          <>
            {isSuccess ? (
              data?.pages.map((page) =>
                page.map((item: any, i: number) => {
                  if (page.length === i + 1) {
                    return <LoggerListItem ref={ref} key={item.id} item={item} index={i} />;
                  }
                  return <LoggerListItem key={item.id} index={i} item={item} />;
                })
              )
            ) : (
              <TableRowsSkeleton />
            )}
            {isEmpty && <DataNotFound title="No Logs Available" isTable Icon={Hash} />}
            {(isFetchingNextPage || isLoading) && (
              <tr>
                <td className="w-full p-4 text-center" colSpan={100}>
                  <CircularProgress size={16} />
                </td>
              </tr>
            )}
          </>
        }
      />
    </div>
  );
};

export default ThingLoggerTab;
