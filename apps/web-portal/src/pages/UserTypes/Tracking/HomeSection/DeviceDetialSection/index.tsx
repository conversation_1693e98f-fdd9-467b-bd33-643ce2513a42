import { deleteGeoThing } from "@api/geoLocation";
import ActionButton from "@components/ActionButton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DetailsCell2 from "@components/DetailCell2";
import HeadingIcon from "@components/HeadingIcon";
import NoDataFound from "@components/NoDataFound";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { setDeviceDetailMinimize } from "@src/features/geoThingDetailSlice";
import { useAppDispatch, useAppSelector } from "@src/store";
import { useMutation } from "@tanstack/react-query";
import { THING_DEFAULT_IMAGE } from "@utils/deviceMapping";
import { GEO_PRODUCT_VAR, GEO_THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { DATACELL_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { latLngBounds, LatLngExpression } from "leaflet";
import { Calendar, ChevronDown, Clock, Hash, History, MapPin, Truck } from "lucide-react";
import React, { ElementRef, useCallback, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { Card } from "@components/ui";
import useGeoThingDetails from "../../../../../hooks/geo/useGeoThingDetails";
import DeviceSkeleton from "../Components/DeviceSkeleton";
import EditThingModal from "../EditThingModal";
import { convetUTCToLocal, getColorBasedOnBattery, getIconBasedOnBattery } from "../utils";
import DeviceMapLeaf from "./MapSection/DeviceMapLeaf";
import MoreOption from "./MoreOption";
import TabSection from "./TabSection";
import ZoomableImage from "@components/ZoomableImage";

const DeviceDetialSection = () => {
  const [deleteDevice, setDeleteDevice] = useState<string | null>(null);
  const [updateDevice, setUpdateDevice] = useState(null);
  const subFeature = useAppSelector(({ user }) => user.tenant?.subFeature);

  const thingName = useParams().thingName!;
  const deviceDetailMinimize = useAppSelector((state) => state.geoDevice.deviceDetailMinimize);
  const deviceMapLeafRef = useRef<ElementRef<typeof DeviceMapLeaf>>(null);

  const navigate = useCustomNavigate();
  const dispatch = useAppDispatch();
  const {
    data: thingsDetails,
    isLoading,
    refetch: refetchDeivces
  } = useGeoThingDetails({ thingName });

  useEffect(() => {
    if (thingsDetails?.productName == "student" && thingsDetails.meta?.studentClass) {
      navigate(`/classes/${thingsDetails.meta?.studentClass}/${thingsDetails.thingName}`, {
        replace: true
      });
    }
  }, [thingsDetails]);

  const { mutate, isPending } = useMutation({
    mutationFn: deleteGeoThing,
    onSuccess: () => {
      showSuccessToast(`${GEO_THING_VAR} deleted successfully`);
      setDeleteDevice(null);
      navigate(-1);
      refetchDeivces();
    },
    onError: (err) => {
      showErrorToast(err.message);
    }
  });

  const fitBounds = useCallback(
    (bounds: LatLngExpression[]) => {
      if (bounds && latLngBounds(bounds).isValid() && deviceMapLeafRef.current) {
        deviceMapLeafRef.current.fitBounds(bounds);
      }
    },
    [deviceMapLeafRef]
  );
  return isLoading || !thingsDetails ? (
    <DeviceSkeleton />
  ) : (
    <div className=" flex-1  space-y-4 flex flex-col h-[calc(100vh-6.5rem)] overflow-scroll no-scrollbar">
      <div className="grid gap-4 grid-cols-12">
        <Card className="space-y-2   p-4 col-span-5">
          <div className="between !items-start">
            <div className="flex gap-2 items-center ">
              <HeadingIcon title={thingName} Icon={Truck} />

              <div
                className={clsx(
                  thingsDetails.status === "connected" ? "dot-pulse-success" : "dot-pulse-danger",
                  "!w-2.5 !h-2.5 ml-2 "
                )}
              />
              <div className="flex items-center gap-1 ">
                {React.createElement(
                  getIconBasedOnBattery(
                    thingsDetails?.attributes?.batteryLevel || thingsDetails?.attributes?.battery
                  ),
                  {
                    className: clsx(
                      "!h-8",
                      getColorBasedOnBattery(
                        thingsDetails?.attributes?.batteryLevel ||
                          thingsDetails?.attributes?.battery
                      )
                    )
                  }
                )}

                <p className="heading-4 min-w-[24px]">
                  {thingsDetails?.attributes?.batteryLevel?.toFixed(0) ||
                    thingsDetails?.attributes?.battery?.toFixed(0) ||
                    0}
                  %
                </p>
              </div>
            </div>
            <div className=" flex gap-3 items-center">
              <ActionButton
                Icon={ChevronDown}
                visibleBg
                className={clsx(
                  "duration-300 p-2.5",
                  deviceDetailMinimize ? "rotate-0" : "rotate-180"
                )}
                onClick={() => dispatch(setDeviceDetailMinimize())}
              />

              <MoreOption
                setDeleteDevice={() => setDeleteDevice(thingsDetails.thingName)}
                setEditDevice={() => setUpdateDevice(thingName)}
              />
            </div>
          </div>
          <div className="flex gap-4 items-center">
            <ZoomableImage>
              <img
                className="h-24 w-24 object-contain rounded-md"
                alt="thing"
                src={thingsDetails.imgURL || THING_DEFAULT_IMAGE}
              />
            </ZoomableImage>
            <div className="">
              <div className="heading-4 text-muted-foreground flex items-center gap-2">
                <p className="font-normal w-32">Display Name : </p>
                <h3 className="text-foreground">{thingsDetails.displayName}</h3>
              </div>
              <div className="heading-4 text-muted-foreground flex items-center gap-2">
                <p className="font-normal w-32">Unique Id : </p>
                <h3 className="text-foreground">{thingsDetails?.uniqueId}</h3>
              </div>

              {subFeature !== "studentSafety" && (
                <div className="heading-4 text-muted-foreground flex items-center gap-2">
                  <p className="font-normal w-32">{GEO_PRODUCT_VAR} Name : </p>
                  <h3
                    onClick={() => navigate(`/products/${thingsDetails.productName}`)}
                    className="heading-4 text-foreground !font-semibold hover:underline hover:cursor-pointer"
                  >
                    {thingsDetails.productName}
                  </h3>
                </div>
              )}
            </div>
          </div>
          {!deviceDetailMinimize && (
            <div className="space-y-2">
              <DetailsCell2
                icon={<History size={DATACELL_ICON_SIZE} />}
                title="Version"
                titleWidth="!min-w-[8rem]"
                className="!mb-2"
                data={thingsDetails.version}
              />
              <DetailsCell2
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                title="Created"
                titleWidth="!min-w-[8rem]"
                className="!mb-2"
                data={convetUTCToLocal(thingsDetails.createdAt)}
              />
              <DetailsCell2
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                title="Last Updated"
                titleWidth="!min-w-[8rem]"
                className="!mb-2"
                data={convetUTCToLocal(thingsDetails.latestUpdate)}
              />
              <DetailsCell2
                icon={<MapPin size={DATACELL_ICON_SIZE} />}
                title="Address"
                className="!mb-2 !items-start"
                titleWidth="!min-w-[8rem]"
                data={`${thingsDetails?.address}`}
              />

              <hr className="hr !my-2" />

              <div className="space-y-2">
                <h3 className="heading-3">Tags</h3>

                <div className="space-y-1 min-h-[4rem]">
                  {!thingsDetails.meta || !Object.entries(thingsDetails.meta).length ? (
                    <NoDataFound content="No Tag Added" className="!text-center" />
                  ) : (
                    Object.entries(thingsDetails.meta).map(([key, value]) => (
                      <DetailsCell2
                        title={key}
                        icon={<Hash size={DATACELL_ICON_SIZE} />}
                        titleWidth="min-w-[6rem]"
                        className="!mb-0"
                        data={value}
                      />
                    ))
                  )}
                </div>
              </div>
            </div>
          )}
        </Card>
        <DeviceMapLeaf
          className="rounded-md col-span-7 card-border"
          deviceLat={thingsDetails.lat}
          deviceLong={thingsDetails.long}
          lastUpDate={thingsDetails.latestUpdate}
          imgURL={thingsDetails.imgURL || ""}
          ref={deviceMapLeafRef}
          productName={thingsDetails.productName}
        />
      </div>
      <TabSection fitBounds={fitBounds} />
      <ConfirmPrompt
        show={Boolean(deleteDevice)}
        validate
        loading={isPending}
        item={deleteDevice}
        onCancel={() => {
          setDeleteDevice(null);
        }}
        onConfirm={() => mutate(deleteDevice)}
      />
      {updateDevice && <EditThingModal setShow={setUpdateDevice} thingName={updateDevice} />}
    </div>
  );
};

export default DeviceDetialSection;
