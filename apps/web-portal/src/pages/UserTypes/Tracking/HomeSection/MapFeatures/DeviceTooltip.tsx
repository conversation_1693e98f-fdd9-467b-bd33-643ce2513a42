import ActionButton from "@components/ActionButton";
import NoDataFound from "@components/NoDataFound";
import useGeoThingDetails from "@hooks/geo/useGeoThingDetails";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { IconButton, Tooltip as MuiToolTip } from "@mui/material";
import { setMapCurrentPoint } from "@src/features/geofenceSlice";
import { useAppDispatch } from "@src/store";
import {
  Minus,
  X,
  Map,
  LocateFixed,
  BusFront,
  CalendarFold,
  Info,
  CircleParking,
  ChartNoAxesCombined
} from "lucide-react";
import { Tooltip } from "react-leaflet";
import { useSearchParams } from "react-router-dom";
import { convetUTCToLocal } from "../utils";
import { GEO_PRODUCT_VAR } from "@utils/featureLabels";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Card } from "@components/ui";

const DeviceTooltip = ({ thingName }: { thingName: string }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const selectedDevice = searchParams.get("thingName")!;
  const tooltipMode = searchParams.get("tooltipMode") || ("details" as "details" | "min");
  const naviagte = useCustomNavigate();
  const dispatch = useAppDispatch();

  const { data: thingsDetails, isLoading } = useGeoThingDetails({
    thingName: selectedDevice,
    enabled: !!selectedDevice
  });

  const onToolsClick = (thing: string, feature: string) => {
    naviagte(`/things/${thing}?tab=${feature}`);
  };

  const toggleToolTipMode = () => {
    setSearchParams((prev) => {
      const newMode = prev.get("tooltipMode") === "min" ? "details" : "min";
      prev.set("tooltipMode", newMode);
      return prev;
    });
  };

  if (selectedDevice !== thingName || tooltipMode === "min") {
    return (
      <Tooltip key={thingName} offset={[0, -10]} permanent interactive direction="top" opacity={1}>
        <div className=" cursor-pointer " onClick={toggleToolTipMode}>
          {thingsDetails?.thingName}
        </div>
      </Tooltip>
    );
  }

  const openInGoogleMaps = (lat: number, lng: number) => {
    window.open(`https://www.google.com/maps?q=${lat},${lng}&t=k`, "_blank");
  };

  return (
    <Tooltip
      offset={[0, -4]}
      permanent
      key="deviceDetial"
      direction="top"
      interactive
      opacity={1}
      className="z-10 !p-0 overflow-hidden !rounded-lg no-padding !border-none"
    >
      <div className="min-w-[22rem]  ">
        {isLoading ? (
          <div className="animate-pulse space-y-3 flex-1 flex flex-col p-4">
            <div className="bg-gray-300 dark:bg-gray-700 h-6 w-1/2 rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-4 rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-4 rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-4 rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-4 rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-4 rounded" />
          </div>
        ) : !thingsDetails ? (
          <NoDataFound className="!mt-0" />
        ) : (
          <Card className="p-0">
            <div className=" p-1.5 between items-center">
              <div className="flex gap-2 items-center">
                <BusFront size={INPUT_ICON_SIZE} className=" !text-blue-500 dark:!text-white" />
                <p className="heading-3  text-black dark:text-white">{thingName}</p>

                <div
                  className={`w-[0.75rem] h-[0.75rem] rounded-full ${
                    thingsDetails.status === "connected" ? "bg-green-500" : "bg-red-500"
                  }`}
                ></div>
              </div>
              <div className="flex gap-2">
                <ActionButton onClick={toggleToolTipMode} Icon={Minus} className="!p-1" />
                <ActionButton
                  onClick={() => {
                    searchParams.delete("thingName");
                    setSearchParams(searchParams);
                    dispatch(setMapCurrentPoint({ identifier: "", points: [] }));
                  }}
                  Icon={X}
                  className="!p-1"
                />
              </div>
            </div>
            <hr className="hr" />
            <div className="space-y-1.5 px-3 py-2">
              <DetailCell label="Display Name" value={thingsDetails.displayName} />
              <DetailCell label={`${GEO_PRODUCT_VAR} Name`} value={thingsDetails.productName} />
              <DetailCell label="Latitude" value={thingsDetails.lat} />
              <DetailCell label="Longitude" value={thingsDetails.long} />
              <DetailCell
                label="Last Update"
                value={convetUTCToLocal(thingsDetails.latestUpdate)}
              />
              <DetailCell
                label="Battery"
                value={thingsDetails.attributes?.batteryLevel?.toFixed(0)}
                extraValue="%"
              />

              <hr className="hr !my-2" />
              <div className="flex gap-3 justify-evenly divide-x-2">
                <MuiToolTip arrow title="Trips">
                  <IconButton onClick={() => onToolsClick(thingName, "Trips")}>
                    <ChartNoAxesCombined size={BUTTON_ICON_SIZE} />
                  </IconButton>
                </MuiToolTip>
                <MuiToolTip arrow title="Events">
                  <IconButton onClick={() => onToolsClick(thingName, "Events")}>
                    <CalendarFold size={BUTTON_ICON_SIZE} />
                  </IconButton>
                </MuiToolTip>
                <MuiToolTip arrow title="Stops">
                  <IconButton onClick={() => onToolsClick(thingName, "Stops")}>
                    <CircleParking size={BUTTON_ICON_SIZE} />
                  </IconButton>
                </MuiToolTip>
                <MuiToolTip arrow title="Geofence">
                  <IconButton onClick={() => onToolsClick(thingName, "Geofence")}>
                    <LocateFixed size={BUTTON_ICON_SIZE} />
                  </IconButton>
                </MuiToolTip>
                <MuiToolTip arrow title="More Info">
                  <IconButton onClick={() => onToolsClick(thingName, "")}>
                    <Info size={BUTTON_ICON_SIZE} />
                  </IconButton>
                </MuiToolTip>
                <MuiToolTip arrow title="Google Maps">
                  <IconButton
                    onClick={() => openInGoogleMaps(thingsDetails.lat, thingsDetails.long)}
                  >
                    <Map size={BUTTON_ICON_SIZE} />
                  </IconButton>
                </MuiToolTip>
              </div>
            </div>
          </Card>
        )}
      </div>
    </Tooltip>
  );
};

export default DeviceTooltip;

const DetailCell = ({
  label,
  value,
  extraValue
}: {
  label: string;
  value: any;
  extraValue?: string;
}) => {
  return (
    <div className="flex gap-2 items-center">
      <p className="heading-4 text-muted-foreground min-w-[7rem]">{label}</p>
      <p className="heading-4   text-black dark:text-white truncate">
        {value || "N/A"} {value && extraValue}
      </p>
    </div>
  );
};
