import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import Input from "@components/Input";
import NoDataFound from "@components/NoDataFound";
import { Card } from "@components/ui";
import { Badge } from "@components/shadcn/components/badge";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@components/shadcn/components/tooltip";
import useGeoProductProtocol from "@hooks/geo/useGeoProductProtocol";
import useGeoThingDetails from "@hooks/geo/useGeoThingDetails";
import useProductDocInfo from "@hooks/product/useProductDocInfo";
import { IconButton, Modal } from "@mui/material";
import { GEO_THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Cable, Copy, Globe, X } from "lucide-react";

function checkForMqtt(productType = "") {
  if (!productType) return "";
  return productType === "mqtt";
}
const handleCopyClick = async (textToCopy) => {
  try {
    await navigator.clipboard.writeText(textToCopy);
    showSuccessToast("URL copied to clipboard!");
  } catch (err) {
    console.log(err);
    showErrorToast("Failed to copy URL.");
  }
};
const HowToConnectModal = ({ deviceData, setDeviceData }) => {
  const { data: thingDetials } = useGeoThingDetails({
    thingName: deviceData?.thingName
  });

  const { data: productDocInfo, isLoading } = useProductDocInfo({
    enabled: Boolean(!!thingDetials?.productType && checkForMqtt(thingDetials?.productType))
  });

  const protocol = useGeoProductProtocol(deviceData?.productName, thingDetials?.productType);
  return (
    <Modal
      open={Boolean(deviceData)}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        outline: "none"
      }}
    >
      <Card className="!p-4 !pb-6 w-[50rem]">
        <div className="between items-center ">
          <h2 className="heading-2 ">
            How to Connect {thingDetials?.productType || ""} {GEO_THING_VAR}
          </h2>
          <IconButton onClick={() => setDeviceData(null)}>
            <X size={BUTTON_ICON_SIZE} />
          </IconButton>
        </div>
        <hr className="hr my-4" />

        {checkForMqtt(thingDetials?.productType) ? (
          <div>
            {isLoading ? (
              <CardLoadingSkeleton col={4} className="w-full" />
            ) : !productDocInfo ? (
              <NoDataFound />
            ) : (
              <div className="space-y-4">
                <p className="heading-3-normal ">
                  You can connect your {GEO_THING_VAR}s with this url :
                </p>

                <Card
                  variant="second"
                  className="space-y-2 flex gap-6 items-center justify-between rounded-lg "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <Globe size="1.2rem" />
                    </Card>
                    <div className="space-y-0.5">
                      <h3 className="font-medium text-base">HOST</h3>
                    </div>
                  </div>
                  <Input
                    className="w-[25rem]"
                    value={productDocInfo[0].info}
                    endIcon={
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <IconButton
                              onClick={() => {
                                handleCopyClick(productDocInfo[0].info);
                              }}
                            >
                              <Copy size={INPUT_ICON_SIZE} />
                            </IconButton>
                          </TooltipTrigger>
                          <TooltipContent>Copy URL</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    }
                  />
                </Card>
                <p className="heading-3-normal ">
                  Based on selected preferences, you can connect your {GEO_THING_VAR} with this url
                  and below ports :
                </p>

                <Card
                  variant="second"
                  className="space-y-2 flex gap-6 items-center justify-between rounded-lg "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <Cable size="1.2rem" />
                    </Card>
                    <div className="space-y-0.5">
                      <h3 className="font-medium text-base">MQTTS-MTLS</h3>
                      <p className="text-[0.8rem] text-muted-foreground">
                        Certificate based Authentication
                      </p>
                    </div>
                  </div>
                  <Badge>{productDocInfo[1].info}</Badge>
                </Card>
                <Card
                  variant="second"
                  className="space-y-2 flex gap-6 items-center justify-between rounded-lg "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <Cable size="1.2rem" />
                    </Card>
                    <div className="space-y-0.5">
                      <h3 className="font-medium text-base">WSS</h3>
                      <p className="text-[0.8rem] text-muted-foreground">
                        Username/Password based Authentication
                      </p>
                    </div>
                  </div>
                  <Badge>{productDocInfo[2].info}</Badge>
                </Card>
                <Card
                  variant="second"
                  className="space-y-2 flex gap-6 items-center justify-between rounded-lg "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <Cable size="1.2rem" />
                    </Card>
                    <div className="space-y-0.5">
                      <h3 className="font-medium text-base">MQTTS</h3>
                      <p className="text-[0.8rem] text-muted-foreground">
                        Username/Password based Authentication
                      </p>
                    </div>
                  </div>
                  <Badge>{productDocInfo[3].info}</Badge>
                </Card>
              </div>
            )}
          </div>
        ) : !protocol ? (
          <CardLoadingSkeleton col={2} className="w-full" />
        ) : (
          <div className="space-y-4">
            <p className="heading-3-normal ">
              You can connect your {GEO_THING_VAR}s with this url :
            </p>

            <Card
              variant="second"
              className="space-y-2 flex gap-6 items-center justify-between rounded-lg "
            >
              <div className="flex gap-4 items-center">
                <Card variant="third">
                  <Globe size="1.2rem" />
                </Card>
                <div className="space-y-0.5">
                  <h3 className="font-medium text-base">HOST ( {protocol?.key} )</h3>
                  <Badge>{protocol?.value}</Badge>
                </div>
              </div>
              <Input
                className="w-[25rem]"
                value={import.meta.env.VITE_TRACCAR_URL || `traccar-dev.firewires.net`}
                endIcon={
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          onClick={() => {
                            handleCopyClick(
                              import.meta.env.VITE_TRACCAR_URL || `traccar-dev.firewires.net`
                            );
                          }}
                        >
                          <Copy size={INPUT_ICON_SIZE} />
                        </IconButton>
                      </TooltipTrigger>
                      <TooltipContent>Copy URL</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                }
              />
            </Card>
          </div>
        )}
      </Card>
    </Modal>
  );
};

export default HowToConnectModal;
