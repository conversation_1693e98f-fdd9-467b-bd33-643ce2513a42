import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import { IconButton } from "@mui/material";
import { useAppSelector } from "@src/store";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import clsx from "clsx";
import { CirclePlus, ArrowLeft } from "lucide-react";
import { ElementRef, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { convetUTCToLocal } from "../HomeSection/utils";
import AddGeofenceToDeviceModal from "./AddGeofenceToDeviceModal";
import GeofenceDevices from "./GeofenceDevices";
import { Card } from "@components/ui";

const GeofenceDetails = ({ fence, setShow }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const linkDeviceRef = useRef<ElementRef<typeof AddGeofenceToDeviceModal>>(null);

  const selectedGeofence = useAppSelector((state) => state.geofence.selectedGeofence);

  const selectedGeofenceId = searchParams.get("fence") || selectedGeofence!;
  const { data: permissions } = useUserGroupPermissions();

  return (
    <div className="px-1 space-y-4">
      <Card
        variant="second"
        className={clsx(
          "group  min-w-[12.5rem] !p-3 relative overflow-hidden rounded-md  ",
          "flex items-center  gap-4 "
        )}
      >
        <IconButton className="!bg-gray-200 dark:!bg-gray-500 !p-1 " onClick={() => setShow(false)}>
          <ArrowLeft size={BUTTON_ICON_SIZE} />
        </IconButton>

        <div>
          <h2 className="heading-4">{fence.name}</h2>
          <p className="content-2  text-muted-foreground">
            {convetUTCToLocal(fence?.attributes?.createdAt)}
          </p>
        </div>
        <div className="flex gap-0.5 ml-auto ">
          {fence.id.toString() === selectedGeofenceId && (
            <IconButton
              className="!p-2"
              onClick={(e) => {
                linkDeviceRef.current?.showModal(e);
              }}
              disabled={permissions.geofence !== "write"}
            >
              <CirclePlus size="1.5rem" />
            </IconButton>
          )}
        </div>
      </Card>
      <GeofenceDevices />
      <AddGeofenceToDeviceModal ref={linkDeviceRef} geofenceId={selectedGeofenceId} />
    </div>
  );
};

export default GeofenceDetails;
