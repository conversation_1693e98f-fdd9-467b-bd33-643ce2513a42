import { linkDeviceGeofence } from "@api/geoLocation";
import Button from "@components/Button";
import Input from "@components/Input";
import useGeofenceDevices from "@hooks/geo/useGeofenceDevices";
import useGeoThingList from "@hooks/geo/useGeoThingList";
import useDebounce from "@hooks/useDebounce";
import { Checkbox, Popover } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import {
  ForwardedRef,
  forwardRef,
  MouseEvent,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState
} from "react";
import { Search } from "lucide-react";
import SkeletonList from "../HomeSection/Components/SkeletonList";
import { INPUT_ICON_SIZE } from "@utils/utilities";
import { GEO_THING_VAR } from "@utils/featureLabels";
import { Card } from "@components/ui";

type Props = {
  geofenceId: string;
};
type Ref = {
  showModal: (e: MouseEvent<HTMLButtonElement, MouseEvent>) => void;
};

const AddGeofenceToDeviceModal = ({ geofenceId }: Props, ref: ForwardedRef<Ref>) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const [selectedDevicesMap, setSelectedDevicesMap] = useState<Record<string, boolean>>({});
  const { data: geofenceDevices } = useGeofenceDevices({ geofenceId });

  const selectedDevices = Object.keys(selectedDevicesMap).filter((key) => selectedDevicesMap[key]);

  const geofenceDevicesMap = useMemo(() => {
    if (!geofenceDevices) {
      return {};
    }
    return geofenceDevices.reduce(
      (acc, item) => {
        acc[item.thingName] = true;
        return acc;
      },
      {} as Record<string, true>
    );
  }, [geofenceDevices?.length]);

  const { data: thingsList, isLoading: deviceListLoading } = useGeoThingList({
    search: debouncedSearchQuery
  });

  const hideModal = () => {
    setSelectedDevicesMap({});
    setAnchorEl(null);
  };

  const showModal = useCallback((e: MouseEvent<HTMLButtonElement, MouseEvent>) => {
    setAnchorEl(e.currentTarget);
  }, []);

  useImperativeHandle(ref, () => ({ showModal }), []);

  const mutation = useMutation({
    mutationFn: linkDeviceGeofence,
    onSuccess: () => {
      showSuccessToast("Devices Linked successfully");
      queryClient.invalidateQueries({ queryKey: ["geofence-devices", geofenceId] });

      setTimeout(() => {
        hideModal();
      }, 1000);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const handleSubmit = () => {
    const payload = {
      geofenceId,
      thingNames: selectedDevices
    };
    mutation.mutate(payload);
  };

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={hideModal}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      }}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left"
      }}
      elevation={1}
      BackdropProps={{
        style: {
          backdropFilter: "none"
        }
      }}
    >
      <Card className="w-80 p-2 max-h-[80vh] flex flex-col ">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
          }}
          small
          className="w-full"
          placeholder={`Search for ${GEO_THING_VAR}`}
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        {deviceListLoading ? (
          <SkeletonList className="mt-2" count={4} />
        ) : !thingsList?.things.length ? (
          <div className="card flex items-center justify-center mt-2">
            <p className="helper-text">no thing found</p>
          </div>
        ) : (
          <div className=" overflow-y-scroll no-scrollbar m-1">
            {thingsList.things

              .filter((item) => !geofenceDevicesMap[item.thingName])
              .map((thing) => (
                <div key={thing.thingName} className="flex items-center shadow-sm py-2">
                  <img alt="thing" src={thing.imgURL || ""} className="h-8 w-8 rounded-full mr-2" />
                  <div>
                    <p className="heading-4">{thing.thingName}</p>
                    <p className="content-2 text-muted-foreground">{thing.uniqueId}</p>
                  </div>
                  <Checkbox
                    className="!p-1 !ml-auto"
                    size="small"
                    checked={selectedDevicesMap[thing.thingName]}
                    onChange={(e, checked) => {
                      setSelectedDevicesMap((prevState) => ({
                        ...prevState,
                        [thing.thingName]: checked
                      }));
                    }}
                  />
                </div>
              ))}
          </div>
        )}
        <Button
          small
          onClick={handleSubmit}
          loading={mutation.isPending}
          disabled={!selectedDevices.length}
          className="mt-auto py-2"
        >
          Add To Geofence
        </Button>
      </Card>
    </Popover>
  );
};

export default forwardRef(AddGeofenceToDeviceModal);
