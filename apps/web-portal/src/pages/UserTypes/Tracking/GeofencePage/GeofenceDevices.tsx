import { unlinkDeviceGeofence } from "@api/geoLocation";
import Button from "@components/Button";
import useGeofenceDevices from "@hooks/geo/useGeofenceDevices";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import clsx from "clsx";
import { Trash2 } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import SkeletonList from "../HomeSection/Components/SkeletonList";
import { convertSecondsToTime } from "../HomeSection/utils";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import { GEO_THING_VAR } from "@utils/featureLabels";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";

const GeofenceDevices = () => {
  const searchParam = useSearchParams()[0];

  const fenceId = searchParam.get("fence")!;
  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();

  const {
    data: geofenceDevices,
    isLoading,
    refetch: refetchGeoFence
  } = useGeofenceDevices({ geofenceId: fenceId });

  const unlinkGeofenceMutation = useMutation({
    mutationFn: unlinkDeviceGeofence,
    onSuccess: () => {
      showSuccessToast("Geofence unlinked successfully");
      refetchGeoFence();
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  if (isLoading) {
    return (
      <div>
        <SkeletonList className="mt-2 ml-2" count={4} />
      </div>
    );
  }

  if (!geofenceDevices || geofenceDevices.length === 0) {
    return (
      <Card className="flex mt-2 items-center justify-center">
        <p className="helper-text">No connected {GEO_THING_VAR}</p>
      </Card>
    );
  }

  return (
    <div className=" space-y-2 ml-2 overflow-y-scroll no-scrollbar max-h-[80vh]">
      {geofenceDevices.map((geofenceDevice) => (
        <Card
          variant="second"
          key={geofenceDevice.thingName}
          className="flex items-center justify-between !p-2  relative"
        >
          <div className=" flex gap-2 items-center">
            <div className="rounded-full w-[2rem]  overflow-hidden  object-cover">
              <img className="" src={geofenceDevice.imgURL} alt="device" />
            </div>

            <div className="ml-2">
              <button onClick={() => navigate(`/things/${geofenceDevice.thingName}?tab=Geofence`)}>
                <p className=" heading-4 underline">{geofenceDevice.name}</p>
              </button>
              {"eventName" in geofenceDevice ? (
                <div className="flex gap-2 items-center">
                  <div
                    className={clsx(
                      " text-xs border border-solid py-1 px-2 mt-1 rounded-md max-w-[13rem] truncate font-semibold uppercase",
                      geofenceDevice.eventName === "geofenceEnter"
                        ? "border-green-500 text-green-500"
                        : "border-red-500 text-red-500"
                    )}
                  >
                    {geofenceDevice.eventName === "geofenceEnter" ? "Inside" : "Outside"}
                    <span className="text-muted-foreground ml-1 font-normal lowercase">
                      ({convertSecondsToTime(geofenceDevice.timePassed)} ago)
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <div
                    className={clsx(
                      " text-xs border border-muted-foreground border-solid py-1 px-2 mt-1 rounded-md   text-muted-foreground uppercase"
                    )}
                  >
                    unknown
                  </div>
                </div>
              )}
            </div>
          </div>
          <Button
            small
            color="gray"
            outlined
            loading={
              unlinkGeofenceMutation.isPending &&
              unlinkGeofenceMutation.variables.thingName === geofenceDevice.thingName
            }
            onClick={() => {
              unlinkGeofenceMutation.mutate({
                thingName: geofenceDevice.thingName,
                geofenceId: parseInt(fenceId)
              });
            }}
            className="!p-0 !border-transparent !bg-transparent"
            noAccess={permissions.geofence !== "write"}
          >
            <Trash2 size={BUTTON_ICON_SIZE} />
          </Button>
        </Card>
      ))}
    </div>
  );
};

export default GeofenceDevices;
