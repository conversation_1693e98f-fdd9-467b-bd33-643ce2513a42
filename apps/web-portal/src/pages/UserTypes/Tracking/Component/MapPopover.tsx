import React, { ComponentProps } from "react";
import { Popup } from "react-leaflet";

const MapPopover = ({
  children,
  title,
  offset = [0, -5],
  eventHandlers
}: {
  children: React.ReactNode;
  title?: string;
  offset?: [number, number];
  eventHandlers?: ComponentProps<typeof Popup>["eventHandlers"];
}) => {
  return (
    <Popup
      eventHandlers={eventHandlers}
      offset={offset}
      autoClose={false}
      className="!min-w-[200px]"
    >
      <div className="bg-card  !border-gray-300 dark:!border-gray-500 !p-0 rounded-md overflow-hidden">
        {title && <p className="py-1 px-2  heading-4-bold  text-black dark:text-white">{title}</p>}
        <hr className="hr" />

        <div className=" p-2 ">{children}</div>
      </div>
    </Popup>
  );
};

export default MapPopover;
