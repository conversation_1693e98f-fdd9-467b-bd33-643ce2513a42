import useMapStore from "@hooks/useMapStore";
import { IconButton } from "@mui/material";
import clsx from "clsx";
import { motion } from "framer-motion";
import { ChevronFirst, ChevronLeft, ChevronRight, Menu } from "lucide-react";
import { HTMLAttributes, ReactElement, useState } from "react";
import { useLocation, useParams } from "react-router-dom";

const isMapScreen = (path: string, partnerName: string) => {
  if (path === "/") {
    return true;
  }
  if (path.endsWith(`/tenants/geo/${partnerName}`)) {
    return true;
  }
  if (path.endsWith(`/tenants/geo/${partnerName}/`)) {
    return true;
  }
  return false;
};

const getListVisibility = (path: string) => {
  return Boolean(localStorage.getItem(`menu-open-${path}`) !== "false");
};

type SideBarWrapperType = HTMLAttributes<HTMLDivElement> & {
  children: ReactElement | ReactElement[];
  title?: string;
  addButton?: ReactElement;
  titleComponent?: ReactElement;
  stickyItem?: ReactElement;
  shrinkNotAllowed?: boolean;
};

export const SidebarWrapper = ({
  children,
  className = "",
  title,
  addButton,
  titleComponent,
  stickyItem,
  shrinkNotAllowed = true
}: SideBarWrapperType) => {
  const route = useLocation().pathname;
  const { partnerName } = useParams();
  const [listVisibility, setListVisibility] = useState(getListVisibility(route));
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const mapVisible = isMapScreen(route, partnerName);
  const mapRef = useMapStore((state) => state.mapRef);
  const toggleSideBarVisibility = () => {
    localStorage.setItem(`menu-open-${route}`, `${!listVisibility}`);
    setListVisibility(!listVisibility);
  };

  const shrinkSideBar = !listVisibility && mapVisible;
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
    setTimeout(() => {
      mapRef?.invalidateSize();
    }, 200);
  };

  return (
    <div
      className={clsx(
        "transition-all pb-2 shadow-md rounded-md relative",
        "flex flex-col   bg-card card-border",
        isSidebarOpen ? " w-[21rem]" : "w-0",
        mapVisible && "z-10 !absolute  my-4 ml-4  top-0 bottom-0 flex",
        shrinkSideBar && "!h-12 !w-12",
        className
      )}
    >
      {isSidebarOpen && (
        <div>
          <div className="flex items-center p-2 gap-2">
            {mapVisible && (
              <IconButton
                className="!p-1 dark:!bg-white  !bg-gray-700 !text-background"
                onClick={toggleSideBarVisibility}
              >
                {listVisibility ? <ChevronFirst size="1.2rem" /> : <Menu size="1.2rem" />}
              </IconButton>
            )}
            {!shrinkSideBar && (
              <div className="flex py-2 mx-1 items-center justify-between w-full">
                {titleComponent || <p className="truncate heading-2-bold ">{title}</p>}
                {addButton && addButton}
              </div>
            )}
          </div>
          {!shrinkSideBar && stickyItem && stickyItem}
        </div>
      )}
      {!mapVisible && shrinkNotAllowed && (
        <button
          onClick={toggleSidebar}
          className={clsx(
            "absolute top-0 p-0  z-10 bg-secondary  border  rounded-r-md  transition-all duration-200 flex items-center justify-center group",
            "h-16 w-4  ",
            isSidebarOpen ? "-right-4" : "-right-4"
          )}
        >
          {isSidebarOpen ? (
            <ChevronLeft className="h-4 w-4 " />
          ) : (
            <ChevronRight className="h-4 w-4 " />
          )}
        </button>
      )}
      {!shrinkSideBar && (
        <motion.div className="flex flex-col pb-2 px-1 overflow-y-scroll no-scrollbar flex-1 space-y-4 pt-1">
          {children}
        </motion.div>
      )}
    </div>
  );
};

export default SidebarWrapper;
