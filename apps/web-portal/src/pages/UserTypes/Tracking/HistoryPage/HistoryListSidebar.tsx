import { HistoryItem } from "@/index";
import DataNotFound from "@components/DataNotFound";
import { But<PERSON> } from "@components/shadcn/components/button";
import { Popover, PopoverContent, PopoverTrigger } from "@components/shadcn/components/popover";
import useThingHistory from "@hooks/geo/useThingHistory";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { CircularProgress, IconButton, Tooltip } from "@mui/material";
import { GEO_THING_VAR } from "@utils/featureLabels";
import { BUTTON_ICON_SIZE, isBasicAssetTracking, separateCamelCase } from "@utils/utilities";
import clsx from "clsx";
import { Filter, LocateFixed, RotateCcw } from "lucide-react";
import { ForwardedRef, forwardRef, memo, useEffect, useState } from "react";
import { useInView } from "react-intersection-observer";
import { batch } from "react-redux";
import { useSearchParams } from "react-router-dom";
import Dropdown from "../../../../components/Dropdown";
import {
  setHistoryData,
  setHistoryTripData,
  setMapCurrentPoint
} from "../../../../features/geofenceSlice";
import useGeoThingList from "../../../../hooks/geo/useGeoThingList";
import { useAppDispatch, useAppSelector } from "../../../../store";
import SidebarWrapper from "../Component/SidebarWrapper";
import SkeletonList from "../HomeSection/Components/SkeletonList";
import { EventElement, ParkinElement, TripElement } from "../HomeSection/CustomMapIcon";
import EventFilter from "../HomeSection/DeviceDetialSection/MapSection/EventFilter";
import SelectDataTime from "../HomeSection/SelectDataTime";
import { convertDeviceStatus, convertSecondsToTime, convetUTCToLocal } from "../HomeSection/utils";
import HirstoryReplayTab from "./HirstoryReplayTab";
import { Card } from "@components/ui";

const HistoryListSidebar = () => {
  const { selectedAsset, types } = useAppSelector(({ geofence }) => geofence.historyData);
  const duration = useAppSelector((state) => state.geoDevice.duration);
  const historyTripData = useAppSelector(({ geofence }) => geofence.historyTripData);
  const subFeatureType = useAppSelector((state) => state.user.tenant?.subFeature);
  const [searchThingQuery, setSearchThingQuery] = useState("");

  const debouncedSearchThingQuery = useDebounce(searchThingQuery, 500);

  const HISTORY_FILTERS = isBasicAssetTracking(subFeatureType)
    ? ["event"]
    : ["event", "trip", "stop"];

  const dispatch = useAppDispatch();
  const { data: thingsList, isLoading: thingListLoading } = useGeoThingList({
    search: debouncedSearchThingQuery
  });
  const [searchParams] = useSearchParams();
  const { ref, inView } = useInView();
  const selectedFilters: string[] = searchParams.get("filter")
    ? JSON.parse(searchParams.get("filter") as string)
    : [];

  const {
    data: pathDetails,
    refetch: refetchPath,
    isLoading: pathIsLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useThingHistory({
    thingNames: selectedAsset.map((item) => item),

    filters: selectedFilters,
    duration,
    excluded: isBasicAssetTracking(subFeatureType)
      ? ["trip", "stop"]
      : HISTORY_FILTERS.filter((item) => !types.includes(item)),
    isBasicAssetTracking: isBasicAssetTracking(subFeatureType)
  });

  const submitHandler = (e: any) => {
    e.preventDefault();
    refetchPath();
  };
  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);
  const dataCount = pathDetails?.pages.reduce((acc, page) => acc + page?.history?.length, 0);

  return (
    <SidebarWrapper
      addButton={<SelectDataTime className=" min-w-20" />}
      title="History"
      stickyItem={
        historyTripData?.selectedTrip ? null : (
          <form className="p-2 space-y-2" onSubmit={submitHandler}>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="h-10 border-dashed w-full border-gray-500">
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                </Button>
              </PopoverTrigger>
              <PopoverContent side="right" align="start" className="w-[30rem]  space-y-2">
                <div className="space-y-1">
                  <p className="heading-4 ">{GEO_THING_VAR}</p>
                  <Dropdown
                    onChange={(option) =>
                      dispatch(
                        setHistoryData({
                          selectedAsset: option
                        })
                      )
                    }
                    isSearchable
                    deepSearch={(search) => setSearchThingQuery(search)}
                    isMulti
                    optionsLoading={thingListLoading}
                    className="w-full"
                    value={selectedAsset}
                    options={thingsList?.things.map((item) => item.thingName) || []}
                    placeHolder={`Select ${GEO_THING_VAR} Name `}
                  />
                </div>
                <div className="space-y-1">
                  <p className="heading-4">Filters</p>
                  <Dropdown
                    onChange={(option) => {
                      dispatch(
                        setHistoryData({
                          types: option
                        })
                      );
                    }}
                    className="flex-1"
                    optionItemClassName=" capitalize"
                    placeHolder="Select capabilites"
                    options={HISTORY_FILTERS}
                    isMulti
                    value={types}
                    required
                  />
                </div>
                {types.includes("event") && (
                  <div className="space-y-1">
                    <p className="heading-4">Event Filters</p>
                    <EventFilter
                      type="historySection"
                      filterOptions={["geofenceEnter", "geofenceExit", "overSpeed"]}
                    />
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </form>
        )
      }
    >
      {historyTripData?.selectedTrip ? (
        <HirstoryReplayTab />
      ) : (
        <div className="flex flex-col gap-4 h-full p-2 pt-0">
          {pathIsLoading ? (
            <SkeletonList />
          ) : !dataCount ? (
            <DataNotFound title="History not found in this interval" />
          ) : (
            <div className="space-y-2 h-1 ">
              {pathDetails?.pages.map((page) =>
                page.history?.map((historyItem, i) => {
                  if (page.history.length === i + 1) {
                    return (
                      // eslint-disable-next-line no-use-before-define
                      <MemorizedHistoryListItem
                        ref={ref}
                        path={historyItem}
                        key={`${historyItem.id}-${historyItem.mode}`}
                      />
                    );
                  }
                  return (
                    // eslint-disable-next-line no-use-before-define
                    <MemorizedHistoryListItem
                      ref={ref}
                      path={historyItem}
                      key={`${historyItem.id}-${historyItem.mode}`}
                    />
                  );
                })
              )}
              {isFetchingNextPage && (
                <div className="flex items-center justify-center py-2">
                  <CircularProgress size={16} />
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </SidebarWrapper>
  );
};

const HistoryListItem = ({ path }: { path: HistoryItem }, ref: ForwardedRef<HTMLDivElement>) => {
  const dispatch = useAppDispatch();
  const navigate = useCustomNavigate();
  const isSelected = useAppSelector(
    ({ geofence }) => geofence.mapCurrentPoint.identifier === `${path.mode}-${path.id}`
  );

  const handleHistoryItemClick = (item: HistoryItem, replay?: boolean) => {
    if (item.mode === "trip" && replay) {
      batch(() => {
        dispatch(
          setMapCurrentPoint({
            points: [item.startLat, item.startLong]
          })
        );
        dispatch(
          setHistoryTripData({
            selectedTrip: {
              id: item.id,
              thingName: item.thingName,
              distance: item.distance,
              startTime: item.startTime,
              endTime: item.endTime,
              startAddress: item.startAddress,
              endAddress: item.endAddress,
              duration: item.duration,
              maxSpeed: item.maxSpeed,
              avgSpeed: item.avgSpeed
            }
          })
        );
      });
      return;
    }

    dispatch(
      setMapCurrentPoint({
        points: [
          item.mode === "event" ? item.latitude! : item.mode === "stop" ? item.lat : item.startLat,
          item.mode === "event" ? item.longitude! : item.mode === "stop" ? item.lng : item.startLong
        ],
        identifier: `${item.mode}-${item.id}`,
        path: item.mode === "trip" ? item.path : undefined,
        extraInfo: item
      })
    );
  };

  return (
    <Card
      ref={ref}
      interactive
      variant="second"
      onClick={() => handleHistoryItemClick(path)}
      className={clsx(
        "group min-w-[12.5rem] flex-1 p-1 px-2  relative overflow-hidden !p-3",
        "flex gap-2 items-center  ",
        "hover:!ring-1 hover:ring-brandColor",
        isSelected && "ring-1 ring-brandColor !bg-brandColor/10"
      )}
    >
      <div className=" w-8 ml-1">
        {path.mode === "event" && <EventElement eventType={path.type} />}
        {path.mode === "stop" && ParkinElement}
        {path.mode === "trip" && TripElement}
      </div>
      <div>
        <div className="ml-1 mb-1">
          <button
            onClick={() =>
              navigate(
                `/things/${path.thingName}?tab=${
                  path.mode === "event" ? "Events" : path.mode === "stop" ? "Stops" : "Trips"
                }`
              )
            }
            className="heading-4 font-medium hover:underline max-w-[13rem] truncate"
          >
            {path.thingName}
          </button>
          <p className="content-2 text-muted-foreground">
            ({path.mode === "event" && separateCamelCase(convertDeviceStatus(path.type))}
            {(path.mode === "stop" || path.mode === "trip") && convertSecondsToTime(path.duration)})
          </p>
        </div>
        <p className="content-2 text-muted-foreground ml-1">{convetUTCToLocal(path.timestamp)}</p>
      </div>
      <div className="ml-auto absolute right-1">
        {path.mode === "trip" && (
          <Tooltip title="Replay">
            <IconButton className="!p-2" onClick={() => handleHistoryItemClick(path, true)}>
              <RotateCcw size={BUTTON_ICON_SIZE} />
            </IconButton>
          </Tooltip>
        )}

        {path.mode === "event" && !path.latitude ? (
          <IconButton className="!p-2" disabled>
            <LocateFixed size={BUTTON_ICON_SIZE} />
          </IconButton>
        ) : (
          <Tooltip title="Locate">
            <IconButton className="!p-2" onClick={() => handleHistoryItemClick(path)}>
              <LocateFixed size={BUTTON_ICON_SIZE} />
            </IconButton>
          </Tooltip>
        )}
      </div>
    </Card>
  );
};

const MemorizedHistoryListItem = memo(forwardRef(HistoryListItem), () => true);

export default HistoryListSidebar;
