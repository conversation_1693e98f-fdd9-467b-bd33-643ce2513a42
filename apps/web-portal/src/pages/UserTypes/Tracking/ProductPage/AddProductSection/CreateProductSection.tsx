import { MODE } from "@api/index";
import ActionButton from "@components/ActionButton";
import HeadingIcon from "@components/HeadingIcon";
import NoDataFound from "@components/NoDataFound";
import RadioSelect from "@components/RadioSelect";
import UploadImagePlaceholder from "@components/UploadImagePlaceholder";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useNotificationEscalationGroup from "@hooks/notifications/useNotificationEscalationGroup";
import usePolicyTemplateList from "@hooks/security/usePolicyTemplateList";
import useCaNameList from "@hooks/tenant/useCANameList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import { Step, StepContent, StepLabel, Stepper } from "@mui/material";
import { GEO_PRODUCT_VAR, GEO_THING_VAR } from "@utils/featureLabels";
import { DEFAULT_VALIDATE, LENGTH_VALIDATE, PRODUCT_NAME_VALIDATE } from "@utils/from_schema";
import { BUTTON_ICON_SIZE, checkForHost } from "@utils/utilities";
import clsx from "clsx";
import {
  BookLock,
  ChevronLeft,
  ChevronRight,
  Cpu,
  MessageSquareCode,
  PackagePlus,
  Plus,
  Smartphone,
  X
} from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { createGeoProduct } from "../../../../../api/geoLocation";
import { createSimulatedProduct, uploadProductImage } from "../../../../../api/product";
import Button from "../../../../../components/Button";
import Dropdown from "../../../../../components/Dropdown";
import Input from "../../../../../components/Input";
import Switch from "../../../../../components/Switch";
import useGeoProductList from "../../../../../hooks/geo/useGeoProductList";
import { showErrorToast, showSuccessToast, validateVersion } from "../../../../../utils";
import { PRODUCT_DEFAULT_IMAGE } from "../../../../../utils/deviceMapping";
import { PROTOCOL_VALUES } from "../utils";
import SimulatedStep from "./SimulatedStep";
import { useMutation } from "@tanstack/react-query";
import { queryClient } from "@utils/queryClient";
import { useBeforeUnload } from "@hooks/useBeforeUnload";
import { Card } from "@components/ui";

const IS_SASKEN = checkForHost();

const steps = () => {
  return [
    !IS_SASKEN && `Select ${GEO_PRODUCT_VAR} Connection Type`,
    `${GEO_PRODUCT_VAR} Details`
  ].filter((item) => !!item);
};
const CreateProductSection = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [productType, setProductType] = useState("mqtt");
  const [escalationGroupsSearchQuery, setEscalationGroupsSearchQuery] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [versionError, setVersionError] = useState("");
  const [authenticationType, setAuthenticationType] = useState<{
    key: string;
    value: string;
  }>();
  const [metaDataInputs, setMetaDataInputs] = useState<
    {
      key: string;
      required: boolean;
      id: number;
    }[]
  >([]);
  const [imageFormData, setImageFormData] = useState<FormData>();
  const [imagePreview, setImagePreview] = useState<string>();
  const [loadingBtn, setLoadingBtn] = useState(false);
  const productForm = useForm({
    defaultValues: {
      description: MODE === "development" ? "test autofilled for development" : "",
      monitoringGroups: [],
      dataManagement: ["geo"],
      enrollmentType: "admin",
      provisioningEnabled: false,
      inventoryEnabled: false,
      version: "1.0.0",
      otaType: "none"
    },
    mode: "onBlur"
  });

  const { data: permissions } = useUserGroupPermissions();
  useBeforeUnload();

  const caNames = useCaNameList({});
  // const policyTemplate = useAppSelector((state) => state.product.policyTemplate);
  const navigate = useCustomNavigate();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: policyTemplate, isLoading: isPolicyTemplateLoading } = usePolicyTemplateList({
    search: debouncedSearchQuery
  });

  const { refetch: refetchProducts } = useGeoProductList({ enabled: false });

  const { data: escalationGroups, isLoading: escalationGroupsLoading } =
    useNotificationEscalationGroup({
      searchQuery: escalationGroupsSearchQuery
    });

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const removeMetaInputHandler = (inputId: number) => {
    setMetaDataInputs((prev) => prev.filter((input) => input.id !== inputId));
  };

  const createSimulatorMutation = useMutation({
    mutationFn: createSimulatedProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product-list"] });
      navigate(-1);
      showSuccessToast(`Simulation ${GEO_PRODUCT_VAR} created successfully`);
    },
    onError(error) {
      showErrorToast(error.message);
    }
  });

  const handleDeviceImageUpload = async (e) => {
    const imageFile = e.target.files[0];
    const imageUrl = URL.createObjectURL(imageFile);

    const formData = new FormData();
    formData.append("image", imageFile, imageFile.name);

    setImageFormData(formData);
    setImagePreview(imageUrl);
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setLoadingBtn(true);
    const transformedMetaData = metaDataInputs.map((meta) => ({
      key: meta.key,
      required: meta.required || false
    }));

    let resp;
    let payload;
    if (productType === "simulated") {
      payload = {
        productName: productForm.getValues("productName"),
        thingPrefix: productForm.getValues("thingPrefix"),
        thingCount: Number(productForm.getValues("thingCount")),
        description: productForm.getValues("description"),
        parameters: [],
        messageIntervalSec: Number(productForm.getValues("messageIntervalSec")),
        featureType: "geo",
        defaultTemplateId: productForm.getValues("simulationTemplate")?.name
      };
    } else {
      payload = {
        productType,
        metadata: transformedMetaData,
        productName: productForm.getValues("productName"),
        enrollmentType: productForm.getValues("enrollmentType"),
        monitoringGroups: productForm.getValues("monitoringGroups"),
        inventoryEnabled: productForm.getValues("inventoryEnabled"),
        version: productForm.getValues("version") || "1.0.0",
        description: productForm.getValues("description") || "",
        ...(productType === "gps" && {
          protocol: productForm.getValues("protocol").key,
          dataManagement: ["geo"]
        }),
        ...(productType === "mqtt" && {
          provisioningEnabled: productForm.getValues("provisioningEnabled"),

          otaType: productForm.getValues("otaType"),
          dataManagement: productForm.getValues("dataManagement"),
          authentication: {
            type: authenticationType.value,
            ...(authenticationType.value === "tls" && {
              caName: productForm.getValues("caName")
            })
          },
          policyName: productForm.getValues("authorization").templateName
        })
      };
    }

    if (imageFormData) {
      resp = await uploadProductImage(productForm.getValues("productName"), imageFormData);
    }
    if (resp?.status === "Success" || !imageFormData) {
      if (productType === "simulated") {
        createSimulatorMutation.mutate({
          ...payload,
          imgURL: !imageFormData
            ? "https://platform-product-images.s3.ap-south-1.amazonaws.com/imgFolder/0bc28903-f6af-4d9c-ac2e-f4f4416f5db4.jpg"
            : resp.data.fileUrl
        });
        setLoadingBtn(false);
        return;
      }

      const productResp = await createGeoProduct({
        ...payload,
        imgURL: !imageFormData
          ? "https://platform-product-images.s3.ap-south-1.amazonaws.com/imgFolder/0bc28903-f6af-4d9c-ac2e-f4f4416f5db4.jpg"
          : resp.data.fileUrl
      });
      setLoadingBtn(false);
      if (productResp.status === "Success") {
        showSuccessToast(`${GEO_PRODUCT_VAR} created successfully`);
        refetchProducts();
        navigate("/products");
      } else {
        showErrorToast(productResp.message);
      }
    } else {
      showErrorToast(resp.message);
      setLoadingBtn(false);
    }
  };
  const validateAndSaveVersion = ({ target: { value } }) => {
    productForm.setValue("version", value);

    if (validateVersion(value)) {
      productForm.setValue("version", value);
      setVersionError("");
    } else {
      setVersionError("enter a valid format , eg:1.0.3");
    }
  };

  const stepDetial = (step: number) => {
    switch (step) {
      case IS_SASKEN ? -1 : 0:
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <RadioSelect
              data="MQTT Protocol"
              onClick={() => {
                productForm.reset();
                setProductType("mqtt");
              }}
              description={`Choose this option  to create your IoT ${GEO_PRODUCT_VAR}, where we provide you pre-built firmware`}
              selected={productType === "mqtt"}
            />
            <RadioSelect
              data="GPS Protocol"
              onClick={() => {
                setProductType("gps");
                productForm.reset();
                productForm.setValue("dataManagement", ["geo"]);
              }}
              description={`Choose this option to initiate the creation of your IoT ${GEO_PRODUCT_VAR} with ease.`}
              selected={productType === "gps"}
            />
            <RadioSelect
              data={`Simulated  ${GEO_PRODUCT_VAR}`}
              onClick={() => {
                setProductType("simulated");
                productForm.reset();
              }}
              description={`The Simulated Product type is a virtual option for testing. You can simulate  ${GEO_THING_VAR} behavior and test functionality without needing physical hardware. `}
              selected={productType === "simulated"}
            />
          </div>
        );

      case IS_SASKEN ? 0 : 1:
        return productType === "simulated" ? (
          <SimulatedStep
            productForm={productForm}
            imagePreview={imagePreview}
            handleDeviceImageUpload={handleDeviceImageUpload}
          />
        ) : (
          <section className="grid grid-cols-1 sm:grid-cols-4 gap-4 ">
            <UploadImagePlaceholder
              onUpload={handleDeviceImageUpload}
              className="col-span-1 aspect-auto mb-auto"
              imagePreview={imagePreview || PRODUCT_DEFAULT_IMAGE}
            />

            <div className={clsx("space-y-4 col-span-3")}>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Input
                  required
                  label={`${GEO_PRODUCT_VAR} Name"`}
                  {...productForm.register("productName", PRODUCT_NAME_VALIDATE.schema)}
                  noSpace
                  placeholder={`${GEO_PRODUCT_VAR} Name`}
                  error={!!productForm.formState.errors.productName}
                  helperText={
                    productForm.formState.errors.productName && PRODUCT_NAME_VALIDATE.message
                  }
                />
                <Dropdown
                  {...productForm.register("monitoringGroups")}
                  label="Monitoring Groups "
                  onChange={(option) => {
                    productForm.setValue("monitoringGroups", option);
                    productForm.trigger("monitoringGroups");
                  }}
                  options={escalationGroups?.escalationGroups?.map((item) => item.groupName) || []}
                  optionsLoading={escalationGroupsLoading}
                  isSearchable
                  isMulti
                  value={productForm.getValues("monitoringGroups")}
                  placeHolder="Select Escalations"
                  newOption={{
                    placeHolder: "Create New Escalations...",
                    target: "/notifications/escalations"
                  }}
                  deepSearch={(value) => setEscalationGroupsSearchQuery(value)}
                />
                <Input
                  label="Version"
                  placeholder={`${GEO_PRODUCT_VAR} Version`}
                  error={versionError}
                  helperText={versionError}
                  value={productForm.watch("version")}
                  onChange={validateAndSaveVersion}
                />

                {productType === "gps" ? (
                  <Dropdown
                    {...productForm.register(
                      "protocol",
                      productType === "gps" ? DEFAULT_VALIDATE.schema : null
                    )}
                    onChange={(option) => {
                      productForm.setValue("protocol", option);
                      productForm.trigger("protocol");
                    }}
                    required
                    value={productForm.getValues("protocol")}
                    options={PROTOCOL_VALUES}
                    isSearchable
                    label="Protocols"
                    getOptionLabel="key"
                    placeHolder="Select protocol "
                    error={!!productForm.formState.errors.protocol}
                    helperText={productForm.formState.errors.protocol && DEFAULT_VALIDATE.message}
                  />
                ) : (
                  <Dropdown
                    required
                    {...productForm.register("authorization", DEFAULT_VALIDATE.schema)}
                    onChange={(option) => {
                      productForm.setValue("authorization", option);
                      productForm.trigger("authorization");
                    }}
                    value={productForm.getValues("authorization")}
                    getOptionLabel="templateName"
                    options={policyTemplate?.templates || []}
                    optionsLoading={isPolicyTemplateLoading}
                    label="Policy Template"
                    newOption={{
                      placeHolder: "Create New Policy Template...",
                      target: "/security/template",
                      openCreateModal: true,
                      from: "createProduct"
                    }}
                    deepSearch={(val) => setSearchQuery(val)}
                    isSearchable
                    placeHolder="Select Policy Template"
                    error={!!productForm.formState.errors.authorization}
                    helperText={
                      productForm.formState.errors.authorization && DEFAULT_VALIDATE.message
                    }
                  />
                )}

                {productType === "mqtt" && (
                  <>
                    <Dropdown
                      {...productForm.register("otaType", DEFAULT_VALIDATE.schema)}
                      onChange={(option) => {
                        productForm.setValue("otaType", option);
                        productForm.trigger("otaType");
                      }}
                      value={productForm.getValues("otaType")}
                      options={["http", "mqtt", "none"]}
                      label="OTA Type"
                      required
                      placeHolder="Select OTA Type"
                      error={!!productForm.formState.errors.otaType}
                      helperText={productForm.formState.errors.otaType && DEFAULT_VALIDATE.message}
                    />
                    <Dropdown
                      required
                      {...productForm.register("authentication", DEFAULT_VALIDATE.schema)}
                      onChange={(option) => {
                        setAuthenticationType(option);
                        productForm.setValue("authentication", option);
                        productForm.trigger("authentication");
                        if (option.value !== "tls") {
                          productForm.unregister("caName");
                        }
                      }}
                      value={authenticationType}
                      options={[
                        { key: "Certificate Based", value: "tls" },
                        { key: "Username/Password", value: "basic" }
                      ]}
                      getOptionLabel="key"
                      label="Authentication "
                      placeHolder="Select Authentication Type"
                      error={!!productForm.formState.errors.authentication}
                      helperText={
                        productForm.formState.errors.authentication && DEFAULT_VALIDATE.message
                      }
                    />
                    {authenticationType?.value === "tls" && (
                      <Dropdown
                        {...productForm.register("caName", DEFAULT_VALIDATE.schema)}
                        onChange={(option) => {
                          productForm.setValue("caName", option);
                          productForm.setValue("caName", option);
                          productForm.trigger("caName");
                        }}
                        value={productForm.getValues("caName")}
                        options={caNames.data?.list || []}
                        label="Certificate authority"
                        isSearchable
                        required
                        placeHolder="Select Ca Name"
                        newOption={{
                          placeHolder: "Create Cert Authority...",
                          target: "/security/certificateAuth/addCertAuth"
                        }}
                        error={!!productForm.formState.errors.caName}
                        helperText={productForm.formState.errors.caName && DEFAULT_VALIDATE.message}
                      />
                    )}
                  </>
                )}
              </div>

              <Input
                inputType="textarea"
                label="Description"
                required
                {...productForm.register("description", LENGTH_VALIDATE(10).schema)}
                error={!!productForm.formState.errors.description}
                helperText={productForm.formState.errors.description && LENGTH_VALIDATE(10).message}
              />
              {productType === "mqtt" && (
                <Card
                  variant="second"
                  className="  space-y-2 flex gap-6 items-center justify-between  "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <MessageSquareCode size="1.2rem" />
                    </Card>
                    <div className="space-y-1">
                      <h3 className="font-medium text-base">Enable Shadow</h3>

                      <p className="text-[0.8rem] text-muted-foreground">
                        Unlock seamless connectivity with our IoT {GEO_PRODUCT_VAR}'s advanced topic
                        enablement.
                      </p>
                    </div>
                  </div>
                  <Switch
                    onChange={(e) => {
                      productForm.setValue(
                        "dataManagement",
                        e.target.checked ? ["geo", "shadow"] : ["geo"]
                      );
                    }}
                    defaultChecked={
                      productForm.getValues("dataManagement")?.includes("shadow") || false
                    }
                  />
                </Card>
              )}

              {!IS_SASKEN && (
                <Card
                  variant="second"
                  className="  space-y-2 flex gap-6 items-center justify-between  "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <Smartphone size="1.2rem" />
                    </Card>
                    <div className="space-y-1">
                      <h3 className="font-medium text-base">Enable Mobile App</h3>

                      <p className="text-[0.8rem] text-muted-foreground">
                        Allow users to add {GEO_THING_VAR} from mobile app.
                      </p>
                    </div>
                  </div>
                  <Switch
                    onChange={(e) => {
                      productForm.setValue("enrollmentType", e.target.checked ? "user" : "admin");
                    }}
                    defaultChecked={productForm.getValues("enrollmentType") === "user"}
                  />
                </Card>
              )}

              <Card
                variant="second"
                className="  space-y-2 flex gap-6 items-center justify-between  "
              >
                <div className="flex gap-4 items-center">
                  <Card variant="third">
                    <PackagePlus size="1.2rem" />
                  </Card>
                  <div className="space-y-1">
                    <h3 className="font-medium text-base">Enable Inventory</h3>

                    <p className="text-[0.8rem] text-muted-foreground">
                      Tracks device status in a central system to ensure only registered devices are
                      provisioned.
                    </p>
                  </div>
                </div>
                <Switch
                  onChange={(e) => {
                    productForm.setValue("inventoryEnabled", e.target.checked);
                  }}
                  defaultChecked={productForm.getValues("inventoryEnabled")}
                />
              </Card>

              {productType === "mqtt" && (
                <Card
                  variant="second"
                  className="  space-y-2 flex gap-6 items-center justify-between  "
                >
                  <div className="flex gap-4 items-center">
                    <Card variant="third">
                      <BookLock size="1.2rem" />
                    </Card>
                    <div className="space-y-1">
                      <h3 className="font-medium text-base">Enable Secure Provisioning</h3>

                      <p className="text-[0.8rem] text-muted-foreground">
                        Securely delivers credentials to verified devices during onboarding.
                      </p>
                    </div>
                  </div>
                  <Switch
                    onChange={(e) => {
                      productForm.setValue("provisioningEnabled", e.target.checked);
                    }}
                    defaultChecked={productForm.getValues("provisioningEnabled")}
                  />
                </Card>
              )}
              <hr className="hr !my-8" />

              <h3 className="heading-3">Tags</h3>

              <div className="space-y-4 ml-4">
                {!metaDataInputs.length ? (
                  <NoDataFound
                    className="!mt-0 !text-left !sub-heading-1 "
                    content={`No Tag associated with ${GEO_PRODUCT_VAR}`}
                  />
                ) : (
                  metaDataInputs.map((input, idx) => (
                    <div className="flex gap-4 items-start relative" key={input.id}>
                      <Input
                        {...productForm.register(`meta-key-${idx}`, DEFAULT_VALIDATE.schema)}
                        placeholder="Key"
                        className=" w-full"
                        onChange={(e) => {
                          setMetaDataInputs((prev) =>
                            prev.map((inputField) =>
                              inputField.id === input.id
                                ? { ...inputField, key: e.target.value }
                                : inputField
                            )
                          );
                        }}
                        value={input.key}
                        error={!!productForm.formState.errors[`meta-key-${idx}`]}
                        helperText={
                          productForm.formState.errors[`meta-key-${idx}`] &&
                          DEFAULT_VALIDATE.message
                        }
                      />
                      <div className="flex gap-4 mt-1 items-center">
                        <Switch
                          label="Required"
                          onChange={(e) => {
                            setMetaDataInputs((prev) =>
                              prev.map((inputField) =>
                                inputField.id === input.id
                                  ? {
                                      ...inputField,
                                      required: e.target.checked
                                    }
                                  : inputField
                              )
                            );
                          }}
                          defaultChecked={input.required}
                        />
                        <ActionButton
                          onClick={() => removeMetaInputHandler(input.id)}
                          type="delete"
                        />
                      </div>
                    </div>
                  ))
                )}
                <div className="space-y-2">
                  <Button
                    disabled={metaDataInputs.length >= 5}
                    type="button"
                    startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                    onClick={() =>
                      setMetaDataInputs((prev) => [
                        ...prev,
                        { key: "", required: false, id: Math.random() }
                      ])
                    }
                    small
                  >
                    Add New Tag
                  </Button>
                  <p className=" helper-text font-medium">You can add up to 5 Tag</p>
                </div>
              </div>
            </div>
          </section>
        );

      default:
        return <div />;
    }
  };

  return (
    <div className=" flex-1 p-4 space-y-12 flex flex-col sm:px-40 sm:py-12">
      <div className="between">
        <HeadingIcon Icon={Cpu} title={`Create ${GEO_PRODUCT_VAR}`} />

        <div
          onClick={() => {
            navigate("/products");
          }}
          className="!bg-accent-foreground/10 cursor-pointer p-2 rounded-full"
        >
          <X size={BUTTON_ICON_SIZE} />
        </div>
      </div>
      <Stepper activeStep={activeStep} orientation="vertical">
        {steps().map((step, index) => (
          <Step key={index}>
            <StepLabel>{step}</StepLabel>
            <StepContent>
              <form onSubmit={onSubmit}>
                <div className="py-4">{stepDetial(index)}</div>

                <div className="my-2">
                  <div className="flex gap-2">
                    {!IS_SASKEN && (
                      <Button
                        startIcon={<ChevronLeft size={BUTTON_ICON_SIZE} />}
                        small
                        type="button"
                        disabled={index === 0}
                        onClick={handleBack}
                      >
                        Back
                      </Button>
                    )}
                    {index === steps().length - 1 ? (
                      <Button
                        startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                        small
                        type="submit"
                        noAccess={permissions.geoProduct !== "write"}
                        loading={loadingBtn || createSimulatorMutation.isPending}
                      >
                        Submit
                      </Button>
                    ) : (
                      <Button
                        endIcon={<ChevronRight size={BUTTON_ICON_SIZE} />}
                        small
                        type="button"
                        onClick={handleNext}
                        noAccess={permissions.geoProduct !== "write"}
                      >
                        Next
                      </Button>
                    )}
                  </div>
                </div>
              </form>
            </StepContent>
          </Step>
        ))}
      </Stepper>
    </div>
  );
};

export default CreateProductSection;
