import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ubarContent,
  Menubar<PERSON>tem,
  MenubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { EllipsisVertical, Pencil, Trash } from "lucide-react";

const MoreOption = ({ setDeleteProduct, setEditProduct, productDetails }) => {
  const { data: permissions } = useUserGroupPermissions();
  if (permissions.geoProduct !== "write") return null;
  return (
    <Menubar className="!bg-transparent border-none p-0 ">
      <MenubarMenu>
        <MenubarTrigger className="bg-transparent dark:bg-transparent cursor-pointer">
          <EllipsisVertical size={BUTTON_ICON_SIZE} />
        </MenubarTrigger>
        <MenubarContent className="">
          <MenubarItem
            startIcon={<Pencil size={BUTTON_ICON_SIZE} />}
            onClick={() => {
              setEditProduct();
            }}
          >
            Edit
          </MenubarItem>
          <MenubarItem
            onClick={() => {
              setDeleteProduct();
            }}
            startIcon={<Trash size={BUTTON_ICON_SIZE} />}
          >
            Delete
          </MenubarItem>
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  );
};

export default MoreOption;
