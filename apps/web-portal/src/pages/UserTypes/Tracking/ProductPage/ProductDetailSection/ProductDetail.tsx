import { deleteGeoProduct } from "@api/geoLocation";
import Button from "@components/Button";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DetailsCell2 from "@components/DetailCell2";
import NoDataFound from "@components/NoDataFound";
import ZoomableImage from "@components/ZoomableImage";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useGeoProductList from "@hooks/geo/useGeoProductList";
import useDeleteSimulatorProduct from "@hooks/product/useDeleteSimulatorProduct";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { fetchSimulatorDetails } from "@src/pages/ProductPage/ProductDetailSection/DefaultProductDetails";
import ToggleSimulatorState from "@src/pages/ProductPage/ToggleSimulatorState";
import { useAppSelector } from "@src/store";
import { useMutation, useQuery } from "@tanstack/react-query";
import { GEO_PRODUCT_VAR, GEO_THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE } from "@utils/utilities";
import "leaflet/dist/leaflet.css";
import {
  Calendar,
  Clipboard,
  ClipboardList,
  Clock,
  Cpu,
  Database,
  Hash,
  ListTodo,
  Monitor
} from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { Card } from "@components/ui";
import Label from "../../../../../components/Label";
import useGeoProductDetails from "../../../../../hooks/geo/useGeoProductDetails";
import DeviceSkeleton from "../../HomeSection/Components/DeviceSkeleton";
import "../../HomeSection/leafletStyle.css";
import { convetUTCToLocal } from "../../HomeSection/utils";
import EditProductModal from "../EditProductModal";
import MoreOption from "./MoreOption";
import ProductMap from "./ProductMap";
import TabSection from "./TabSection";

const ProductDetail = () => {
  const [deleteProduct, setDeleteProduct] = useState<{
    productName: string;
    productType: "mqtt" | "gps";
  } | null>(null);
  const [updateProduct, setUpdateProduct] = useState(null);
  const navigate = useCustomNavigate();
  const productName = useParams().productName!;
  const { data: productDetials, isLoading } = useGeoProductDetails({ productName });
  const { refetch: refetchProducts } = useGeoProductList();
  const subFeature = useAppSelector(({ user }) => user.tenant?.subFeature);

  const { mutate, isPending } = useMutation({
    mutationFn: deleteGeoProduct,
    onSuccess: () => {
      showSuccessToast(`${GEO_PRODUCT_VAR} deleted successfully`);
      setDeleteProduct(null);
      refetchProducts();
      navigate("/products");
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });
  const deleteSimulationMutation = useDeleteSimulatorProduct({
    onSuccess: () => {
      navigate("/products");
      setDeleteProduct(null);
      refetchProducts();
    }
  });

  const { data: simulatorDetails, isLoading: simulatorDetailsLoading } = useQuery({
    queryKey: ["simulators", productDetials?.properties?.simulatorName],
    queryFn: () => fetchSimulatorDetails(productDetials?.properties?.simulatorName || ""),
    enabled: !!productDetials?.properties?.simulatorName
  });
  const { data: permissions } = useUserGroupPermissions();

  const handleAddThingClick = () => {
    if (subFeature === "studentSafety") {
      if (productName === "bus") {
        navigate(`/buses-list`);
      } else {
        navigate(`/things/addThing?productName=${productDetials?.productName}`);
      }
    } else {
      navigate(`/things/addThing?productName=${productDetials?.productName}`);
    }
  };

  return isLoading || !productDetials ? (
    <DeviceSkeleton />
  ) : (
    <div className=" flex-1  space-y-4 flex flex-col h-[calc(100vh-6.5rem)] overflow-scroll no-scrollbar">
      <div className="grid gap-4 grid-cols-12 ">
        <Card className=" space-y-6   p-4 col-span-5">
          <div className="between !items-start gap-2">
            <div className="flex gap-4 items-start  ">
              <ZoomableImage>
                <img className="h-16 rounded-md" alt="product" src={productDetials.imgURL} />
              </ZoomableImage>
              <div className="space-y-1">
                <div className="flex items-center gap-4">
                  <h2 className="heading-3-bold">{productName}</h2>
                  <Label className="uppercase" text={productDetials.productType} />
                </div>

                <p className="content-2 text-muted-foreground">{productDetials.description}</p>
              </div>
            </div>
            <div className=" flex gap-3 items-center">
              {!productDetials?.properties?.simulatorName && (
                <Button
                  small
                  color="green"
                  className="w-max"
                  onClick={handleAddThingClick}
                  noAccess={permissions.geoThing !== "write"}
                  startIcon={<Cpu size={BUTTON_ICON_SIZE} />}
                >
                  Add{" "}
                  {subFeature === "studentSafety" && productName === "student"
                    ? "Student"
                    : subFeature === "studentSafety" && productName === "bus"
                      ? "Bus"
                      : GEO_THING_VAR}
                </Button>
              )}

              {productDetials.properties?.simulatorName && !simulatorDetailsLoading && (
                <ToggleSimulatorState id={simulatorDetails.name} status={simulatorDetails.status} />
              )}
              <MoreOption
                setDeleteProduct={() =>
                  setDeleteProduct({
                    productName: productDetials.productName,
                    productType: productDetials.productType
                  })
                }
                setEditProduct={() => setUpdateProduct(productDetials)}
                productDetails={productDetials}
              />
            </div>
          </div>

          <div className=" space-y-4">
            <div className="space-y-2">
              <DetailsCell2
                title="Data Management"
                flexDirectionRow
                titleWidth="!min-w-[10rem]"
                icon={<Database size={DATACELL_ICON_SIZE} />}
                data={
                  !productDetials.dataManagement.length
                    ? "N/A"
                    : productDetials.dataManagement.map((item) => (
                        <Label color="blue" key={item} text={item} lowercase linear />
                      ))
                }
              />
              {productDetials.productType === "mqtt" && (
                <>
                  <DetailsCell2
                    titleWidth="!min-w-[10rem]"
                    title="Policy Template"
                    icon={<Clipboard size={DATACELL_ICON_SIZE} />}
                    data={
                      <span
                        onClick={() =>
                          navigate(
                            `/security/template/${productDetials.authorization["policy-template"]}`
                          )
                        }
                        className="font-semibold text-foreground cursor-pointer hover:underline"
                      >
                        {(productDetials.authorization &&
                          productDetials.authorization["policy-template"]) ||
                          "N/A"}
                      </span>
                    }
                  />
                  <DetailsCell2
                    titleWidth="!min-w-[10rem]"
                    title="Authentication Type"
                    icon={<ListTodo size={DATACELL_ICON_SIZE} />}
                    data={productDetials?.authentication?.type}
                  />
                  {productDetials?.authentication?.type === "tls" && (
                    <DetailsCell2
                      titleWidth="!min-w-[10rem]"
                      title="CA Name"
                      icon={<ClipboardList size={DATACELL_ICON_SIZE} />}
                      data={productDetials?.authentication?.caName}
                    />
                  )}
                </>
              )}
              <DetailsCell2
                flexDirectionRow
                title="Monitoring Groups  "
                icon={<Monitor size={DATACELL_ICON_SIZE} />}
                data={
                  !productDetials?.monitoringGroups?.length
                    ? "N/A"
                    : productDetials.monitoringGroups.map((item) => (
                        <Label key={item} text={item} lowercase />
                      ))
                }
              />
              <DetailsCell2
                titleWidth="!min-w-[10rem]"
                title="Updated At"
                icon={<Calendar size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(productDetials.updatedAt)}
              />
              <DetailsCell2
                title="Created At"
                titleWidth="!min-w-[10rem]"
                icon={<Clock size={DATACELL_ICON_SIZE} />}
                data={convetUTCToLocal(productDetials.createdAt)}
              />
            </div>
            <hr className="hr !my-2" />

            <div className="space-y-2">
              <h3 className="heading-3">Tags</h3>

              <div className="space-y-1 ">
                {!productDetials.metadata || !productDetials.metadata.length ? (
                  <NoDataFound content="No Tag Added" className="!text-center mb-8" />
                ) : (
                  productDetials.metadata.map((meta) => (
                    <DetailsCell2
                      title={meta.key}
                      icon={<Hash size={DATACELL_ICON_SIZE} />}
                      data={meta.required ? "Requried" : "Not Required"}
                    />
                  ))
                )}
              </div>
            </div>
            {/* <div>
                  {thingStatsLoading || !thingStatsData ? (
                    <DeviceStatsSkeleton />
                  ) : (
                    <div className=" grid-cols-2 grid gap-2">
                      <CardCell title="Total Distance" value={thingStatsData.totalDistance} />

                      <CardCell title="Max Speed" value={thingStatsData.maxSpeed} />

                      <CardCell title="Avg Speed" value={thingStatsData.avgSpeed} />
                      <CardCell title="Running Time" value={thingStatsData.runningTime} />
                    </div>
                  )}
                </div> */}
          </div>
        </Card>
        <ProductMap className="card-border col-span-7" />
      </div>
      <TabSection productDetials={productDetials} />
      <ConfirmPrompt
        show={Boolean(deleteProduct)}
        validate
        loading={isPending || deleteSimulationMutation.isPending}
        item={deleteProduct?.productName}
        onCancel={() => {
          setDeleteProduct(null);
        }}
        onConfirm={() =>
          productDetials?.properties?.simulatorName
            ? deleteSimulationMutation.mutate({ simulatorId: deleteProduct?.productName })
            : mutate(deleteProduct)
        }
      />
      {updateProduct && (
        <EditProductModal setShow={setUpdateProduct} productDetails={updateProduct} />
      )}
    </div>
  );
};

export default ProductDetail;
