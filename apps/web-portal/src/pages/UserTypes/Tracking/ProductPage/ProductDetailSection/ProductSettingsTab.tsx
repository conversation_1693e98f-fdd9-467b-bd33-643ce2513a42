import { ProductConfigItem } from "@/index";
import { fetchApi } from "@api/_helpers";
import { MODE, PRODUCT_URL } from "@api/index";
import Button from "@components/Button";
import { Card } from "@components/ui";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import { Checkbox } from "@components/shadcn/components/checkbox";
import { Collapsible, CollapsibleContent } from "@components/shadcn/components/collapasible";
import Switch from "@components/Switch";
import useUserGroupPermissions, { getUserType } from "@hooks/classic/useUserGroupPermissions";
import useProductConfig, { TrackRows } from "@hooks/geo/useProductConfig";
import { IconButton } from "@mui/material";
import { store, useAppSelector } from "@src/store";
import { useMutation } from "@tanstack/react-query";
import { GEO_PRODUCT_VAR, GEO_THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, checkForHost } from "@utils/utilities";
import { ChevronDown, ChevronUp, Settings } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { Tabs, TabsList, TabsTrigger } from "@components/shadcn/components/tabs";

const updateProductConfig = async (settings: {
  minDistanceForTripMeters: number;
  minSpeedForOverSpeedKmph: number;
  minTimeToAddTripOrStopMin: number;
  eventConfig?: Record<string, boolean>;
  productName: string;
  trackTabVisible: boolean;
  trackDistanceFilterThresholdMeters: number;
  trackTableRows: TrackRows[];
  tsTableRows: TrackRows[];
}) => {
  const isGeoProduct = store.getState().user.tenant?.featureType === "geo";
  const response = await fetchApi(
    `/products/config/${settings.productName}?geo=${isGeoProduct}`,
    {
      method: "POST",
      body: {
        minDistanceForTripMeters: Number(settings.minDistanceForTripMeters),
        minSpeedForOverSpeedKmph: Number(settings.minSpeedForOverSpeedKmph),
        minTimeToAddTripOrStopMin: Number(settings.minTimeToAddTripOrStopMin),
        eventConfig: settings.eventConfig,
        trackDistanceFilterThresholdMeters: Number(settings.trackDistanceFilterThresholdMeters),
        trackTableRows: settings.trackTableRows,
        trackTabVisible: settings.trackTabVisible,
        tsTableRows: settings.tsTableRows
      }
    },
    PRODUCT_URL
  );

  const res = await response.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

const ProductSettingsTab = () => {
  const productName = useParams().productName!;
  const featureType = useAppSelector(({ user }) => user.tenant?.featureType);

  const [settings, setSettingsValue] = useState<ProductConfigItem>({
    minDistanceForTripMeters: 0,
    minSpeedForOverSpeedKmph: 0,
    minTimeToAddTripOrStopMin: 0,
    trackDistanceFilterThresholdMeters: 50,
    trackTabVisible: true,
    trackTableRows: [],
    trackTableOptions: [],
    tsTableRows: [],
    tsTableOptions: []
  });
  const { data: permissions } = useUserGroupPermissions();

  const {
    minDistanceForTripMeters,
    minTimeToAddTripOrStopMin,
    minSpeedForOverSpeedKmph,
    trackDistanceFilterThresholdMeters,
    trackTableRows,
    tsTableRows
  } = settings;
  const { data, isLoading } = useProductConfig({ productName });

  useEffect(() => {
    if (data) {
      setSettingsValue(data);
    }
  }, [data]);

  const updateMutation = useMutation({
    mutationFn: updateProductConfig,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product-config", productName] });
      showSuccessToast("Settings updated successfully");
    },
    onError: (err) => {
      showErrorToast(err.message);
    }
  });

  const canSaveSettings =
    (data &&
      minDistanceForTripMeters &&
      minDistanceForTripMeters !== data?.minDistanceForTripMeters) ||
    (minTimeToAddTripOrStopMin && minTimeToAddTripOrStopMin !== data?.minTimeToAddTripOrStopMin) ||
    (minSpeedForOverSpeedKmph && minSpeedForOverSpeedKmph !== data?.minSpeedForOverSpeedKmph) ||
    data?.eventConfig?.overSpeed !== settings?.eventConfig?.overSpeed ||
    data?.trackDistanceFilterThresholdMeters !== settings?.trackDistanceFilterThresholdMeters ||
    JSON.stringify(data?.trackTableRows) !== JSON.stringify(settings?.trackTableRows) ||
    JSON.stringify(data?.tsTableRows) !== JSON.stringify(settings?.tsTableRows) ||
    settings?.trackTabVisible !== data?.trackTabVisible;

  const trackRowMap = useMemo(() => {
    if (!trackTableRows) {
      return {} as Record<string, TrackRows>;
    }
    return trackTableRows.reduce(
      (acc, item) => {
        acc[item.field] = item;
        return acc;
      },
      {} as Record<string, TrackRows>
    );
  }, [trackTableRows]);

  const tsRowMap = useMemo(() => {
    if (!tsTableRows) {
      return {};
    }
    return tsTableRows.reduce(
      (acc, item) => {
        acc[item.field] = item;
        return acc;
      },
      {} as Record<string, TrackRows>
    );
  }, [tsTableRows]);

  const handleOptionChange = (key: string, item: TrackRows, addNew?: boolean) => {
    if (addNew) {
      setSettingsValue((prev) => ({
        ...prev,
        trackTableRows: [...trackTableRows, item]
      }));
    } else {
      setSettingsValue((prev) => ({
        ...prev,
        trackTableRows: trackTableRows?.map((row) => {
          if (row.field === key) {
            return item;
          }
          return row;
        })
      }));
    }
  };

  const toggleAllTrackFields = (items: string[], check: boolean) => {
    setSettingsValue((prev) => ({
      ...prev,
      trackTableRows: items.map((item) => {
        if (trackRowMap[item]) {
          return {
            ...trackRowMap[item],
            visible: check
          };
        } else {
          return {
            displayName: item,
            unit: "",
            field: item,
            visible: check
          };
        }
      })
    }));
  };

  const toggleAllTsFields = (items: string[], check: boolean) => {
    setSettingsValue((prev) => ({
      ...prev,
      tsTableRows: items.map((item) => {
        if (tsRowMap[item]) {
          return {
            ...tsRowMap[item],
            visible: check
          };
        } else {
          return {
            displayName: item,
            unit: "",
            field: item,
            visible: check
          };
        }
      })
    }));
  };

  const handleTsOptionChange = (key: string, item: TrackRows, addNew?: boolean) => {
    if (addNew) {
      setSettingsValue((prev) => ({
        ...prev,
        tsTableRows: [...(prev.tsTableRows || []), item]
      }));
    } else {
      setSettingsValue((prev) => ({
        ...prev,
        tsTableRows: prev.tsTableRows?.map((row) => {
          if (row.field === key) {
            return item;
          }
          return row;
        })
      }));
    }
  };
  return (
    <Card className="space-y-4">
      <div className=" flex items-center justify-between ">
        <HeadingIcon Icon={Settings} title={`${GEO_PRODUCT_VAR} Settings`} />
      </div>
      <div>
        {isLoading ? (
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-gray-300 dark:bg-gray-700 h-10 animate-pulse rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-10 mx-10 animate-pulse rounded" />
            <div className="bg-gray-300 dark:bg-gray-700 h-10 rounded animate-pulse" />
          </div>
        ) : !data ? (
          <DataNotFound title="Settings not found" />
        ) : (
          <div className="space-y-4">
            {featureType === "geo" && (
              <div className="grid grid-cols-3 gap-4 w-full">
                <Card className="space-y-4 p-4">
                  <div className="space-y-1">
                    <h3 className="heading-3">Trip Distance (meter)</h3>
                    <p className="description text-muted-foreground">
                      Minimum distance to define a trip ({GEO_THING_VAR} must move this distance for
                      trip creation).
                    </p>
                  </div>
                  <hr className="hr" />
                  <Input
                    defaultValue={data.minDistanceForTripMeters}
                    placeholder="Min distance for trip (meter)"
                    type="number"
                    onChange={(e) =>
                      setSettingsValue({
                        ...settings,
                        minDistanceForTripMeters: Number(e.target.value)
                      })
                    }
                  />
                </Card>

                <Card className="space-y-4 p-4">
                  <div className="flex items-start relative">
                    <div className="space-y-1">
                      <div className="between items-center">
                        <h3 className="heading-3">Speed Limit (Km/h)</h3>

                        <Switch
                          onChange={(e) =>
                            setSettingsValue({
                              ...settings,
                              eventConfig: { ...data.eventConfig, overSpeed: e.target.checked }
                            })
                          }
                          checked={
                            typeof settings.eventConfig?.overSpeed === "boolean"
                              ? settings.eventConfig?.overSpeed
                              : data.eventConfig?.overSpeed
                          }
                        />
                      </div>
                      <p className="description text-muted-foreground">
                        Minimum speed to trigger an overspeed event (e.g., 40Km/h if speed exceeds
                        60Km/h).
                      </p>
                    </div>
                  </div>
                  <hr className="hr" />

                  <Input
                    defaultValue={data.minSpeedForOverSpeedKmph}
                    placeholder="Speed limit (kmh)"
                    disabled={
                      typeof settings.eventConfig?.overSpeed === "boolean"
                        ? !settings.eventConfig?.overSpeed
                        : !data.eventConfig?.overSpeed
                    }
                    type="number"
                    onChange={(e) =>
                      setSettingsValue({
                        ...settings,
                        minSpeedForOverSpeedKmph: Number(e.target.value)
                      })
                    }
                  />
                </Card>
                <Card className="space-y-4 p-4">
                  <div className="space-y-1">
                    <h3 className="heading-3">Trip Time (minute)</h3>
                    <p className="description text-muted-foreground">
                      Maximums interval to construct a trip. (if your {GEO_THING_VAR} publish data
                      in 5min interval, set this to 5*2 = 10 min)
                    </p>
                  </div>
                  <hr className="hr" />

                  <Input
                    type="number"
                    defaultValue={data.minTimeToAddTripOrStopMin}
                    onChange={(e) =>
                      setSettingsValue({
                        ...settings,
                        minTimeToAddTripOrStopMin: Number(e.target.value)
                      })
                    }
                    placeholder="Min time to save trip (minute)"
                  />
                </Card>

                <Card className="space-y-4 p-4">
                  <div className="space-y-1">
                    <h3 className="heading-3">Track distance filter threshold (meters)</h3>
                    <p className="description text-muted-foreground">
                      Minimum distance threshold for {GEO_THING_VAR} movement to be included in the
                      track.
                    </p>
                  </div>
                  <hr className="hr" />

                  <Input
                    defaultValue={data.trackDistanceFilterThresholdMeters}
                    type="number"
                    onChange={(e) =>
                      setSettingsValue({
                        ...settings,
                        trackDistanceFilterThresholdMeters: Number(e.target.value)
                      })
                    }
                    placeholder="Min distance threshold (meters)"
                  />
                </Card>
              </div>
            )}

            {featureType === "geo" && (
              <TrackRowSelector
                options={data?.trackTableOptions || []}
                handleOptionChange={handleOptionChange}
                trackRowMap={trackRowMap}
                onReOrder={() => {}}
                toggleAllField={toggleAllTrackFields}
                handleVisibleToggle={() => {
                  setSettingsValue((prev) => ({
                    ...prev,
                    trackTabVisible: !prev.trackTabVisible
                  }));
                }}
                visibility={data.trackTabVisible}
                type="track"
              />
            )}

            <TrackRowSelector
              options={data?.tsTableOptions || []}
              handleOptionChange={handleTsOptionChange}
              trackRowMap={tsRowMap}
              onReOrder={() => {}}
              toggleAllField={toggleAllTsFields}
              type="ts"
            />

            <div className=" flex  justify-end ">
              <Button
                disabled={!canSaveSettings}
                className="px-8 mt-4"
                loading={updateMutation.isPending}
                onClick={() => {
                  const visibleCountInTrackTable = settings.trackTableRows.filter(
                    (item) => item.visible
                  ).length;

                  const visibleCountInTsTable =
                    (settings.tsTableRows &&
                      settings.tsTableRows.filter((item) => item.visible).length) ||
                    0;
                  if (
                    visibleCountInTrackTable === 0 &&
                    featureType === "geo" &&
                    settings.trackTabVisible
                  ) {
                    showErrorToast("At least one field should be visible in track table");
                    return;
                  }
                  if (visibleCountInTsTable === 0) {
                    showErrorToast("At least one field should be visible in time series table");
                    return;
                  }

                  updateMutation.mutate({
                    productName,
                    eventConfig: settings.eventConfig,
                    minDistanceForTripMeters:
                      minDistanceForTripMeters || data.minDistanceForTripMeters,
                    minSpeedForOverSpeedKmph:
                      minSpeedForOverSpeedKmph || data.minSpeedForOverSpeedKmph,
                    minTimeToAddTripOrStopMin:
                      minTimeToAddTripOrStopMin || data.minTimeToAddTripOrStopMin,
                    trackDistanceFilterThresholdMeters:
                      trackDistanceFilterThresholdMeters || data.trackDistanceFilterThresholdMeters,
                    trackTableRows: settings.trackTableRows,
                    trackTabVisible: settings.trackTabVisible,
                    tsTableRows: settings.tsTableRows
                  });
                }}
                startIcon={<Settings size={BUTTON_ICON_SIZE} />}
                noAccess={permissions.geoProduct !== "write" && permissions.product !== "write"}
              >
                Update Settings
              </Button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

const isSasken = checkForHost("sasken");

const TrackRowSelector = ({
  options,
  trackRowMap,
  handleOptionChange,
  type = "track",
  handleVisibleToggle,
  visibility,
  toggleAllField
  // onReOrder
}: {
  options: string[];
  trackRowMap: Record<string, TrackRows>;
  handleOptionChange: (key: string, item: TrackRows, addNew?: boolean) => void;
  onReOrder: (newItems: TrackRows[]) => void;
  type?: "track" | "ts";
  visibility?: boolean;
  handleVisibleToggle?: () => void;
  toggleAllField: (items: string[], check: boolean) => void;
}) => {
  const [isOpen, setIsOpen] = useState(true);

  const userType = useAppSelector((state) => getUserType(state.user.user?.role as string));

  const isAllSelected = useMemo(() => {
    if (!trackRowMap) {
      return false;
    }
    return options.every((item) => trackRowMap[item]?.visible);
  }, [options, trackRowMap]);

  if (userType !== "operator" && userType !== "msp" && MODE !== "development") {
    if (isSasken) {
      return null;
    }
  }

  const handleFieldChange = (key: string, item: Partial<TrackRows>) => {
    if (!trackRowMap[key]) {
      handleOptionChange(
        key,
        {
          displayName: key,
          unit: "",
          field: key,
          visible: false,
          ...item
        },
        true
      );
    } else {
      handleOptionChange(key, {
        ...trackRowMap[key],
        ...item
      });
    }
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <Card className=" col-span-2 space-y-4 ">
        <div className="flex items-center justify-between relative">
          <div className="flex items-start gap-2">
            <div>
              <h3 className="heading-3">
                {type === "track" ? "Track table" : "Time series"} visible fields
              </h3>
              <p className=" text-muted-foreground text-sm">
                Only checked fields will be visible in{" "}
                {type === "track" ? "track tab" : "time series"}
              </p>
            </div>
          </div>

          {handleVisibleToggle && (
            <Tabs className="ml-auto mr-3" defaultValue={visibility ? "Visible" : "Hidden"}>
              <TabsList
                className="grid  grid-cols-2"
                defaultValue={visibility ? "Visible" : "Hidden"}
              >
                <TabsTrigger value="Visible" onClick={handleVisibleToggle}>
                  Visible
                </TabsTrigger>
                <TabsTrigger value="Hidden" onClick={handleVisibleToggle}>
                  Hidden
                </TabsTrigger>
              </TabsList>
            </Tabs>
          )}

          <div className="flex gap-2 items-center">
            <div className="flex gap-2 items-center mr-2">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={() => {
                  toggleAllField(options, !isAllSelected);
                }}
              />
              <p className="  text-muted-foreground text-sm">Toggle All Fields</p>
            </div>
            <IconButton onClick={() => setIsOpen((prev) => !prev)} className=" !p-1 ">
              {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </IconButton>
          </div>
        </div>
        <CollapsibleContent>
          {!options.length ? (
            <DataNotFound title="No Field available" />
          ) : (
            <div className=" grid grid-cols-2 gap-3">
              {options.map((option) => {
                const showUnit = ![
                  "No.",
                  "deviceTime",
                  "latitude",
                  "longitude",
                  "accuracy",
                  "address",
                  "locate",
                  "motion",
                  "distance"
                ].includes(option);

                const checked = trackRowMap[option]?.visible;

                return (
                  <Card className="flex items-center gap-3 p-4">
                    {/* <div className="cursor-move">
                    <GripVertical className="h-5 w-5 text-[#4B5563] flex-shrink-0" />
                  </div> */}

                    <Checkbox
                      id={`checkbox-${option}`}
                      checked={checked}
                      onCheckedChange={(checked) => {
                        if (checked === "indeterminate") {
                          return;
                        }
                        handleFieldChange(option, { visible: checked });
                      }}
                      className="flex-shrink-0"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <p className="text-base  flex-1 capitalize font-medium truncate">{option}</p>
                    <div className="flex-1 flex gap-2">
                      <Input
                        placeholder="Row name"
                        className="flex-1"
                        defaultValue={trackRowMap[option]?.displayName || option}
                        onChange={(e) => {
                          handleFieldChange(option, {
                            displayName: e.target.value
                          });
                        }}
                      />
                      {showUnit && (
                        <Input
                          placeholder="Unit"
                          defaultValue={trackRowMap[option]?.unit}
                          className="max-w-[5rem]"
                          onChange={(e) => {
                            handleFieldChange(option, {
                              unit: e.target.value
                            });
                          }}
                        />
                      )}
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
};

export default ProductSettingsTab;
