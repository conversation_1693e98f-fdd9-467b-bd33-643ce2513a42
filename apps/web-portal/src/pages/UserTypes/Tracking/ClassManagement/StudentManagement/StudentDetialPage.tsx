import ActionButton from "@components/ActionButton";
import { Card } from "@components/ui";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DetailsCell2 from "@components/DetailCell2";
import Label from "@components/Label";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger
} from "@components/shadcn/components/menubar";
import ZoomableImage from "@components/ZoomableImage";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE } from "@frontend/shared/config/defaults";
import useRemoveStudentFromClass from "@hooks/geo/student-safety/students/useRemoveStudentFromClass";
import useGeoThingDetails from "@hooks/geo/useGeoThingDetails";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import clsx from "clsx";
import { LatLngExpression, latLngBounds } from "leaflet";
import {
  AtSign,
  Calendar,
  Clock,
  Edit,
  GraduationCap,
  Map,
  MoreVertical,
  Phone,
  Trash2
} from "lucide-react";
import React, { ElementRef, useCallback, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import DeviceSkeleton from "../../HomeSection/Components/DeviceSkeleton";
import DeviceMapLeaf from "../../HomeSection/DeviceDetialSection/MapSection/DeviceMapLeaf";
import {
  convetUTCToLocal,
  getColorBasedOnBattery,
  getIconBasedOnBattery
} from "../../HomeSection/utils";
import StudentsTabSection from "./StudentsTabSection";

const StudentDetialPage = () => {
  const thingName = useParams().thingName!;
  const nameOfClass = useParams().nameOfClass!;

  const [showDeleteStudent, setShowDeleteStudent] = useState<{ id: string; name: string }>();
  const deviceMapLeafRef = useRef<ElementRef<typeof DeviceMapLeaf>>(null);

  const { data: studentDetails, isLoading: isStudentLoading } = useGeoThingDetails({
    thingName
  });
  const navigate = useCustomNavigate();

  const removeStudentMutation = useRemoveStudentFromClass({
    onSuccess: () => {
      showSuccessToast("Student removed successfully");
      queryClient.invalidateQueries({ queryKey: ["student-list-in-class", nameOfClass] });
      setShowDeleteStudent(undefined);
      navigate(-1);
    }
  });

  const fitBounds = useCallback(
    (bounds: LatLngExpression[]) => {
      if (bounds && latLngBounds(bounds).isValid() && deviceMapLeafRef.current) {
        deviceMapLeafRef.current.fitBounds(bounds);
      }
    },
    [deviceMapLeafRef]
  );

  console.log(studentDetails);

  return isStudentLoading || !studentDetails ? (
    <DeviceSkeleton />
  ) : (
    <div className=" flex-1  space-y-4 flex flex-col h-[calc(100vh-7.5rem)] overflow-scroll no-scrollbar">
      <div className="grid gap-4 grid-cols-12">
        <Card className="flex gap-8   p-4 col-span-5">
          {isStudentLoading || !studentDetails ? (
            <CardLoadingSkeleton col={3} />
          ) : (
            <div className="w-full">
              <div className="flex items-center justify-between">
                <div className="flex justify-between flex-1">
                  <div className="flex items-center space-x-4">
                    <ZoomableImage>
                      <img
                        className="object-cover size-16 rounded-full "
                        src={studentDetails.imgURL}
                        alt="student-img"
                      />
                    </ZoomableImage>
                    <div>
                      <div className="flex items-center gap-2">
                        <h2 className="heading-2 capitalize">{studentDetails.displayName} </h2>
                        <Label
                          text={studentDetails.meta?.gender}
                          className="uppercase"
                          color="blue"
                        />
                        <Label
                          text={`Blood Group  : ${studentDetails.meta?.bloodGroup}`}
                          color="red"
                        />
                      </div>
                      <p className="text-muted-foreground">Roll No : {studentDetails.thingName}</p>
                    </div>
                  </div>
                  <div className="flex gap-4 items-center">
                    <div
                      className={clsx(
                        studentDetails.status === "connected"
                          ? "dot-pulse-success"
                          : "dot-pulse-danger",
                        "!w-2.5 !h-2.5 ml-2 "
                      )}
                    />
                    <div className="flex items-center gap-1 ">
                      {React.createElement(
                        getIconBasedOnBattery(
                          studentDetails?.attributes?.batteryLevel ||
                            studentDetails?.attributes?.battery
                        ),
                        {
                          className: clsx(
                            "!h-8",
                            getColorBasedOnBattery(
                              studentDetails?.attributes?.batteryLevel ||
                                studentDetails?.attributes?.battery
                            )
                          )
                        }
                      )}

                      <p className="heading-4 min-w-[24px]">
                        {studentDetails?.attributes?.batteryLevel?.toFixed(0) ||
                          studentDetails?.attributes?.battery?.toFixed(0) ||
                          0}
                        %
                      </p>
                    </div>
                  </div>
                </div>
                <Menubar className="!bg-transparent border-none p-0 ">
                  <MenubarMenu>
                    <MenubarTrigger>
                      <ActionButton Icon={MoreVertical} />
                    </MenubarTrigger>
                    <MenubarContent>
                      <MenubarItem
                        startIcon={<Edit size={BUTTON_ICON_SIZE} />}
                        onClick={() => navigate("editStudent")}
                      >
                        Edit
                      </MenubarItem>
                      <MenubarItem
                        startIcon={<Trash2 size={BUTTON_ICON_SIZE} />}
                        onClick={() =>
                          setShowDeleteStudent({
                            id: studentDetails.thingName,
                            name: studentDetails.displayName
                          })
                        }
                        className="text-red-600"
                      >
                        Delete
                      </MenubarItem>
                    </MenubarContent>
                  </MenubarMenu>
                </Menubar>
              </div>
              <hr className="hr !my-4" />

              <div className="space-y-4 w-full">
                <div className="grid grid-cols-2 gap-4 ">
                  <Card variant="second" className=" ">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Calendar className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="">
                        <p className="text-sm font-medium text-muted-foreground ">Date of Birth</p>
                        <p className="text-base font-semibold ">
                          {convetUTCToLocal(studentDetails.meta?.dob, "DD MMM YYYY")}
                        </p>
                      </div>
                    </div>
                  </Card>

                  <Card variant="second" className=" ">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <GraduationCap className="h-5 w-5 text-purple-600" />
                      </div>
                      <div className="">
                        <p className="text-sm font-medium text-muted-foreground ">Class</p>
                        <p className="text-base font-semibold ">
                          {studentDetails.meta?.studentClass}
                        </p>
                      </div>
                    </div>
                  </Card>
                </div>
                <Card variant="second" className="space-y-4 ">
                  <h3 className="heading-3 ">Parents Details</h3>
                  <div className="space-y-2 ">
                    <DetailsCell2
                      icon={<AtSign size={DATACELL_ICON_SIZE} />}
                      title="Email"
                      data={studentDetails.meta?.parentEmail}
                    />
                    <DetailsCell2
                      icon={<Phone size={DATACELL_ICON_SIZE} />}
                      title="Contact"
                      data={studentDetails.meta?.parentContact}
                    />
                    <DetailsCell2
                      icon={<Map size={DATACELL_ICON_SIZE} />}
                      title="Address"
                      className="!items-start"
                      data={studentDetails.meta?.address}
                    />
                  </div>
                </Card>
                <div className=" flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>Created: {convetUTCToLocal(studentDetails.createdAt)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>Updated: {convetUTCToLocal(studentDetails.latestUpdate)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>
        <DeviceMapLeaf
          className="rounded-md col-span-7 card-border"
          deviceLat={studentDetails.lat}
          deviceLong={studentDetails.long}
          lastUpDate={studentDetails.latestUpdate}
          productName={studentDetails.productName}
          ref={deviceMapLeafRef}
        />
      </div>
      <StudentsTabSection fitBounds={fitBounds} />
      <ConfirmPrompt
        show={Boolean(showDeleteStudent)}
        onCancel={() => setShowDeleteStudent(undefined)}
        title="Delete Student"
        validate
        item={showDeleteStudent?.name || ""}
        loading={removeStudentMutation.isPending}
        onConfirm={() =>
          removeStudentMutation.mutate({
            studentId: showDeleteStudent?.id as string,
            studentClass: nameOfClass
          })
        }
      />
    </div>
  );
};

export default StudentDetialPage;
