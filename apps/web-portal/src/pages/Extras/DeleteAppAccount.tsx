import React, { useState } from "react";
import Input from "../../components/Input";
import Button from "../../components/Button";
import axios from "axios";
import { PROCESSING_URL } from "../../api";
import { showErrorToast, showSuccessToast } from "../../utils";
import <PERSON><PERSON>ogo from "@components/BrandLogo";

const INITIAL_STATE = { email: "", password: "" };
const DeleteAppAccount = () => {
  const [formData, setFormData] = useState(INITIAL_STATE);
  const [loading, setLoading] = useState(false);

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await axios.delete(`${PROCESSING_URL}/delete-user-account/app`);
      showSuccessToast("Account deleted successfully");
    } catch (error) {
      showErrorToast("Something went wrong while account delete!");
    } finally {
      setLoading(false);
      setFormData(INITIAL_STATE);
    }
  };

  return (
    <main className="bg-background flex items-center justify-center h-[100vh]">
      <div className="card p-6">
        <form className="flex flex-col gap-6" onSubmit={handleFormSubmit}>
          <BrandLogo className="w-48 self-center mb-4 mt-4" />
          <div>
            <p className=" text-center text-xl">Delete User Account</p>
            <p className="text-center text-gray-500">**this is action is irreversible</p>
          </div>

          <Input
            label="Email"
            className="w-[280px]"
            type="email"
            onChange={({ target: { value } }) => {
              setFormData({ ...formData, email: value });
            }}
            placeholder="eg: <EMAIL>"
            required
          />
          <Input
            className="w-[280px]"
            placeholder="eg: password"
            label="Password"
            type="password"
            onChange={({ target: { value } }) => {
              setFormData({ ...formData, password: value });
            }}
            required
          />
          <Button loading={loading} type="submit" color="red" className="mt-3">
            Delete
          </Button>
        </form>
      </div>
    </main>
  );
};

export default DeleteAppAccount;
