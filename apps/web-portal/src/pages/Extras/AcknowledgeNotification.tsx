import { useState } from "react";
import Button from "../../components/Button";
import { Check } from "lucide-react";
import axios from "axios";
import { PROCESSING_URL } from "../../api";
import { showErrorToast, showSuccessToast } from "../../utils";
import { useParams } from "react-router-dom";
import { BUTTON_ICON_SIZE } from "@utils/utilities";

const AcknowledgeNotification = () => {
  const [currentStep, setCurrentStep] = useState("PENDING");
  const { ackId } = useParams();

  const onAcknowledge = async () => {
    setCurrentStep("LOADING");
    try {
      const res = await axios.get(`${PROCESSING_URL}/in/ack/${ackId}`);
      const { message } = res.data;

      showSuccessToast(message);
      setCurrentStep("ACKNOWLEDGED");
    } catch (error) {
      showErrorToast(error.response?.data?.message || error.message);
      setCurrentStep("PENDING");
    }
  };
  const onCancel = () => {
    setCurrentStep("IGNORED");
  };

  return (
    <div className=" center flex-col gap-4 items-center py-[4vh]  h-[100vh] bg-background">
      <p className=" text-lg font-medium">Acknowledge this Notification!</p>
      {currentStep === "ACKNOWLEDGED" ? (
        <Button startIcon={<Check size={BUTTON_ICON_SIZE} />} color="green">
          Acknowledged Successfully
        </Button>
      ) : currentStep === "IGNORED" ? (
        <Button startIcon={<Check size={BUTTON_ICON_SIZE} />}>Ignored</Button>
      ) : currentStep === "COMPLETED" ? (
        <Button startIcon={<Check size={BUTTON_ICON_SIZE} />}>Already Acknowledged</Button>
      ) : (
        <div className="flex gap-4">
          <Button onClick={onAcknowledge} loading={currentStep === "LOADING"}>
            Yes! Acknowledge
          </Button>
          <Button color="red" onClick={onCancel}>
            Ignore
          </Button>
        </div>
      )}
    </div>
  );
};

export default AcknowledgeNotification;
