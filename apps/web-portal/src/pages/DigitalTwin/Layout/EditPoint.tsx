import clsx from "clsx";
import useNewPointCoordinates from "../hooks/store/useNewPointCoordinates";
import Button from "@components/Button";
import Input from "@components/Input";
import useUpdateDigitalTwinPoint from "../hooks/useUpdateDigitalTwinPoint";
import Dropdown from "@components/Dropdown";
import useTimeSeriesOptions from "@hooks/timeseries/useTimeseriesOptions";
import React, { ElementRef, FormEventHandler, useMemo, useRef, useState } from "react";
import MultiImageUpload from "./MultiImageUpload";
import useGetDTThingProduct from "../hooks/useGetDTThingProduct";
import useDigitalTwinDetails from "../hooks/useDigitalTwinDetails";
import { AddRangeComponent } from "@src/pages/site/layouts/AddAreaDrawer";
import ActionButton from "@components/ActionButton";
import FormulaBuilderModal from "@src/pages/site/layouts/FormulaBuilderModal";
import { SiteMetrics } from "@/index";
import { Card } from "@components/ui";

const EditPoint = () => {
  const { editing, setEditing } = useNewPointCoordinates();
  const isEditing = Boolean(editing);

  const [assetUplading, setAssetUplading] = useState(false);

  const multiAssetUploadRef = useRef<ElementRef<typeof MultiImageUpload>>(null);
  const { thingName, digitalTwinName } = useGetDTThingProduct();
  const { data: options, isLoading: optionsLoading } = useTimeSeriesOptions({ thingName });

  const { data: digitalTwinDetails, isLoading: digitalTwinDetailsLoading } = useDigitalTwinDetails({
    thingName,
    enabled: Boolean(thingName || digitalTwinName),
    name: digitalTwinName
  });

  const updateMutation = useUpdateDigitalTwinPoint(digitalTwinDetails?.id);

  const assetFields = useMemo(() => {
    if (!digitalTwinDetails?.assets) {
      return null;
    }
    return digitalTwinDetails.assets.map((item) => item.fields).flat();
  }, [digitalTwinDetails]);

  const handleSubmit: FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    if (!editing) return;

    setAssetUplading(true);
    const images = await multiAssetUploadRef.current?.uploadImage();
    setAssetUplading(false);

    updateMutation.mutate({
      body: {
        partName: editing?.partName,
        partDescription: editing?.partDescription,
        datapoints: editing?.datapoints?.map((item) => {
          if (typeof item === "object" && "id" in item) {
            // @ts-expect-error - this is a special case for digital twin
            return item.id;
          }
          return item;
        }),
        videoIn: editing?.videoIn || undefined,
        videoOut: editing?.videoOut || undefined,
        videoLoop: editing?.videoLoop || undefined,
        images,
        metrics: editing?.metrics
      },
      id: editing?.id
    });
  };

  const onUpdateMetric = (metric: Partial<SiteMetrics> & { id: number }) => {
    setEditing({
      ...editing,
      metrics: editing?.metrics.map((item) =>
        item.id === metric.id ? { ...item, ...metric } : item
      )
    });
  };

  return (
    <form
      className={clsx(
        "flex flex-col card h-full transition-all gap-4 duration-500 px-3 max-h-[100vh] overflow-y-auto fixed right-0 top-0 z-[102]",
        isEditing ? "w-[30rem]" : "w-0 !p-0"
      )}
      onSubmit={handleSubmit}
    >
      {isEditing && (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className=" heading-2">Edit Part Details</h1>
            </div>
          </div>

          <Input
            value={editing?.partName}
            placeholder="Enter part name"
            onChange={(e) => setEditing({ ...editing, partName: e.target.value })}
            label="Part Name"
            required
          />
          <Input
            value={editing?.partDescription}
            placeholder="Enter part description"
            onChange={(e) => setEditing({ ...editing, partDescription: e.target.value })}
            label="Part Description"
            required
          />

          {assetFields ? (
            <Dropdown
              options={assetFields}
              optionsLoading={digitalTwinDetailsLoading}
              label="Data points"
              placeHolder="Select data points"
              getOptionLabel="displayName"
              value={
                editing?.datapoints?.map((pointId) => {
                  const itemInAsset = assetFields.find((item) => item.id === Number(pointId));
                  return itemInAsset;
                }) || []
              }
              isMulti
              onChange={(option) => setEditing({ datapoints: option })}
            />
          ) : (
            <Dropdown
              options={options || []}
              optionsLoading={optionsLoading}
              label="Data points"
              placeHolder="Select data points"
              value={editing?.datapoints || []}
              isMulti
              onChange={(option) => setEditing({ datapoints: option })}
            />
          )}
          {digitalTwinDetails?.type !== "3D" && (
            <MultiImageUpload ref={multiAssetUploadRef} images={editing?.images || []} />
          )}

          <div className="flex flex-col gap-3">
            {editing?.metrics?.map((item) => {
              return (
                <div key={item.id} className="flex flex-col gap-3 card p-2">
                  <AddRangeComponent
                    onAddRange={(range) =>
                      onUpdateMetric({ ...item, metrics: [...item.metrics, range] })
                    }
                  />

                  <p className="content-2">Defined Ranges</p>
                  <div>
                    {item.metrics.map((range) => (
                      <Card
                        variant="second"
                        className="flex  items-center justify-between  !p-2 rounded mb-2"
                        key={range.id}
                      >
                        <span>
                          {range.min === -Infinity ? "-∞" : range.min} to{" "}
                          {range.max === Infinity ? "∞" : range.max}
                        </span>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-6 h-6 rounded"
                            style={{ backgroundColor: range.color }}
                          ></div>
                          <ActionButton
                            type="delete"
                            onClick={() => {
                              onUpdateMetric({
                                ...item,
                                metrics: item.metrics.filter((item) => item.id !== range.id)
                              });
                            }}
                          />
                        </div>
                      </Card>
                    ))}
                  </div>

                  <p className="content-2">Compare with</p>
                  <Card
                    variant="second"
                    className="flex  items-center justify-between  !p-2 rounded mb-2"
                  >
                    <code className="bg-secondary p-2 rounded block">
                      {item.formula || "No formula added"}
                    </code>

                    <FormulaBuilderModal
                      options={assetFields?.map((item) => item.displayName).flat() || []}
                      onFormulaChange={(formula) => console.log(formula)}
                      initialFormula={item.formula || ""}
                      onSave={(formula) => {
                        onUpdateMetric({ id: item.id, formula });
                      }}
                    />
                  </Card>
                </div>
              );
            })}
          </div>

          <div className="flex items-center justify-end gap-4">
            <Button
              type="submit"
              small
              color="green"
              className="px-8"
              loading={updateMutation.isPending}
            >
              {assetUplading ? "Uploading Assets..." : "Update"}
            </Button>
            <Button small onClick={() => setEditing(false)} className="px-6" color="red">
              Discard
            </Button>
          </div>
        </>
      )}
    </form>
  );
};

export default EditPoint;
