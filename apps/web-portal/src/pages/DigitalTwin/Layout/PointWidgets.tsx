import { ThingWidget } from "@/index";
import { fetchThingWidgets, updateThingWidgets } from "@api/thingWidgets";
import Button from "@components/Button";
import EditWidgetModal from "@src/pages/DevicesPage/layout/EditWidgetModal";
import EditWidgetOptions from "@src/pages/DevicesPage/layout/EditWidgetsOptions";
import ThingMonitorWidget from "@src/pages/DevicesPage/layout/ThingMonitorWidget";
import {
  colorAtI,
  DIGITAL_TWIN_PRODUCT,
  digitalTwinProductThing,
  generateRandomString,
  isProductLevelDigitalTwin,
  SENSOR_KEY
} from "@src/pages/MonitorPage/utils";
import SkeletonList from "@src/pages/UserTypes/Tracking/HomeSection/Components/SkeletonList";
import { useMutation, useQuery } from "@tanstack/react-query";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { CheckI<PERSON>, Pencil, Plus } from "lucide-react";
import { ElementRef, useCallback, useMemo, useRef, useState } from "react";
import useNewPointCoordinates from "../hooks/store/useNewPointCoordinates";
import useGetDTThingProduct from "../hooks/useGetDTThingProduct";
import { showErrorToast } from "@utils/index";
import useDigitalTwinDetails from "../hooks/useDigitalTwinDetails";
import { buildWidgetFieldList } from "@src/pages/DevicesPage/layout/WidgetFieldListItem";

const PointWidgets = () => {
  const { thingName, productThing, productName, digitalTwinName } = useGetDTThingProduct();
  const { selectedPointMap } = useNewPointCoordinates();

  const editModalRef = useRef<ElementRef<typeof EditWidgetModal>>(null);
  const [editing, setEditing] = useState(false);

  const { data: digitalTwinDetails } = useDigitalTwinDetails({
    thingName,
    productName,
    name: digitalTwinName
  });

  const availableAsset = useMemo(() => {
    if (!digitalTwinDetails) return [];
    return digitalTwinDetails.assets;
  }, [digitalTwinDetails]);

  const selectedPoint = useMemo(() => {
    if (!digitalTwinDetails) return undefined;
    return selectedPointMap[digitalTwinDetails.id];
  }, [digitalTwinDetails, selectedPointMap]);

  const widgetThing = isProductLevelDigitalTwin(digitalTwinDetails?.thingName || "")
    ? digitalTwinProductThing(digitalTwinDetails?.productName || "")
    : digitalTwinDetails?.thingName || "";

  const { data: widgetList, isLoading: widgetListLoading } = useQuery({
    queryKey: ["thing-widgets", widgetThing, DIGITAL_TWIN_PRODUCT],
    queryFn: () => fetchThingWidgets(widgetThing, DIGITAL_TWIN_PRODUCT),
    enabled: Boolean(thingName || digitalTwinName)
  });

  const duration = "last_24_hours";

  const partWidgets = useMemo(() => {
    // @ts-expect-error dg part is widget specific
    return widgetList?.filter((item) => item.dgPart === selectedPoint?.id) || [];
  }, [selectedPoint?.id, widgetList]);

  const mutation = useMutation({
    mutationFn: updateThingWidgets,
    onMutate: async (newWidgets) => {
      const prevWidgets = queryClient.getQueryData([
        "thing-widgets",
        widgetThing,
        DIGITAL_TWIN_PRODUCT
      ]);
      queryClient.setQueryData(
        ["thing-widgets", widgetThing, DIGITAL_TWIN_PRODUCT],
        newWidgets.widgets
      );
      return { prevWidgets, newWidgets };
    }
  });

  const addNewWidget = () => {
    if (productName && !productThing) {
      showErrorToast("Please select a thing to add widget");
      return;
    }
    editModalRef.current?.showModal(
      {
        color: colorAtI(widgetList?.length || 0),
        title: "Chart",
        operation: "latest",
        range: [
          {
            id: generateRandomString(10),
            color: colorAtI(widgetList?.length || 0),
            start: 0,
            compareTo: "0",
            conditionOperator: "==",
            conditionKey: "",
            conditionValue: ""
          }
        ],
        decimal: 2,
        unit: "",
        // @ts-expect-error dg id is check for part id
        dgPart: selectedPoint?.id,
        // eslint-disable-next-line no-unneeded-ternary
        source: SENSOR_KEY,
        id: generateRandomString(10)
      },
      {
        thingName: productName ? productThing : thingName,
        productName: DIGITAL_TWIN_PRODUCT,
        mutationType: "Add"
      }
    );
  };

  const onUpdate = useCallback(
    (updatedWidget: Partial<ThingWidget>) => {
      const newWidgets = widgetList?.map((widget) => {
        if (widget.id === updatedWidget.id) {
          return {
            ...widget,
            ...updatedWidget
          };
        }
        return widget;
      });
      mutation.mutate({
        thingName: widgetThing,
        widgets: newWidgets || [],
        productName: DIGITAL_TWIN_PRODUCT
      });
    },
    [widgetList]
  );

  const handleAddThingWidget = useCallback(
    (newWidget: ThingWidget) => {
      mutation.mutate({
        thingName: widgetThing,
        widgets: widgetList ? [...widgetList, newWidget] : [newWidget],
        productName: DIGITAL_TWIN_PRODUCT
      });
    },
    [widgetList]
  );

  const onClone = (cloneId: string) => {
    const newWidget = widgetList?.find((widget) => widget.id === cloneId);
    if (!newWidget) {
      return;
    }
    const widget = {
      ...newWidget,
      id: generateRandomString(),
      product: false
    };
    mutation.mutate({
      thingName: widgetThing,
      productName: DIGITAL_TWIN_PRODUCT,
      widgets: !widgetList ? [widget] : [widget, ...widgetList]
    });
  };

  const onRemove = (removeId: string) => {
    const newWidget = widgetList?.filter((widget) => widget.id !== removeId);
    mutation.mutate({
      thingName: widgetThing,
      widgets: newWidget || [],
      productName: DIGITAL_TWIN_PRODUCT
    });
  };

  const onEdit = (item: Partial<ThingWidget>) => {
    editModalRef.current?.showModal(item, {
      thingName,
      productName: DIGITAL_TWIN_PRODUCT,
      mutationType: "Edit"
    });
  };

  if (!selectedPoint) return null;

  return (
    <div className="flex flex-col gap-4 px-4">
      {widgetListLoading && <SkeletonList childrenClassName=" !h-[14rem]" count={1} />}
      <div className="flex items-center justify-between">
        <p className=" heading-3">Widgets</p>
        <div className="flex items-center gap-2">
          <Button
            small
            onClick={addNewWidget}
            outlined
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          >
            Add widgets
          </Button>

          {Boolean(partWidgets.length) && (
            <Button
              small
              color={editing ? "sky" : "orange"}
              onClick={() => {
                setEditing((prev) => !prev);
              }}
              startIcon={
                editing ? <CheckIcon size={BUTTON_ICON_SIZE} /> : <Pencil size={BUTTON_ICON_SIZE} />
              }
            >
              {editing ? "Save" : "Edit"}
            </Button>
          )}
        </div>
      </div>
      {!partWidgets?.length && (
        <div className="card  flex items-center justify-center py-16">
          No Widgets Added for this part.
        </div>
      )}
      {partWidgets?.map((widget) => {
        return (
          <div className=" !h-48  relative" key={widget.id}>
            <ThingMonitorWidget
              thingName={productName ? productThing! : thingName}
              productName={DIGITAL_TWIN_PRODUCT}
              type={widget.type}
              widgetFields={buildWidgetFieldList(widget)}
              operation={widget.operation}
              range={widget.range}
              duration={duration}
              source={widget.source}
              // showAxis={showAxis}
              showAxis
              assetFields={widget.assetFields}
            />

            {editing && (
              <EditWidgetOptions
                editable
                onEdit={() => onEdit(widget)}
                onClone={() => onClone(widget.id)}
                onRemove={() => onRemove(widget.id)}
                id={widget.id}
              />
            )}
          </div>
        );
      })}

      <EditWidgetModal
        ref={editModalRef}
        onUpdate={onUpdate}
        onAdd={handleAddThingWidget}
        duration={duration}
        digitalTwinWidget
        assets={availableAsset}
      />
    </div>
  );
};

export default PointWidgets;
