import { Button } from "@components/shadcn/components/button";
// import { Card, CardContent } from "@components/shadcn/components/card";
import { X } from "lucide-react";
import React, {
  ForwardedRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState
} from "react";
import { handleUploadVideo } from "./CreateDigitalTwin";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Card } from "@components/ui";

interface MediaItem {
  type: "image" | "video";
  url: string;
}
type Ref = {
  uploadImage: () => Promise<{ url: string; type: "video" | "image" }[]>;
};

const MultiImageUpload = (
  {
    images = []
  }: {
    images?: MediaItem[];
  },
  ref: ForwardedRef<Ref>
) => {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<(File | undefined)[]>([]);

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        const type = file.type.startsWith("image/") ? "image" : "video";
        setMediaItems((prevItems) => [...prevItems, { type, url }]);
        setSelectedFiles((prevFiles) => [...prevFiles, file]);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeMediaItem = (index: number) => {
    setMediaItems((prevItems) => prevItems.filter((_, i) => i !== index));
    setSelectedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  useEffect(() => {
    setMediaItems(images);
    setSelectedFiles(images.map(() => undefined));
  }, []);

  const uploadImage = useCallback(async () => {
    const uploadPromises = selectedFiles.map((file, index) => {
      if (file) {
        return handleUploadVideo(file);
      }
      return {
        data: {
          fileUrl: mediaItems[index]!.url
        }
      };
    });
    const urls = await Promise.all(uploadPromises);
    return urls.map((item) => {
      const url = item.data.fileUrl as string;
      const type: "video" | "image" = url.includes("/images/") ? "image" : "video";
      return { url, type };
    });
  }, [selectedFiles, mediaItems]);

  useImperativeHandle(ref, () => ({ uploadImage }), [uploadImage, selectedFiles, mediaItems]);

  return (
    <div className="w-full max-w-3xl mx-auto space-y-4">
      <h1 className="input-label-text">Media Uploader</h1>
      <Card variant="second" className=" ">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {mediaItems.map((item, index) => (
            <Card variant="third" key={index} className="relative !p-0 group  border rounded-md">
              {item.type === "image" ? (
                <img
                  src={item.url}
                  alt={`Uploaded ${index + 1}`}
                  className="w-full h-40 object-contain rounded-md"
                />
              ) : (
                <video
                  src={item.url}
                  className="w-full h-40 object-cover rounded-md"
                  muted
                  controls
                />
              )}
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeMediaItem(index)}
                aria-label={`Remove ${item.type} ${index + 1}`}
              >
                <X className="h-4 w-4" />
              </Button>
            </Card>
          ))}
        </div>
        {mediaItems.length === 0 && (
          <p className="text-center text-muted-foreground py-8">No media uploaded yet.</p>
        )}
      </Card>
      <div className="flex justify-center">
        <div className="flex justify-center">
          <Button
            variant="outline"
            type="button"
            onClick={() => document.getElementById("media-upload")?.click()}
            className="cursor-pointer"
          >
            Add New Media
          </Button>
          <input
            id="media-upload"
            type="file"
            accept="image/*,video/*"
            className="sr-only"
            onChange={handleMediaUpload}
          />
        </div>
      </div>
    </div>
  );
};

export default forwardRef(MultiImageUpload);
