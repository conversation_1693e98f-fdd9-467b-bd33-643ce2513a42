import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import HeaderSection from "@components/layout/HeaderSection";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import useAppUserList from "@hooks/classic/useAppUserList";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { getTableIndex } from "@utils/tableUtils";
import { Plus, Search } from "lucide-react";
import { ElementRef, useRef } from "react";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";
import AddAppUserThing from "./components/AddAppUserThing";
import { Card } from "@components/ui";

const AppUsers = () => {
  const { searchQuery, setSearchQuery, setPage, page, setLimit, limit } = useTablePagination();
  const { data: permissions } = useUserGroupPermissions();
  const [sortFn, sort] = useTableSort();
  const addAppUserThingRef = useRef<ElementRef<typeof AddAppUserThing>>(null);

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: userList, isLoading } = useAppUserList({
    enabled: true,
    searchQuery: debouncedSearchQuery
  });

  const navigate = useCustomNavigate();

  return (
    <main className="space-y-4">
      <HeaderSection
        title="App users"
        description="Manage your App users"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            onClick={() => {
              addAppUserThingRef.current?.openDrawer();
            }}
            noAccess={permissions.thingGroup !== "write"}
          >
            Add App User
          </Button>
        }
      />
      <Card className="space-y-4">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setPage(1);
            setSearchQuery(e.target.value);
          }}
          className="flex-1"
          placeholder="Search user.."
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("name", order)}> Name</TableHead>
              <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            </>
          }
          body={
            isLoading ? (
              <TableRowsSkeleton />
            ) : !userList?.users?.length ? (
              <DataNotFound title="No Users Available" isTable />
            ) : (
              userList.users.toSorted(sortFn).map((user, i) => (
                <tr key={user.id} className="cursor-pointer" onClick={() => navigate(user.email)}>
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{user.name}</TableRow>
                  <TableRow>{user.email}</TableRow>
                  <TableRow>{convetUTCToLocal(user.createdAt)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: Math.ceil((userList?.userCount ?? 0) / limit)
          }}
        />
      </Card>

      <AddAppUserThing ref={addAppUserThingRef} />
    </main>
  );
};

export default AppUsers;
