import { MODE } from "@api/index";
import Button from "@components/Button";
import Input from "@components/Input";
import PasswordInput from "@components/PasswordInput";
import { PhoneInput } from "@components/PhoneNumberInput";
import FormDialog from "@components/FormDialog";
import { addAppUserApi } from "@frontend/shared/api/user";
import { useAppSelector } from "@src/store";
import { useMutation } from "@tanstack/react-query";
import { PASSWORD_VALIDATE, PHONE_VALIDATE } from "@utils/from_schema";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import {MapPin, Cpu, Lock, Mail, Map, X, Plus } from "lucide-react";
import { ForwardedRef, forwardRef, useCallback, useImperativeHandle, useState } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import * as RPNInput from "react-phone-number-input";

type props = unknown;
type Ref = {
  openDrawer: () => void;
  closeDrawer: () => void;
};

const userDefaultValues = {
  userName: "",
  email: "",
  address: "",
  phone: "",
  pincode: "",
  password: MODE === "development" ? "Qwertyuiop@123" : "",
  confirmPassword: MODE === "development" ? "Qwertyuiop@123" : ""
};

const AddAppUserThing = (props: props, ref: ForwardedRef<Ref>) => {
  const [showAddDevice, setShowAddDevice] = useState(false);
  const addAppUserForm = useForm({
    defaultValues: userDefaultValues
  });
  const tenant = useAppSelector(({ user }) => user.tenant?.name as string);
  const addUserMutation = useMutation({
    mutationFn: addAppUserApi,
    onSuccess: () => {
      showSuccessToast("User created successfully");
      queryClient.invalidateQueries({ queryKey: ["app-user-list"] });
      closeDrawer();
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const openDrawer = useCallback(() => {
    setShowAddDevice(true);
  }, []);

  const closeDrawer = useCallback(() => {
    setShowAddDevice(false);
    addAppUserForm.reset();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      openDrawer,
      closeDrawer
    }),
    [openDrawer, closeDrawer]
  );

  const onSubmit: SubmitHandler<typeof userDefaultValues> = async (values) => {
    if (values.password !== values.confirmPassword) {
      showErrorToast("Passwords do not match");
      return;
    }
    addUserMutation.mutate({ ...values, tenant });
  };

  return (
    <FormDialog
      open={showAddDevice}
      onClose={closeDrawer}
      notDismissable
      title="Create App User"
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={closeDrawer}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            loading={addUserMutation.isPending}
            type="submit"
            form="add-app-user-form"
          >
            Create
          </Button>
        </div>
      }
    >
      <form
        className="space-y-4"
        onSubmit={addAppUserForm.handleSubmit(onSubmit)}
        id="add-app-user-form"
      >
        <Input
          label="Username"
          placeholder="Enter username"
          startIcon={<Cpu size={INPUT_ICON_SIZE} />}
          required
          {...addAppUserForm.register("userName")}
        />

        <Input
          label="Email"
          type="email"
          placeholder="Enter email"
          required
          startIcon={<Mail size={INPUT_ICON_SIZE} />}
          {...addAppUserForm.register("email", {
            required: true
          })}
        />

        <PhoneInput
          required
          label="Phone Number"
          placeholder="Phone Number"
          //startIcon={<Phone size={INPUT_ICON_SIZE} />}
          {...addAppUserForm.register("phone", PHONE_VALIDATE.schema)}
          onChange={(e) => {
            addAppUserForm.setValue("phone", e);
          }}
          value={addAppUserForm.getValues("phone") as RPNInput.Value}
          error={!!addAppUserForm.formState.errors.phone}
          helperText={addAppUserForm.formState.errors.phone && PHONE_VALIDATE.message}
        />

        <Input
          label="Address"
          placeholder="Enter address"
          {...addAppUserForm.register("address")}
          startIcon={<Map size={INPUT_ICON_SIZE} />}
        />

        <Input
          label="Pincode"
          placeholder="Enter pincode"
          startIcon={<MapPin size={INPUT_ICON_SIZE} />}
          {...addAppUserForm.register("pincode")}
        />

        <PasswordInput
          label="Password"
          type="password"
          startIcon={<Lock size={INPUT_ICON_SIZE} />}
          placeholder="Enter password"
          showProgress
          register={{ ...addAppUserForm.register("password", PASSWORD_VALIDATE.schema) }}
          error={!!addAppUserForm.formState.errors.password}
          helperText={addAppUserForm.formState.errors.password && PASSWORD_VALIDATE.message}
        />

        <PasswordInput
          label="Confirm Password"
          type="password"
          startIcon={<Lock size={INPUT_ICON_SIZE} />}
          showProgress
          register={{ ...addAppUserForm.register("confirmPassword", PASSWORD_VALIDATE.schema) }}
          error={!!addAppUserForm.formState.errors.confirmPassword}
          helperText={addAppUserForm.formState.errors.confirmPassword && PASSWORD_VALIDATE.message}
        />
      </form>
    </FormDialog>
  );
};

export default forwardRef(AddAppUserThing);
