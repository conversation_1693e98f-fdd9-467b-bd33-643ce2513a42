import { fetchApi } from "@api/_helpers";
import { APP_URL } from "@api/index";
import Button from "@components/Button";
import { Card } from "@components/ui";
import DataNotFound from "@components/DataNotFound";
import Dropdown from "@components/Dropdown";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@components/shadcn/components/alert-dialog";
import { Checkbox } from "@components/shadcn/components/checkbox";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { updateUserRoleApi } from "@frontend/shared/api/user";
import { DEFAULT_PAGE_COUNT } from "@frontend/shared/config/defaults";
import useAppUserThings from "@hooks/classic/useAppUserThings";
import useThingList from "@hooks/classic/useThingList";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { CircularProgress } from "@mui/material";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { useMutation } from "@tanstack/react-query";
import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Boxes, Edit } from "lucide-react";
import {
  ElementRef,
  ForwardedRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from "react";
import { Check, Search, Settings, X } from "lucide-react";
import { useParams } from "react-router-dom";

const syncUserThingsApi = async ({
  email,
  thingNames
}: {
  email: string;
  thingNames: string[];
}) => {
  const fetchResponse = await fetchApi(
    `/app-things/user-things`,
    {
      method: "PUT",
      body: {
        email,
        thingNames
      }
    },
    APP_URL
  );
  const res = await fetchResponse.json();
  if (res.status === "Success") {
    return res.data;
  }
  throw new Error(res.message);
};

const AppUserThingList = () => {
  const { data: permissions } = useUserGroupPermissions();
  const [manageThings, setManageThings] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [searchQuery, setSearchQuery] = useState("");

  const [thingListPage, setThingListPage] = useState(1);
  const [thingListLimit, setThingListLimit] = useState(DEFAULT_PAGE_COUNT);

  const updateRoleModalRef = useRef<ElementRef<typeof UpdateRoleModal>>(null);

  const userThingSyncMutation = useMutation({
    mutationFn: syncUserThingsApi,
    onSuccess: () => {
      showSuccessToast("Things synced successfully");

      queryClient.invalidateQueries({ queryKey: ["app-user-things", email] });
      setManageThings(false);
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: thingList, isLoading: thingListLoading } = useThingList({
    enabled: true,
    search: debouncedSearchQuery,
    limit: thingListLimit,
    page: thingListPage
  });

  const [sortFn, sort] = useTableSort();
  const email = useParams().email!;
  const { data: userThings, isLoading: userThingsLoading } = useAppUserThings({
    searchQuery: debouncedSearchQuery,
    email,
    limit,
    page
  });

  const navigate = useCustomNavigate();

  const [thingNameList, setThingNameList] = useState<
    {
      thingName: string;
      productName: string;
      displayName: string;
    }[]
  >([]);

  useEffect(() => {
    if (userThings?.things) setThingNameList(userThings?.things);
  }, [userThings]);

  const navigateToThingDetails = (thingName: string) => {
    navigate(`/things/${thingName}`);
  };

  return (
    <>
      <Card className=" space-y-6">
        <div className="between items-center">
          <HeadingIcon Icon={Boxes} title={`User ${THING_VAR}s `} />
          {!manageThings ? (
            <Button
              onClick={() => {
                setManageThings(true);
                setSearchQuery("");
              }}
              noAccess={permissions.thingGroup !== "write"}
              startIcon={<Settings size={BUTTON_ICON_SIZE} />}
              color="gray"
            >
              Manage {THING_VAR}
            </Button>
          ) : (
            <div className="flex gap-4">
              <Input
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setPage(1);
                }}
                className="w-[25rem]"
                placeholder="Search"
                endIcon={<Search size={INPUT_ICON_SIZE} />}
              />
              <Button
                onClick={() => {
                  userThingSyncMutation.mutate({
                    email,
                    thingNames: thingNameList.map((item) => item.thingName)
                  });
                }}
                loading={userThingSyncMutation.isPending}
                startIcon={<Check size={BUTTON_ICON_SIZE} />}
              >
                Confirm
              </Button>
              <Button
                outlined
                onClick={() => {
                  setManageThings(false);
                }}
                disabled={!thingList?.things?.length}
                startIcon={<X size={BUTTON_ICON_SIZE} />}
                color="orange"
              >
                Cancel
              </Button>
            </div>
          )}
        </div>
        {manageThings ? (
          <Table
            head={
              <>
                <TableHead>No.</TableHead>
                <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>

                <TableHead onSort={(order) => sort("productName", order)}>
                  {PRODUCT_VAR} Name
                </TableHead>
                <TableHead onSort={(order) => sort("displayName", order)}>Display Name</TableHead>
                <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              </>
            }
            body={
              thingListLoading ? (
                <TableRowsSkeleton />
              ) : !thingList?.things?.length ? (
                <DataNotFound title={`No ${THING_VAR} Available`} isTable />
              ) : (
                <>
                  {userThings?.things.map((thing) => (
                    <tr
                      key={thing.thingName}
                      data-state={
                        thingNameList.some((item) => item.thingName === thing.thingName)
                          ? "selected"
                          : ""
                      }
                    >
                      {/* <TableRow>{i + 1}</TableRow> */}
                      <TableRow onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={thingNameList.some((item) => item.thingName === thing.thingName)}
                          onCheckedChange={(value) => {
                            if (value) {
                              setThingNameList((prev) => [
                                ...prev,
                                {
                                  thingName: thing.thingName,
                                  productName: thing.productName,
                                  displayName: thing.displayName
                                }
                              ]);
                            } else {
                              setThingNameList((prev) =>
                                prev.filter((item) => item.thingName !== thing.thingName)
                              );
                            }
                          }}
                        />
                      </TableRow>
                      <TableRow>{thing.thingName}</TableRow>
                      <TableRow>{thing.productName}</TableRow>
                      <TableRow>{thing.displayName || "N/A"}</TableRow>
                      <TableRow>{convetUTCToLocal(thing.createdAt)}</TableRow>
                    </tr>
                  ))}
                  {thingList.things
                    .filter(
                      (item) =>
                        !userThings?.things.some((compare) => compare.thingName === item.thingName)
                    )
                    .map((thing) => (
                      <tr
                        key={thing.thingName}
                        data-state={
                          thingNameList.some((item) => item.thingName === thing.thingName)
                            ? "selected"
                            : ""
                        }
                      >
                        {/* <TableRow>{i + 1}</TableRow> */}
                        <TableRow onClick={(e) => e.stopPropagation()}>
                          <Checkbox
                            checked={thingNameList.some(
                              (item) => item.thingName === thing.thingName
                            )}
                            onCheckedChange={(value) => {
                              if (value) {
                                setThingNameList((prev) => [
                                  ...prev,
                                  {
                                    thingName: thing.thingName,
                                    productName: thing.productName,
                                    displayName: thing.displayName
                                  }
                                ]);
                              } else {
                                setThingNameList((prev) =>
                                  prev.filter((item) => item.thingName !== thing.thingName)
                                );
                              }
                            }}
                          />
                        </TableRow>
                        <TableRow>{thing.thingName}</TableRow>
                        <TableRow>{thing.productName}</TableRow>
                        <TableRow>{thing.displayName || "N/A"}</TableRow>
                        <TableRow>{convetUTCToLocal(thing.createdAt)}</TableRow>
                      </tr>
                    ))}
                </>
              )
            }
            checkable={thingNameList}
            pagination={{
              page: thingListPage,
              setPage: setThingListPage,
              setLimit: setThingListLimit,
              totalPages: thingList?.page ?? 1
            }}
          />
        ) : (
          <Table
            head={
              <>
                <TableHead>No.</TableHead>
                <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
                <TableHead onSort={(order) => sort("displayName", order)}>Display Name</TableHead>
                <TableHead onSort={(order) => sort("productName", order)}>
                  {PRODUCT_VAR} Name
                </TableHead>
                <TableHead onSort={(order) => sort("role", order)}>Role</TableHead>
              </>
            }
            body={
              userThingsLoading ? (
                <TableRowsSkeleton />
              ) : !userThings?.things?.length ? (
                <DataNotFound title={`${THING_VAR}s found for the group`} isTable />
              ) : (
                userThings.things.toSorted(sortFn).map((thing, i) => (
                  <tr
                    className="cursor-pointer"
                    key={thing.thingName}
                    onClick={() => navigateToThingDetails(thing.thingName)}
                  >
                    <TableRow>{i + 1}</TableRow>
                    <TableRow>{thing.thingName}</TableRow>
                    <TableRow>{thing.displayName || "N/A"}</TableRow>
                    <TableRow>{thing.productName}</TableRow>
                    <TableRow>
                      <div className="flex items-center gap-2">
                        {thing.role}
                        <Edit
                          size={BUTTON_ICON_SIZE}
                          onClick={(e) => {
                            e.stopPropagation();
                            updateRoleModalRef.current?.showModal({
                              thingName: thing.thingName,
                              role: thing.role
                            });
                          }}
                        />
                      </div>
                    </TableRow>
                  </tr>
                ))
              )
            }
            pagination={{
              page,
              setPage,
              setLimit,
              totalPages: Math.ceil((userThings?.totalCount ?? 0) / limit)
            }}
          />
        )}
      </Card>
      <UpdateRoleModal ref={updateRoleModalRef} />
    </>
  );
};

type Ref = {
  showModal: ({ thingName, role }: { thingName: string; role: string }) => void;
  closeModal: () => void;
};

const UpdateRoleModalRefComp = (props: unknown, ref: ForwardedRef<Ref>) => {
  const [open, setOpen] = useState(false);
  const [thingName, setThingName] = useState("");
  const [role, setRole] = useState("");
  const email = useParams().email!;

  const showModal = useCallback(({ thingName, role }: { thingName: string; role: string }) => {
    setThingName(thingName);
    setRole(role);
    setOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setOpen(false);
    setThingName("");
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      showModal: showModal,
      closeModal
    }),
    [showModal, closeModal]
  );

  const updateUserMutation = useMutation({
    mutationFn: updateUserRoleApi,
    onSuccess: () => {
      showSuccessToast("Role updated successfully");
      queryClient.invalidateQueries({ queryKey: ["app-user-things", email] });
      closeModal();
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent className="!max-w-[32rem] ">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center">Role Update</AlertDialogTitle>
          <AlertDialogDescription>
            This is the role update modal to update the role of the user.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <Dropdown
          options={["owner", "admin", "member"]}
          value={role}
          onChange={(value) => {
            setRole(value);
          }}
          label="Role"
          required
          menuClassName="!z-[100000]"
        />

        <AlertDialogFooter>
          <Button color="gray" small onClick={closeModal}>
            Cancel
          </Button>
          <Button
            small
            onClick={() => {
              updateUserMutation.mutate({ thingName, role, email });
            }}
            className="bg-brandColor text-black hover:bg-brandColor/90 "
          >
            {updateUserMutation.isPending ? (
              <CircularProgress size={18} color="inherit" />
            ) : (
              "Update Role"
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const UpdateRoleModal = forwardRef(UpdateRoleModalRefComp);

export default AppUserThingList;
