import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { ElementRef, useRef } from "react";
import { Cpu, Plus } from "lucide-react";
import Button from "../../../components/Button";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import AddDynamicGroupItem from "./AddDynamicGroupItem";
import useDynamicThingGroupList from "@hooks/group/useDynamicThingGroupList";
import useTablePagination from "@hooks/classic/useTablePagination";
import useDebounce from "@hooks/useDebounce";
import Table, { TableHead, TableRow } from "@components/Table";
import HeaderSection from "@components/layout/HeaderSection";

const DynamicGroupList = () => {
  const navigate = useCustomNavigate();
  const { limit, setLimit, setPage, page, searchQuery } = useTablePagination();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const addDynamicThingGroupRef = useRef<ElementRef<typeof AddDynamicGroupItem>>(null);
  const { data: thingGroups, isLoading } = useDynamicThingGroupList({
    enabled: true,
    page,
    limit,
    search: debouncedSearchQuery
  });

  return (
    <main className="space-y-4">
       <HeaderSection
        title={`Dynamic Groups`}
        description="Manage your Dynamic Groups"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => {
              addDynamicThingGroupRef.current?.openDrawer();
            }}       
          >
            Add Group
          </Button>
        }
      />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead>Group Name</TableHead>
              <TableHead>Created At</TableHead>
            </>
          }
          body={
            isLoading ? (
              <TableRowsSkeleton />
            ) : !thingGroups?.thingGroups?.length ? (
              <DataNotFound title="No Groups Available" isTable />
            ) : (
              thingGroups.thingGroups.map((thing, i) => (
                <tr
                  key={i}
                  className="cursor-pointer"
                  onClick={() => navigate(`${thing.thingGroupName}`)}
                >
                  <TableRow>{i + 1}</TableRow>
                  <TableRow title>{thing.thingGroupName}</TableRow>
                  <TableRow>{convetUTCToLocal(thing.createdAt) || "N/A"}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: thingGroups?.pages || 1
          }}
        />

      <AddDynamicGroupItem ref={addDynamicThingGroupRef} />
    </main>
  );
};

export default DynamicGroupList;
