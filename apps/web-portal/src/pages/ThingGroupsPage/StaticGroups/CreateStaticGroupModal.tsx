import { addStaticThingGroup } from '@api/thingGroup';
import ActionButton from '@components/ActionButton';
import Button from '@components/Button';
import FormDialog from '@components/FormDialog';
import Input from '@components/Input';
import { showErrorToast, showSuccessToast } from '@utils/index';
import { queryClient } from '@utils/queryClient';
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from '@utils/utilities';
import { Cpu, Plus, X } from 'lucide-react';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { THING_VAR } from "@utils/featureLabels";
import { useForm } from 'react-hook-form';
import { MetaDataInputs } from './StaticGroupList';
export type CreateStaticGroupModalRef = {
  openModal : () => void;
}

type StaticThingGroup = {
thingGroupName : string;
description : string
}
const CreateStaticGroupModal = forwardRef<CreateStaticGroupModalRef>((props:unknown , ref ) => {
    const thingForm = useForm<StaticThingGroup>();
    const [showAddDevice, setShowAddDevice] = useState(false);
    const [metaDataInputs, setMetaDataInputs] = useState<MetaDataInputs[]>([]);

   const removeMetaInputHandler = (inputId:number) => {
    setMetaDataInputs((prev) => prev.filter((input) => input.id !== inputId));
  };

  const onSubmit = async (values:StaticThingGroup) => {
    const transformedMetaData = metaDataInputs.reduce<Record<string,string>>(
      (obj, item) => ((obj[item.key] = item.value), obj),
      {}
    );


    const resp = await addStaticThingGroup({
      thingGroupName: values.thingGroupName,
      description: values.description,
      meta: {
        ...transformedMetaData
      }
    });

    if (resp.status === "Success") {
      showSuccessToast(`${THING_VAR} Group  created successfully`);

      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["static-group-list"] });
        resetFormState();
      }, 500);
    } else {
      showErrorToast(resp.message);
    }
  };

  const resetFormState = () => {
    setShowAddDevice(false);
    thingForm.reset();
    setMetaDataInputs([]);
  };
    useImperativeHandle( ref , ()=>({
        openModal : ()=> setShowAddDevice(true),
    }))

  return (
     <FormDialog
          open={showAddDevice}
          onClose={() => resetFormState()}
          notDismissable
          title={`Create ${THING_VAR} Group`}
          footer={
            <div className="flex gap-4 justify-end">
              <Button
                onClick={resetFormState}
                small
                color="gray"
                type="button"
                startIcon={<X size={BUTTON_ICON_SIZE} />}
              >
                Close
              </Button>
              <Button
                small
                startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                loading={thingForm.formState.isSubmitting}
                type="submit"
                form="add-static-group-form"
              >
                Create
              </Button>
            </div>
          }
        >
          <form
            className=" space-y-4"
            id="add-static-group-form"
            onSubmit={thingForm.handleSubmit(onSubmit)}
          >
            <div className="space-y-4">
              <Input
                label={`${THING_VAR} Group Name`}
                startIcon={<Cpu size={INPUT_ICON_SIZE} />}
                required
                {...thingForm.register("thingGroupName")}
                onKeyDown={(e) => {
                 if(e.key === " ")
                    { 
                        e.preventDefault();
                    }
                }}
              />

              <Input
                label="Description"
                inputType="textarea"
                required
                {...thingForm.register("description", {
                  required: true
                })}
              />

              <hr className="hr !my-8" />
              <div className="between items-center">
                <h3 className="sidebar-sub-heading">Tags</h3>

                <Button
                  disabled={metaDataInputs.length >= 5}
                  startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                  type="button"
                  outlined
                  color="gray"
                  onClick={() =>
                    setMetaDataInputs((prev) => [
                      ...prev,
                      { key: "", value: "", id: Math.random() }
                    ])
                  }
                  small
                >
                  Add Tag
                </Button>
              </div>

              {!metaDataInputs.length ? (
                <p className="center description-lg  text-gray-400">No Tags Added</p>
              ) : (
                metaDataInputs.map((input) => (
                  <div className="flex gap-4 items-center " key={input.id}>
                    <Input
                      className="w-full"
                      small
                      placeholder="Key"
                      onChange={(e) => {
                        setMetaDataInputs((prev) =>
                          prev.map((inputField) =>
                            inputField.id === input.id
                              ? { ...inputField, key: e.target.value }
                              : inputField
                          )
                        );
                      }}
                      value={input.key}
                    />
                    <Input
                      small
                      onChange={(e) => {
                        setMetaDataInputs((prev) =>
                          prev.map((inputField) =>
                            inputField.id === input.id
                              ? { ...inputField, value: e.target.value }
                              : inputField
                          )
                        );
                      }}
                      className="w-full"
                      placeholder="Value"
                      value={input.value}
                    />
                    <ActionButton onClick={() => removeMetaInputHandler(input.id)} type="delete" />
                  </div>
                ))
              )}
            </div>
          </form>
        </FormDialog>
  )
});

export default CreateStaticGroupModal