import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useStaticGroupList from "@hooks/group/useStaticGroupList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Cpu, Plus, Search } from "lucide-react";
import { useRef, useState } from "react";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import { TableHead, TableRow } from "../../../components/Table";
import Table from "../../../components/Table/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import CreateStaticGroupModal, { CreateStaticGroupModalRef } from "./CreateStaticGroupModal";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

export type MetaDataInputs = {
  id: number;
  key: string;
  value: string;
};

const StaticGroupList = () => {
  const createStaticGroupModalRef = useRef<CreateStaticGroupModalRef>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const { data: groupList, isLoading: groupLoading } = useStaticGroupList({
    searchQuery,
    page,
    limit
  });
  const { data: permissions } = useUserGroupPermissions();

  return (
    <main className="space-y-4">
       <HeaderSection
              title={`Static ${THING_VAR} Group`}
              description={`Manage your Static ${THING_VAR} groups`}
              actions={
                <Button
                  startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                  onClick={() => createStaticGroupModalRef?.current?.openModal()}
                   noAccess={permissions.thingGroup !== "write"}
                >
                   Add {THING_VAR} Group
                </Button>
              }
              />
      <Card className="space-y-6">
            <Input
              value={searchQuery}
              onChange={(e) => {
                setPage(1);
                setSearchQuery(e.target.value);
              }}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("thingGroupName", order)}>Group Name</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Create Date</TableHead>
              <TableHead onSort={(order) => sort("updatedAt", order)}>Update Date</TableHead>
              <TableHead>{THING_VAR} Count</TableHead>
            </>
          }
          body={
            groupLoading ? (
              <TableRowsSkeleton />
            ) : !groupList?.thingGroups?.length ? (
              <DataNotFound title={`No ${THING_VAR} Group Available`} isTable />
            ) : (
              groupList.thingGroups.toSorted(sortFn).map((thing, i) => (
                <tr key={thing.id} className="cursor-pointer" onClick={() => navigate(thing.id)}>
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{thing.thingGroupName}</TableRow>
                  <TableRow>{convetUTCToLocal(thing.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(thing.updatedAt)}</TableRow>
                  <TableRow>{thing.things.length || "0"}</TableRow>

                  {/* <TableRow>
                <Label
                  className="!text-[.8rem]"
                  color={certificate.status === "active" ? "green" : "red"}
                  text={certificate.status}
                  linear
                />
              </TableRow> */}
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: groupList?.pages || 1
          }}
        />
      </Card>

      <CreateStaticGroupModal ref={createStaticGroupModalRef} />
    </main>
  );
};

export default StaticGroupList;
