import { Button } from "@components/shadcn/components/button";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, Check, Rocket, User } from "lucide-react";
import * as React from "react";

const steps = [
  {
    id: "create",
    title: "Create Account",
    icon: Check,
    description: "Account created successfully"
  },
  {
    id: "setup",
    title: "Setup User",
    icon: User,
    description: "Personalizing your experience"
  },
  {
    id: "enjoy",
    title: "Enjoy Platform",
    icon: Rocket,
    description: "Ready to explore"
  }
];

export default function UserSetupStepper({ loadingBtn, setupPartnerHandler }) {
  const [currentStep] = React.useState(1);

  return (
    <div className="space-y-8">
      {/* Header */}

      {/* Stepper */}
      <div className="relative">
        <div className="flex justify-between items-center">
          {steps.map((step, index) => (
            <div key={step.id} className="flex flex-col items-center relative z-10 min-w-60">
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  index < currentStep
                    ? "bg-brandColor text-primary-foreground"
                    : index === currentStep
                      ? "bg-emerald-100 text-emerald-500"
                      : "bg-gray-100 text-gray-500"
                }`}
              >
                <step.icon className="w-5 h-5" />
              </motion.div>
              <div className="mt-2 space-y-1">
                <p
                  className={`text-sm font-medium ${
                    index <= currentStep ? "text-primary" : "text-muted-foreground"
                  }`}
                >
                  {step.title}
                </p>
                <p className="text-xs text-muted-foreground hidden sm:block">{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Progress Line */}
        <div className="absolute top-6 left-10 right-10 h-[2px] bg-gray-200 -z-0">
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
            className="h-full bg-brandColor"
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Thank you for registering with us!</h2>
          <p className="text-muted-foreground">
            To complete your account setup, please click the button below.
          </p>
        </div>

        {/* Action Button */}
        <div className="flex justify-center">
          <Button size="lg" loading={loadingBtn} onClick={setupPartnerHandler}>
            Setup your account
            <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}
