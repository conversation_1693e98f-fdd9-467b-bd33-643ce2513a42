import { fetchThingListeners, updateThingListeners } from "@api/thing";
import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Switch from "@components/Switch";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Badge } from "@components/shadcn/components/badge";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useProductDocInfo from "@hooks/product/useProductDocInfo";
import { CircularProgress } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { AudioLines, Lock, Radio, Wifi } from "lucide-react";
import { useState } from "react";
import Table, { TableHead, TableRow } from "../../components/Table";

const ListenerPage = () => {
  const [listenersLoadingState, setListenersLoadingState] = useState<
    "wss" | "mtls" | "mqtts" | null
  >();
  const { data: permissions } = useUserGroupPermissions();

  const { data: productDocInfo, isLoading: isProductDocLoading } = useProductDocInfo({});

  const { data: listenersList, isLoading: listenersLoading } = useQuery({
    queryKey: ["listener-list"],
    queryFn: fetchThingListeners
  });
  const mutation = useMutation({
    mutationFn: updateThingListeners,
    onSuccess: () => {
      // refetchGeoFence();
      setListenersLoadingState(null);

      showSuccessToast("Lister Updated Successfully.");
      queryClient.invalidateQueries({ queryKey: ["listener-list"] });
    },
    onError: (error) => {
      showErrorToast(error.message);
    }
  });

  const updateListener = (type: "wss" | "mtls" | "mqtts", status: boolean) => {
    setListenersLoadingState(type);
    mutation.mutate({ type, status: status ? "start" : "stop" });
  };

  if (isProductDocLoading) {
    return <CardLoadingSkeleton />;
  }

  if (!productDocInfo) {
    return <DataNotFound title="Something went wrong" />;
  }

  return (
    <div className="space-y-6">
      <HeadingIcon Icon={AudioLines} title="Protocol Services" />

      <Table
        head={
          <>
            <TableHead>Protocol</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-[100px] text-right">Port</TableHead>
            <TableHead className="w-[100px] text-right">Status</TableHead>
          </>
        }
        body={
          listenersLoading || !listenersList.data ? (
            <TableRowsSkeleton rowCount={5} />
          ) : (
            <>
              <tr>
                <TableRow title className="font-medium">
                  <div className="flex items-center space-x-2">
                    <Lock className="h-5 w-5 text-blue-500" />
                    <span>MQTTS-MTLS</span>
                  </div>
                </TableRow>
                <TableRow>
                  <div className="space-y-1">
                    <p>Certificate based Authentication</p>
                    <p className="text-sm text-muted-foreground">
                      Highest security with mutual TLS authentication
                    </p>
                  </div>
                </TableRow>
                <TableRow>
                  <Badge className="!text-background">{productDocInfo[1].info}</Badge>
                </TableRow>
                <TableRow>
                  {listenersLoading || listenersLoadingState === "mtls" ? (
                    <div className="">
                      <CircularProgress size={18} />
                    </div>
                  ) : (
                    <Switch
                      checked={listenersList.data.mtls}
                      disabled={permissions.listners !== "write"}
                      onChange={(e) => {
                        updateListener("mtls", e.target.checked);
                      }}
                    />
                  )}
                </TableRow>
              </tr>
              <tr>
                <TableRow title className="font-medium">
                  <div className="flex items-center space-x-2">
                    <Radio className="h-5 w-5 text-green-500" />
                    <span>MQTTS</span>
                  </div>
                </TableRow>
                <TableRow>
                  <div className="space-y-1">
                    <p>Username/Password based Authentication</p>
                    <p className="text-sm text-muted-foreground">Secure MQTT with TLS encryption</p>
                  </div>
                </TableRow>
                <TableRow>
                  <Badge className="!text-background">{productDocInfo[3].info}</Badge>
                </TableRow>
                <TableRow>
                  {listenersLoading || listenersLoadingState === "mqtts" ? (
                    <div className="">
                      <CircularProgress size={18} />
                    </div>
                  ) : (
                    <Switch
                      checked={listenersList.data.mqtts}
                      disabled={permissions.listners !== "write"}
                      onChange={(e) => {
                        updateListener("mqtts", e.target.checked);
                      }}
                    />
                  )}
                </TableRow>
              </tr>
              <tr>
                <TableRow title className="font-medium">
                  <div className="flex items-center space-x-2">
                    <Wifi className="h-5 w-5 text-purple-500" />
                    <span>WSS</span>
                  </div>
                </TableRow>
                <TableRow>
                  <div className="space-y-1">
                    <p>Username/Password based Authentication</p>
                    <p className="text-sm text-muted-foreground">
                      WebSocket Secure for real-time communication
                    </p>
                  </div>
                </TableRow>
                <TableRow>
                  <Badge className="!text-background">{productDocInfo[2].info}</Badge>
                </TableRow>
                <TableRow>
                  {listenersLoading || listenersLoadingState === "wss" ? (
                    <div className="">
                      <CircularProgress size={18} />
                    </div>
                  ) : (
                    <Switch
                      checked={listenersList.data.wss}
                      disabled={permissions.listners !== "write"}
                      onChange={(e) => {
                        updateListener("wss", e.target.checked);
                      }}
                    />
                  )}
                </TableRow>
              </tr>
            </>
          )
        }
      />
    </div>
  );
};

export default ListenerPage;
