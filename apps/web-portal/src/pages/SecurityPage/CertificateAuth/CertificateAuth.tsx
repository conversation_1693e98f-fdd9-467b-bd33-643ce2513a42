import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCertificateAuthList from "@hooks/security/useCertificateAuthList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { ClipboardList, Plus, Search } from "lucide-react";
import Button from "../../../components/Button";
import { TableHead, TableRow } from "../../../components/Table";
import Table from "../../../components/Table/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import { convetUTCToLocal } from "../../UserTypes/Tracking/HomeSection/utils";
import { Card } from "@components/ui";
import HeaderSection from "@components/layout/HeaderSection";

const CertificateAuth = () => {
  const { page, searchQuery, limit, setPage, setLimit, setSearchQuery } = useTablePagination();
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();

  const { data: certificateAutList, isLoading: isCertificateListLoading } = useCertificateAuthList({
    page,
    limit,
    search: debouncedSearchQuery
  });

  return (
    <div className=" space-y-6">
      <HeaderSection
        title="Certificate Issued"
        description="Manage your certificates"
        actions={
          <Button
            noAccess={permissions.tls !== "write"}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("addCertAuth")}
          >
            Add Certificate Auth
          </Button>
        }
      />

      <Card className="space-y-4">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setPage(1);
            setSearchQuery(e.target.value);
          }}
          className="w-full"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("caName", order)}>Certificate Authority</TableHead>
              <TableHead onSort={(order) => sort("type", order)}>Type</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("expiration", order)}>Expiration</TableHead>
            </>
          }
          body={
            isCertificateListLoading ? (
              <TableRowsSkeleton />
            ) : !certificateAutList?.list.length || !certificateAutList ? (
              <DataNotFound
                title={
                  !certificateAutList
                    ? "Something went wrong"
                    : "No Certificate Authority Available"
                }
                isTable
              />
            ) : (
              certificateAutList.list.toSorted(sortFn).map((certificate, i) => (
                <tr
                  key={certificate.caName}
                  className="cursor-pointer"
                  onClick={() => navigate(`${certificate.caName}`)}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{certificate.caName}</TableRow>
                  <TableRow>{certificate.type}</TableRow>
                  <TableRow>{convetUTCToLocal(certificate.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(certificate.expiration)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: certificateAutList?.pages || 1
          }}
        />
      </Card>
    </div>
  );
};

export default CertificateAuth;
