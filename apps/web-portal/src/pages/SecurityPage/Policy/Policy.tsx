import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import usePolicyList from "@hooks/security/usePolicyList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { ClipboardPenIcon } from "lucide-react";
import { ElementRef, useRef } from "react";
import { Plus, Search } from "lucide-react";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import { TableHead, TableRow } from "../../../components/Table";
import Table from "../../../components/Table/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import useDebounce from "../../../hooks/useDebounce";
import { convetUTCToLocal } from "../../UserTypes/Tracking/HomeSection/utils";
import CreatePolicy from "./CreatePolicy";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

const Policy = () => {
  const {
    page,
    setPage,
    limit,
    setLimit,
    searchQuery: policySearch,
    setSearchQuery: setPolicySearch
  } = useTablePagination();

  const navigate = useCustomNavigate();

  const debouncedSearchQuery = useDebounce(policySearch, 500);

  const [sortFn, sort] = useTableSort();
  const createPolicyRef = useRef<ElementRef<typeof CreatePolicy>>(null);

  const { data: policyList, isLoading: isPolicyLoading } = usePolicyList({
    page,
    limit,
    search: debouncedSearchQuery
  });

  const { data: permissions } = useUserGroupPermissions();

  return (
    <main className="space-y-4">
      <section className="space-y-4">
        <HeaderSection
          title={`${THING_VAR} Policy`}
          description={`Manage your ${THING_VAR} Policy`}
          actions={
            <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              type="submit"
              onClick={() => createPolicyRef.current?.openCreatePolicySidebar()}
              noAccess={permissions.policy !== "write"}
            >
              Add Policy
            </Button>
          }
        />
      </section>
      <Card className="space-y-4">
        <Input
          value={policySearch}
          onChange={(e) => setPolicySearch(e.target.value)}
          className="flex-1"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              {/* <TableHead>ID</TableHead> */}
              <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
              <TableHead onSort={(order) => sort("clientId", order)}>Client ID</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
            </>
          }
          body={
            isPolicyLoading ? (
              <TableRowsSkeleton />
            ) : !policyList?.policies?.length || !policyList ? (
              <DataNotFound
                title={!policyList ? "SomeThing went wrong!" : "No Policies Available"}
                isTable
              />
            ) : (
              policyList?.policies.toSorted(sortFn).map((policy, i) => (
                <tr
                  key={policy.thingName}
                  className="cursor-pointer"
                  onClick={() => navigate(`${policy.thingName}`)}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{policy.thingName}</TableRow>
                  <TableRow>{policy.clientId}</TableRow>
                  <TableRow>{convetUTCToLocal(policy.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(policy.updatedAt)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: policyList?.pages || 1
          }}
        />
      </Card>
      <CreatePolicy ref={createPolicyRef} />
    </main>
  );
};

export default Policy;
