import DataNotFound from "@components/DataNotFound";
import HeaderSection from "@components/layout/HeaderSection";
import { Card, StatCard } from "@components/ui";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useCertificateStats from "@hooks/security/useCertificateStats";
import useThingCertificateList from "@hooks/security/useThingCertificateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { StatCardSkeleton } from "@src/pages/InventoryPage/InventoryCardSection";
import { THING_VAR } from "@utils/featureLabels";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { <PERSON><PERSON><PERSON><PERSON>, FileText, Plus, Search, XCircle } from "lucide-react";
import { ElementRef, useRef } from "react";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import Label from "../../../components/Label";
import { TableHead, TableRow } from "../../../components/Table";
import Table from "../../../components/Table/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import { convetUTCToLocal } from "../../UserTypes/Tracking/HomeSection/utils";
import CreateThingCertificate from "./CreateThingCertificate";

const Certificates = () => {
  const createCertificateRef = useRef<ElementRef<typeof CreateThingCertificate>>(null);

  const [sortFn, sort] = useTableSort();
  const navigate = useCustomNavigate();

  const { searchQuery, setSearchQuery, setPage, page, setLimit, limit } = useTablePagination();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();
  const { data: certificateList, isLoading: isCertificateLoading } = useThingCertificateList({
    page,
    limit,
    search: debouncedSearchQuery
  });
  const { data: certificateStats, isLoading: certificateStatsLoading } = useCertificateStats();
  return (
    <>
      <section className=" space-y-4">
        <HeaderSection
          title={`${THING_VAR} Certificate`}
          description={`Manage your ${THING_VAR} Certificate`}
          actions={
            <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              type="submit"
              onClick={() => createCertificateRef.current?.openCreateCertificateSidebar()}
              noAccess={permissions.tls !== "write"}
            >
              Add Certificate
            </Button>
          }
        />
        {!certificateStats || certificateStatsLoading ? (
          <StatCardSkeleton />
        ) : (
          <section className="flex w-full flex-wrap gap-4 ">
            <StatCard
              title={`Active Certificates`}
              loading={certificateStatsLoading}
              value={certificateStats?.active}
              colorScheme="success"
              icon={CircleCheck}
              description={
                ((certificateStats?.active / certificateStats?.total) * 100).toFixed(1) + "%"
              }
            />
            <StatCard
              title={`InActive Certificates`}
              value={certificateStats?.inactive}
              loading={certificateStatsLoading}
              colorScheme="danger"
              icon={XCircle}
              description={
                ((certificateStats?.inactive / certificateStats?.total) * 100).toFixed(1) + "%"
              }
            />

            <StatCard
              title={`Total Certificates`}
              loading={certificateStatsLoading}
              value={certificateStats?.total}
              colorScheme="info"
              icon={FileText}
            />
          </section>
        )}

        <Card className="flex-1 space-y-4">
          <h3 className="heading-2">{THING_VAR}s Certificate List</h3>

          <div className="flex gap-4 items-center">
            <Input
              value={searchQuery}
              onChange={(e) => {
                setPage(1);
                setSearchQuery(e.target.value);
              }}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />
          </div>

          <Table
            head={
              <>
                <TableHead>No.</TableHead>
                <TableHead onSort={(order) => sort("thingName", order)}>{THING_VAR} Name</TableHead>
                <TableHead onSort={(order) => sort("status", order)}>Status</TableHead>
                <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
                <TableHead onSort={(order) => sort("expiration", order)}>Expiration</TableHead>
              </>
            }
            body={
              isCertificateLoading ? (
                <TableRowsSkeleton />
              ) : !certificateList?.certificates?.length || !certificateList ? (
                <DataNotFound
                  title={!certificateList ? "SomeThing went wrong!" : "No Certificates Available"}
                  isTable
                />
              ) : (
                certificateList.certificates.toSorted(sortFn).map((certificate, i) => (
                  <tr
                    key={certificate.id}
                    className="cursor-pointer"
                    onClick={() => navigate(`${certificate.id}`)}
                  >
                    <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                    <TableRow title>{certificate.thingName}</TableRow>
                    <TableRow>
                      <Label
                        className=""
                        color={certificate.status === "active" ? "green" : "red"}
                        text={certificate.status}
                      />
                    </TableRow>
                    <TableRow>{convetUTCToLocal(certificate.createdAt)}</TableRow>
                    <TableRow>{convetUTCToLocal(certificate.expiration)}</TableRow>
                  </tr>
                ))
              )
            }
            pagination={{
              page,
              setPage,
              setLimit,
              totalPages: certificateList?.pages || 1
            }}
          />
        </Card>
      </section>
      <CreateThingCertificate ref={createCertificateRef} />
    </>
  );
};

export default Certificates;
