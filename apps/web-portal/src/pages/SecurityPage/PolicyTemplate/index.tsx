import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import usePolicyTemplateList from "@hooks/security/usePolicyTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Plus, Search } from "lucide-react";
import { ElementRef, useRef } from "react";

import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";
import Button from "../../../components/Button";
import { TableHead, TableRow } from "../../../components/Table";
import Table from "../../../components/Table/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import CreateTemplateDrawer from "./CreateTemplateDrawer";

const PolicyTemplate = () => {
  const templateDrawerRef = useRef<ElementRef<typeof CreateTemplateDrawer>>(null);

  const navigate = useCustomNavigate();
  const { page, setPage, limit, setLimit, searchQuery, setSearchQuery } = useTablePagination();

  const { data: permissions } = useUserGroupPermissions();

  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: policyTemplate, isLoading: isPolicyTemplateLoading } = usePolicyTemplateList({
    page,
    search: debouncedSearchQuery,
    limit
  });

  return (
    <main className="space-y-4">
      <HeaderSection
        title={`Policy Templates`}
        description="Manage your policy templates"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            noAccess={permissions.policy !== "write"}
            onClick={() => {
              templateDrawerRef.current?.openDrawer();
            }}
          >
            Add Template
          </Button>
        }
      />

      <Card className="space-y-4">
        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-1 space-y-4"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("templateName", order)}>Template Name</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("productCount", order)}>Product Count</TableHead>
            </>
          }
          body={
            isPolicyTemplateLoading ? (
              <TableRowsSkeleton />
            ) : !policyTemplate || !policyTemplate?.templates.length ? (
              <DataNotFound title="No Policy Template Available" isTable />
            ) : (
              policyTemplate.templates.toSorted(sortFn).map((policy, i) => (
                <tr
                  key={policy.templateName}
                  className="cursor-pointer"
                  onClick={() => navigate(`${policy.templateName}`)}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title data-testid={policy.templateName}>
                    {policy.templateName}
                  </TableRow>
                  <TableRow>{convetUTCToLocal(policy.createdAt)}</TableRow>
                  <TableRow>{policy.productCount || "0"}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: policyTemplate?.pages || 1
          }}
        />
      </Card>

      <CreateTemplateDrawer ref={templateDrawerRef} />
    </main>
  );
};

export default PolicyTemplate;
