import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useAuthenticationList from "@hooks/security/useAuthenticationList";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { IconButton } from "@mui/material";
import { AuthUser } from "@src/features/features";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Edit3, Plus, Search, Trash } from "lucide-react";
import { ElementRef, useRef, useState } from "react";
import { deleteAuthorization } from "../../../api/partner";
import Button from "../../../components/Button";
import Input from "../../../components/Input";
import { TableHead, TableRow } from "../../../components/Table";
import Table from "../../../components/Table/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import { showSuccessToast } from "../../../utils";
import { convetUTCToLocal } from "../../UserTypes/Tracking/HomeSection/utils";
import UserAuthDetailsSidebar from "./AuthenticationDetailsSidebar";
import CreateAuthorization from "./CreateAuthorization";
import UpdateAuthorization from "./UpdateAuthorization";

const UserAuth = () => {
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showUpdateUserModal, setShowUpdateUserModal] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [searchQuery, setSearchQuery] = useState("");
  const authDetailsSideBarRef = useRef<ElementRef<typeof UserAuthDetailsSidebar>>(null);
  const [deleteUser, setDeleteUser] = useState<{ authName: string; authType: string } | null>(null);
  const [editUser, setEditUser] = useState<AuthUser | null>(null);
  const [loadingBtn, setLoadingBtn] = useState(false);

  const [sortFn, sort] = useTableSort();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: permissions } = useUserGroupPermissions();

  const {
    data: authorizationList,
    isLoading: authLoading,
    isRefetching,
    refetch: refetchAuthorization
  } = useAuthenticationList({ page, limit, search: debouncedSearchQuery });

  const submitHandler = async () => {
    setLoadingBtn(true);
    try {
      await deleteAuthorization({
        username: deleteUser?.authName,
        authType: deleteUser?.authType
      });
      setTimeout(() => {
        refetchAuthorization();
      }, 1000);
      showSuccessToast("User Auth Deleted successfully");
      setDeleteUser(null);
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
      } else {
        console.error("An unexpected error occurred");
      }
    } finally {
      setLoadingBtn(false);
    }
  };

  return (
    <main className="space-y-4">
      <HeaderSection
        title={`Authentication`}
        description="Manage your Authentication"
        actions={
          <Button
            noAccess={permissions.authentication !== "write"}
            onClick={() => setShowAddUserModal(true)}
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
          >
            Add Authentication
          </Button>
        }
      />
      <Card className="space-y-4">
        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-1"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("username", order)}>User Name</TableHead>
              <TableHead onSort={(order) => sort("authType", order)}>Auth Type</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
              <TableHead>Actions</TableHead>
            </>
          }
          body={
            authLoading || isRefetching ? (
              <TableRowsSkeleton />
            ) : !authorizationList?.authList?.length ? (
              <DataNotFound title="No Authentication Available" isTable />
            ) : (
              authorizationList?.authList.toSorted(sortFn).map((auth, i: number) => (
                <tr
                  key={auth.username}
                  className="cursor-pointer"
                  onClick={() => {
                    authDetailsSideBarRef.current?.openDrawer({
                      authName: auth.username,
                      authType: auth.authType as "custom" | "thing"
                    });
                  }}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{auth.username}</TableRow>
                  <TableRow>{auth.authType}</TableRow>

                  <TableRow>{convetUTCToLocal(auth.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(auth.updatedAt)}</TableRow>
                  <TableRow>
                    <IconButton
                      disabled={permissions.authentication !== "write"}
                      className="!p-2 !mr-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeleteUser({ authName: auth.username, authType: auth.authType });
                      }}
                    >
                      <Trash size={INPUT_ICON_SIZE} />
                    </IconButton>

                    <IconButton
                      disabled={permissions.authentication !== "write"}
                      className="!p-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowUpdateUserModal(true);
                        setEditUser(auth);
                      }}
                    >
                      <Edit3 size={INPUT_ICON_SIZE} />
                    </IconButton>
                  </TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: authorizationList?.pages || 1
          }}
        />
      </Card>
      <CreateAuthorization show={showAddUserModal} setShow={setShowAddUserModal} />
      <UpdateAuthorization
        show={showUpdateUserModal}
        setShow={setShowUpdateUserModal}
        authDetails={editUser}
        setAuthDetails={setEditUser}
      />

      <UserAuthDetailsSidebar ref={authDetailsSideBarRef} />
      <ConfirmPrompt
        show={Boolean(deleteUser)}
        validate
        item={deleteUser?.authName}
        onCancel={() => setDeleteUser(null)}
        loading={loadingBtn}
        onConfirm={() => {
          submitHandler();
        }}
      />
    </main>
  );
};

export default UserAuth;
