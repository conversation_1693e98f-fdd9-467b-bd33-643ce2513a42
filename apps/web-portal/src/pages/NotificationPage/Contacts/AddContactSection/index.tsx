import Button from "@components/Button";
import HeadingIcon from "@components/HeadingIcon";
import PageSekeleton from "@components/PageSekeleton";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useContactConfig from "@hooks/classic/useContactConfig";
import useContactDetails from "@hooks/notifications/useContactDetials";
import useCreateContact from "@hooks/notifications/useCreateContact";
import useUpdateContact from "@hooks/notifications/useUpdateContact";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { Check, Plus, User, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import ContactDetails from "./ContactDetails";
import ContactDnd from "./ContactDnd";
import ContactSidebar from "./ContactSidebar";
import NotificationConfig from "./NotificationConfig";

type TimeSlot = {
  startTime: Date | null;
  endTime: Date | null;
  id: string;
};

type DoNotDisturb = {
  enabled: boolean;
  slots: TimeSlot[];
};

type FormValues = {
  name: string;
  email: string;
  address: string;
  country: string;
  pincode: string | number | null;
  phone: string | number | null;
  doNotDisturbStatus?: DoNotDisturb;
};

const dateFormatter = (value: Date) => {
  const dateTransform = new Date(value).toISOString();
  return dateTransform;
};

const validateContactForm = (formData: FormValues) => {
  const { name, email, phone, doNotDisturbStatus, country, address, pincode } = formData;

  // Basic required field validation
  if (!name?.trim()) {
    showErrorToast("Please enter Contact name");
    return false;
  }

  if (!email?.trim()) {
    showErrorToast("Please enter an email address");
    return false;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    showErrorToast("Please enter a valid email address");
    return false;
  }

  if (!String(phone)?.trim()) {
    showErrorToast("Please enter a valid phone number");
    return false;
  }
  if (!address?.trim()) {
    showErrorToast("Please enter Address");
    return false;
  }

  if (!country?.trim()) {
    showErrorToast("Please enter a country name");
    return false;
  }

  if (!String(pincode)?.trim()) {
    showErrorToast("Please enter a country name");
    return false;
  }

  // Validate Do Not Disturb slots if enabled
  if (doNotDisturbStatus?.enabled && doNotDisturbStatus.slots.length > 0) {
    for (let i = 0; i < doNotDisturbStatus.slots.length; i++) {
      const slot = doNotDisturbStatus.slots[i];

      if (!slot.startTime) {
        showErrorToast(`Do Not Disturb slot ${i + 1} is missing a start time`);
        return false;
      }

      if (!slot.endTime) {
        showErrorToast(`Do Not Disturb slot ${i + 1} is missing an end time`);
        return false;
      }

      // Optional: Validate that end time is after start time
      if (new Date(slot.startTime) >= new Date(slot.endTime)) {
        showErrorToast(`Do Not Disturb slot ${i + 1} has end time before or equal to start time`);
        return false;
      }
    }
  }

  // All validations passed
  return true;
};

const AddContactSection = () => {
  const contactId = useParams().contactId!;

  const [activeSection, setActiveSection] = useState("details");
  const navigate = useCustomNavigate();
  const {
    name,
    email,
    address,
    country,
    pincode,
    phone,
    alertNotificationTypes,
    systemNotificationTypes,
    doNotDisturbStatus,
    resetConfig,
    updateContactConfig
  } = useContactConfig();

  const { data: contactDetails, isLoading: contactLoading } = useContactDetails({
    contactId,
    enabled: Boolean(contactId)
  });

  const createContactMutation = useCreateContact({
    onSuccess: () => {
      navigate("/notifications/contacts");
      showSuccessToast("Contact created successfully");
      queryClient.invalidateQueries({ queryKey: ["contact-list"] });
    }
  });

  const updateContactMutation = useUpdateContact({
    onSuccess: () => {
      showSuccessToast("Contact updated successfully");
      navigate(-1);
      queryClient.invalidateQueries({ queryKey: ["contactDetail", contactId] });
    }
  });

  useEffect(() => {
    resetConfig();
    if (contactId && !contactLoading && contactDetails) {
      updateContactConfig(contactDetails);
    }
  }, [contactDetails]);

  const submitHandler = async (e:React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (
      !validateContactForm({ name, email, address, country, pincode, phone, doNotDisturbStatus })
    ) {
      // Validation failed, stop form submission
      return;
    }

    const formattedDoNotDisturbStatus = {
      enabled: doNotDisturbStatus.enabled,
      slots: doNotDisturbStatus.enabled
        ? doNotDisturbStatus.slots.map((slot) => ({
            startTime: slot.startTime ? dateFormatter(slot.startTime) : null,
            endTime: slot.endTime ? dateFormatter(slot.endTime) : null,
            id: slot.id
          }))
        : []
    };

    const payload = {
      contactDetails: {
        name,
        email,
        phone,
        address,
        country,
        pincode
      },
      preferences: [alertNotificationTypes, systemNotificationTypes],
      doNotDisturb: formattedDoNotDisturbStatus
    };

    if (contactId) {
      updateContactMutation.mutate({ contactId, body: payload });
    } else {
      createContactMutation.mutate(payload);
    }
  };

  return (
    <div className="mt-2 space-y-4">
      {contactId && contactLoading ? (
        <PageSekeleton />
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <HeadingIcon Icon={User} title="Contact" />

            <div className="flex gap-2">
              <Button
                color="gray"
                onClick={() => navigate(-1)}
                startIcon={<X size={BUTTON_ICON_SIZE} />}
              >
                Cancel
              </Button>
              <Button
                startIcon={
                  contactId ? <Check size={BUTTON_ICON_SIZE} /> : <Plus size={BUTTON_ICON_SIZE} />
                }
                onClick={submitHandler}
                loading={createContactMutation.isPending || updateContactMutation.isPending}
              >
                {contactId ? "Update" : "Add"} Contact
              </Button>
            </div>
          </div>
          <div className="flex gap-6">
            <ContactSidebar activeSection={activeSection} setActiveSection={setActiveSection} />
            <div className="flex-1">
              {activeSection === "details" && <ContactDetails />}

              {activeSection === "notificationConfig" && <NotificationConfig />}

              {activeSection === "contactDnd" && <ContactDnd />}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddContactSection;
