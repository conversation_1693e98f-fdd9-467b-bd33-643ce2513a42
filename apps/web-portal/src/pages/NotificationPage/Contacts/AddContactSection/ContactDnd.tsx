import NoDataFound from "@components/NoDataFound";
import { Button } from "@components/shadcn/components/button";
import { DateTimePicker } from "@components/shadcn/components/date-time-picker";
import { Switch } from "@components/shadcn/components/switch";
import { Card } from "@components/ui";
import useContactConfig from "@hooks/classic/useContactConfig";
import { generateRandomId } from "@src/pages/MonitorPage/utils";
import { Plus, Trash2 } from "lucide-react";

const ContactDnd = () => {
  const { doNotDisturbStatus, editDetails } = useContactConfig();

  const addNewDnd = () => {
    editDetails("doNotDisturbStatus", {
      ...doNotDisturbStatus,
      slots: [
        ...doNotDisturbStatus.slots,
        { startTime: null, endTime: null, id: generateRandomId(16) }
      ]
    });
  };

  const editDnd = (id: string, field: string, value: any) => {
    editDetails("doNotDisturbStatus", {
      ...doNotDisturbStatus,
      slots: doNotDisturbStatus.slots.map((slot) => {
        if (slot.id === id) {
          return {
            ...slot,
            [field]: value
          };
        }
        return slot;
      })
    });
  };
  const removeDnd = (id: string) => {
    editDetails("doNotDisturbStatus", {
      ...doNotDisturbStatus,
      slots: doNotDisturbStatus.slots.filter((slot) => slot.id !== id)
    });
  };

  return (
    <Card className="space-y-4">
      <div className="flex flex-row justify-between items-center">
        <div className="flex gap-4 items-center">
          <h3 className=" text-lg font-medium">Do Not Disturb</h3>
          <Switch
            checked={doNotDisturbStatus.enabled}
            onCheckedChange={(val) => {
              if (!val) {
                editDetails("doNotDisturbStatus", {
                  enabled: val,
                  slots: []
                });

                return;
              }
              editDetails("doNotDisturbStatus", {
                ...doNotDisturbStatus,
                enabled: val
              });
            }}
          />
        </div>

        {doNotDisturbStatus.enabled && (
          <Button size="sm" onClick={addNewDnd}>
            <Plus className="h-4 w-4 mr-2" /> Add new DnD
          </Button>
        )}
      </div>
      <div className="grid grid-cols-2 gap-4">
        {doNotDisturbStatus.enabled ? (
          !doNotDisturbStatus.slots.length ? (
            <NoDataFound className="col-span-2" content="No Do Not Disturb is added" />
          ) : (
            doNotDisturbStatus.slots.map((slot) => (
              <Card variant="second" className="flex gap-4 items-center">
                <div className="flex-1">
                  <label className="input-label-text">Start Date *</label>
                  <DateTimePicker
                    value={slot.startTime ?? undefined}
                    onChange={(newValue) => {
                      if (!newValue) return;
                      editDnd(slot.id, "startTime", newValue);
                    }}
                  />
                </div>
                <div className="flex-1">
                  <label className="input-label-text">End Date *</label>
                  <DateTimePicker
                    value={slot.endTime ?? undefined}
                    onChange={(newValue) => {
                      if (!newValue) return;
                      editDnd(slot.id, "endTime", newValue);
                    }}
                  />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeDnd(slot.id)}
                  className="h-8 w-8 mt-6"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </Card>
            ))
          )
        ) : (
          <NoDataFound className="col-span-2" content="Do Not Disturb is not enabled" />
        )}
      </div>
    </Card>
  );
};

export default ContactDnd;
