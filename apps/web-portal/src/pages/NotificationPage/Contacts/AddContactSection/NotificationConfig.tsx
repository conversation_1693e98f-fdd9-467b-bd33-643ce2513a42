import { Switch } from "@components/shadcn/components/switch";
import { Card } from "@components/ui";
import useContactConfig, { NotificationType } from "@hooks/classic/useContactConfig";
import { AlertTriangle, Bell, Mail, MessageSquare, Shield, Users } from "lucide-react";

const NotificationConfig = () => {
  const { alertNotificationTypes, systemNotificationTypes, editDetails } = useContactConfig();
  const handleChange = (category:string, type:string) => {
    if (category === "alertNotificationTypes") {
      editDetails("alertNotificationTypes", {
        ...alertNotificationTypes,
        [type]: !alertNotificationTypes[type as keyof NotificationType]
      });
    } else if (category === "systemNotificationTypes") {
      editDetails("systemNotificationTypes", {
        ...systemNotificationTypes,
        [type]: !systemNotificationTypes[type as keyof NotificationType]
      });
    }
  };
  return (
    <Card className="space-y-4">
      <h3 className=" text-lg font-medium">Notification Config </h3>
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2  divide-x">
          {/* Alert Notifications */}
          <div className="space-y-4 !pr-8">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-500" />
                <h3 className="font-medium">Alert Notifications</h3>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  <span className="">Push Notifications</span>
                </div>
                <Switch
                  checked={alertNotificationTypes.push}
                  onCheckedChange={() => handleChange("alertNotificationTypes", "push")}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <span>SMS</span>
                </div>
                <Switch
                  checked={alertNotificationTypes.sms}
                  onCheckedChange={() => handleChange("alertNotificationTypes", "sms")}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <span>Email</span>
                </div>
                <Switch
                  checked={alertNotificationTypes.email}
                  onCheckedChange={() => handleChange("alertNotificationTypes", "email")}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Teams</span>
                </div>
                <Switch
                  checked={alertNotificationTypes.teams}
                  onCheckedChange={() => handleChange("alertNotificationTypes", "teams")}
                />
              </div>
            </div>
          </div>

          {/* System Notifications */}
          <div className="space-y-4 !pl-8">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-emerald-500" />
                <h3 className="font-medium">System Notifications</h3>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  <span>Push Notifications</span>
                </div>
                <Switch
                  checked={systemNotificationTypes.push}
                  onCheckedChange={() => handleChange("systemNotificationTypes", "push")}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <span>SMS</span>
                </div>
                <Switch
                  checked={systemNotificationTypes.sms}
                  onCheckedChange={() => handleChange("systemNotificationTypes", "sms")}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <span>Email</span>
                </div>
                <Switch
                  checked={systemNotificationTypes.email}
                  onCheckedChange={() => handleChange("systemNotificationTypes", "email")}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Teams</span>
                </div>
                <Switch
                  checked={systemNotificationTypes.teams}
                  onCheckedChange={() => handleChange("systemNotificationTypes", "teams")}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NotificationConfig;
