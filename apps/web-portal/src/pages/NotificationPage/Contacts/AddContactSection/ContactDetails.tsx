import Input from "@components/Input";
import { PhoneInput } from "@components/PhoneNumberInput";
import { Card } from "@components/ui";
import useContactConfig from "@hooks/classic/useContactConfig";
import * as RPNInput from "react-phone-number-input";
import { useParams } from "react-router-dom";

const ContactDetails = () => {
  const contactId = useParams().contactId!;

  const { name, email, address, country, pincode, phone, editDetails } = useContactConfig();

  return (
    <Card className="space-y-4">
      <h3 className=" text-lg font-medium">Contact Details</h3>
      <div className="space-y-4">
        <Input
          label="Contact Name"
          required
          value={name}
          onChange={(e) => editDetails("name", e.target.value)}
          placeholder="Contact Name"
        />

        <div className="grid grid-cols-2 gap-4">
          <Input
            label="Email"
            disabled={Boolean(contactId)}
            required
            placeholder="Enter email"
            value={email}
            onChange={(e) => editDetails("email", e.target.value)}
          />
          <PhoneInput
            required
            label="Phone Number"
            placeholder="Phone Number"
            onChange={(e) => {
              editDetails("phone", e);
            }}
            value={phone as RPNInput.Value}
          />
        </div>
        <Input
          label="Address"
          required
          placeholder="Address"
          value={address}
          onChange={(e) => editDetails("address", e.target.value)}
        />

        <div className="grid grid-cols-2 gap-4">
          <Input
            label="Country"
            required
            placeholder="Enter Country"
            value={country}
            onChange={(e) => editDetails("country", e.target.value)}
          />
          <Input
            label="Pincode"
            required
            placeholder="Enter Pincode"
            value={pincode || ""}
            onChange={(e) => editDetails("pincode", e.target.value)}
          />
        </div>
      </div>
    </Card>
  );
};

export default ContactDetails;
