import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useContactTeamsList from "@hooks/notifications/useContactTeamsList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { Plus, Search } from "lucide-react";
import CreateContactTeams from "./CreateContactTeams";
import { ElementRef, useRef } from "react";

const ContactTeamsTab = () => {
  const [sortFn, sort] = useTableSort();
  const { setPage, searchQuery, setSearchQuery } = useTablePagination();
  const createContactTeamsRef = useRef<ElementRef<typeof CreateContactTeams>>(null);

  const { data: teamsList, isLoading: teamsListLoading } = useContactTeamsList({});
  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();

  return (
    <Card className="space-y-4">
      <h3 className="heading-2">Groups List</h3>
      <div className="flex items-center  gap-4">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setPage(1);
            setSearchQuery(e.target.value);
          }}
          className="flex-1"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />

        <Button
          startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          onClick={() => createContactTeamsRef.current?.openCreateContactTeamsSidebar()}
          noAccess={permissions.contacts !== "write"}
        >
          Create Group
        </Button>
      </div>
      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Group Name</TableHead>
            <TableHead onSort={(order) => sort("description", order)}>Description</TableHead>
            <TableHead onSort={(order) => sort("createAt", order)}>Create Date</TableHead>
          </>
        }
        body={
          teamsListLoading ? (
            <TableRowsSkeleton />
          ) : !teamsList?.length || !teamsList ? (
            <DataNotFound
              isTable
              title={!teamsList ? "Something went wrong" : "No Contacts Available"}
            />
          ) : (
            teamsList.toSorted(sortFn).map((team, i) => (
              <tr
                key={team.id}
                className="cursor-pointer"
                onClick={() => navigate(`contacts-teams/${team.id}`)}
                data-testid={team.id}
              >
                <TableRow>{i + 1}</TableRow>
                <TableRow title>{team.name}</TableRow>
                <TableRow>{team.description}</TableRow>
                <TableRow>{convetUTCToLocal(team.createdAt)}</TableRow>
              </tr>
            ))
          )
        }
      />
      <CreateContactTeams ref={createContactTeamsRef} />
    </Card>
  );
};

export default ContactTeamsTab;
