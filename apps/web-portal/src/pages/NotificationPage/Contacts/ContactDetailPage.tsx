import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import { CardDescription } from "@components/shadcn/components/card";
import { Checkbox } from "@components/shadcn/components/checkbox";
import { DateTimePicker } from "@components/shadcn/components/date-time-picker";
import { Tabs, TabsContent } from "@components/shadcn/components/tabs";
import TabsListV2 from "@components/Tabs/TabsListV2";
import TabsTriggerV2 from "@components/Tabs/TabsTriggerV2";
import { Card } from "@components/ui";
import { UpdateContactConfigInput } from "@hooks/classic/useContactConfig";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useContactDetails from "@hooks/notifications/useContactDetials";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import {
  AlertCircle,
  Bell,
  Contact,
  Edit2,
  Mail,
  MessageSquare,
  Settings,
  Trash,
  Users
} from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { deleteContact } from "../../../api/cloud";
import Button from "../../../components/Button";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import { showErrorToast, showSuccessToast } from "../../../utils";
import ContactShift from "./ContactShift";

type TabsType = "preferences" | "dnd" | "shifts";

type NotificationTypeStyleProps = {
  label: string;
  checked: boolean;
  Icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
};

export const getNotificationType = (type:string, notificationData:UpdateContactConfigInput) => {
  const notificationType = notificationData?.preferences.find(
    (item) => item.notificationType === type
  );
  return notificationType;
};

const ContactDetailPage = () => {
  const contactId = useParams().contactId!;

  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [tabValue, setTabValue] = useState("shifts");

  const navigate = useCustomNavigate();
  const { data: permissions } = useUserGroupPermissions();

  const { data: contactDetails, isLoading: contactLoading } = useContactDetails({ contactId });

  const handleContactDelete = async () => {
    setLoadingBtn(true);
    const resp = await deleteContact(contactId);
    setLoadingBtn(false);
    setShowDeletePrompt(false);
    if (resp.status === "Success") {
      showSuccessToast("Contact deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["contact-list"] });
      navigate(-1);
    } else {
      showErrorToast(resp.message);
    }
  };
  const handleChange = (val: TabsType) => {
    setTabValue(val);
  };

  return (
    <main className="space-y-6">
      <Card className="space-y-6">
        <div className="between">
          <HeadingIcon Icon={Contact} title="Contact Details" />

          <div className="flex gap-4">
            <Button
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
              noAccess={permissions.contacts !== "write"}
              onClick={() => {
                navigate("edit");
              }}
              color="gray"
            >
              Edit
            </Button>
            <Button
              noAccess={permissions.contacts !== "write"}
              startIcon={<Trash size={BUTTON_ICON_SIZE} />}
              onClick={() => setShowDeletePrompt(true)}
              color="red"
              loading={loadingBtn}
            >
              Delete
            </Button>
          </div>
        </div>

        {contactLoading ? (
          <CardLoadingSkeleton />
        ) : !contactDetails ? (
          <DataNotFound title="Something went wrong" />
        ) : (
          <section className="grid grid-cols-3 gap-4">
            <DetailsCell title="Name" data={contactDetails.name} />
            <DetailsCell title="Email" data={contactDetails.email} />

            <DetailsCell title="Phone" data={contactDetails.phone} />
            <DetailsCell
              title="User Type"
              data={
                <Label
                  text={contactDetails.userType}
                  color={contactDetails.userType === "platform" ? "green" : "orange"}
                />
              }
            />
            <DetailsCell dataClassName="truncate" title="Address" data={contactDetails.address} />

            <DetailsCell title="Pincode" data={contactDetails.pincode} />

            <DetailsCell title="Country" data={contactDetails.country} />

            {/* <DetailsCell title="Whatsapp" data={contactDetails.whatsapp} /> */}
            <DetailsCell title="Created At" data={convetUTCToLocal(contactDetails.createdAt)} />

            <DetailsCell title="Updated At" data={convetUTCToLocal(contactDetails.updatedAt)} />
          </section>
        )}
      </Card>
      <Card>
        {contactLoading || !contactDetails ? (
          <CardLoadingSkeleton className="w-full" />
        ) : (
          <Tabs value={tabValue}>
            <TabsListV2>
              <TabsTriggerV2 value="shifts" onClick={() => handleChange("shifts")}>
                Shifts
              </TabsTriggerV2>
              <TabsTriggerV2 value="preferences" onClick={() => handleChange("preferences")}>
                Notification Preferences
              </TabsTriggerV2>

              <TabsTriggerV2 value="dnd" onClick={() => handleChange("dnd")}>
                Do Not Disturb
              </TabsTriggerV2>
            </TabsListV2>

            <TabsContent value="preferences">
              <PreferencesTab contactDetails={contactDetails} />
            </TabsContent>
            <TabsContent value="dnd">
              <DoNotDisturbTab contactDetails={contactDetails} />
            </TabsContent>

            <TabsContent value="shifts">
              {!contactDetails?.shifts.length ? (
                <DataNotFound title="No Shifts Added" />
              ) : (
                <ContactShift teamsData={contactDetails?.shifts} />
              )}
            </TabsContent>
          </Tabs>
        )}
      </Card>

      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={contactDetails?.name}
        loading={loadingBtn}
        onCancel={() => setShowDeletePrompt(false)}
        onConfirm={() => {
          handleContactDelete();
        }}
      />
    </main>
  );
};

export default ContactDetailPage;

const NotificationTypeStyle = ({ label, checked, Icon }:NotificationTypeStyleProps) => {
  return (
    <Card variant="third" className="flex items-center justify-between ">
      <div className="flex items-center gap-3">
        <Icon className="h-5 w-5 " />
        <label className="font-medium">{label}</label>
      </div>
      <Checkbox checked={checked} />
    </Card>
  );
};

export const PreferencesTab = ({ contactDetails }:{contactDetails:UpdateContactConfigInput} ) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="">
        <div className="flex items-start  space-x-2">
          <AlertCircle className="h-6 w-6 text-amber-500 " />
          <div>
            <h3 className="heading-3">Alert Notifications</h3>
            <CardDescription>Time-sensitive alerts that require attention</CardDescription>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-6">
          <NotificationTypeStyle
            label="Push Notification
"
            checked={getNotificationType("alert", contactDetails)?.push?? false}
            Icon={Bell}
          />
          <NotificationTypeStyle
            label="Email"
            checked={getNotificationType("alert", contactDetails)?.email ?? false}
            Icon={Mail}
          />
          <NotificationTypeStyle
            label="SMS"
            checked={getNotificationType("alert", contactDetails)?.sms ?? false}
            Icon={MessageSquare}
          />
          <NotificationTypeStyle
            label="Teams"
            checked={getNotificationType("alert", contactDetails)?.teams ?? false}
            Icon={Users}
          />
        </div>
      </Card>
      <Card className="">
        <div className="flex items-start space-x-2">
          <Settings className="h-6 w-6 text-blue-500" />
          <div>
            <h3 className="heading-3">System Generated</h3>
            <CardDescription className="">Automated notifications from the system</CardDescription>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-6">
          <NotificationTypeStyle
            label="Push Notification
"
            checked={getNotificationType("system_generated", contactDetails)?.push?? false}
            Icon={Bell}
          />
          <NotificationTypeStyle
            label="Email"
            checked={getNotificationType("system_generated", contactDetails)?.email?? false}
            Icon={Mail}
          />
          <NotificationTypeStyle
            label="SMS"
            checked={getNotificationType("system_generated", contactDetails)?.sms?? false}
            Icon={MessageSquare}
          />
          <NotificationTypeStyle
            label="Teams"
            checked={getNotificationType("system_generated", contactDetails)?.teams?? false}
            Icon={Users}
          />
        </div>
      </Card>
    </div>
  );
};
export const DoNotDisturbTab = ({ contactDetails }:{contactDetails:UpdateContactConfigInput}) => {
  return contactDetails?.doNotDisturbStatus?.enabled && contactDetails?.doNotDisturbStatus.slots.length ? (
    <div className="grid grid-cols-2 gap-4">
      {contactDetails?.doNotDisturbStatus.slots.map((slot, idx) => (
        <Card variant="second" className=" space-y-4 ">
          <h3 className="heading-3">Do Not Disturb {idx + 1}</h3>
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <p className="input-label-text">Start Time *</p>
              <DateTimePicker popupDisabled value={slot.startTime ? new Date(slot.startTime) : undefined} />
            </div>
            <div className="flex-1">
              <p className="input-label-text">End Time *</p>
              <DateTimePicker popupDisabled value={slot.endTime ? new Date(slot.endTime) : undefined} />
            </div>
          </div>
        </Card>
      ))}
    </div>
  ) : (
    <DataNotFound title="It is Disabled or No DnD Slots Added" />
  );
};
