import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import {
  BUTTON_ICON_SIZE,
  DEFAULT_PAGE_COUNT,
  INPUT_ICON_SIZE
} from "@frontend/shared/config/defaults";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useContactList from "@hooks/notifications/useContactList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { Plus, Search } from "lucide-react";
import { useState } from "react";

const ContactListTab = () => {
  const { page, setPage, searchQuery, setSearchQuery } = useTablePagination();
  const navigate = useCustomNavigate();
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [sortFn, sort] = useTableSort();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();

  const { data: contactList, isLoading: isContactListLoading } = useContactList({
    page,
    search: debouncedSearchQuery,
    limit
  });

  return (
    <Card className="space-y-4">
      <h3 className="heading-2">Contacts List</h3>
      <div className="flex items-center  gap-4">
        <Input
          value={searchQuery}
          onChange={(e) => {
            setPage(1);
            setSearchQuery(e.target.value);
          }}
          className="flex-1"
          placeholder="Search"
          endIcon={<Search size={INPUT_ICON_SIZE} />}
        />

        <Button
          startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          onClick={() => navigate("add-contact")}
          noAccess={permissions.contacts !== "write"}
        >
          New Contact
        </Button>
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Contact Name</TableHead>
            <TableHead onSort={(order) => sort("email", order)}>Email</TableHead>
            <TableHead onSort={(order) => sort("phone", order)}>Phone</TableHead>
            <TableHead onSort={(order) => sort("userType", order)}>User Type</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
          </>
        }
        body={
          isContactListLoading ? (
            <TableRowsSkeleton />
          ) : !contactList?.contacts.length || !contactList ? (
            <DataNotFound
              isTable
              title={!contactList ? "Something went wrong" : "No Contacts Available"}
            />
          ) : (
            contactList.contacts.toSorted(sortFn).map((contact, i) => (
              <tr
                key={contact.contactId}
                className="cursor-pointer"
                onClick={() => navigate(`${contact.contactId}`)}
                data-testid={contact.email}
              >
                <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                <TableRow title>{contact.name}</TableRow>
                <TableRow>{contact.email}</TableRow>
                <TableRow>{contact.phone}</TableRow>
                <TableRow>
                  <Label
                    text={contact.userType}
                    color={contact.userType === "platform" ? "green" : "orange"}
                  />
                </TableRow>
                <TableRow>{convetUTCToLocal(contact.createdAt)}</TableRow>
                <TableRow>{convetUTCToLocal(contact.updatedAt)}</TableRow>
              </tr>
            ))
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: contactList?.pages || 1
        }}
      />
    </Card>
  );
};

export default ContactListTab;
