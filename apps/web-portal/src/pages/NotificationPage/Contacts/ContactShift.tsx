"use client";

import { useState } from "react";
// import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@components/shadcn/components/badge";
import { <PERSON><PERSON> } from "@components/shadcn/components/button";
import { Card, CardContent } from "@components/shadcn/components/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@components/shadcn/components/tooltip";
import { ArrowRight, ChevronDown, ChevronUp } from "lucide-react";

interface Shift {
  name: string;
  startTime: string;
  endTime: string;
}

interface Team {
  id: number;
  name: string;
  enabled: boolean;
  shifts: Shift[];
}

export default function ContactShift({ teamsData }: { teamsData: Team[] }) {
  const [expandedTeams, setExpandedTeams] = useState<Record<number, boolean>>({});

  // Hours to display in the timeline (all 24 hours)
  const hours = [
    "12 AM",
    "1 AM",
    "2 AM",
    "3 AM",
    "4 AM",
    "5 AM",
    "6 AM",
    "7 AM",
    "8 AM",
    "9 AM",
    "10 AM",
    "11 AM",
    "12 PM",
    "1 PM",
    "2 PM",
    "3 PM",
    "4 PM",
    "5 PM",
    "6 PM",
    "7 PM",
    "8 PM",
    "9 PM",
    "10 PM",
    "11 PM"
  ];

  // Toggle team expansion
  const toggleTeamExpansion = (teamId: number) => {
    setExpandedTeams((prev) => ({
      ...prev,
      [teamId]: !prev[teamId]
    }));
  };

  return (
    <div className="flex flex-col space-y-6">
      <Card>
        <CardContent>
          {/* Time markers */}
          <div className="flex border-b mb-4 pb-2 pt-4">
            <div className="w-[12rem] flex-shrink-0  font-medium">Schedules</div>
            <div className="flex-1 flex relative">
              {/* Hour markers */}
              {hours.map((hour, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div className="text-xs font-medium text-center">{hour}</div>
                  <div className="h-2 w-px bg-muted-foreground mt-1"></div>
                </div>
              ))}

              {/* Vertical grid lines */}
              {Array.from({ length: 25 }).map((_, index) => (
                <div
                  key={`grid-${index}`}
                  className="absolute h-full w-px bg-muted-foreground/10"
                  style={{
                    left: `${index * (100 / 24)}%`,
                    top: "100%"
                  }}
                ></div>
              ))}
            </div>
          </div>

          {/* Team rows */}
          <div className="space-y-6">
            {teamsData.map((team) => (
              <TeamTimelineRow
                key={team.id}
                team={team}
                isExpanded={!!expandedTeams[team.id]}
                toggleExpansion={() => toggleTeamExpansion(team.id)}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface TeamTimelineRowProps {
  team: Team;
  isExpanded: boolean;
  toggleExpansion: () => void;
}

function TeamTimelineRow({ team, isExpanded, toggleExpansion }: TeamTimelineRowProps) {
  // Detect overlapping shifts
  const shiftsWithOverlap = detectOverlappingShifts(team.shifts);
  const hasOverlaps = shiftsWithOverlap.some((s) => s.overlaps.length > 0);

  // Calculate row height based on number of overlapping shifts
  const getRowHeight = () => {
    if (!hasOverlaps) return "h-16";
    return isExpanded ? "h-auto" : "h-16";
  };

  return (
    <div className="flex flex-col">
      <div className="flex mb-1">
        <div className="w-[12rem] flex-shrink-0 pr-4">
          <div className="flex items-center">
            <div className="flex-1">
              <div className="font-medium">{team.name}</div>
              <div className="text-xs mt-1">
                <Badge variant={team.enabled ? "default" : "outline"}>
                  {team.enabled ? "Active" : "Inactive"}
                </Badge>
                {hasOverlaps && (
                  <Badge variant="outline" className="ml-1 border-red-500/70 text-red-500">
                    Overlaps
                  </Badge>
                )}
              </div>
            </div>
            {hasOverlaps && (
              <Button variant="ghost" size="icon" onClick={toggleExpansion} className="h-8 w-8">
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>

        <div className={`flex-1 relative ${getRowHeight()} bg-muted/30 rounded-md overflow-hidden`}>
          {/* Background grid for hours */}
          <div className="absolute inset-0 flex pointer-events-none">
            {Array.from({ length: 24 }).map((_, i) => (
              <div
                key={i}
                className="flex-1 border-l border-muted-foreground/10 first:border-l-0"
                style={{ borderLeftWidth: i % 6 === 0 ? "2px" : "1px" }}
              />
            ))}
          </div>

          {isExpanded ? (
            // Expanded view - show shifts in rows by overlap groups
            <ExpandedShiftsView shifts={shiftsWithOverlap} />
          ) : (
            // Collapsed view - show all shifts in a single row with overlap indicators
            shiftsWithOverlap.map((shiftData, index) => (
              <OvernightShiftBlock
                key={index}
                shift={shiftData.shift}
                index={index}
                hasOverlap={shiftData.overlaps.length > 0}
                isStacked={false}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
}

interface ShiftWithOverlap {
  shift: Shift;
  overlaps: number[]; // Indices of shifts that this one overlaps with
}

function detectOverlappingShifts(shifts: Shift[]): ShiftWithOverlap[] {
  return shifts.map((shift, index) => {
    const overlaps: number[] = [];

    // Check for overlaps with other shifts
    shifts.forEach((otherShift, otherIndex) => {
      if (index !== otherIndex) {
        if (shiftsOverlap(shift, otherShift)) {
          overlaps.push(otherIndex);
        }
      }
    });

    return { shift, overlaps };
  });
}

function shiftsOverlap(shift1: Shift, shift2: Shift): boolean {
  const start1 = timeToMinutes(shift1.startTime);
  const end1 = timeToMinutes(shift1.endTime);
  const start2 = timeToMinutes(shift2.startTime);
  const end2 = timeToMinutes(shift2.endTime);

  // Handle overnight shifts
  const isOvernight1 = end1 <= start1;
  const isOvernight2 = end2 <= start2;

  // For overnight shifts, we need to adjust the end time
  const adjustedEnd1 = isOvernight1 ? end1 + 24 * 60 : end1;
  const adjustedEnd2 = isOvernight2 ? end2 + 24 * 60 : end2;

  // Check for overlap
  if (isOvernight1 || isOvernight2) {
    // More complex check for overnight shifts
    if (isOvernight1 && isOvernight2) {
      // Both are overnight - they always overlap
      return true;
    } else if (isOvernight1) {
      // First shift is overnight
      return !(adjustedEnd1 <= start2 || start1 >= adjustedEnd2);
    } else {
      // Second shift is overnight
      return !(end1 <= start2 || start1 >= adjustedEnd2);
    }
  } else {
    // Simple case - neither shift is overnight
    return !(end1 <= start2 || start1 >= end2);
  }
}

interface ExpandedShiftsViewProps {
  shifts: ShiftWithOverlap[];
}

function ExpandedShiftsView({ shifts }: ExpandedShiftsViewProps) {
  // Group shifts by overlap
  const overlapGroups = groupShiftsByOverlap(shifts);

  return (
    <div className="flex flex-col h-auto">
      {overlapGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="relative h-12 w-full">
          {group.map((shiftIndex) => (
            <OvernightShiftBlock
              key={shiftIndex}
              shift={shifts[shiftIndex].shift}
              index={shiftIndex}
              hasOverlap={shifts[shiftIndex].overlaps.length > 0}
              isStacked={true}
              stackIndex={groupIndex}
            />
          ))}
        </div>
      ))}
    </div>
  );
}

// Group shifts into rows where no shifts in the same row overlap
function groupShiftsByOverlap(shifts: ShiftWithOverlap[]): number[][] {
  const groups: number[][] = [];
  const assigned: Record<number, boolean> = {};

  // Sort shifts by start time to optimize grouping
  const sortedIndices = shifts
    .map((_, index) => index)
    .sort((a, b) => {
      const startA = timeToMinutes(shifts[a].shift.startTime);
      const startB = timeToMinutes(shifts[b].shift.startTime);
      return startA - startB;
    });

  // Assign each shift to a group
  sortedIndices.forEach((shiftIndex) => {
    if (assigned[shiftIndex]) return;

    // Find or create a group for this shift
    let foundGroup = false;
    for (const group of groups) {
      // Check if this shift overlaps with any shift in the group
      const hasOverlap = group.some((groupShiftIndex) =>
        shifts[shiftIndex].overlaps.includes(groupShiftIndex)
      );

      if (!hasOverlap) {
        group.push(shiftIndex);
        assigned[shiftIndex] = true;
        foundGroup = true;
        break;
      }
    }

    // If no existing group works, create a new one
    if (!foundGroup) {
      groups.push([shiftIndex]);
      assigned[shiftIndex] = true;
    }
  });

  return groups;
}

function OvernightShiftBlock({
  shift,
  hasOverlap,
  isStacked,
}: {
  shift: Shift;
  index: number;
  hasOverlap: boolean;
  isStacked: boolean;
  stackIndex?: number;
}) {
  // Convert time strings to minutes since midnight for positioning
  const startMinutes = timeToMinutes(shift.startTime);
  const endMinutes = timeToMinutes(shift.endTime);

  // Check if this is an overnight shift (ends on the next day)
  const isOvernight = endMinutes <= startMinutes;

  // Determine color based on shift name
  const colorClass = getShiftColorClass(shift.name);

  // Adjust position for stacked view
  const topPosition = isStacked ? "0" : "2px";
  const height = isStacked ? "90%" : "75%";

  // Calculate the total duration for the tooltip
  const duration = isOvernight ? 24 * 60 - startMinutes + endMinutes : endMinutes - startMinutes;

  // Common styles for shift blocks
  const commonStyles = `absolute rounded-md ${colorClass} flex items-center justify-center px-2 
      ${hasOverlap ? "border-2 border-red-500/70" : ""}`;

  // Common tooltip content
  const tooltipContent = (
    <div className="space-y-1">
      <p className="font-medium capitalize">{shift.name}</p>
      <div className="text-xs">
        {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
      </div>
      <div className="text-xs">Duration: {formatDuration(duration)}</div>
      {isOvernight && <div className="text-xs font-medium">🌙 Overnight shift</div>}
      {hasOverlap && (
        <div className="text-xs text-red-500 font-medium">⚠️ Overlaps with other shifts</div>
      )}
    </div>
  );

  // UPDATED: For overnight shifts, we render two connected blocks
  if (isOvernight) {
    // First part: from start time to midnight (end of day)
    const firstPartWidth = ((24 * 60 - startMinutes) / (24 * 60)) * 100;

    // Second part: from midnight (start of day) to end time
    const secondPartWidth = (endMinutes / (24 * 60)) * 100;

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {/* First part of the overnight shift (start time to midnight) */}
            <div
              className={commonStyles}
              style={{
                left: `${(startMinutes / (24 * 60)) * 100}%`,
                width: `${firstPartWidth}%`,
                top: topPosition,
                height: height
              }}
            >
              <div className="text-xs font-medium text-white truncate">
                {shift.name}
                {/* Arrow indicating continuation */}
                <ArrowRight className="inline-block h-3 w-3 ml-1" />
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>{tooltipContent}</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            {/* Second part of the overnight shift (midnight to end time) */}
            <div
              className={commonStyles}
              style={{
                left: "0%",
                width: `${secondPartWidth}%`,
                top: topPosition,
                height: height
              }}
            >
              <div className="text-xs font-medium text-white truncate">
                {/* Left arrow indicating continuation from previous day */}
                <ArrowRight className="inline-block h-3 w-3 mr-1 rotate-180" />
                {shift.name}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>{tooltipContent}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // For regular shifts (not overnight), render a single block
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={commonStyles}
            style={{
              left: `${(startMinutes / (24 * 60)) * 100}%`,
              width: `${((endMinutes - startMinutes) / (24 * 60)) * 100}%`,
              top: topPosition,
              height: height
            }}
          >
            <div className="text-xs font-medium text-white truncate">{shift.name}</div>
          </div>
        </TooltipTrigger>
        <TooltipContent>{tooltipContent}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Helper Functions
function timeToMinutes(timeString: string): number {
  const [hours, minutes] = timeString.split(":").map(Number);
  return hours * 60 + minutes;
}

function formatTime(timeString: string) {
  const [hours, minutes] = timeString.split(":");
  const hour = Number.parseInt(hours, 10);
  const period = hour >= 12 ? "PM" : "AM";
  const formattedHour = hour % 12 || 12;
  return `${formattedHour}:${minutes.substring(0, 2)} ${period}`;
}

function formatDuration(minutes: number) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
}

function getShiftColorClass(shiftName: string): string {
  const name = shiftName.toLowerCase();

  if (name.includes("morning")) {
    return "bg-amber-500/70";
  } else if (name.includes("afternoon")) {
    return "bg-sky-500/70";
  } else if (name.includes("evening")) {
    return "bg-pink-500/70";
  } else if (name.includes("night")) {
    return "bg-indigo-500/70";
  }

  return "bg-slate-500/70"; // default
}
