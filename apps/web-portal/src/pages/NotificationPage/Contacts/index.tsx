import HeaderSection from "@components/layout/HeaderSection";
import { Tabs, TabsContent } from "@components/shadcn/components/tabs";
import TabsListV2 from "@components/Tabs/TabsListV2";
import TabsTriggerV2 from "@components/Tabs/TabsTriggerV2";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import ContactListTab from "./ContactListTab";
import ContactTeamsTab from "./ContactTeamsTab";

const ContactList = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentView, setCurrentView] = useState(searchParams.get("tab") || "contacts");

  const handleTabChange = (newValue: "contacts" | "groups") => {
    setCurrentView(newValue);
    setSearchParams({ tab: newValue });
  };
  return (
    <main className="space-y-4">
      <HeaderSection title="Contacts & Groups" description="Manage your contacts and groups" />
      <section className=" space-y-2">
        <Tabs defaultValue={currentView}>
          <div className="between items-center">
            <TabsListV2 className="child:w-40 ">
              <TabsTriggerV2
                value="contacts"
                onClick={() => {
                  handleTabChange("contacts");
                }}
              >
                Contacts
              </TabsTriggerV2>
              <TabsTriggerV2 value="groups" onClick={() => handleTabChange("groups")}>
                Groups
              </TabsTriggerV2>
            </TabsListV2>
          </div>

          <TabsContent value="contacts">
            <ContactListTab />
          </TabsContent>

          <TabsContent value="groups">
            <ContactTeamsTab />
          </TabsContent>
        </Tabs>
      </section>
    </main>
  );
};

export default ContactList;
