import Button from "@components/Button";
import Dropdown from "@components/Dropdown";
import FormDialog from "@components/FormDialog";
import Input from "@components/Input";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useContactList from "@hooks/notifications/useContactList";
import useCreateContactGroup from "@hooks/notifications/useCreateContactGroup";
import useUpdateContactTeams from "@hooks/notifications/useUpdateContactTeams";
import useDebounce from "@hooks/useDebounce";
import { ContactDetail, TeamGroupDetails } from "@src/features/features";
import { DEFAULT_VALIDATE } from "@utils/from_schema";
import { showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { Check, X } from "lucide-react";
import { ForwardedRef, forwardRef, useCallback, useImperativeHandle, useState } from "react";
import { useForm } from "react-hook-form";
import { useParams } from "react-router-dom";

type Ref = {
  openCreateContactTeamsSidebar: (data?: TeamGroupDetails) => void;
};

const CreateContactTeams = (props: unknown, ref: ForwardedRef<Ref>) => {
  const groupId = useParams().groupId!;
  const [showCreateTeams, setShowCreateTeams] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: contactList, isLoading: isContactListLoading } = useContactList({
    search: debouncedSearchQuery
  });

  const teamsForm = useForm({
    defaultValues: {
      name: "",
      description: "",
      members: [] as ContactDetail[]
    }
  });

  const openCreateContactTeamsSidebar = useCallback((data?: TeamGroupDetails) => {
    if (groupId && data) {
      teamsForm.reset({ name: data.name, description: data.description, members: data.members });
    }
    setShowCreateTeams(true);
  }, []);

  useImperativeHandle(ref, () => ({ openCreateContactTeamsSidebar }));

  const createTeamsMutation = useCreateContactGroup({
    onSuccess: () => {
      showSuccessToast("Teams created successfully");
      queryClient.invalidateQueries({ queryKey: ["contact-teams-list"] });
      resetFormState();
    }
  });
  const updateTeamsMutation = useUpdateContactTeams({
    onSuccess: () => {
      showSuccessToast("Teams updated successfully");
      queryClient.invalidateQueries({ queryKey: ["contact-teams-details", groupId] });
      resetFormState();
    }
  });

  const submitHandler = async (values: {
    name: string;
    description: string;
    members: ContactDetail[];
  }) => {
    const payload = {
      name: values.name,
      description: values.description,
      members: values.members.map((item) => item.contactId)
    };
    if (groupId) {
      updateTeamsMutation.mutate({ groupId, body: payload });
    } else {
      createTeamsMutation.mutate(payload);
    }
  };

  const resetFormState = () => {
    setShowCreateTeams(false);
    teamsForm.reset();
  };
  return (
    <FormDialog
      open={showCreateTeams}
      onClose={resetFormState}
      notDismissable
      title={`${groupId ? "Edit" : "Create"} Group`}
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={resetFormState}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Check size={BUTTON_ICON_SIZE} />}
            loading={createTeamsMutation.isPending || updateTeamsMutation.isPending}
            type="submit"
            form="create-contact-teams-form"
          >
            Submit
          </Button>
        </div>
      }
    >
      <form
        className="space-y-4"
        onSubmit={teamsForm.handleSubmit(submitHandler)}
        id="create-contact-teams-form"
      >
        <Input
          label="Group Name"
          placeholder="Enter Group Name"
          required
          {...teamsForm.register("name", DEFAULT_VALIDATE.schema)}
          error={!!teamsForm.formState.errors.name}
          helperText={teamsForm.formState.errors.name && DEFAULT_VALIDATE.message}
        />
        <Input
          label="Description"
          placeholder="Enter description"
          required
          inputType="textarea"
          {...teamsForm.register("description", DEFAULT_VALIDATE.schema)}
          error={!!teamsForm.formState.errors.description}
          helperText={teamsForm.formState.errors.description && DEFAULT_VALIDATE.message}
        />
        <Dropdown
          {...teamsForm.register("members", DEFAULT_VALIDATE.schema)}
          options={contactList?.contacts || []}
          label="Members"
          required
          isMulti
          getOptionLabel="name"
          optionsLoading={isContactListLoading}
          isSearchable
          deepSearch={(value) => setSearchQuery(value)}
          onChange={(value: ContactDetail[]) => {
            teamsForm.setValue("members", value);
            teamsForm.trigger("members");
          }}
          value={teamsForm.getValues("members")}
          error={!!teamsForm.formState.errors.members}
          helperText={teamsForm.formState.errors.members && DEFAULT_VALIDATE.message}
        />
        <div></div>
      </form>
    </FormDialog>
  );
};

export default forwardRef(CreateContactTeams);
