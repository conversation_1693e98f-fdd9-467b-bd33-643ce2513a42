import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import { Card } from "@components/ui";
import useNotificationLogsDetails from "@hooks/notifications/useNotificationLogsDetails";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { Logs, User } from "lucide-react";
import { useParams, useSearchParams } from "react-router-dom";

const NotificationLogsDetails = () => {
  const logId = useParams().logId!;
  const [searchParams] = useSearchParams();
  const nfType = (searchParams.get("nfType") as "push" | "email" | "sms" | "teams") || "push";
  const { data: notificationLogs, isLoading: notificationLogsLoading } = useNotificationLogsDetails(
    {
      id: logId,
      nfType
    }
  );
  return notificationLogsLoading ? (
    <CardLoadingSkeleton />
  ) : !notificationLogs ? (
    <Card>
      <DataNotFound />
    </Card>
  ) : (
    <section className="grid grid-cols-2 gap-4">
      <Card className="space-y-6">
        <div className="flex items-center justify-between">
          <HeadingIcon Icon={Logs} title="Schedule Details" />
        </div>
        <div className="space-y-4">
          <DetailsCell title="Title" data={notificationLogs?.title} />
          <DetailsCell title="Body" data={notificationLogs?.body} />
          <DetailsCell title="Failure Reason" data={notificationLogs?.failureReason} />
          <div className="grid grid-cols-2 gap-4">
            <DetailsCell title="Category" data={notificationLogs?.category} />
            <DetailsCell title="Notification Type" data={notificationLogs?.notificationType} />
            <DetailsCell title="Delivery Type" data={notificationLogs?.type} />
            <DetailsCell title="Status" data={notificationLogs?.status} />
            <DetailsCell title="Priority" data={notificationLogs?.priority} />
            <DetailsCell
              title="Notification Execution"
              data={notificationLogs?.notificationExecution}
            />
          </div>
        </div>
      </Card>
      <Card className="space-y-6">
        <div className="flex items-center justify-between">
          <HeadingIcon Icon={User} title="Recipient Information" />
        </div>
        <div className="space-y-4">
          <DetailsCell title="Contact Name" data={notificationLogs?.contactName} />
          <DetailsCell title="Email" data={notificationLogs?.email} />
          <div className="grid grid-cols-2 gap-4">
            <DetailsCell title="Phone" data={notificationLogs?.phoneNo} />
            <DetailsCell title="Create Date" data={convetUTCToLocal(notificationLogs?.createdAt)} />
            <DetailsCell
              title="Schedule Date"
              data={convetUTCToLocal(notificationLogs?.scheduledAt)}
            />
            <DetailsCell
              title="Acknowledged By"
              data={notificationLogs?.ackBy || "Not acknowledged"}
            />
            <DetailsCell
              title="Acknowledged At"
              data={
                notificationLogs?.ackAt
                  ? convetUTCToLocal(notificationLogs?.ackAt)
                  : "Not acknowledged"
              }
            />
            <DetailsCell title="Thing Name" data={notificationLogs?.metadata.thingName} />
            <DetailsCell title="Product Name" data={notificationLogs?.metadata.productName} />
          </div>
        </div>
      </Card>
    </section>
  );
};

export default NotificationLogsDetails;
