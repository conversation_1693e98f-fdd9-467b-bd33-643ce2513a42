import DataNotFound from "@components/DataNotFound";
import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import Label from "@components/Label";
import HeaderSection from "@components/layout/HeaderSection";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import { INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import useTablePagination from "@hooks/classic/useTablePagination";
import useNotificationStatusLogs from "@hooks/notifications/useNotificationStatusLogs";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { generateColor } from "@utils/color";
import { getTableIndex } from "@utils/tableUtils";
import { separateCamelCase } from "@utils/utilities";
import { Search } from "lucide-react";
import { useState } from "react";

type NotificationType = "push" | "email" | "sms" | "teams";

const NotificationStatusLogs = () => {
  const {
    page,
    setPage,
    limit,
    setLimit,
    searchQuery,
    setSearchQuery,
    addSearchParams,
    searchParams
  } = useTablePagination();

  const [sortFn, sort] = useTableSort();
  const [nfType, setNfType] = useState<"push" | "email" | "sms" | "teams">(
    (searchParams.get("nfType") as NotificationType) || "push"
  );

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: notificationLogs, isLoading: notificationLogsLoading } = useNotificationStatusLogs({
    page,
    limit,
    search: debouncedSearchQuery,
    nfType
  });
  const navigate = useCustomNavigate();

  return (
    <main className=" space-y-6">
      <HeaderSection
        title="Notification Status"
        description="Manage your Status"
      />
      <Card className=" space-y-4">
          <div className="flex gap-4 items-center">
            <Input
              value={searchQuery}
              onChange={(e) => {
                setPage(1);
                setSearchQuery(e.target.value);
              }}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />
            <Dropdown
              placeHolder="Select Notification Type"
              options={["push", "email", "sms", "teams"]}
              className="w-[25rem]"
              value={nfType}
              onChange={(option) => {
                setPage(1);
                setNfType(option);
                addSearchParams({ nfType: option });
              }}
            />
          </div>

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("contactName", order)}>Contact Name</TableHead>
              <TableHead onSort={(order) => sort("failureReason", order)}>Failed Reason</TableHead>
              <TableHead onSort={(order) => sort("priority", order)}>Priority</TableHead>
              <TableHead onSort={(order) => sort("category", order)}>Category</TableHead>
              <TableHead onSort={(order) => sort("notificationType", order)}>
                Notification Type
              </TableHead>
              <TableHead onSort={(order) => sort("ackAt", order)}>Acknowledge Date</TableHead>
              <TableHead onSort={(order) => sort("scheduledAt", order)}>Schedule Date</TableHead>
            </>
          }
          body={
            notificationLogsLoading ? (
              <TableRowsSkeleton />
            ) : !notificationLogs?.nfStatusLogsList.length || !notificationLogs ? (
              <DataNotFound
                isTable
                title={!notificationLogs ? "Something went wrong" : "No Logs Available"}
              />
            ) : (
              notificationLogs.nfStatusLogsList.toSorted(sortFn).map((log, i) => (
                <tr
                  key={log.id}
                  className="cursor-pointer"
                  onClick={() => navigate(`${log.id}?nfType=${nfType}`)}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title className="truncate max-w-80">
                    {log.contactName}
                  </TableRow>
                  <TableRow className="truncate max-w-80">{log.failureReason}</TableRow>
                  <TableRow>
                    <Label text={log.priority} color={generateColor(log.priority)} />
                  </TableRow>
                  <TableRow>{log.category}</TableRow>
                  <TableRow>{separateCamelCase(log.notificationType)}</TableRow>
                  <TableRow>{log.ackAt ? convetUTCToLocal(log.createdAt) : "N/A"}</TableRow>
                  <TableRow>{convetUTCToLocal(log.scheduledAt)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: notificationLogs?.pages || 1
          }}
        />
      </Card>
    </main>
  );
};

export default NotificationStatusLogs;
