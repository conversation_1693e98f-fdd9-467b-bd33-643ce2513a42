import Alert from "@components/Alert";
import DataNotFound from "@components/DataNotFound";
import Dropdown from "@components/Dropdown";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import HeaderSection from "@components/layout/HeaderSection";
import { Button } from "@components/shadcn/components/button";
import { Checkbox } from "@components/shadcn/components/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@components/shadcn/components/dropdown-menu";
import { Card } from "@components/ui";
import { notification } from "@frontend/shared";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useDeleteNotificationHistory from "@hooks/useDeleteNotificationHistory";
import useReadNotificationHistory from "@hooks/useReadNotificationHistory";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { DEFAULT_PAGE_COUNT, separateCamelCase } from "@utils/utilities";
import clsx from "clsx";
import { Check, Info, LoaderCircle, MoreVertical } from "lucide-react";
import { useState } from "react";

const CATEGORY_OPTIONS = [
  "productCreated",
  "productUpdated",
  "productDeleted",
  "thingCreated",
  "thingUpdated",
  "thingDeleted",
  "userCreated",
  "userUpdated",
  "userDeleted"
];

const ACTION_COLOR_MAP = {
  created: "green",
  updated: "yellow",
  deleted: "red",
  default: "blue"
} as const;

const colorGenerator = (category: string) => {
  const match = category.match(/^(product|thing|user)(Created|Updated|Deleted)$/i);
  const action = match?.[2]?.toLowerCase() || "default";

  const color = ACTION_COLOR_MAP[action as keyof typeof ACTION_COLOR_MAP] || ACTION_COLOR_MAP.default;
  return color;
};

const NotificationHistroyList = () => {
  const [sortFn, sort] = useTableSort();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const [categoryFilter, setCategoryFilter] = useState<string[]>([]);
  const [selectedNotification, setSelectedNotification] = useState<number[]>([]);

  const { data: notificationList, isLoading } = notification.useNotificationHistory({
    page,
    limit,
    categoryFilter
  });
  const deleteNotification = useDeleteNotificationHistory();
  const readNotification = useReadNotificationHistory();
  
  return (
    <div className="space-y-4">
      <HeaderSection
              title="Notification History"
              description="Manage your History"
            />
      <Card className="space-y-4">
        <Dropdown
          onChange={(option) => {
            setCategoryFilter(option);
          }}
          value={categoryFilter}
          placeHolder="Filter by Category"
          className="flex-1"
          isMulti
          options={CATEGORY_OPTIONS}
        />
      {!!selectedNotification.length && (
        <>
          <Alert
            Icon={Info}
            title={
              <div className="flex items-start justify-between">
                <span>Info</span>
                <Button
                  size="sm"
                  disabled={readNotification.isPending}
                  onClick={() => {
                    readNotification.mutate(selectedNotification, {
                      onSuccess: () => {
                        setSelectedNotification([]);
                      }
                    });
                  }}
                >
                  {readNotification.isPending ? (
                    <>
                      <LoaderCircle
                        size={BUTTON_ICON_SIZE}
                        className="animate-spin text-blue-600 dark:text-blue-400"
                      />
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      <span>Mark as Read</span>
                    </>
                  )}
                </Button>
              </div>
            }
            description={`${selectedNotification.length} notification
              ${selectedNotification.length !== 1 ? "s" : ""} selected`}
            type="info"
          />
        </>
      )}
      <Table
        head={
          <>
            <TableHead>
              <Checkbox
                className="!bg-gray-100"
                onCheckedChange={(value) => {
                  if (value) {
                    if (notificationList?.notificationHistoryList) {
                      setSelectedNotification(
                        notificationList?.notificationHistoryList
                          .filter((notification) => !notification.read)
                          .map((notification) => Number(notification.id))
                      );
                    } else {
                      console.warn("No notifications available to select");
                      setSelectedNotification([]);
                    }
                  } else {
                    setSelectedNotification([]);
                  }
                }}
              />
            </TableHead>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("title", order)}>Title</TableHead>
            <TableHead onSort={(order) => sort("category", order)}>Category</TableHead>
            <TableHead onSort={(order) => sort("body", order)}>Message</TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created Date</TableHead>
            <TableHead>Actions</TableHead>
          </>
        }
        body={
          isLoading ? (
            <TableRowsSkeleton />
          ) : !notificationList || !notificationList.notificationHistoryList?.length ? (
            <DataNotFound title="No Notification Available" isTable />
          ) : (
            notificationList.notificationHistoryList.toSorted(sortFn).map((notification, i) => (
              <tr
                key={notification.id}
                className={clsx(notification.read && "!bg-secondary")}
                data-state={
                  selectedNotification.some((id) => id === notification.id) ? "selected" : ""
                }
              >
                <TableRow onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    disabled={notification.read}
                    checked={selectedNotification.some((id) => id === notification.id)}
                    onCheckedChange={(value) => {
                      if (value) {
                        setSelectedNotification((prev) => [...prev, notification.id]);
                      } else {
                        setSelectedNotification((prev) =>
                          prev.filter((id) => id !== notification.id)
                        );
                      }
                    }}
                  />
                </TableRow>
                <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                <TableRow title>{notification.title}</TableRow>
                <TableRow>
                  <Label
                    text={separateCamelCase(notification.category)}
                    color={colorGenerator(notification.category)}
                  />
                </TableRow>
                <TableRow>{notification.body}</TableRow>
                <TableRow>{convetUTCToLocal(notification.createdAt)}</TableRow>
                <TableRow>
                  {" "}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="z-[10000]">
                      {!notification.read && (
                        <DropdownMenuItem
                          onClick={() => readNotification.mutate([notification.id])}
                        >
                          Mark as read
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => deleteNotification.mutate(notification.id)}>
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableRow>
              </tr>
            ))
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: notificationList?.pages || 1
        }}
      />
      </Card>
    </div>
  );
};

export default NotificationHistroyList;
