import { createEscalation, updateEscalation } from "@api/cloud";
import ActionButton from "@components/ActionButton";
import Input from "@components/Input";
import NoDataFound from "@components/NoDataFound";
import FormDialog from "@components/FormDialog";
import useContactList from "@hooks/notifications/useContactList";
import useDebounce from "@hooks/useDebounce";
import { generateRandomId } from "@src/pages/MonitorPage/utils";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { queryClient } from "@utils/queryClient";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import { useEffect, useState } from "react";
import { Plus, X } from "lucide-react";
import { useForm } from "react-hook-form";
import Button from "../../../components/Button";
import Dropdown from "../../../components/Dropdown";
import { EscalationGroup, EscalationMember} from "@src/features/features";

type EscalationFormValues = {
  groupName: string;
  description: string;
  notificationExecution: string;
};
type Member = {
  id : string;
  contactId: string;
  name : string;
  escalateAfter: number;
}

const CreateEscalation = ({
  showCreateAsset,
  setShowCreateAsset,
  escalationDetails,
  type = "new"
}: {
  showCreateAsset: boolean;
  setShowCreateAsset: React.Dispatch<React.SetStateAction<boolean>>;
  escalationDetails?: EscalationGroup;
  type?: string;
}) => {
  const [membersList, setMembersList] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const escalationForm = useForm<EscalationFormValues>({ defaultValues: { groupName: "",
    description: "", notificationExecution: "escalation" } });

  const { data: contactList, isLoading: isContactListLoading } = useContactList({
    search: debouncedSearchQuery
  });

 

  useEffect(() => {
    if (type === "edit") {
      escalationForm.reset({
        groupName: escalationDetails?.groupName,
        description: escalationDetails?.description,
        notificationExecution: escalationDetails?.notificationExecution
      });
      const transformedMembers = escalationDetails?.members.map((member:EscalationMember) => ({
        ...member,
        id: generateRandomId(16)
      }))|| [];
      setMembersList(transformedMembers);
    }
  }, [type]);

  const onSubmit = async (values : EscalationFormValues) => {
    const transformedMembers = membersList.map((member:Member) => ({
      name: member.name,
      ...(values.notificationExecution === "escalation" && {
        escalateAfter: Number(member.escalateAfter)
      }),
      contactId: member.contactId
    }));

    let resp;
    if (type === "edit") {
      resp = await updateEscalation(escalationDetails?.groupName, {
        ...values,
        members: transformedMembers
      });
    } else {
      resp = await createEscalation({
        ...values,
        members: transformedMembers
      });
    }

    if (resp.status === "Success") {
      if (type === "edit") {
        showSuccessToast("Escalation Updated successfully");
        queryClient.invalidateQueries({
          queryKey: ["notification-escalation-detail", escalationDetails?.groupName]
        });
        resetFormState();
      } else {
        showSuccessToast("Escalation created successfully");
        queryClient.invalidateQueries({ queryKey: ["notification-escalation-group"] });
        resetFormState();
      }
    } else {
      showErrorToast(resp.message);
    }
  };
  const removeMemberHandler = (memberId:string) => {
    setMembersList((prev) => prev.filter((member:Member) => member.id !== memberId));
  };

  const resetFormState = () => {
    setShowCreateAsset(false);
    setMembersList([]);
    escalationForm.reset();
  };

  return (
    <FormDialog
      open={showCreateAsset}
      notDismissable
      onClose={resetFormState}
      title={`${type === "edit" ? "Edit" : "Create"} Escalation`}
      footer={
        <div className="flex gap-4 justify-end">
          <Button
            onClick={resetFormState}
            small
            color="gray"
            type="button"
            startIcon={<X size={BUTTON_ICON_SIZE} />}
          >
            Close
          </Button>
          <Button
            small
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            loading={escalationForm.formState.isSubmitting}
            type="submit"
            form="create-escalation-form"
          >
            {type === "edit" ? "Update" : "Create"}
          </Button>
        </div>
      }
    >
      <form
        className=" space-y-4"
        onSubmit={escalationForm.handleSubmit(onSubmit)}
        id="create-escalation-form"
      >
        <div className="space-y-4">
          <Input
            label="Group Name"
            disabled={type === "edit"}
            {...escalationForm.register("groupName")}
            noSpace
            placeholder="Enter Escalation Group Name"
          />
          <Dropdown
            required
            value={escalationForm.getValues("notificationExecution")}
            onChange={(option) => {
              escalationForm.setValue("notificationExecution", option);
            }}
            label="Execution"
            options={["escalation", "parallel"]}
          />
          <Input
            label="Description"
            placeholder="Escalation Group description"
            inputType="textarea"
            required
            {...escalationForm.register("description", {
              required: true
            })}
          />
        </div>
        <hr className="hr my-6" />
        <div className="between">
          <h3 className="sidebar-sub-heading">Members</h3>
          <Button
            type="button"
            small
            outlined
            color="gray"
            onClick={() =>
              setMembersList((prev) => [
                ...prev,
                {
                  id: generateRandomId(16),
                  escalateAfter: 0,
                  name: "",
                  contactId: ""
                }
              ])
            }
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
          >
            Add Member
          </Button>
        </div>

        {!membersList.length ? (
          <NoDataFound content="No Member Added" />
        ) : (
          membersList.map((member) => {
            return (
              <div className="space-y-4">
                <div className="flex gap-4 items-center  mb-4">
                  <div className="flex gap-4 flex-1 ">
                    <Dropdown
                      className="w-full"
                      onChange={(option) => {
                        setMembersList((prev) =>
                          prev.map((item) =>
                            item.id === member.id
                              ? { ...member, contactId: option.contactId, name: option.name }
                              : item
                          )
                        );
                      }}
                      options={contactList?.contacts || []}
                      optionsLoading={isContactListLoading}
                      value={member}
                      deepSearch={(value) => setSearchQuery(value)}
                      isSearchable
                      label="Contact Name"
                      required
                      getOptionLabel={"name"}
                      placeHolder="Select name"
                    />
                    {escalationForm.watch("notificationExecution") === "escalation" && (
                      <Input
                        className="w-full"
                        label="Escalate After(Sec)"
                        type="number"
                        defaultValue={0}
                        value={member.escalateAfter}
                        onChange={(e) => {
                          setMembersList((prev) =>
                            prev.map((item) =>
                              item.id === member.id
                                ? { ...member, escalateAfter: Number(e.target.value)}
                                : item
                            )
                          );
                        }}
                      />
                    )}
                  </div>
                  <ActionButton
                    className="!mt-6"
                    type="delete"
                    onClick={() => removeMemberHandler(member.id)}
                  />
                </div>
              </div>
            );
          })
        )}
      </form>
    </FormDialog>
  );
};

export default CreateEscalation;
