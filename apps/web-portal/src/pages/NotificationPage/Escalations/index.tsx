import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useNotificationEscalationGroup from "@hooks/notifications/useNotificationEscalationGroup";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { Box, Plus, Search } from "lucide-react";
import { useState } from "react";
import Button from "../../../components/Button";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import CreateEscalation from "./CreateEscalation";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

const EscalationPage = () => {
  const [showCreateAsset, setShowCreateAsset] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const [sortFn, sort] = useTableSort();
  const navigate = useCustomNavigate();

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const { data: permissions } = useUserGroupPermissions();

  const { data: escalationList, isLoading: isEscalationListLoading } =
    useNotificationEscalationGroup({
      page,
      limit,
      searchQuery: debouncedSearchQuery
    });

  return (
    <main className=" space-y-4">
        <HeaderSection
                title="Escalation Groups"
                description="Manage your Escalation Groups"
                actions={
                  <Button
                    startIcon={<Plus size={BUTTON_ICON_SIZE} />}
                    type="submit"
                    noAccess={permissions.escalations !== "write"}
                    onClick={() => setShowCreateAsset(true)}
                  >
                    New Escalation
                  </Button>
                }
              />
      <Card className=" space-y-4">
            <Input
              value={searchQuery}
              onChange={(e) => {
                setPage(1);
                setSearchQuery(e.target.value);
              }}
              className="flex-1"
              placeholder="Search"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("groupName", order)}>Group Name</TableHead>
              <TableHead onSort={(order) => sort("members", order)}>Members</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
              <TableHead onSort={(order) => sort("updatedAt", order)}>Updated At</TableHead>
            </>
          }
          body={
            isEscalationListLoading ? (
              <TableRowsSkeleton />
            ) : !escalationList?.escalationGroups.length || !escalationList ? (
              <DataNotFound
                title={!escalationList ? "Something went wrong" : "No assets Available"}
                isTable
              />
            ) : (
              escalationList.escalationGroups.toSorted(sortFn).map((asset, i) => (
                <tr
                  key={asset.groupName}
                  className="cursor-pointer"
                  onClick={() => navigate(`${asset.groupName}`)}
                  data-testid={asset.groupName}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>{asset.groupName}</TableRow>
                  <TableRow>{asset.members}</TableRow>

                  <TableRow>{convetUTCToLocal(asset.createdAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(asset.updatedAt)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: escalationList?.pages || 1
          }}
        />
      </Card>
      <CreateEscalation showCreateAsset={showCreateAsset} setShowCreateAsset={setShowCreateAsset} />
    </main>
  );
};

export default EscalationPage;
