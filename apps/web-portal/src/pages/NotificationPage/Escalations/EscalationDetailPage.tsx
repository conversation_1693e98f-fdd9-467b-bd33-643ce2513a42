import { useState } from "react";
import { Card } from "@components/ui";

import { CardLoadingSkeleton } from "@components/Card/CardSkeleton";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { BUTTON_ICON_SIZE, DATACELL_ICON_SIZE } from "@utils/utilities";
import { Box, List, Settings2,Archive, Calendar, Clock, Cpu, Edit2, Trash } from "lucide-react";
import { deleteEscalation } from "../../../api/cloud";
import Button from "../../../components/Button";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import { showErrorToast, showSuccessToast } from "../../../utils";

import useEscalationDetails from "@hooks/notifications/useEscalationDetails";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { queryClient } from "@utils/queryClient";
import { useParams } from "react-router-dom";
import CreateEscalation from "./CreateEscalation";
import DetailsCell from "@components/DetailsCell";

const EscalationDetailPage = () => {
  const thingName = useParams().thingName!;
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [showUpdateAsset, setShowUpdateAsset] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [sortFn, sort] = useTableSort();
  const { data: permissions } = useUserGroupPermissions();
  const navigate = useCustomNavigate();

  const { data: escalationDetails, isLoading: isEscalationLoading } = useEscalationDetails({
    groupName: thingName
  });
  const handleEscalationDelete = async () => {
    setLoadingBtn(true);
    const resp = await deleteEscalation(escalationDetails?.groupName);
    setLoadingBtn(false);
    if (resp.status === "Success") {
      showSuccessToast("Escalation Deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["notification-escalation-group"] });
      navigate(-1);
    } else {
      showErrorToast(resp.message);
    }
  };

  return (
    <main className="space-y-6">
      <Card className=" space-y-6">
        <div className="between">
          <HeadingIcon Icon={Box} title="Escalation Details" />

          <div className="flex gap-4 items-center">
            <Button
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
              onClick={() => {
                setShowUpdateAsset(true);
              }}
              color="gray"
              noAccess={permissions.escalations !== "write"}
            >
              Edit
            </Button>
            <Button
              startIcon={<Trash size={BUTTON_ICON_SIZE} />}
              color="red"
              onClick={() => setShowDeletePrompt(true)}
              loading={loadingBtn}
              noAccess={permissions.escalations !== "write"}
            >
              Delete
            </Button>
          </div>
        </div>

        {isEscalationLoading ? (
          <CardLoadingSkeleton />
        ) : !escalationDetails ? (
          <DataNotFound title="Something went wrong" />
        ) : (
          <section className="grid grid-cols-2 gap-4">
            <DetailsCell
              icon={<Cpu size={DATACELL_ICON_SIZE} />}
              title="Group Name"
              data={escalationDetails.groupName}
            />
            <DetailsCell
              icon={<Archive size={DATACELL_ICON_SIZE} />}
              title="Description"
              data={escalationDetails.description}
            />
            <DetailsCell
              icon={<Settings2 size={DATACELL_ICON_SIZE} />}
              title="Execution"
              data={escalationDetails.notificationExecution}
            />

            <DetailsCell
              icon={<Calendar size={DATACELL_ICON_SIZE} />}
              title="Created At"
              data={convetUTCToLocal(escalationDetails.createdAt)}
            />

            <DetailsCell
              icon={<Clock size={DATACELL_ICON_SIZE} />}
              title="Updated At"
              data={convetUTCToLocal(escalationDetails.updatedAt)}
            />
          </section>
        )}
      </Card>
      <Card className="space-y-4">
        <HeadingIcon Icon={List} title="Members" />

        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
              <TableHead onSort={(order) => sort("escalateAfter", order)}>Escalate After</TableHead>
            </>
          }
          body={
            isEscalationLoading ? (
              <TableRowsSkeleton />
            ) : !escalationDetails || !escalationDetails.members.length ? (
              <DataNotFound title="No Member Available" isTable />
            ) : (
              <>
                {escalationDetails.members.toSorted(sortFn).map((member, i) => (
                  <tr
                    onClick={() => navigate(`/notifications/contacts/${member.contactId}`)}
                    data-testid={member.contactId}
                    className="cursor-pointer"
                    key={member.contactId}
                  >
                    <TableRow>{i + 1}</TableRow>
                    <TableRow>{member.name}</TableRow>
                    <TableRow>{member.escalateAfter} sec</TableRow>
                  </tr>
                ))}
              </>
            )
          }
        />
      </Card>
      {showUpdateAsset && (
        <CreateEscalation
          escalationDetails={escalationDetails}
          type="edit"
          showCreateAsset={showUpdateAsset}
          setShowCreateAsset={setShowUpdateAsset}
        />
      )}

      <ConfirmPrompt
        show={showDeletePrompt}
        validate
        item={escalationDetails?.groupName}
        loading={loadingBtn}
        onCancel={() => setShowDeletePrompt(false)}
        onConfirm={() => {
          handleEscalationDelete();
        }}
      />
    </main>
  );
};

export default EscalationDetailPage;
