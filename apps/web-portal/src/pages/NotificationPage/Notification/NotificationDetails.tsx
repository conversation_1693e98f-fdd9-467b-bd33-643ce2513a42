import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import PageSekeleton from "@components/PageSekeleton";
import { Badge } from "@components/shadcn/components/badge";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import { Separator } from "@components/shadcn/components/separator";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useNotificationTemplateDetail from "@hooks/notifications/useNotificationTemplateDetail";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import {
  Calendar,
  Clock,
  Info,
  Mail,
  MessageSquare,
  Bell,
  Edit2,
  FileText,
  Trash,
  Users
} from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { deleteNotificationTemplate } from "../../../api/cloud";
import Button from "../../../components/Button";
import ConfirmPrompt from "../../../components/ConfirmPrompt";
import { showErrorToast, showSuccessToast } from "../../../utils";
import DetailsCell from "@components/DetailsCell";
import { DATACELL_ICON_SIZE } from "@frontend/shared/config/defaults";
import { Card } from "@components/ui";

const NotificationDetail = () => {
  const [showNotificationDeletePrompt, setShowNotificationDeletePrompt] = useState(false);
  const [loadingState, setLoadingState] = useState(false);
  const [activeChannel, setActiveChannel] = useState("notification");

  const templateName = useParams().templateName!;
  const navigate = useCustomNavigate();

  const { data: notificationDetail, isLoading: isNotificationDetailLoading } =
    useNotificationTemplateDetail({ templateName });

  const { data: permissions } = useUserGroupPermissions();

  const handleDelete = async () => {
    setLoadingState(true);
    const resp = await deleteNotificationTemplate(templateName);

    if (resp.status === "Success") {
      showSuccessToast("Notification template deleted successfully");

      setTimeout(() => {
        navigate(-1);
      }, 500);
      setShowNotificationDeletePrompt(false);
    } else {
      showErrorToast(resp.message);
    }
    setLoadingState(false);
  };

  return isNotificationDetailLoading ? (
    <PageSekeleton />
  ) : !notificationDetail ? (
    <DataNotFound title="Something went wrong" />
  ) : (
    <main className="space-y-6 mt-2">
      <div className="flex items-center justify-between mb-4">
        <HeadingIcon Icon={Bell} title="Notification Template Details" />

        <div className="flex gap-2">
          <Button
            onClick={() => {
              navigate(`edit`, { state: notificationDetail });
            }}
            startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
            color="gray"
            noAccess={permissions.notificationTemplates !== "write"}
          >
            Edit
          </Button>
          <Button
            onClick={() => setShowNotificationDeletePrompt(true)}
            startIcon={<Trash size={BUTTON_ICON_SIZE} />}
            color="red"
            noAccess={permissions.notificationTemplates !== "write"}
          >
            Delete
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="md:col-span-1">
          <Card className="sticky top-4 p-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className=" font-medium text-muted-foreground">Template Info</h3>
                <div className="space-y-3">
                  <DetailsCell
                    icon={<FileText size={DATACELL_ICON_SIZE} />}
                    title="Name"
                    data={notificationDetail.templateName}
                  />
                  <DetailsCell
                    icon={<Info size={DATACELL_ICON_SIZE} />}
                    title="Description"
                    data={notificationDetail.description}
                  />
                  <DetailsCell
                    icon={<Calendar size={DATACELL_ICON_SIZE} />}
                    title="Create Date"
                    data={convetUTCToLocal(notificationDetail.createdAt)}
                  />
                  <DetailsCell
                    icon={<Clock size={DATACELL_ICON_SIZE} />}
                    title="Update Date"
                    data={convetUTCToLocal(notificationDetail.updatedAt)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="font-medium text-muted-foreground">
                  Notification Channels Templates
                </h3>
                <div className="space-y-1">
                  <ShadcnButton
                    variant={activeChannel === "notification" ? "default" : "ghost"}
                    className={`w-full justify-start `}
                    onClick={() => setActiveChannel("notification")}
                    disabled={!notificationDetail.pushTemplates.length}
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    In-App Notification
                  </ShadcnButton>
                  <ShadcnButton
                    variant={activeChannel === "email" ? "default" : "ghost"}
                    className={`w-full justify-start`}
                    onClick={() => setActiveChannel("email")}
                    disabled={!notificationDetail.emailTemplates.length}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Email
                  </ShadcnButton>

                  <ShadcnButton
                    variant={activeChannel === "sms" ? "default" : "ghost"}
                    className={`w-full justify-start `}
                    onClick={() => setActiveChannel("sms")}
                    disabled={!notificationDetail.smsTemplates.length}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    SMS
                  </ShadcnButton>
                  <ShadcnButton
                    variant={activeChannel === "teams" ? "default" : "ghost"}
                    className={`w-full justify-start`}
                    onClick={() => setActiveChannel("teams")}
                    disabled={!notificationDetail.teamsTemplates.length}
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Teams
                  </ShadcnButton>
                </div>
              </div>
            </div>
          </Card>
        </div>
        <div className="md:col-span-3">
          {activeChannel === "notification" && (
            <Card className="space-y-4">
              <HeadingIcon Icon={Bell} title="In-App Notifications" />

              <div className="grid grid-cols-2 gap-4">
                {notificationDetail.pushTemplates.map((item) => (
                  <Card
                    variant="second"
                    className=" flex flex-col items-start gap-2   text-sm transition-all  rounded-lg"
                  >
                    <div className="flex gap-2">
                      {item.conditions.map((condition) => (
                        <Badge>{condition}</Badge>
                      ))}
                    </div>
                    <div className="text-sm font-medium">Title : {item.title}</div>

                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      {item.body.substring(0, 300)}
                    </div>
                  </Card>
                ))}
              </div>
            </Card>
          )}

          {activeChannel === "email" && (
            <Card className="space-y-4">
              <HeadingIcon Icon={Mail} title="Email Settings" />

              <div className="grid grid-cols-2 gap-4">
                {notificationDetail.emailTemplates.map((item) => (
                  <Card
                    variant="second"
                    className=" flex flex-col items-start gap-2     text-sm transition-all "
                  >
                    <div className="flex gap-2">
                      {item.conditions.map((condition) => (
                        <Badge>{condition}</Badge>
                      ))}
                    </div>
                    <div className="flex w-full flex-col gap-1">
                      <div className="text-sm font-medium">Subject : {item.subject}</div>
                    </div>
                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      {item.body.substring(0, 300)}
                    </div>
                  </Card>
                ))}
              </div>
            </Card>
          )}

          {activeChannel === "sms" && (
            <Card className="space-y-4">
              <HeadingIcon Icon={MessageSquare} title="SMS Settings" />

              <div className="grid grid-cols-2 gap-4">
                {notificationDetail.smsTemplates.map((item) => (
                  <Card
                    variant="second"
                    className=" flex flex-col items-start gap-2     text-sm transition-all"
                  >
                    <div className="flex gap-2">
                      {item.conditions.map((condition) => (
                        <Badge>{condition}</Badge>
                      ))}
                    </div>
                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      {item.body.substring(0, 300)}
                    </div>
                  </Card>
                ))}
              </div>
            </Card>
          )}

          {activeChannel === "teams" && (
            <Card className="space-y-4">
              <HeadingIcon Icon={Users} title="Teams Settings" />

              <div className="grid grid-cols-2 gap-4">
                {notificationDetail.teamsTemplates.map((item) => (
                  <Card
                    variant="second"
                    className=" flex flex-col items-start gap-2    text-sm transition-all "
                  >
                    <div className="flex gap-2 items-center">
                      <div
                        style={{ backgroundColor: `${item.themeColor}` }}
                        className="h-6 w-6 rounded-full"
                      />
                      {item.conditions.map((condition: string) => (
                        <Badge>{condition}</Badge>
                      ))}
                    </div>
                    <div className="text-sm font-medium">Title : {item.title}</div>

                    <div className="line-clamp-2 text-sm text-muted-foreground">
                      {item.body.substring(0, 300)}
                    </div>
                  </Card>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>
      <ConfirmPrompt
        show={showNotificationDeletePrompt}
        item={templateName}
        validate
        onCancel={() => setShowNotificationDeletePrompt(false)}
        onConfirm={handleDelete}
        loading={loadingState}
      />
    </main>
  );
};

export default NotificationDetail;
