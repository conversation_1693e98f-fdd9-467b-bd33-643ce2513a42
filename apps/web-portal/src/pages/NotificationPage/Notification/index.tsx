import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import { Bell, Mail, Plus, Search } from "lucide-react";

import Input from "@components/Input";
import { Badge } from "@components/shadcn/components/badge";
import useTablePagination from "@hooks/classic/useTablePagination";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import useNotificationTemplates from "@hooks/notifications/useNotificationTemplates";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { HEADING_DESCRIPTION } from "@src/config/heading-description";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { MessageCircleMore, Users } from "lucide-react";
import Button from "../../../components/Button";
import Table, { TableHead, TableRow } from "../../../components/Table";
import TableRowsSkeleton from "../../../components/Table/TableRowsSkeleton";
import HeaderSection from "@components/layout/HeaderSection";
import { Card } from "@components/ui";

export default function NotificationList() {
  const { page, setPage, limit, setLimit, searchQuery, setSearchQuery } = useTablePagination();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();
  const { data: permissions } = useUserGroupPermissions();

  const { data: notificationList, isLoading: isNotificationListLoading } = useNotificationTemplates(
    { page, limit, searchQuery: debouncedSearchQuery }
  );

  return (
    <main className="space-y-4">
       <HeaderSection
        title="Notification Templates"
        description="Manage your Notification Templates"
        actions={
          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            onClick={() => navigate("add-Notification")}
            noAccess={permissions.notificationTemplates !== "write"}
          >
            New Template
          </Button>
        }
      />
      <Card className="space-y-4">
          <Input
            value={searchQuery}
            onChange={(e) => {
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            className="flex-1"
            placeholder="Search"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("templateName", order)}>Template Name</TableHead>
            <TableHead onSort={(order) => sort("templateName", order)}>Description </TableHead>
            <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            <TableHead>Channels</TableHead>
          </>
        }
        body={
          isNotificationListLoading ? (
            <TableRowsSkeleton />
          ) : !notificationList?.templates.length || !notificationList ? (
            <DataNotFound
              title={
                !notificationList ? "Something went wrong" : "No Notification Template Available"
              }
              isTable
            />
          ) : (
            notificationList.templates.toSorted(sortFn).map((notification, i) => (
              <tr
                key={notification.templateName}
                className="cursor-pointer"
                data-testid={notification.templateName}
                onClick={() => navigate(`${notification.templateName}`)}
              >
                <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                <TableRow title>{notification.templateName}</TableRow>
                <TableRow className="truncate max-w-80">{notification.description}</TableRow>
                <TableRow>{convetUTCToLocal(notification.createdAt)}</TableRow>
                <TableRow>
                  <div className="flex gap-4">
                    <Badge
                      variant={notification.pushTemplates.length ? "default" : "outline"}
                      className={
                        notification.pushTemplates.length
                          ? "bg-green-500 hover:bg-green-600"
                          : "text-muted-foreground "
                      }
                    >
                      <Bell className="h-3 w-3 mr-1" />
                      Notification
                    </Badge>
                    <Badge
                      variant={notification.emailTemplates.length ? "default" : "outline"}
                      className={
                        notification.emailTemplates.length
                          ? "bg-green-500 hover:bg-green-600"
                          : "text-muted-foreground "
                      }
                    >
                      <Mail className="h-3 w-3 mr-1" />
                      Email
                    </Badge>
                    <Badge
                      variant={notification.smsTemplates.length ? "default" : "outline"}
                      className={
                        notification.smsTemplates.length
                          ? "bg-green-500 hover:bg-green-600 "
                          : "text-muted-foreground "
                      }
                    >
                      <MessageCircleMore className="h-3 w-3 mr-1" />
                      SMS
                    </Badge>
                    <Badge
                      variant={notification.teamsTemplates.length ? "default" : "outline"}
                      className={
                        notification.teamsTemplates.length
                          ? "bg-green-500 hover:bg-green-600"
                          : "text-muted-foreground "
                      }
                    >
                      <Users className="h-3 w-3 mr-1" />
                      Teams
                    </Badge>
                  </div>
                </TableRow>
              </tr>
            ))
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: notificationList?.pages || 1
        }}
      />
      </Card>
    </main>
  );
}
