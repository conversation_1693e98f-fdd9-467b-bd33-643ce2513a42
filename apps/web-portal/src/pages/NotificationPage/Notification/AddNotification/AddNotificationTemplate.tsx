import Button from "@components/Button";
import HeadingIcon from "@components/HeadingIcon";
import PageSekeleton from "@components/PageSekeleton";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useNotificationConfig from "@hooks/classic/useNotificationConfig";
import useCreateNotificationTemplate from "@hooks/notifications/useCreateNotificationTemplate";
import useNotificationTemplateDetail from "@hooks/notifications/useNotificationTemplateDetail";
import useUpdateNotificationTemplate from "@hooks/notifications/useUpdateNotificationTemplate";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { EmailTemplate, PushTemplate, SmsTemplate, TeamsTemplate } from "@src/features/features";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { Bell, Check, Plus, X } from "lucide-react";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import ChannelsSection from "./ChannelsSection";
import EmailSection from "./EmailSection";
import InAppNotification from "./InAppNotification";
import SidebarContent from "./SidebarContent";
import SmsSection from "./SmsSection";
import TeamsSection from "./TeamsSection";
import TemplateDetailsSection from "./TemplateDetailsSection";

type FormData = {
  details:{
    templateName: string;
    description: string;
  };
  channels: Record<string, boolean>;
    pushTemplates: PushTemplate[];
    emailTemplates: EmailTemplate[];
    smsTemplates: SmsTemplate[];
    teamsTemplates: TeamsTemplate[];
};

const validateTemplateForm = (formData:FormData, setActiveSection:Dispatch<SetStateAction<string>>) => {
  
  const { details, channels, pushTemplates, emailTemplates, smsTemplates, teamsTemplates } =
    formData;

  // Validate basic details
  if (!details.templateName?.trim()) {
    showErrorToast("Please enter a template name");
    setActiveSection("details");
    return false;
  }

  if (!details.description?.trim()) {
    setActiveSection("details");
    showErrorToast("Please enter a template description");
    return false;
  }

  // Validate Push Templates if enabled
  if (channels.pushTemplates) {
    if (!pushTemplates || pushTemplates.length === 0) {
      setActiveSection("inapp");
      showErrorToast("Please add at least one push notification template");
      return false;
    }

    for (let i = 0; i < pushTemplates.length; i++) {
      const template = pushTemplates[i];
      if (template.conditions?.length === 0) {
        showErrorToast(`Push template ${i + 1} is missing conditions`);
        setActiveSection("inapp");
        return false;
      }
      if (!template.title?.trim()) {
        showErrorToast(`Push template ${i + 1} is missing a title`);
        setActiveSection("inapp");
        return false;
      }

      if (!template.body?.trim()) {
        showErrorToast(`Push template ${i + 1} is missing a body`);
        setActiveSection("inapp");
        return false;
      }
    }
  }

  // Validate Email Templates if enabled
  if (channels.emailTemplates) {
    if (!emailTemplates || emailTemplates.length === 0) {
      showErrorToast("Please add at least one email template");
      setActiveSection("email");
      return false;
    }

    for (let i = 0; i < emailTemplates.length; i++) {
      const template = emailTemplates[i];
      if (template.conditions?.length === 0) {
        showErrorToast(`Email template ${i + 1} is missing conditions`);
        setActiveSection("email");

        return false;
      }
      if (!template.subject?.trim()) {
        showErrorToast(`Email template ${i + 1} is missing a subject`);
        setActiveSection("email");

        return false;
      }

      if (!template.body?.trim() && !template.HTMLbody?.trim()) {
        showErrorToast(`Email template ${i + 1} is missing a body`);
        setActiveSection("email");

        return false;
      }
    }
  }

  // Validate SMS Templates if enabled
  if (channels.smsTemplates) {
    if (!smsTemplates || smsTemplates.length === 0) {
      showErrorToast("Please add at least one SMS template");
      setActiveSection("sms");

      return false;
    }

    for (let i = 0; i < smsTemplates.length; i++) {
      const template = smsTemplates[i];
      if (template.conditions?.length === 0) {
        showErrorToast(`SMS template ${i + 1} is missing conditions`);
        setActiveSection("sms");

        return false;
      }
      if (!template.body?.trim()) {
        showErrorToast(`SMS template ${i + 1} is missing a body`);
        setActiveSection("sms");

        return false;
      }

      if (!template.msg91TemplateId?.trim()) {
        showErrorToast(`SMS template ${i + 1} is missing a template ID`);
        setActiveSection("sms");

        return false;
      }
    }
  }

  // Validate Teams Templates if enabled
  if (channels.teamsTemplates) {
    if (!teamsTemplates || teamsTemplates.length === 0) {
      showErrorToast("Please add at least one Teams template");
      setActiveSection("teams");

      return false;
    }

    for (let i = 0; i < teamsTemplates.length; i++) {
      const template = teamsTemplates[i];
      if (template.conditions?.length === 0) {
        showErrorToast(`Teams template ${i + 1} is missing conditions`);
        setActiveSection("teams");

        return false;
      }
      if (!template.title?.trim()) {
        showErrorToast(`Teams template ${i + 1} is missing a title`);
        setActiveSection("teams");

        return false;
      }

      if (!template.body?.trim()) {
        showErrorToast(`Teams template ${i + 1} is missing a body`);
        setActiveSection("teams");

        return false;
      }
    }
  }

  // All validations passed
  return true;
};

const AddNotificationTemplate = () => {
  const [activeSection, setActiveSection] = useState("details");

  const {
    details,
    pushTemplates,
    teamsTemplates,
    updateNotificationConfig,
    emailTemplates,
    smsTemplates,
    channels,
    resetConfig
  } = useNotificationConfig();

  const templateName = useParams().templateName!;

  const { data: notificationDetail, isLoading: isNotificationDetailLoading } =
    useNotificationTemplateDetail({ templateName, enabled: Boolean(templateName) });

  useEffect(() => {
    resetConfig();
    if (templateName && !isNotificationDetailLoading && notificationDetail) {
      updateNotificationConfig(notificationDetail);
    }
  }, []);

  const navigate = useCustomNavigate();

  const createTemplateMutation = useCreateNotificationTemplate({
    onSuccess: () => {
      showSuccessToast(`Notification Template created successfully`);
      navigate("/notifications/templates");
    }
  });

  const updateTemplateMutation = useUpdateNotificationTemplate({
    onSuccess: () => {
      showSuccessToast(`Notification Template updated successfully`);
      navigate(-1);
    }
  });

  const submitHandler = async () => {
    if (
      !validateTemplateForm(
        {
          details,
          channels,
          pushTemplates,
          emailTemplates,
          smsTemplates,
          teamsTemplates
        },
        setActiveSection
      )
    ) {
      // Validation failed, stop form submission
      return;
    }
    const payload = {
      templateName: details.templateName,
      description: details.description,
      pushTemplates,
      emailTemplates: channels.emailTemplates ? emailTemplates : [],
      smsTemplates: channels.smsTemplates ? smsTemplates : [],
      teamsTemplates: channels.teamsTemplates ? teamsTemplates : []
    };

    if (templateName) {
      updateTemplateMutation.mutate({ templateName, body: payload });
    } else {
      createTemplateMutation.mutate(payload);
    }
  };
  return (
    <div className="mt-2 space-y-4">
      {templateName && isNotificationDetailLoading ? (
        <PageSekeleton />
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <HeadingIcon Icon={Bell} title="Notification Template" />

            <div className="flex gap-2">
              <Button
                color="gray"
                onClick={() => navigate("/notifications/templates")}
                startIcon={<X size={BUTTON_ICON_SIZE} />}
              >
                Cancel
              </Button>
              <Button
                startIcon={
                  templateName ? (
                    <Check size={BUTTON_ICON_SIZE} />
                  ) : (
                    <Plus size={BUTTON_ICON_SIZE} />
                  )
                }
                onClick={submitHandler}
                loading={createTemplateMutation.isPending || updateTemplateMutation.isPending}
              >
                {templateName ? "Update" : "Create"} Template
              </Button>
            </div>
          </div>
          <div className="flex gap-6">
            <SidebarContent activeSection={activeSection} setActiveSection={setActiveSection} />

            <div className="flex-1">
              {activeSection === "details" && <TemplateDetailsSection />}

              {activeSection === "channels" && <ChannelsSection />}

              {activeSection === "email" && <EmailSection />}

              {activeSection === "inapp" && <InAppNotification />}

              {activeSection === "sms" && <SmsSection />}

              {activeSection === "teams" && <TeamsSection />}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddNotificationTemplate;
