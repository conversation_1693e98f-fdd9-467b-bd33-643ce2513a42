import Button from "@components/Button";
import { Card } from "@components/ui";
import ConfirmPrompt from "@components/ConfirmPrompt";
import DataNotFound from "@components/DataNotFound";
import DetailsCell from "@components/DetailsCell";
import HeadingIcon from "@components/HeadingIcon";
import Label from "@components/Label";
import NoDataFound from "@components/NoDataFound";
import PageSekeleton from "@components/PageSekeleton";
import { Badge } from "@components/shadcn/components/badge";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import { BUTTON_ICON_SIZE } from "@frontend/shared/config/defaults";
import useDeleteSchedule from "@hooks/notifications/useDeleteSchedule";
import useScheduleDetails from "@hooks/notifications/useScheduleDetail";
import useCustomNavigate from "@hooks/useCustomNavigate";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { showSuccessToast } from "@utils/index";
import clsx from "clsx";
import { CalendarClock, CalendarIcon, Clock, Edit2, Trash } from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";

const WEEK_LIST = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"];

const ScheduleDetailPage = () => {
  const scheduleId = useParams().scheduleId!;
  const [showScheduleDeletePrompt, setShowScheduleDeletePrompt] = useState(false);

  const navigate = useCustomNavigate();
  const { data: scheduleData, isLoading: isScheduleDataLoading } = useScheduleDetails({
    scheduleId
  });

  const deleteScheduleMutation = useDeleteSchedule({
    onSuccess: () => {
      showSuccessToast("Schedule deleted successfully");
      navigate("/notifications/schedules");
    }
  });

  return isScheduleDataLoading ? (
    <PageSekeleton />
  ) : !scheduleData ? (
    <Card>
      <DataNotFound />
    </Card>
  ) : (
    <section className="grid grid-cols-2 gap-4">
      <Card className="space-y-6">
        <div className="flex items-center justify-between">
          <HeadingIcon Icon={CalendarClock} title="Schedule Details" />
          <div className="flex gap-4 ">
            <Button
              onClick={() => {
                navigate("edit");
              }}
              color="gray"
              startIcon={<Edit2 size={BUTTON_ICON_SIZE} />}
            >
              Edit
            </Button>
            <Button
              onClick={() => setShowScheduleDeletePrompt(true)}
              startIcon={<Trash size={BUTTON_ICON_SIZE} />}
              color="red"
            >
              Delete
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <DetailsCell title={"Name"} data={scheduleData?.name} />
          <DetailsCell title={"Time zone"} data={scheduleData?.timezone} />
          <DetailsCell
            title={"Status"}
            data={
              <Label
                text={scheduleData?.status ? "Active" : "Inactive"}
                color={scheduleData?.status ? "green" : "red"}
              />
            }
          />
          <DetailsCell title={"Pattern"} data={scheduleData?.pattern} />

          <DetailsCell
            title={"Schedule Starting Date"}
            data={convetUTCToLocal(scheduleData?.startDate, "DD MMM YYYY")}
          />
          <DetailsCell
            title={"Schedule Ending Date"}
            data={
              scheduleData?.endDate ? convetUTCToLocal(scheduleData?.endDate, "DD MMM YYYY") : "N/A"
            }
          />
          <DetailsCell title={"Create Date"} data={convetUTCToLocal(scheduleData?.createdAt)} />
          <DetailsCell title={"Update Date"} data={convetUTCToLocal(scheduleData?.updatedAt)} />
        </div>
        {scheduleData?.pattern === "custom" && (
          <div className="space-y-4">
            <hr className="hr my-4" />
            <h3 className="text-lg !font-medium">Working Days</h3>
            {!scheduleData?.workingDays.length ? (
              <NoDataFound content="No Non Working Days Found" />
            ) : (
              <div className="grid gap-4 grid-cols-3">
                {scheduleData?.workingDays.map((day) => (
                  <Card key={day} variant="third" className=" flex items-center gap-2">
                    <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                    <span className="text-sm">{convetUTCToLocal(day, "DD MMMM YYYY ")}</span>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
        {scheduleData?.pattern === "weekly" && (
          <div className="space-y-4">
            <hr className="hr my-4" />
            <h3 className="text-lg !font-medium"> Working Days</h3>

            <div className="grid grid-cols-7 gap-2">
              {WEEK_LIST.map((day) => (
                <div key={day} className="flex flex-col items-center">
                  <ShadcnButton
                    type="button"
                    variant="outline"
                    className={clsx(
                      "w-full h-10 capitalize",
                      scheduleData?.workingDays.includes(day) ? "!bg-emerald-500  text-white" : ""
                    )}
                  >
                    {day}
                  </ShadcnButton>
                </div>
              ))}
            </div>
          </div>
        )}

        {scheduleData?.pattern !== "custom" && (
          <div className="space-y-4">
            <hr className="hr my-4" />
            <h3 className="text-lg !font-medium">Non Working Days</h3>
            {!scheduleData?.nonWorkingDates.length ? (
              <NoDataFound content="No Non Working Days Found" />
            ) : (
              <div className="grid gap-4 grid-cols-3">
                {scheduleData?.nonWorkingDates.map((day) => (
                  <Card variant="third" key={day} className=" flex items-center gap-2">
                    <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                    <span className="text-sm">{convetUTCToLocal(day, "DD MMMM YYYY ")}</span>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
      </Card>
      <Card className="space-y-6">
        <HeadingIcon Icon={CalendarClock} title="Shifts " />
        <div className="space-y-4 ">
          {!scheduleData?.shifts.length ? (
            <div className="center ">
              <DataNotFound title="No Shifts Available" />
            </div>
          ) : (
            scheduleData?.shifts.map((shift) => (
              <Card variant="second" key={shift.id} className="">
                <div className="space-y-2">
                  <div className="flex items-start">
                    <Clock className="h-6 w-6 mt-1 mr-2 text-muted-foreground" />
                    <div className="space-y-2 flex-1">
                      <div className="between items-center gap-4">
                        <p className="font-medium capitalize">{shift.name}</p>
                        <Badge>{shift.assignees?.type}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {convetUTCToLocal(shift.startTime, "h:mm:ss A")} -{" "}
                        {convetUTCToLocal(shift.endTime, "h:mm:ss A")}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {shift.assignees &&
                          shift.assignees.members.map((assignee) => <Badge>{assignee.name}</Badge>)}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </Card>
      <ConfirmPrompt
        show={showScheduleDeletePrompt}
        item={scheduleData?.name}
        validate
        onCancel={() => setShowScheduleDeletePrompt(false)}
        onConfirm={() => {
          deleteScheduleMutation.mutate({ scheduleId });
        }}
        loading={deleteScheduleMutation.isPending}
      />
    </section>
  );
};

export default ScheduleDetailPage;
