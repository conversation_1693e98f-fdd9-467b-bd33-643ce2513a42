import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import HeaderSection from "@components/layout/HeaderSection";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import { Card } from "@components/ui";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@frontend/shared/config/defaults";
import useTablePagination from "@hooks/classic/useTablePagination";
import useScheduleList from "@hooks/notifications/useScheduleList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import { getTableIndex } from "@utils/tableUtils";
import { CalendarClock, Plus, Search } from "lucide-react";

const SchedulesList = () => {
  const { page, limit, setPage, searchQuery, setSearchQuery, setLimit } = useTablePagination();
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const { data: schedulesList, isLoading: isLoadingSchedules } = useScheduleList({});
  return (
    <main className="space-y-4">
       <HeaderSection
        title="Schedules"
        description="Manage your Schedules"
        actions={
          <Button
           startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            type="submit"
            onClick={() => navigate("add-schedules")}
            // noAccess={permissions.notificationTemplates !== "write"}
          >
             Add Schedules
          </Button>
        }
      />
      <Card className="space-y-4">
          <Input
            value={searchQuery}
            onChange={(e) => {
              setPage(1);
              setSearchQuery(e.target.value);
            }}
            className="flex-1"
            placeholder="Search"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />
    
      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
            <TableHead onSort={(order) => sort("pattern", order)}>Pattern </TableHead>
            <TableHead onSort={(order) => sort("startDate", order)}>Start Date</TableHead>
            <TableHead onSort={(order) => sort("endDate", order)}>End Date</TableHead>
            <TableHead onSort={(order) => sort("status", order)}>Status </TableHead>

            <TableHead onSort={(order) => sort("createdAt", order)}>Created Date</TableHead>
            <TableHead onSort={(order) => sort("updatedAt", order)}>Updated Date</TableHead>
          </>
        }
        body={
          isLoadingSchedules ? (
            <TableRowsSkeleton />
          ) : !schedulesList || !schedulesList?.length ? (
            <DataNotFound
              title={!schedulesList ? "Something went wrong" : "No Schedules Available"}
              isTable
            />
          ) : (
            schedulesList.toSorted(sortFn).map((schedule, i) => (
              <tr
                key={schedule.id}
                className="cursor-pointer"
                data-testid={schedule.id}
                onClick={() => navigate(`${schedule.id}`)}
              >
                <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                <TableRow title>{schedule.name}</TableRow>
                <TableRow className="capitalize">{schedule.pattern}</TableRow>
                <TableRow>{convetUTCToLocal(schedule.startDate, "DD MMM YYYY ")}</TableRow>
                <TableRow>
                  {schedule.endDate ? convetUTCToLocal(schedule.endDate, "DD MMM YYYY ") : "N/A"}
                </TableRow>

                <TableRow>
                  <Label
                    text={schedule.status ? "Active" : "Inactive"}
                    color={schedule.status ? "green" : "red"}
                  />
                </TableRow>
                <TableRow>{convetUTCToLocal(schedule.createdAt)}</TableRow>
                <TableRow>{convetUTCToLocal(schedule.updatedAt)}</TableRow>
              </tr>
            ))
          )
        }
        pagination={{
          page,
          setPage,
          setLimit,
          totalPages: 1
        }}
      />
        </Card>
    </main>
  );
};

export default SchedulesList;
