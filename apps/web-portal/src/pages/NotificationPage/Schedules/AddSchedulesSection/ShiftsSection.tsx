import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import NoDataFound from "@components/NoDataFound";
import { Button } from "@components/shadcn/components/button";
import { TimePicker } from "@components/shadcn/components/date-time-picker";
import { Card } from "@components/ui";
import useSchedulesConfig from "@hooks/classic/useSchedulesConfig";
import useContactList from "@hooks/notifications/useContactList";
import useContactTeamsList from "@hooks/notifications/useContactTeamsList";
import useDebounce from "@hooks/useDebounce";
import { generateRandomId } from "@src/pages/MonitorPage/utils";
import { Plus, Trash2Icon } from "lucide-react";
import { useState } from "react";

type Shift = {
  id: string;
  name: string;
  startTime?: Date;
  endTime?: Date;
  assignees: string[] | null; 
  targetType: "Contacts" | "Group";
}

const ShiftsSection = () => {
  const { shifts, editDetails } = useSchedulesConfig();
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: contactList, isLoading: isContactListLoading } = useContactList({
    search: debouncedSearchQuery
  });
  const { data: teamsList, isLoading: teamsListLoading } = useContactTeamsList({});

  const editShift = <K extends keyof Shift> (id: string, field: K, value: Shift[K]) => {
    editDetails(
      "shifts",
      shifts.map((shift) => (shift.id === id ? { ...shift, [field]: value } : shift))
    );
  };

  const editShiftMultipleValues = (id: string, updates: Partial<Shift> ) => {
    editDetails(
      "shifts",
      shifts.map((shift) => (shift.id === id ? { ...shift, ...updates } : shift))
    );
  };

  // Usage

  const addNewShift = () => {
    const newShift: Shift = {
      id: generateRandomId(16),
      name: "",
      startTime: undefined,
      endTime: undefined,
      assignees: [],
      targetType: "Contacts"
    };
    editDetails("shifts", [...shifts, newShift]);
  };
  const removeShift = (id: string) => {
    editDetails(
      "shifts",
      shifts.filter((shift) => shift.id !== id)
    );
  };

  return (
    <Card className="space-y-4">
      <div className="flex flex-row justify-between items-center">
        <h3 className=" text-lg font-medium">Shifts & Assignments</h3>
        <Button onClick={addNewShift} size="sm">
          <Plus className="h-4 w-4 mr-2" /> Add Shift
        </Button>
      </div>
      <div className="grid grid-cols-2 gap-4">
        {!shifts.length ? (
          <NoDataFound className="col-span-2" content="No Shifts Added" />
        ) : (
          shifts.map((shift) => (
            <Card variant="second" className=" space-y-4">
              <div className="flex flex-row items-center justify-between">
                <Input
                  value={shift.name}
                  placeholder="Shift Name"
                  className=" w-2/3"
                  onChange={(e) => editShift(shift.id, "name", e.target.value)}
                  // value={shift.name} onChange={(e) => onEdit("name", e.target.value)}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeShift(shift.id)}
                  className="h-8 w-8"
                >
                  <Trash2Icon className="h-4 w-4" />
                </Button>
              </div>
              <div className=" grid grid-cols-2 gap-4">
                <div className="mt-auto  flex flex-col items-start gap-2">
                  <p className=" input-label-text mb-1/2">Start Time</p>
                  <TimePicker
                    date={shift.startTime}
                    hideIcon
                    hourCycle={12}
                    granularity="minute"
                    onChange={(newTime) => {
                      if (!newTime) {
                        return;
                      }
                      editShift(shift.id, "startTime", newTime);
                    }}
                  />
                </div>
                <div className="mt-auto  flex flex-col items-start gap-2">
                  <p className=" input-label-text mb-1/2">End Time</p>
                  <TimePicker
                    date={shift.endTime}
                    hideIcon
                    hourCycle={12}
                    granularity="minute"
                    onChange={(newTime) => {
                      if (!newTime) {
                        return;
                      }
                      editShift(shift.id, "endTime", newTime);
                    }}
                  />
                </div>
                <Dropdown
                  className="w-full "
                  onChange={(option) => {
                    if (option === "Contacts") {
                      editShiftMultipleValues(shift.id, { assignees: [], targetType: option });
                    } else {
                      editShiftMultipleValues(shift.id, { assignees: null, targetType: option });
                    }
                  }}
                  options={["Contacts", "Group"]}
                  value={(shift as Shift).targetType }
                  label="Type"
                  required
                  placeHolder="Select Type"
                />
                {(shift as Shift).targetType === "Contacts" ? (
                  <Dropdown
                    className="w-full col-span-2"
                    onChange={(option) => {
                      editShift(shift.id, "assignees", option);
                    }}
                    options={contactList?.contacts || []}
                    optionsLoading={isContactListLoading}
                    value={shift.assignees || []}
                    deepSearch={(value) => setSearchQuery(value)}
                    isSearchable
                    label="Contact Name"
                    required
                    isMulti
                    getOptionLabel={"name"}
                    placeHolder="Select name"
                    key="contact"
                  />
                ) : (
                  <Dropdown
                    className="w-full col-span-2"
                    onChange={(option) => {
                      editShift(shift.id, "assignees", option);
                    }}
                    options={teamsList || []}
                    optionsLoading={teamsListLoading}
                    value={shift.assignees}
                    isSearchable
                    label="Groups"
                    required
                    getOptionLabel={"name"}
                    placeHolder="Select Group Name"
                  />
                )}
              </div>
            </Card>
          ))
        )}
      </div>
    </Card>
  );
};

export default ShiftsSection;
