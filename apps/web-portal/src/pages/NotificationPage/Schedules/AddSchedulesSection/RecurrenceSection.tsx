import { Button } from "@components/shadcn/components/button";
import { DateTimePicker } from "@components/shadcn/components/date-time-picker";
import { RadioGroup, RadioGroupItem } from "@components/shadcn/components/radio-group";
import useSchedulesConfig from "@hooks/classic/useSchedulesConfig";
import { convetUTCToLocal } from "@src/pages/UserTypes/Tracking/HomeSection/utils";
import clsx from "clsx";
import { CalendarIcon, Plus, Trash2Icon } from "lucide-react";
import { useState } from "react";
import { Card, Card as DefaultCard } from "@components/ui";

const WEEK_LIST = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"];

const RecurrenceSection = () => {
  const [currentCustomDate, setCurrentCustomDate] = useState<Date | null>();
  const [currentNonWorkingDate, setCurrentNonWorkingDate] = useState<Date | null>();
  const { pattern, editDetails, workingDays, nonWorkingDates } =
    useSchedulesConfig();
  const setRecurrenceType = (value:string) => {
    if (value === "custom") {
      editDetails("nonWorkingDates", []);
    }
    editDetails("pattern", value);
    editDetails("workingDays", []);
  };
  const toggleWorkingDay = (day: string) => {
    if (workingDays.includes(day)) {
      editDetails(
        "workingDays",
        workingDays.filter((currentDay) => currentDay !== day)
      );
      return;
    }
    editDetails("workingDays", [...workingDays, day]);
  };
  const toggleNonWorkingDay = (day:string) => {
    if (nonWorkingDates.includes(day)) {
      editDetails(
        "nonWorkingDates",
        nonWorkingDates.filter((currentDay) => currentDay !== day)
      );
      return;
    }
    editDetails("nonWorkingDates", [...nonWorkingDates, day]);
  };

  return (
    <Card className="space-y-4">
      <h3 className=" text-lg font-medium">Recurrence Pattern</h3>
      <div className="space-y-4">
        <RadioGroup
          defaultValue="daily"
          value={pattern}
          onValueChange={setRecurrenceType}
          className="space-y-2"
        >
          <div className="flex items-start space-x-2">
            <RadioGroupItem
              value="daily"
              id="daily"
              className="mt-1.5 text-foreground border-foreground"
            />
            <div className=" ">
              <label htmlFor="daily" className=" font-medium">
                Daily
              </label>
              <p className="text-sm text-muted-foreground">Repeat every day</p>
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <RadioGroupItem
              value="weekly"
              id="weekly"
              className="mt-1.5 text-foreground border-foreground"
            />
            <div className="">
              <label htmlFor="weekly" className="font-medium">
                Weekly
              </label>
              <p className="text-sm text-muted-foreground mb-2">
                Repeat on specific days of the week
              </p>

              {pattern === "weekly" && (
                <div className="grid grid-cols-7 gap-2">
                  {WEEK_LIST.map((day) => (
                    <div key={day} className="flex flex-col items-center">
                      <Button
                        type="button"
                        variant="outline"
                        className={clsx(
                          "w-full h-10 capitalize",
                          workingDays.includes(day) ? "!bg-emerald-500  text-white" : ""
                        )}
                        onClick={() => toggleWorkingDay(day)}
                      >
                        {day}
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <RadioGroupItem
              value="custom"
              id="custom"
              className="mt-1.5 text-foreground border-foreground"
            />
            <div className="w-full">
              <label htmlFor="custom" className="font-medium">
                Custom Dates
              </label>
              <p className="text-sm text-muted-foreground">
                Select specific individual dates without a regular pattern
              </p>
              {pattern === "custom" && (
                <div className="space-y-4 mt-2">
                  <div className="flex items-end gap-2">
                    <div className="w-[25rem]">
                      <DateTimePicker
                        value={currentCustomDate ?? undefined }
                        granularity="day"
                        onChange={(newValue) => {
                          if (!newValue) return;
                          setCurrentCustomDate(newValue);
                        }}
                      />
                    </div>
                    <Button
                      className="!h-10"
                      onClick={() => {
                        if (!currentCustomDate || workingDays.includes(String(currentCustomDate))) return;
                        toggleWorkingDay(String(currentCustomDate));
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </div>
                  <div className="grid gap-4 grid-cols-3">
                    {workingDays.map((day) => (
                      <DefaultCard
                        key={day}
                        variant="third"
                        className=" flex items-center justify-between"
                      >
                        <div className=" flex items-center gap-2">
                          <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                          <span className="text-sm">{convetUTCToLocal(day, "DD MMMM YYYY ")}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => toggleWorkingDay(day)}
                          className="h-8 w-8"
                        >
                          <Trash2Icon className="h-4 w-4" />
                        </Button>
                      </DefaultCard>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </RadioGroup>
        {pattern !== "custom" && (
          <>
            <hr className="hr" />
            <h3 className=" heading-3">Non-Working Days</h3>
            <div className="space-y-4">
              <div className="flex items-end gap-2">
                <div className="w-[25rem]">
                  <label className="input-label-text">Select Date</label>
                  <DateTimePicker
                    value={currentNonWorkingDate ?? undefined}
                    granularity="day"
                    onChange={(newValue) => {
                      if (!newValue) return;
                      setCurrentNonWorkingDate(newValue);
                    }}
                  />
                </div>
                <Button
                  onClick={() => {
                    if (!currentNonWorkingDate || nonWorkingDates.includes(String(currentNonWorkingDate)))
                      return;
                    toggleNonWorkingDay(String(currentNonWorkingDate));
                  }}
                  className="!h-10"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add
                </Button>
              </div>
              <div className="grid gap-4 grid-cols-3">
                {nonWorkingDates.map((day) => (
                  <DefaultCard
                    key={day}
                    variant="third"
                    className=" r flex items-center justify-between"
                  >
                    <div className=" flex items-center gap-2">
                      <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                      <span className="text-sm">{convetUTCToLocal(day, "DD MMMM YYYY ")}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => toggleNonWorkingDay(day)}
                      className="h-8 w-8"
                    >
                      <Trash2Icon className="h-4 w-4" />
                    </Button>
                  </DefaultCard>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </Card>
  );
};

export default RecurrenceSection;
