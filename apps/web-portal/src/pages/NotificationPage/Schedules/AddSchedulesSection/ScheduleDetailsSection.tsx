import Dropdown from "@components/Dropdown";
import Input from "@components/Input";
import { DateTimePicker } from "@components/shadcn/components/date-time-picker";
import { Card } from "@components/ui";
import useSchedulesConfig from "@hooks/classic/useSchedulesConfig";

const ScheduleDetailsSection = () => {
  const { name, timezone, status, editDetails, startDate, endDate } = useSchedulesConfig();

  return (
    <Card className="space-y-4">
      <h3 className=" text-lg font-medium">Schedule Details</h3>
      <div className="space-y-4">
        <Input
          label="Schedule Name"
          placeholder="Schedule Name"
          description="Choose a clear and descriptive name that identifies the purpose of this template."
          value={name}
          onChange={(e) => editDetails("name", e.target.value)}
          //   disabled={Boolean(templateName)}
        />

        <div className="grid grid-cols-2 gap-4">
          <Dropdown
            onChange={(option) => {
              editDetails("timezone", option);
            }}
            placeHolder="Select Status"
            options={["UTC", "Asia/Kolkata", "America/New_York"]}
            label="Time Zone"
            required
            value={timezone}
            description="Select the device status: pending (awaiting provisioning), active (provisioned and in use), or expired (no longer valid or decommissioned)."
          />
          <Dropdown
            onChange={(option) => {
              editDetails("status", option);
            }}
            placeHolder="Select Status"
            options={[
              { label: "Active", value: true },
              { label: "Inactive", value: false }
            ]}
            label="Status"
            required
            value={status}
            getOptionLabel="label"
            description="Select the device status: pending (awaiting provisioning), active (provisioned and in use), or expired (no longer valid or decommissioned)."
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="input-label-text">Start Date *</label>
            <DateTimePicker
              value={startDate}
              granularity="day"
              onChange={(newValue) => {
                if (!newValue) return;
                editDetails("startDate", newValue);
              }}
            />
          </div>
          <div>
            <label className="input-label-text">End Date (Optional)</label>
            <DateTimePicker
              value={endDate ?? undefined}
              granularity="day"
              onChange={(newValue) => {
                if (!newValue) return;
                editDetails("endDate", newValue);
              }}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ScheduleDetailsSection;
