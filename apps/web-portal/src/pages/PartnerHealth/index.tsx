import CustomTooltip from "@components/CustomTooltip";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import FormDialog from "@components/FormDialog";
import useTenantHealthList from "@hooks/tenant/useTenantHealthList";
import useTenantHealthVersions from "@hooks/tenant/useTenantHealthVersions";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useDebounce from "@hooks/useDebounce";
import useTableSort from "@hooks/useTableSort";
import { getTableIndex } from "@utils/tableUtils";
import { BUTTON_ICON_SIZE, DEFAULT_PAGE_COUNT, INPUT_ICON_SIZE } from "@utils/utilities";
import { validate as validateVersion } from "compare-versions";
import { Activity, AlertTriangle, CheckCircle, CloudAlert, Plus, Search, X } from "lucide-react";
import { useEffect, useState } from "react";
import { updatePartnerHealthVersions } from "../../api/partner";
import Button from "../../components/Button";
import Input from "../../components/Input";
import Label from "../../components/Label";
import Table, { TableHead, TableRow } from "../../components/Table";
import TableRowsSkeleton from "../../components/Table/TableRowsSkeleton";
import { showErrorToast, showSuccessToast } from "../../utils";
import { convetUTCToLocal } from "../UserTypes/Tracking/HomeSection/utils";

const PartnerHealthPage = () => {
  const [showUpdateDrawer, setShowUpdateDrawer] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [seviceVersions, setSeviceVersions] = useState({
    broker: "",
    exhook: "",
    vector: ""
  });
  const [currentseviceVersions, setCurrentSeviceVersions] = useState({
    broker: "",
    exhook: "",
    vector: ""
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_PAGE_COUNT);

  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { data: tenantHealthList, isLoading: tenantHealthLoading } = useTenantHealthList({
    page,
    limit,
    search: debouncedSearchQuery
  });

  const { data: tenantServiceVersions, isLoading: tenantServiceVersionsLoading } =
    useTenantHealthVersions();

  useEffect(() => {
    if (!tenantServiceVersionsLoading) {
      const newObj = {};
      const obj = tenantServiceVersions;

      for (const key in obj) {
        if (obj.hasOwnProperty(key) && Array.isArray(obj[key]) && obj[key].length > 0) {
          newObj[key] = obj[key][0];
        }
      }

      setCurrentSeviceVersions(newObj);
      setSeviceVersions(newObj);
    }
  }, [tenantServiceVersions]);

  const resetFormState = () => {
    setShowUpdateDrawer(false);
    setSeviceVersions({
      broker: "",
      exhook: "",
      vector: ""
    });
  };
  const submitHandler = async (e) => {
    e.preventDefault();

    setLoadingBtn(true);

    const resp = await updatePartnerHealthVersions({
      versions: seviceVersions
    });
    setLoadingBtn(false);
    if (resp.status === "Success") {
      resetFormState();
      showSuccessToast("Version Updated Successfully");
    } else {
      showErrorToast(resp.message);
    }
  };
  const handleVersionError = (version, previousVersion) => {
    if (version === "") return "";
    if (!previousVersion) return "";
    if (!validateVersion(version)) return "Invalid version";

    return "";
  };

  return (
    <section>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <HeadingIcon title="Tenants Health" Icon={Activity} />
          <div className="flex gap-4 items-center">
            <Input
              value={searchQuery}
              onChange={(e) => {
                setPage(1);
                setSearchQuery(e.target.value);
              }}
              placeholder="Search"
              className="w-[25rem]"
              endIcon={<Search size={INPUT_ICON_SIZE} />}
            />
            <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              onClick={() => setShowUpdateDrawer(true)}
            >
              Add Version
            </Button>
          </div>
        </div>
        <Table
          head={
            <>
              <TableHead>No.</TableHead>
              <TableHead onSort={(order) => sort("name", order)}>Name</TableHead>
              <TableHead onSort={(order) => sort("healthStatus", order)}>Health</TableHead>
              <TableHead>Image Versions</TableHead>
              <TableHead onSort={(order) => sort("lastSyncAt", order)}>Last Sync</TableHead>
              <TableHead onSort={(order) => sort("createdAt", order)}>Created At</TableHead>
            </>
          }
          body={
            tenantHealthLoading ? (
              <TableRowsSkeleton />
            ) : !tenantHealthList?.tenantsHealth.length ? (
              <DataNotFound title="No Tenants Available" isTable />
            ) : (
              tenantHealthList.tenantsHealth.toSorted(sortFn).map((partner, i) => (
                <tr
                  onClick={() => {
                    navigate(partner.tenant);
                  }}
                  className="cursor-pointer"
                  key={partner.name}
                >
                  <TableRow>{getTableIndex(page, limit, i)}</TableRow>

                  <TableRow title>
                    <div className="flex items-center gap-2">
                      <CustomTooltip title={partner.syncStatus}>
                        {partner.syncStatus === "Synced" ? (
                          <CheckCircle size={BUTTON_ICON_SIZE} className="text-green-500" />
                        ) : partner.syncStatus === "OutOfSync" ? (
                          <CloudAlert size={BUTTON_ICON_SIZE} className="text-amber-500 " />
                        ) : (
                          <AlertTriangle size={BUTTON_ICON_SIZE} className="text-red-500" />
                        )}
                      </CustomTooltip>
                      {partner.name}
                    </div>
                  </TableRow>
                  <TableRow>
                    <Label
                      color={partner.healthStatus === "Healthy" ? "green" : "red"}
                      text={partner.healthStatus}
                    />
                  </TableRow>

                  <TableRow className="truncate">
                    <div className="flex gap-4">
                      {Object.entries(partner.imagesVersion).map(([key, value], i) => (
                        <Label
                          key={key}
                          color={i === 0 ? "blue" : i === 1 ? "green" : "orange"}
                          text={`${key} : ${value}`}
                        />
                      ))}
                    </div>
                  </TableRow>

                  <TableRow>{convetUTCToLocal(partner.lastSyncAt)}</TableRow>
                  <TableRow>{convetUTCToLocal(partner.createdAt)}</TableRow>
                </tr>
              ))
            )
          }
          pagination={{
            page,
            setPage,
            setLimit,
            totalPages: tenantHealthList?.pages || 0
          }}
        />
      </div>
      <FormDialog
        open={showUpdateDrawer}
        onClose={resetFormState}
        title="Add new health version"
        footer={
          <div className="flex gap-4 justify-end">
            <Button
              onClick={resetFormState}
              small
              color="gray"
              type="button"
              startIcon={<X size={BUTTON_ICON_SIZE} />}
            >
              Close
            </Button>
            <Button
              startIcon={<Plus size={BUTTON_ICON_SIZE} />}
              small
              loading={loadingBtn}
              type="submit"
              form="update-health-version-form"
            >
              Add
            </Button>
          </div>
        }
      >
        <form className="space-y-4" onSubmit={submitHandler} id="update-health-version-form">
          <h3 className="sidebar-sub-heading">Broker</h3>
          <div className="grid grid-cols-2 gap-2">
            <Input label="Current Version" value={currentseviceVersions.broker} disabled />
            <Input
              required
              defaultValue={currentseviceVersions.broker}
              error={handleVersionError(seviceVersions.broker, currentseviceVersions.broker)}
              onBlur={(e) => {
                setSeviceVersions((prev) => ({
                  ...prev,
                  broker: e.target.value
                }));
              }}
              helperText={
                handleVersionError(seviceVersions.broker, currentseviceVersions.broker) &&
                `Invalid Version`
              }
              label="New Version"
            />
          </div>
          <hr className="hr !my-4" />
          <h3 className="sidebar-sub-heading">Exhook</h3>
          <div className="grid grid-cols-2 gap-2">
            <Input label="Current Version" value={currentseviceVersions.exhook} disabled />
            <Input
              required
              defaultValue={currentseviceVersions.exhook}
              error={handleVersionError(seviceVersions.exhook, currentseviceVersions.exhook)}
              helperText={
                handleVersionError(seviceVersions.exhook, currentseviceVersions.exhook) &&
                `Invalid Version`
              }
              label="New Version"
              onBlur={(e) => {
                setSeviceVersions((prev) => ({
                  ...prev,
                  exhook: e.target.value
                }));
              }}
            />
          </div>
          <hr className="hr !my-4" />

          <h3 className="sidebar-sub-heading">Vector</h3>
          <div className="grid grid-cols-2 gap-2">
            <Input label="Current Version" value={currentseviceVersions.vector} disabled />
            <Input
              required
              defaultValue={currentseviceVersions.vector}
              error={handleVersionError(seviceVersions.vector, currentseviceVersions.vector)}
              helperText={
                handleVersionError(seviceVersions.vector, currentseviceVersions.vector) &&
                `Invalid Version`
              }
              label="New Version"
              onBlur={(e) => {
                setSeviceVersions((prev) => ({
                  ...prev,
                  vector: e.target.value
                }));
              }}
            />
          </div>
        </form>
      </FormDialog>
    </section>
  );
};

export default PartnerHealthPage;
