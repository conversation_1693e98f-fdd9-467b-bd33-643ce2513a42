import Button from "@components/Button";
import DataNotFound from "@components/DataNotFound";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import Label from "@components/Label";
import Table, { TableHead, TableRow } from "@components/Table";
import TableRowsSkeleton from "@components/Table/TableRowsSkeleton";
import useProductTemplateList from "@hooks/product/useProductTemplateList";
import useCustomNavigate from "@hooks/useCustomNavigate";
import useTableSort from "@hooks/useTableSort";
import { PRODUCT_DEFAULT_IMAGE } from "@utils/deviceMapping";
import { BUTTON_ICON_SIZE, INPUT_ICON_SIZE } from "@utils/utilities";
import { Folder, Search, Plus } from "lucide-react";
import { useState } from "react";

const generateColor = (type) => {
  if (!type || typeof type !== "string") return "gray";
  switch (type) {
    case "standard":
    case "shadow":
      return "green";
    case "managed":
      return "orange";
    case "simulated":
      return "violet";
    case "gatewaymanaged":
      return "yellow";
    case "timeSeries":
      return "blue";
    default:
      return "gray";
  }
};

const turncateText = (text = "") => {
  const truncatedText = text.slice(0, 50) + (text.length > 50 ? "..." : "");
  return truncatedText;
};
const ProductTemplateList = ({ type }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useCustomNavigate();
  const [sortFn, sort] = useTableSort();

  const { data: productTemplateList, isLoading: isProductTemplateLoading } =
    useProductTemplateList();

  return (
    <div className="space-y-6">
      <div className="flex justify-between ">
        <HeadingIcon title="Product Templates" Icon={Folder} />
        <div className="flex gap-4 items-center">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="min-w-[25rem]"
            placeholder="Search"
            endIcon={<Search size={INPUT_ICON_SIZE} />}
          />

          <Button
            startIcon={<Plus size={BUTTON_ICON_SIZE} />}
            onClick={() => navigate("addTemplate")}
          >
            Add Product Template
          </Button>
        </div>
      </div>

      <Table
        head={
          <>
            <TableHead>No.</TableHead>
            <TableHead onSort={(order) => sort("name", order)}>Template Name</TableHead>
            <TableHead onSort={(order) => sort("productTemplateType", order)}>
              Template Type
            </TableHead>
            <TableHead onSort={(order) => sort("otaType", order)}>OTA Type</TableHead>
            <TableHead>Authentication</TableHead>
            <TableHead>Data Management</TableHead>
            <TableHead>Enabled</TableHead>
          </>
        }
        body={
          isProductTemplateLoading ? (
            <TableRowsSkeleton />
          ) : !productTemplateList ||
            (type === "msp"
              ? !productTemplateList?.mspTemplates?.length
              : !productTemplateList?.length) ? (
            <DataNotFound title="No Product Template Available" isTable />
          ) : (
            (type === "msp" ? productTemplateList.mspTemplates : productTemplateList)
              .filter((p) => p.name.toLowerCase().includes(searchQuery.toLowerCase()))
              .sort(sortFn)
              .map((p, i) => (
                <tr
                  onClick={() => {
                    navigate(p.name);
                  }}
                  className="cursor-pointer"
                  key={Object.values(p).join()}
                >
                  <TableRow>{i + 1}</TableRow>
                  {/* <TableRow>{p.name}</TableRow> */}
                  <TableRow title>
                    <div className="flex gap-4 items-center">
                      <img
                        className="object-cover w-[2.5rem] rounded-lg"
                        alt="product"
                        src={p.imgURL || PRODUCT_DEFAULT_IMAGE}
                      />
                      <div className="">
                        <p>{p.name}</p>
                        <p className="text-xs text-muted-foreground ">
                          {turncateText(p.description || "")}
                        </p>
                      </div>
                    </div>
                  </TableRow>
                  <TableRow>
                    <Label
                      color={p.productTemplateType === "standard" ? "blue" : "green"}
                      text={`${p.productTemplateType}`}
                    />
                  </TableRow>
                  <TableRow>{p.otaType}</TableRow>
                  <TableRow>{p?.authentication?.type} </TableRow>
                  <TableRow>
                    <div className="flex gap-3">
                      {!p.dataManagement.length
                        ? "N/A"
                        : p.dataManagement.map((item) => (
                            <Label color={generateColor(item)} text={item} lowercase />
                          ))}
                    </div>
                  </TableRow>
                  <TableRow>
                    <Label color={p.enabled ? "green" : "red"} text={`${p.enabled}`} lowercase />
                  </TableRow>
                </tr>
              ))
          )
        }
      />
    </div>
  );
};

export default ProductTemplateList;
