import { fetchApi } from "@api/_helpers";
import { uploadProductImage } from "@api/product";
import Button from "@components/Button";
import ColorPicker from "@components/ColorPicker";
import ConfirmPrompt from "@components/ConfirmPrompt";
import HeadingIcon from "@components/HeadingIcon";
import Input from "@components/Input";
import PageSekeleton from "@components/PageSekeleton";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@components/shadcn/components/accordian";
import { Button as ShadcnButton } from "@components/shadcn/components/button";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Card as ShadcnCard
} from "@components/shadcn/components/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@components/shadcn/components/dialog";
import { Label } from "@components/shadcn/components/label";
import { Separator } from "@components/shadcn/components/separator";
import useCustomizations, {
  DEFAULT_WHITE_LABEL_CUSTOMIZATION
} from "@hooks/classic/useCustomisetions";
import useUserGroupPermissions from "@hooks/classic/useUserGroupPermissions";
import {
  fetchWhitLabelByHost,
  loadWhiteLabel as syncTheme
} from "@hooks/whiteLabel/loadWhiteLabel";
import { useMutation } from "@tanstack/react-query";
import deviceMapping from "@utils/deviceMapping";
import { showErrorToast, showSuccessToast } from "@utils/index";
import { BUTTON_ICON_SIZE } from "@utils/utilities";
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from "chart.js";
import {
  Check,
  CheckCircle,
  Copy,
  Globe,
  Info,
  Link,
  Save,
  Settings,
  Shield,
  Trash
} from "lucide-react";
import { ChangeEvent, useEffect, useState } from "react";
import { generateRandomString } from "./MonitorPage/utils";
import Alert from "@components/Alert";
import { Card } from "@components/ui";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  BarElement
);

export default function WhiteLabelPage() {
  const { customization, setCustomization, status, setStatus } = useCustomizations();
  const [loadingWhiteLabel, setLoadingWhiteLabel] = useState(true);
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [copied, setCopied] = useState(false);
  const [verificationText, setVerificationText] = useState<string | null>(null);
  // The modal is open when verificationText has a value
  const isModalOpen = verificationText !== null;

  // Function to handle modal open state changes
  const handleModalOpenChange = (open: boolean) => {
    // If modal is closed, clear the verification text
    if (!open) {
      setVerificationText(null);
    }
  };

  const { data: permissions } = useUserGroupPermissions();
  const loadWhitLabel = async () => {
    try {
      const whitLabel = await fetchWhitLabelByHost();
      setLoadingWhiteLabel(false);
      if (status === "demo") {
        return;
      }

      setCustomization({
        id: whitLabel.id,
        brandColor: whitLabel.metadata.brandColor,
        buttonText: whitLabel.metadata.buttonText,
        darkBg: whitLabel.metadata.darkBg,
        accentColor: whitLabel.metadata.accentColor,
        logoDark: whitLabel.logoDark,
        logoLight: whitLabel.logoLight,
        host: whitLabel.host,
        description: whitLabel.description,
        title: whitLabel.title,
        product_alias: whitLabel.product_alias,
        thing_alias: whitLabel.thing_alias,
        verificationCode: whitLabel.verificationCode
      });
      setStatus(whitLabel.status);
    } catch (error) {
      console.log(error instanceof Error ? error.message : "Error");
    } finally {
      setLoadingWhiteLabel(false);
    }
  };

  const createWhiteLabelMutation = useMutation({
    mutationFn: async (body: Record<string, any>) => {
      const resp = await fetchApi(`/white-label`, {
        method: "POST",
        body
      });
      const responseData = await resp.json();
      if (resp.ok) {
        return responseData;
      }

      throw new Error(responseData.message);
    },
    onError: (error) => {
      console.log(error.message);
      showErrorToast(error.message);
    },
    onSuccess: (data) => {
      showSuccessToast(
        "White label request send successfully. It will be activated once approved."
      );
      setVerificationText(data.data.txtRecordForValidation);
      setCustomization({ verificationCode: data.data.txtRecordForValidation });
      setCustomization({ id: data.data.id });
      setStatus("pending");
    }
  });

  const verifyWhiteLabelMutation = useMutation({
    mutationFn: async (id: string) => {
      const resp = await fetchApi(`/white-label/verify/${id}`, {
        method: "POST"
      });
      const responseData = await resp.json();
      if (resp.ok) {
        return responseData;
      }
      throw new Error(responseData.message);
    },
    onError: (error) => {
      console.log(error.message);
      showErrorToast(error.message);
    },
    onSuccess: (data) => {
      showSuccessToast("White label verified Successfully.");
      loadWhitLabel();
    }
  });

  const updateWhiteLabelMutation = useMutation({
    mutationFn: async ({ body, id }: { body: Record<string, any>; id: string | number }) => {
      const resp = await fetchApi(`/white-label/${id}`, {
        method: "PUT",
        body
      });
      const responseData = await resp.json();
      if (resp.ok) {
        return responseData;
      }

      throw new Error(responseData.message);
    },
    onError: (error) => {
      console.log(error.message);
      showErrorToast(error.message);
    },
    onSuccess: () => {
      showSuccessToast(
        "White update request send successfully. It will be activated once approved."
      );
      setStatus("pending");
    }
  });

  const deleteWhiteLabelMutation = useMutation({
    mutationFn: async ({ id }: { id: string | number }) => {
      const resp = await fetchApi(`/white-label/${id}`, {
        method: "DELETE"
      });
      const responseData = await resp.json();
      if (resp.ok) {
        return responseData;
      }
      throw new Error(responseData.message);
    },
    onError: (error) => {
      console.log(error.message);
      showErrorToast(error.message);
    },
    onSuccess: () => {
      setShowDeletePrompt(false);
      setCustomization({ ...DEFAULT_WHITE_LABEL_CUSTOMIZATION, id: undefined });
      showSuccessToast(
        "White update request send successfully. It will be deactivated once approved."
      );
      setStatus(undefined);
    }
  });

  useEffect(() => {
    loadWhitLabel();
  }, []);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(customization.verificationCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  const handleWhiteLabelDelete = () => {
    deleteWhiteLabelMutation.mutate({ id: customization.id });
  };

  const handleWhiteLabelSave = () => {
    // create if doesn't exist
    if (
      !customization.logoLight ||
      !customization.logoDark ||
      !customization.title ||
      !customization.product_alias ||
      !customization.thing_alias
    ) {
      showErrorToast("Please upload logos and fill all the fields");
      return;
    }

    if (!customization.id) {
      createWhiteLabelMutation.mutate(customization);
      return;
    }
    updateWhiteLabelMutation.mutate({
      body: customization,
      id: customization.id
    });
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomization({ [name]: value });
  };

  const handleLogoUpload = async (
    e: ChangeEvent<HTMLInputElement>,
    mode: "logoLight" | "logoDark"
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setCustomization({ [mode]: reader.result as string });
      };
      reader.readAsDataURL(file);

      const formData = new FormData();
      formData.append("image", file, file.name);

      try {
        const imageUrl = await uploadProductImage(generateRandomString(8), formData);

        setCustomization({
          [mode]: imageUrl.data.fileUrl
        });
      } catch (error: any) {
        console.log(error.message);
      }
    }
  };
  if (loadingWhiteLabel) {
    return <PageSekeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="between items-center">
        <HeadingIcon Icon={Settings} title="Customization Options" />

        <div className="flex gap-4 ">
          {(status === "approved" || status === "pending") && (
            <Button
              onClick={() => {
                setShowDeletePrompt(true);
              }}
              color="red"
              outlined
              startIcon={<Trash size={BUTTON_ICON_SIZE} />}
            >
              Delete
            </Button>
          )}

          <Button
            type="submit"
            noAccess={permissions.whiteLabel !== "write"}
            loading={createWhiteLabelMutation.isPending || updateWhiteLabelMutation.isPending}
            onClick={handleWhiteLabelSave}
            startIcon={<Save size={BUTTON_ICON_SIZE} />}
          >
            Save Customization
          </Button>

          <Button
            type="button"
            disabled={!customization.logoDark || !customization.logoLight}
            color="gray"
            noAccess={permissions.whiteLabel !== "write"}
            onClick={() => {
              syncTheme({
                config: {
                  logoDark: customization.logoDark,
                  logoLight: customization.logoLight,
                  metadata: {
                    brandColor: customization.brandColor,
                    darkBg: customization.darkBg,
                    buttonText: customization.buttonText
                  }
                }
              });
              setStatus("demo");
            }}
          >
            Preview Changes
          </Button>
        </div>
      </div>

      {status !== undefined && (
        <Alert
          Icon={Info}
          title={
            status === "pending" ? "In Review" : status === "approved" ? "Approved" : "In Preview"
          }
          description={
            status === "pending" ? (
              <div className="flex gap-6 items-start justify-between">
                <div className="space-y-2">
                  <p>
                    We are currently awaiting domain verification. Once the domain is verified, the
                    white label feature will be available for use. Please ensure that you have added
                    the provided verification code to the domain's TXT record.
                  </p>
                  <div className="flex flex-col space-y-1">
                    <div className="font-medium text-base">Verification Code:</div>
                    <div className="flex items-center gap-2">
                      <div className="relative w-1/2">
                        <div className="rounded-md border border-card-border bg-secondary p-2 font-mono text-sm">
                          {customization.verificationCode}
                        </div>
                      </div>
                      <ShadcnButton
                        variant="outline"
                        size="icon"
                        onClick={copyToClipboard}
                        className="h-10 w-10 shrink-0"
                        aria-label="Copy verification code"
                      >
                        {copied ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </ShadcnButton>
                    </div>
                  </div>
                </div>
                <Button
                  onClick={() => verifyWhiteLabelMutation.mutate(customization.id)}
                  color="yellow"
                  startIcon={<Check size={BUTTON_ICON_SIZE} />}
                  loading={verifyWhiteLabelMutation.isPending}
                >
                  Verify
                </Button>
              </div>
            ) : status === "approved" ? (
              "Your white label solution has been successfully approved and is now live! "
            ) : (
              "You are in preview mode for now. You can try it out and submit your customization. Or Reload the page to go back to default mode"
            )
          }
          type="success"
        />
      )}
      <div className="grid md:grid-cols-2 gap-4 items-start">
        <ShadcnCard className="p-6 ">
          <div className=" space-y-4">
            <CardTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2" />
              General Settings
            </CardTitle>
            <form
              className="space-y-4"
              // onSubmit={(e) => {
              //   e.preventDefault();
              //   handleWhiteLabelSave();
              // }}
            >
              <div className="space-y-4">
                <div>
                  <Input
                    id="host"
                    label="Host"
                    required
                    name="host"
                    value={customization.host}
                    disabled={status === "approved" || status === "pending"}
                    onChange={handleInputChange}
                    placeholder="e.g., app.yourcompany.com"
                  />
                </div>
                <Input
                  id="title"
                  label="Title"
                  required
                  name="title"
                  value={customization.title}
                  onChange={handleInputChange}
                  placeholder="Name to be displayed on the top of the page"
                />

                {/* <div>
                  <Input
                    label="Description"
                    inputType="textarea"
                    placeholder="white label description..."
                    name="description"
                    value={customization.description}
                    onChange={handleInputChange}
                  />
                </div> */}
                <Separator />

                <div className="space-y-4">
                  <h3 className="font-semibold">Colors</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="brandColor">Primary Color</Label>

                      <div className="flex items-center space-x-2">
                        {/* <div
                          onClick={handleClick}
                          className="w-8 h-8 rounded-full mr-1 shadow-md cursor-pointer"
                          style={{ backgroundColor: customization.brandColor }}
                        />
                        <Input
                          value={customization.brandColor}
                          onChange={handleInputChange}
                          className="w-[10rem]"
                        /> */}

                        <ColorPicker
                          defaultColor={customization.brandColor}
                          onChange={(color: string) => setCustomization({ brandColor: color })}
                        />
                        <Input
                          type="text"
                          value={customization.brandColor}
                          onChange={handleInputChange}
                          name="primaryColor"
                          className="flex-grow"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="darkBg">Dark background</Label>
                      <div className="flex items-center space-x-2">
                        <ColorPicker
                          defaultColor={customization.darkBg}
                          onChange={(color: string) => setCustomization({ darkBg: color })}
                        />
                        <Input
                          type="text"
                          value={customization.darkBg}
                          onChange={handleInputChange}
                          name="darkBg"
                          className="flex-grow"
                        />
                      </div>
                    </div>
                    {/* <div>
                      <Label htmlFor="accentColor">Accent Color</Label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="color"
                          id="accentColor"
                          name="accentColor"
                          value={customization.accentColor}
                          onChange={handleInputChange}
                          className="w-10 h-10 rounded-md border cursor-pointer"
                        />
                        <Input
                          type="text"
                          value={customization.accentColor}
                          onChange={handleInputChange}
                          name="accentColor"
                          className="flex-grow"
                        />
                      </div>
                    </div> */}
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold">Logos</h3>
                  <div className="flex gap-4 items-center">
                    <div className="flex items-end gap-4">
                      <img
                        alt="img"
                        className="h-14 aspect-square  rounded-sm object-contain"
                        src={customization.logoLight || deviceMapping.default.image}
                      />
                      <Input
                        id="logoLight"
                        label="Light mode logo"
                        type="file"
                        accept="image/png"
                        onChange={(e) => handleLogoUpload(e, "logoLight")}
                      />
                    </div>
                    <div className="flex items-end gap-4">
                      <img
                        alt="img"
                        className="h-14 aspect-square  rounded-sm object-contain"
                        src={customization.logoDark || deviceMapping.default.image}
                      />
                      <Input
                        id="logoDark"
                        label="Dark mode logo"
                        type="file"
                        accept="image/png"
                        onChange={(e) => handleLogoUpload(e, "logoDark")}
                      />
                    </div>
                  </div>
                </div>
                <Separator />

                <Accordion type="single" collapsible>
                  <AccordionItem value="item-1" className="border-b-0">
                    <AccordionTrigger className="heading-2">Advance Features</AccordionTrigger>
                    <AccordionContent className="space-y-4 px-1">
                      <div className="space-y-2">
                        <div>
                          <h3 className="text-base font-medium tracking-wide uppercase ">
                            Label Overrides
                          </h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            Customize the terminology used throughout your platform to match your
                            industry or brand language
                          </p>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            id="product_alias"
                            // label="Device Type"
                            required
                            name="product_alias"
                            value={customization.product_alias}
                            onChange={handleInputChange}
                            description="This will replace 'Device Type' labels across your platform"
                            placeholder="Product Name to be displayed"
                          />
                          <Input
                            id="thing_alias"
                            // label="Device"
                            required
                            name="thing_alias"
                            value={customization.thing_alias}
                            onChange={handleInputChange}
                            placeholder="Thing Name to be displayed"
                            description="This will replace 'Device' labels across your platform"
                          />
                        </div>
                      </div>
                      <hr className="hr" />
                      <div className="space-y-2">
                        <div>
                          <h3 className="text-base font-medium tracking-wide uppercase ">
                            Button Text Color
                          </h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            Choose the text color for buttons that complements your brand colors and
                            ensures readability
                          </p>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <ColorPicker
                              minimal
                              monoColor
                              defaultColor={customization.buttonText}
                              onChange={(color: string) => setCustomization({ buttonText: color })}
                            />
                            <Input
                              type="text"
                              value={customization.buttonText}
                              onChange={handleInputChange}
                              name="buttonText"
                              className="flex-grow"
                            />
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </form>
          </div>
        </ShadcnCard>
        {status === "pending" ? <PendingInfoCard data={customization} /> : <GeneralInfoCard />}
      </div>
      <DomainVerificationModal
        isOpen={isModalOpen}
        onOpenChange={handleModalOpenChange}
        verificationText={verificationText}
      />
      <ConfirmPrompt
        validate
        show={showDeletePrompt}
        onCancel={() => setShowDeletePrompt(false)}
        loading={deleteWhiteLabelMutation.isPending}
        onConfirm={handleWhiteLabelDelete}
      />
    </div>
  );
}

type VerificationModalProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  verificationText: string | null;
};

const DomainVerificationModal = ({
  isOpen,
  onOpenChange,
  verificationText
}: VerificationModalProps) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    if (!verificationText) {
      return;
    }
    try {
      await navigator.clipboard.writeText(verificationText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>Domain Verification</DialogTitle>
          <DialogDescription>
            Copy this verification code to verify ownership of your domain.
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center space-x-2 mt-4">
          <div className="grid flex-1 gap-2">
            <div className="flex gap-2 items-center">
              <Input readOnly value={verificationText || ""} className="font-mono text-sm flex-1" />
              <ShadcnButton
                size="icon"
                variant="outline"
                className="h-10 w-10 flex-shrink-0"
                onClick={copyToClipboard}
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                <span className="sr-only">Copy verification code</span>
              </ShadcnButton>
            </div>

            <p className="text-sm text-muted-foreground">
              This verification code needs to be added to your domain's DNS records or placed in a
              specific file on your website to prove ownership. Follow the instructions provided by
              OneIoT to complete the verification process.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const PendingInfoCard = ({ data }) => {
  return (
    <ShadcnCard>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Domain Verification Instructions
        </CardTitle>
        <CardDescription>
          To complete your white-label setup and enable your custom domain, please follow these
          steps carefully:
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Step 1 */}
        <div className="space-y-3">
          <div className="">
            <h3 className="font-medium">STEP 1: Point Your Domain Using a CNAME Record</h3>
            <p className="text-sm text-muted-foreground">
              You must create a CNAME record so your domain correctly points to our platform.
            </p>
          </div>

          <Card variant="third" className="space-y-2">
            <div className="grid grid-cols-3 gap-2 text-sm">
              <div>
                <span className="font-medium">Type:</span> CNAME
              </div>
              <div>
                <span className="font-medium">Host/Name:</span> your-subdomain
              </div>
              <div>
                <span className="font-medium">Value:</span> wl-dev.oneiot.io
              </div>
            </div>
            {/* <p className="text-xs text-muted-foreground mt-2">
              (e.g., platform if you want platform.yourdomain.com)
            </p> */}
          </Card>
          <p className="text-sm text-muted-foreground">
            This allows traffic to route to our white-label service under your custom brand.
          </p>
        </div>

        {/* Step 2 */}
        <div className="space-y-3">
          <div className="">
            <h3 className="font-medium">STEP 2: Add a TXT Record for Domain Verification</h3>
            <p className="text-sm text-muted-foreground">
              We use a TXT record to verify ownership of your domain. Please add the following DNS
              record to your domain's DNS configuration:
            </p>
          </div>

          <Card variant="third" className="space-y-2">
            <div className="grid grid-cols-3 gap-2 text-sm">
              <div>
                <span className="font-medium">Type:</span> TXT
              </div>
              <div>
                <span className="font-medium">Host/Name:</span> your-subdomain
              </div>
              <div>
                <span className="font-medium">Value:</span> VERIFICATION_CODE
              </div>
            </div>
          </Card>
          <p className="text-sm text-muted-foreground">
            Replace VERIFICATION_CODE with the code we've provided after saving your customization.
          </p>
        </div>

        {/* Step 3 */}
        <div className="space-y-3">
          <div className="">
            <h3 className="font-medium">STEP 3: Access Your Platform</h3>
            <p className="text-sm text-muted-foreground">
              Once DNS propagation completes (may take up to 24 hours), your customized platform
              will be available at:
            </p>
          </div>

          <Card variant="third" className="space-y-2">
            <div className="text-sm">
              <span className="font-medium">URL:</span> https://{data.host}
            </div>
          </Card>
          <p className="text-sm text-muted-foreground">
            You can now share this URL with your users to access your Oneiot platform! 🎉
          </p>
        </div>
      </CardContent>
    </ShadcnCard>
  );
};

const GeneralInfoCard = () => {
  return (
    <ShadcnCard>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Info className="h-5 w-5 mr-2" />
          Customization Tips
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 text-sm">
          <p>
            Customize your white-label solution to match your brand identity. Here are some tips to
            get the best results:
          </p>
          <ul className="list-disc list-inside space-y-2">
            <li>Use a consistent color scheme across all elements</li>
            <li>Ensure your logos are high quality and properly sized</li>
            <li>Provide clear and concise descriptions for faster approval</li>
            <li>Test your customization in both light and dark modes</li>
            <li>Consider accessibility when choosing colors and contrast</li>
          </ul>
          <p>
            Need more help? Check out our comprehensive customization guide or contact our support
            team .
          </p>
          <hr className="hr" />
          <div className="space-y-2">
            <h3 className="heading-3">Advance Features</h3>
            <p>
              {" "}
              Configure device categorization, identification, and specialized functionality to
              ensure proper system integration and optimal performance for your specific hardware
              deployment.
            </p>
            <ul className="list-disc list-inside space-y-2">
              <li>
                <span className="font-semibold">Device Type:</span> Specifies the category of device
                you're configuring (e.g., Product, Gateway, Sensor, Controller).
              </li>
              <li>
                <span className="font-semibold">Device:</span> Defines the specific device model or
                identifier within the selected device type category. This helps the platform
                recognize and properly configure your particular hardware implementation.
              </li>
            </ul>
          </div>
        </div>
      </CardContent>
    </ShadcnCard>
  );
};
