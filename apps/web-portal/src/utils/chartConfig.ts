import { ChartConfig } from "@components/shadcn/components/chart";
import { COLOR_SCHEMES } from "@src/pages/MonitorPage/utils";

interface InputData {
  type: "usage" | "dashboard";
  labels: string[];
  name: string[];
  series: Array<Number[]>;
  themePallet: string;
  legends?: string[];
}

export const createChartDataForUsage = (data: InputData) => {
  if (data.type == "usage") {
    const chartPlotData = data.labels.map((label, index) => {
      const dataPoint: any = { label };
      data.name.forEach((seriesName, seriesIndex) => {
        dataPoint[seriesName] = data.series[seriesIndex]?.[index] || 0;
      });
      return dataPoint;
    });

    const chartConfig: ChartConfig = {};
    const palette = COLOR_SCHEMES[data.themePallet] || COLOR_SCHEMES["default"];

    data.name.forEach((seriesName, index) => {
      chartConfig[seriesName] = { label: seriesName, color: palette[index] };
    });

    return {
      chartPlotData: chartPlotData,
      chartConfig,
      name: data.name,
      legends: data.legends,
      series: data.series
    };
  }
};

export const editKeyToLabel = (key: string | number | undefined) => {
  return String(key)
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
    .replace("Resp", "");
};
