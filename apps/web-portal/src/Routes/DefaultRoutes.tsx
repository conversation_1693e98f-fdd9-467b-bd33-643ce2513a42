/* eslint-disable react-refresh/only-export-components */
import { getPartnerAndFeature } from "@utils/url";
import { ALL_ROLES, MSP_ROLES, OPERATOR_ROLES, TENANT_ROLES } from "@utils/utilities";
import { ReactElement, Suspense, lazy } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Route } from "react-router-dom";
import { ReactFlowProvider } from "reactflow";
import geoRoutes from "./GeoRoutes";
import { MODE } from "@api/index";
import PageLoadingSkeleton from "@components/Skeletons/PageLoadingSkeleton";
const HomeViewSettings = lazy(() => import("@src/pages/HomeViewSettings"));
const AddContactSection = lazy(
  () => import("@src/pages/NotificationPage/Contacts/AddContactSection")
);
const ContactTeamsDetails = lazy(
  () => import("@src/pages/NotificationPage/Contacts/ContactTeamsDetails")
);
const DriversList = lazy(() => import("@src/pages/UserTypes/Tracking/Drivers"));
const CreateNewDriver = lazy(() => import("@src/pages/UserTypes/Tracking/Drivers/CreateNewDriver"));
const DriverDetailsPage = lazy(
  () => import("@src/pages/UserTypes/Tracking/Drivers/DriverDetailsPage")
);
const InventoryBulkThingList = lazy(
  () => import("@src/pages/InventoryPage/BulkInventoryThings/InventoryBulkThingList")
);
const InventoryBulkThingStatus = lazy(
  () => import("@src/pages/InventoryPage/BulkInventoryThings/InventoryBulkThingStatus")
);
const SchedulesList = lazy(() => import("@src/pages/NotificationPage/Schedules"));
const AddSchedulesSection = lazy(
  () => import("@src/pages/NotificationPage/Schedules/AddSchedulesSection")
);
const ScheduleDetailPage = lazy(
  () => import("@src/pages/NotificationPage/Schedules/ScheduleDetailPage")
);
const ThingTicketPage = lazy(() => import("@src/pages/DevicesPage/ThingTicketPage"));
const NotificationStatusLogs = lazy(() => import("@src/pages/NotificationPage/NotificationLogs"));
const NotificationLogsDetails = lazy(
  () => import("@src/pages/NotificationPage/NotificationLogs/NotificationLogsDetails")
);

const BulkThingsList = lazy(() => import("@src/pages/DevicesPage/BulkCreate/BulkThingsList"));
const CreateThingSection = lazy(() => import("@src/pages/DevicesPage/CreateThingSection"));
const CreateThingProvider = lazy(
  () => import("@src/pages/DevicesPage/CreateThingSection/CreateThingProvider")
);
const MspHome = lazy(() => import("@src/pages/HomeSection/MspHome"));
const OperatorHome = lazy(() => import("@src/pages/HomeSection/OperatorHome"));
const NotificationList = lazy(() => import("@src/pages/NotificationPage/Notification"));
const AddNotificationTemplate = lazy(
  () => import("@src/pages/NotificationPage/Notification/AddNotification/AddNotificationTemplate")
);
const NotificationDetail = lazy(
  () => import("@src/pages/NotificationPage/Notification/NotificationDetails")
);
const MspDetailPage = lazy(() => import("@src/pages/PartnersPage/MSPs/MspDetailPage"));
const MspList = lazy(() => import("@src/pages/PartnersPage/MSPs/MspList"));
const MspUserDetailPage = lazy(() => import("@src/pages/PartnersPage/MSPs/MspUserDetailPage"));
const MspUserList = lazy(() => import("@src/pages/PartnersPage/MSPs/MspUserList"));
const OpUserDetail = lazy(() => import("@src/pages/PartnersPage/Operators/OpUserDetail"));
const OpUserList = lazy(() => import("@src/pages/PartnersPage/Operators/OpUserList"));
const CreateProductTemplate = lazy(
  () => import("@src/pages/ProductTemplatePage/CreateTemplateSection")
);
const AddProductTemplateProvider = lazy(
  () => import("@src/pages/ProductTemplatePage/CreateTemplateSection/AddTemplateProvider")
);
const BasicDetailEdit = lazy(
  () => import("@src/pages/ProductTemplatePage/EditTemplateSection/BasicDetailEdit")
);

const PolicyEdit = lazy(
  () => import("@src/pages/ProductTemplatePage/EditTemplateSection/PolicyEdit")
);
const SettingsEdit = lazy(
  () => import("@src/pages/ProductTemplatePage/EditTemplateSection/SettingsEdit")
);
const TopicsEdit = lazy(
  () => import("@src/pages/ProductTemplatePage/EditTemplateSection/TopicsEdit")
);
const ProductTemplateDetails = lazy(
  () => import("@src/pages/ProductTemplatePage/ProductTemplateDetails")
);
const ProductTemplateList = lazy(
  () => import("@src/pages/ProductTemplatePage/ProductTemplateList")
);
const ListenerPage = lazy(() => import("@src/pages/SecurityPage/ListenerPage"));
const SimulatorTemplateList = lazy(() => import("@src/pages/SimulatorTemplates"));
const CreateSimulation = lazy(() => import("@src/pages/SimulatorTemplates/CreateSimulation"));
const SimulatorDetailPage = lazy(() => import("@src/pages/SimulatorTemplates/SimulatorDetailPage"));

const EscalationPage = lazy(() => import("../pages/NotificationPage/Escalations"));
const EscalationDetailPage = lazy(
  () => import("../pages/NotificationPage/Escalations/EscalationDetailPage")
);
const CreateOtaRelease = lazy(() => import("@src/pages/DevicesPage/CreateOtaRelease"));
const NotificationHistroyList = lazy(
  () => import("@src/pages/NotificationPage/History/NotificationHistroyList")
);
const CreateSiteDigitalTwin = lazy(() => import("@src/pages/site/CreateSiteDigitalTwin"));
const TenantAccessList = lazy(() => import("@src/pages/PartnersPage/TenantAccess"));
const TenantAccessDetails = lazy(
  () => import("@src/pages/PartnersPage/TenantAccess/TenantAccessDetails")
);
const IncidentCategoryList = lazy(() => import("@src/pages/Incidents/Category"));
const TicketsList = lazy(() => import("@src/pages/Incidents/Tickets"));
const TicketDetails = lazy(() => import("@src/pages/Incidents/Tickets/TicketDetails"));
const CreateTicketSection = lazy(() => import("@src/pages/Incidents/Tickets/CreateTicketSection"));
const TypographyShowcase = lazy(() => import("@src/pages/Typography"));

const BulkCreateStatus = lazy(() => import("../pages/DevicesPage/BulkCreate/BulkCreateStatus"));
const AddProductProvider = lazy(
  () => import("../pages/ProductPage/AddProductSection/AddProductProvider")
);
const GatewayListPage = lazy(() => import("../pages/GatewayPage"));
const GatewayDetailPage = lazy(() => import("../pages/GatewayPage/GatewayDetailPage"));
const IntegrationDetialPage = lazy(
  () => import("../pages/RulePage/IntegrationPage/IntegrationDetialPage")
);
const TrackingHistorySection = lazy(() => import("../pages/UserTypes/Tracking/HistoryPage"));

const HomePageSection = lazy(() => import("../pages/HomeSection"));
const PartnerHealthPage = lazy(() => import("../pages/PartnerHealth"));
const PartnerHealtDetial = lazy(() => import("../pages/PartnerHealth/PartnerHealtDetial"));
const DevicesListPage = lazy(() => import("../pages/DevicesPage/DeviceListSection"));
const DeviceDetailsPage = lazy(() => import("../pages/DevicesPage/DeviceDetailSection"));
const PartnersPage = lazy(() => import("../pages/PartnersPage/Tenants"));
const PartnerDetailPage = lazy(() => import("../pages/PartnersPage/PartnerDetailSection"));
const TenantUserDetail = lazy(() => import("../pages/PartnersPage/Tenants/TenantUserDetail"));
const TenantUserList = lazy(() => import("../pages/PartnersPage/Tenants/TenantUserList"));
const ProfilePage = lazy(() => import("../pages/ProfilePage/ProfilePage"));
const ProductsPage = lazy(() => import("../pages/ProductPage"));
const RulePage = lazy(() => import("../pages/RulePage"));
const RuleDetailsPage = lazy(() => import("../pages/RulePage/RuleDetailsPage"));

const ManageDevice = lazy(() => import("../pages/ProductPage/ManageDevice"));
const Certificates = lazy(() => import("../pages/SecurityPage/ThingCertificate/Certificates"));
const PolicyTemplate = lazy(() => import("../pages/SecurityPage/PolicyTemplate"));
const Policy = lazy(() => import("../pages/SecurityPage/Policy/Policy"));
const CertificateAuth = lazy(() => import("../pages/SecurityPage/CertificateAuth/CertificateAuth"));
const UserAuth = lazy(() => import("../pages/SecurityPage/Authentication/Authentication"));
const CertificateDetail = lazy(
  () => import("../pages/SecurityPage/ThingCertificate/CertificateDetail")
);
const TemplateDetail = lazy(() => import("../pages/SecurityPage/PolicyTemplate/TemplateDetail"));
const PolicyDetail = lazy(() => import("../pages/SecurityPage/Policy/PolicyDetail"));
const CertAuthDetail = lazy(() => import("../pages/SecurityPage/CertificateAuth/CertAuthDetail"));
const AddCertAuth = lazy(() => import("../pages/SecurityPage/CertificateAuth/AddCertAuth"));
const OtaDocDetail = lazy(() => import("../pages/DevicesPage/OtaDocDetail"));
const OtaJobDetial = lazy(() => import("../pages/DevicesPage/OtaJobDetial"));
const DynamicGroupList = lazy(
  () => import("../pages/ThingGroupsPage/DynamicGroups/DynamicGroupList")
);
const StaticGroupList = lazy(() => import("../pages/ThingGroupsPage/StaticGroups/StaticGroupList"));
const DynamicGroupDetail = lazy(
  () => import("../pages/ThingGroupsPage/DynamicGroups/DynamicGroupDetail")
);
const ReleasesOtaPage = lazy(() => import("../pages/DevicesPage/ReleasesOtaPage"));
const DeviceOtaPage = lazy(() => import("../pages/DevicesPage/DeviceOtaPage"));
const UsagePage = lazy(() => import("../pages/UsagePage"));
const StaticGroupDetail = lazy(
  () => import("../pages/ThingGroupsPage/StaticGroups/StaticGroupDetail")
);
const InventoryDetailPage = lazy(() => import("../pages/InventoryPage/InventoryDetailPage"));
const MonitorPage = lazy(() => import("../pages/MonitorPage"));
const InventoryPage = lazy(() => import("../pages/InventoryPage"));
const OrganizationPage = lazy(() => import("../pages/OrganizationPage"));
const LoggerPage = lazy(() => import("../pages/Logger/LoggerPage"));
const CreateUser = lazy(() => import("../pages/PartnersPage/CreateUser"));
const CreateDashboard = lazy(() => import("../pages/MonitorPage/CreateDashboard"));
const UserApiPage = lazy(() => import("../pages/PartnersPage/UserApi"));
const CreateApi = lazy(() => import("../pages/PartnersPage/UserApi/CreateApi"));
const ManageInventory = lazy(() => import("../pages/ProductPage/ManageInventory"));
const CreateProduct = lazy(() => import("../pages/ProductPage/AddProductSection"));
const ApiDetailPage = lazy(() => import("../pages/PartnersPage/UserApi/ApiDetailPage"));
const DestinationPage = lazy(() => import("../pages/RulePage/DestinationPage"));
const IntegraionPage = lazy(() => import("../pages/RulePage/IntegrationPage"));
const UserGroups = lazy(() => import("../pages/PartnersPage/UserGroups"));
const CreateUserGroup = lazy(() => import("../pages/PartnersPage/UserGroups/CreateUserGroup"));
const UserGroupDetailPage = lazy(
  () => import("../pages/PartnersPage/UserGroups/UserGroupDetailPage")
);
const DestinationDetailPage = lazy(
  () => import("../pages/RulePage/DestinationPage/DestinationDetailPage")
);
const ProductDetails = lazy(() => import("../pages/ProductPage/ProductDetailSection"));
const ProductTopicsPage = lazy(
  () => import("../pages/ProductPage/ProductDetailSection/ProductTopicsModal")
);
const ContactList = lazy(() => import("../pages/NotificationPage/Contacts"));
const ContactDetailPage = lazy(
  () => import("../pages/NotificationPage/Contacts/ContactDetailPage")
);

const ProductReleasePage = lazy(() => import("../pages/ProductPage/ProductReleaseSection"));
const CreateBuilds = lazy(() => import("../pages/ProductPage/ProductReleaseSection/CreateBuilds"));
const BuildDetailPage = lazy(
  () => import("../pages/ProductPage/ProductReleaseSection/BuildDetailPage")
);
const CreateRuleNext = lazy(() => import("../pages/RulePage/Next"));
const WhiteLabel = lazy(() => import("../pages/WhiteLabels"));
const DigitalTwin = lazy(() => import("../pages/DigitalTwin"));

const AppUsers = lazy(() => import("../pages/AppUsers"));
const AppUserDetails = lazy(() => import("../pages/AppUsers/AppUserDetails"));
const Site = lazy(() => import("../pages/site"));
const CreateSite = lazy(() => import("../pages/site/CreateSite"));
const EditAreaDetailsDashboard = lazy(() => import("../pages/site/EditAreaDetailsDashboard"));
const AreaDetailsDashboard = lazy(() => import("../pages/site/AreaDetailsDashboard"));
const SiteAssets = lazy(() => import("../pages/site/SiteAssets"));
const SiteAssetDetails = lazy(() => import("../pages/site/SiteAssetDetails"));
const SiteDigitalTwin = lazy(() => import("../pages/site/SiteDigitalTwin"));
const ChatBot = lazy(() => import("@components/Chatbot"));
const CreateTenantAccessTemplate = lazy(
  () => import("@src/pages/PartnersPage/TenantAccess/CreateTenantAccessTemplate")
);
const ReportRequestPage = lazy(() => import("@src/pages/ReportRequestPage"));
const TrackingRoute = lazy(() => import("../pages/UserTypes/Tracking/RoutesPage"));
const TrackingGeofenceSection = lazy(() => import("../pages/UserTypes/Tracking/GeofencePage"));

const PageLoader = () => {
  return <PageLoadingSkeleton />;
};

const WrapInSuspense = ({ children }: { children: ReactElement }) => {
  return <Suspense fallback={<PageLoader />}>{children}</Suspense>;
};

const TENANTS_VIEW = () => {
  return (
    <>
      <Route
        index
        element={
          <WrapInSuspense>
            <HomePageSection />
          </WrapInSuspense>
        }
      />
      <Route path="UM">
        <Route path="users">
          <Route
            index
            element={
              <WrapInSuspense>
                <TenantUserList />
              </WrapInSuspense>
            }
          />
          <Route
            path=":userEmail"
            element={
              <WrapInSuspense>
                <TenantUserDetail />
              </WrapInSuspense>
            }
          />
          <Route
            path="createUser"
            element={
              <WrapInSuspense>
                <CreateUser type="tenant" />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="userGroups">
          <Route
            index
            element={
              <WrapInSuspense>
                <UserGroups type="tenant" />
              </WrapInSuspense>
            }
          />

          <Route
            path=":userGroupId"
            element={
              <WrapInSuspense>
                <UserGroupDetailPage type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path=":userGroupId/edit"
            element={
              <WrapInSuspense>
                <CreateUserGroup mode="edit" type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path="createUserGroup"
            element={
              <WrapInSuspense>
                <CreateUserGroup type="tenant" />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="apiKeys">
          <Route
            index
            element={
              <WrapInSuspense>
                <UserApiPage type="tnt" />
              </WrapInSuspense>
            }
          />
          <Route
            path="createApi"
            element={
              <WrapInSuspense>
                <CreateApi type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path="apiDetails"
            element={
              <WrapInSuspense>
                <ApiDetailPage type="tenant" />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="gateway">
        <Route
          index
          element={
            <WrapInSuspense>
              <GatewayListPage />
            </WrapInSuspense>
          }
        />

        <Route
          path=":gatewayName"
          element={
            <WrapInSuspense>
              <GatewayDetailPage />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route path="notifications">
        <Route path="templates">
          <Route
            index
            element={
              <WrapInSuspense>
                <NotificationList />
              </WrapInSuspense>
            }
          />

          <Route
            path="add-Notification"
            element={
              <WrapInSuspense>
                <AddNotificationTemplate />
              </WrapInSuspense>
            }
          />
          <Route
            path=":templateName"
            element={
              <WrapInSuspense>
                <NotificationDetail />
              </WrapInSuspense>
            }
          />
          <Route
            path=":templateName/edit"
            element={
              <WrapInSuspense>
                <AddNotificationTemplate />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="schedules">
          <Route
            index
            element={
              <WrapInSuspense>
                <SchedulesList />
              </WrapInSuspense>
            }
          />
          <Route
            path="add-schedules"
            element={
              <WrapInSuspense>
                <AddSchedulesSection />
              </WrapInSuspense>
            }
          />
          <Route
            path=":scheduleId"
            element={
              <WrapInSuspense>
                <ScheduleDetailPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":scheduleId/edit"
            element={
              <WrapInSuspense>
                <AddSchedulesSection />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="contacts">
          <Route
            index
            element={
              <WrapInSuspense>
                <ContactList />
              </WrapInSuspense>
            }
          />
          <Route path="contacts-teams">
            <Route
              path=":groupId"
              element={
                <WrapInSuspense>
                  <ContactTeamsDetails />
                </WrapInSuspense>
              }
            />
          </Route>
          <Route
            path="add-contact"
            element={
              <WrapInSuspense>
                <AddContactSection />
              </WrapInSuspense>
            }
          />
          <Route path="contacts-teams">
            <Route
              path=":groupId"
              element={
                <WrapInSuspense>
                  <ContactTeamsDetails />
                </WrapInSuspense>
              }
            />
          </Route>
          <Route
            path=":contactId"
            element={
              <WrapInSuspense>
                <ContactDetailPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":contactId/edit"
            element={
              <WrapInSuspense>
                <AddContactSection />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="escalations">
          <Route
            index
            element={
              <WrapInSuspense>
                <EscalationPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":thingName"
            element={
              <WrapInSuspense>
                <EscalationDetailPage />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="history">
          <Route
            index
            element={
              <WrapInSuspense>
                <NotificationHistroyList />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="status">
          <Route
            index
            element={
              <WrapInSuspense>
                <NotificationStatusLogs />
              </WrapInSuspense>
            }
          />
          <Route path=":logId" element={<NotificationLogsDetails />} />
        </Route>
      </Route>
      <Route path="actions">
        <Route path="rules">
          <Route
            index
            element={
              <WrapInSuspense>
                <RulePage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":ruleId"
            element={
              <WrapInSuspense>
                <RuleDetailsPage />
              </WrapInSuspense>
            }
          />

          <Route
            path="create-next"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
          <Route
            path=":ruleId/edit"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="workflow">
          <Route
            index
            element={
              <WrapInSuspense>
                <RulePage mode="workflows" />
              </WrapInSuspense>
            }
          />

          <Route
            path=":workflowId"
            element={
              <WrapInSuspense>
                <RuleDetailsPage />
              </WrapInSuspense>
            }
          />

          <Route
            path="create"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
          <Route
            path=":workflowId/edit"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="destinations">
          <Route
            index
            element={
              <WrapInSuspense>
                <DestinationPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":destinationId"
            element={
              <WrapInSuspense>
                <DestinationDetailPage />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="integrations">
          <Route
            index
            element={
              <WrapInSuspense>
                <IntegraionPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":integrationId"
            element={
              <WrapInSuspense>
                <IntegrationDetialPage />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="products">
        <Route
          index
          element={
            <WrapInSuspense>
              <ProductsPage />
            </WrapInSuspense>
          }
        />

        <Route path=":productName">
          <Route
            index
            element={
              <WrapInSuspense>
                <ProductDetails />
              </WrapInSuspense>
            }
          />
          <Route
            path="manageDevice"
            element={
              <WrapInSuspense>
                <ManageDevice />
              </WrapInSuspense>
            }
          />
          <Route
            path="editTopics"
            element={
              <WrapInSuspense>
                <ProductTopicsPage />
              </WrapInSuspense>
            }
          />
          <Route
            path="manageInventory"
            element={
              <WrapInSuspense>
                <ManageInventory />
              </WrapInSuspense>
            }
          />

          <Route path="digitaltwin">
            <Route index element={<DigitalTwin />} />
          </Route>

          <Route path="releases">
            <Route
              index
              element={
                <WrapInSuspense>
                  <ProductReleasePage />
                </WrapInSuspense>
              }
            />
            <Route
              path=":buildId"
              element={
                <WrapInSuspense>
                  <BuildDetailPage />
                </WrapInSuspense>
              }
            />
            <Route
              path="create-build"
              element={
                <WrapInSuspense>
                  <CreateBuilds />
                </WrapInSuspense>
              }
            />
          </Route>
        </Route>
        <Route
          path="addProduct"
          element={
            <WrapInSuspense>
              <AddProductProvider>
                <CreateProduct />
              </AddProductProvider>
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="security">
        <Route path="certificate">
          <Route
            index
            element={
              <WrapInSuspense>
                <Certificates />
              </WrapInSuspense>
            }
          />
          <Route
            path=":certificateId"
            element={
              <WrapInSuspense>
                <CertificateDetail />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="template">
          <Route index element={<PolicyTemplate />} />
          <Route
            path=":templateName"
            element={
              <WrapInSuspense>
                <TemplateDetail />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="policy">
          <Route index element={<Policy />} />
          <Route
            path=":thingName"
            element={
              <WrapInSuspense>
                <PolicyDetail />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="certificateAuth">
          <Route
            index
            element={
              <WrapInSuspense>
                <CertificateAuth />
              </WrapInSuspense>
            }
          />
          <Route
            path="addCertAuth"
            element={
              <WrapInSuspense>
                <AddCertAuth />
              </WrapInSuspense>
            }
          />
          <Route
            path=":caName"
            element={
              <WrapInSuspense>
                <CertAuthDetail />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="protocol_Services">
          <Route
            index
            element={
              <WrapInSuspense>
                <ListenerPage />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="authentication">
          <Route
            index
            element={
              <WrapInSuspense>
                <UserAuth />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="jobs">
        <Route path="ota">
          <Route
            index
            element={
              <WrapInSuspense>
                <ReleasesOtaPage />
              </WrapInSuspense>
            }
          />

          <Route
            path="createOta"
            element={
              <WrapInSuspense>
                <CreateOtaRelease />
              </WrapInSuspense>
            }
          />
          <Route path=":otaId">
            <Route
              index
              element={
                <WrapInSuspense>
                  <OtaDocDetail />
                </WrapInSuspense>
              }
            />
            <Route
              path=":jobId"
              element={
                <WrapInSuspense>
                  <OtaJobDetial />
                </WrapInSuspense>
              }
            />
          </Route>
        </Route>
        <Route path="bulk-things">
          <Route
            index
            element={
              <WrapInSuspense>
                <BulkThingsList />
              </WrapInSuspense>
            }
          />
          <Route
            path=":docId"
            element={
              <WrapInSuspense>
                <BulkCreateStatus />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="inventory-bulk-things">
          <Route
            index
            element={
              <WrapInSuspense>
                <InventoryBulkThingList />
              </WrapInSuspense>
            }
          />
          <Route
            path=":docId"
            element={
              <WrapInSuspense>
                <InventoryBulkThingStatus />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route
          path="reports"
          element={
            <WrapInSuspense>
              <ReportRequestPage />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route path="settings">
        <Route
          path="white-labeling"
          element={
            <WrapInSuspense>
              <WhiteLabel />
            </WrapInSuspense>
          }
        />
        <Route
          path="home-view"
          element={
            <WrapInSuspense>
              <HomeViewSettings />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="advance">
        <Route path="digital-twin/new" element={<CreateSiteDigitalTwin />} />
        <Route path="digital-twin" element={<SiteDigitalTwin />} />
        <Route path="digital-twin/:digitalTwinName" element={<DigitalTwin />} />
        <Route path="ai-agent" element={<ChatBot view="largeScreen" />} />
      </Route>

      <Route path="processIQ">
        {/* <Route
          index
          element={
            <WrapInSuspense>
              <Site />
            </WrapInSuspense>
          }
        /> */}
        <Route path="sites">
          <Route
            index
            element={
              <WrapInSuspense>
                <Site />
              </WrapInSuspense>
            }
          />
          <Route
            path=":siteName"
            element={
              <WrapInSuspense>
                <Site />
              </WrapInSuspense>
            }
          />
          <Route
            path="create"
            element={
              <WrapInSuspense>
                <CreateSite />
              </WrapInSuspense>
            }
          />

          <Route
            path=":siteName/:areaName/edit"
            element={
              <WrapInSuspense>
                <EditAreaDetailsDashboard />
              </WrapInSuspense>
            }
          />
          <Route
            path=":siteName/:areaName"
            element={
              <WrapInSuspense>
                <AreaDetailsDashboard />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="assets" element={<SiteAssets />} />
        <Route path="assets/:assetName" element={<SiteAssetDetails />} />
      </Route>

      <Route path="app-users">
        <Route
          index
          element={
            <WrapInSuspense>
              <AppUsers />
            </WrapInSuspense>
          }
        />
        <Route
          path=":email"
          element={
            <WrapInSuspense>
              <AppUserDetails />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route
        path="usage"
        element={
          <WrapInSuspense>
            <UsagePage />
          </WrapInSuspense>
        }
      />

      <Route path="things">
        <Route
          index
          element={
            <WrapInSuspense>
              <DevicesListPage />
            </WrapInSuspense>
          }
        />
        <Route
          path="addThing"
          element={
            <CreateThingProvider>
              <CreateThingSection />
            </CreateThingProvider>
          }
        />

        <Route path=":thingName">
          <Route path="digitaltwin">
            <Route index element={<DigitalTwin />} />
          </Route>
          <Route path="tickets">
            <Route index element={<ThingTicketPage />} />
          </Route>

          <Route
            index
            element={
              <WrapInSuspense>
                <DeviceDetailsPage />
              </WrapInSuspense>
            }
          />
          <Route
            path="ota"
            element={
              <WrapInSuspense>
                <DeviceOtaPage />
              </WrapInSuspense>
            }
          />
          <Route
            path="ota/:jobId"
            element={
              <WrapInSuspense>
                <OtaJobDetial />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="routes">
        <Route index element={<TrackingRoute />} />
      </Route>
      <Route path="geofences">
        <Route
          index
          element={
            <WrapInSuspense>
              <TrackingGeofenceSection />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route path="drivers">
        <Route
          index
          element={
            <WrapInSuspense>
              <DriversList />
            </WrapInSuspense>
          }
        />
        <Route
          path="addDriver"
          element={
            <WrapInSuspense>
              <CreateNewDriver />
            </WrapInSuspense>
          }
        />
        <Route
          path=":driverId"
          element={
            <WrapInSuspense>
              <DriverDetailsPage />
            </WrapInSuspense>
          }
        />
        <Route
          path=":driverId/editDriver"
          element={
            <WrapInSuspense>
              <CreateNewDriver />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="history">
        <Route
          index
          element={
            <WrapInSuspense>
              <TrackingHistorySection />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route path="incidents">
        <Route path="category">
          <Route index element={<IncidentCategoryList />} />
        </Route>

        <Route path="tickets">
          <Route index element={<TicketsList />} />
          <Route path="addTicket" element={<CreateTicketSection />} />
          <Route path=":ticketId" element={<TicketDetails />} />
        </Route>
      </Route>
      <Route path="thingGroup">
        <Route path="dynamic">
          <Route index element={<DynamicGroupList />} />
          <Route path=":thingGroupName" element={<DynamicGroupDetail />} />
        </Route>

        <Route path="static">
          <Route index element={<StaticGroupList />} />
          <Route path=":thingGroupName" element={<StaticGroupDetail />} />
        </Route>
      </Route>
      <Route path="monitor">
        <Route path="dashboard">
          <Route
            index
            element={
              <WrapInSuspense>
                <MonitorPage />
              </WrapInSuspense>
            }
          />
          <Route
            path="create"
            element={
              <WrapInSuspense>
                <DndProvider backend={HTML5Backend}>
                  <CreateDashboard />
                </DndProvider>
              </WrapInSuspense>
            }
          />
          <Route
            path=":dashboardId"
            element={
              <WrapInSuspense>
                <DndProvider backend={HTML5Backend}>
                  <CreateDashboard />
                </DndProvider>
              </WrapInSuspense>
            }
          />
        </Route>
        <Route
          path="logs"
          element={
            <WrapInSuspense>
              <LoggerPage />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="inventory">
        <Route
          index
          element={
            <WrapInSuspense>
              <InventoryPage />
            </WrapInSuspense>
          }
        />
        <Route
          path=":thingName"
          element={
            <WrapInSuspense>
              <InventoryDetailPage />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route
        path="organization"
        element={
          <WrapInSuspense>
            <OrganizationPage />
          </WrapInSuspense>
        }
      />
      <Route
        path="chat-bot"
        element={
          <WrapInSuspense>
            <ChatBot view="largeScreen" />
          </WrapInSuspense>
        }
      />
      <Route path="profile">
        <Route
          path="apiDetails"
          element={
            <WrapInSuspense>
              <ApiDetailPage type="tenant" />
            </WrapInSuspense>
          }
        />
        <Route
          path="createApi"
          element={
            <WrapInSuspense>
              <CreateApi type="tenant" />
            </WrapInSuspense>
          }
        />
      </Route>
    </>
  );
};

const defaultRoutes = (userRole: string) => {
  const urlDetails = getPartnerAndFeature();
  return (
    <>
      {ALL_ROLES.includes(userRole) && (
        <>
          <Route path="profile">
            <Route
              index
              element={
                <WrapInSuspense>
                  <ProfilePage />
                </WrapInSuspense>
              }
            />
          </Route>
          {MODE === "development" && <Route path="typography" element={<TypographyShowcase />} />}
        </>
      )}

      {OPERATOR_ROLES.includes(userRole) && (
        <>
          <Route
            index
            element={
              <WrapInSuspense>
                <OperatorHome />
              </WrapInSuspense>
            }
          />
          <Route path="UM">
            <Route path="users">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <OpUserList type="All" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userEmail"
                element={
                  <WrapInSuspense>
                    <OpUserDetail type="" />
                  </WrapInSuspense>
                }
              />
            </Route>

            <Route path="opUsers">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <OpUserList type="Operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userEmail"
                element={
                  <WrapInSuspense>
                    <OpUserDetail type="Operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path="createUser"
                element={
                  <WrapInSuspense>
                    <CreateUser type="operator" />
                  </WrapInSuspense>
                }
              />
            </Route>
            <Route path="platformGroups">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <UserGroups type="platform-operator" />
                  </WrapInSuspense>
                }
              />

              <Route
                path=":userGroupId"
                element={
                  <WrapInSuspense>
                    <UserGroupDetailPage type="platform-operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userGroupId/edit"
                element={
                  <WrapInSuspense>
                    <CreateUserGroup mode="edit" type="platform-operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path="createUserGroup"
                element={
                  <WrapInSuspense>
                    <CreateUserGroup type="platform-operator" />
                  </WrapInSuspense>
                }
              />
            </Route>
            <Route path="userGroups">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <UserGroups type="operator" />
                  </WrapInSuspense>
                }
              />

              <Route
                path=":userGroupId"
                element={
                  <WrapInSuspense>
                    <UserGroupDetailPage type="operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userGroupId/edit"
                element={
                  <WrapInSuspense>
                    <CreateUserGroup mode="edit" type="operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path="createUserGroup"
                element={
                  <WrapInSuspense>
                    <CreateUserGroup type="operator" />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>
          <Route path="mspManagement">
            <Route path="msps">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <MspList />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":mspId"
                element={
                  <WrapInSuspense>
                    <MspDetailPage />
                  </WrapInSuspense>
                }
              />
            </Route>
            <Route path="mspUsers">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <OpUserList type="MSP" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userEmail"
                element={
                  <WrapInSuspense>
                    <OpUserDetail type="MSP" />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>
          <Route path="TM">
            <Route path="tenants">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <PartnersPage type="operator" />
                  </WrapInSuspense>
                }
              />

              <Route path=":featureType/:partnerName">
                {urlDetails.featureType === "default" && (
                  <Route
                    index
                    element={
                      <WrapInSuspense>
                        <HomePageSection />
                      </WrapInSuspense>
                    }
                  />
                )}
                {urlDetails.featureType === "geo" ? geoRoutes() : TENANTS_VIEW()}
              </Route>
              <Route path="tenantDetails/:partnerName">
                <Route
                  index
                  element={
                    <WrapInSuspense>
                      <PartnerDetailPage type="operator" />
                    </WrapInSuspense>
                  }
                />
              </Route>
            </Route>

            <Route path="tenantUsers">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <OpUserList type="Tenant" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userEmail"
                element={
                  <WrapInSuspense>
                    <OpUserDetail type="Tenant" />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>

          <Route path="templates">
            <Route path="product-templates">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <ProductTemplateList type="operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":projectName"
                element={
                  <WrapInSuspense>
                    <ProductTemplateDetails />
                  </WrapInSuspense>
                }
              />

              <Route
                path="addTemplate"
                element={
                  <WrapInSuspense>
                    <AddProductTemplateProvider type="operator">
                      <CreateProductTemplate />
                    </AddProductTemplateProvider>
                  </WrapInSuspense>
                }
              />

              <Route
                path=":projectName/basic-edit"
                element={
                  <WrapInSuspense>
                    <BasicDetailEdit type="operator" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":projectName/policy-edit"
                element={
                  <WrapInSuspense>
                    <PolicyEdit />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":projectName/topics-edit"
                element={
                  <WrapInSuspense>
                    <TopicsEdit />
                  </WrapInSuspense>
                }
              />

              <Route
                path=":projectName/settings-edit"
                element={
                  <WrapInSuspense>
                    <SettingsEdit />
                  </WrapInSuspense>
                }
              />
            </Route>
            <Route path="simulator-templates">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <SimulatorTemplateList />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":templateName"
                element={
                  <WrapInSuspense>
                    <SimulatorDetailPage />
                  </WrapInSuspense>
                }
              />
              <Route
                path="new"
                element={
                  <WrapInSuspense>
                    <CreateSimulation />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>
          <Route path="tenant-health">
            <Route
              index
              element={
                <WrapInSuspense>
                  <PartnerHealthPage />
                </WrapInSuspense>
              }
            />
            <Route
              path=":partnerName"
              element={
                <WrapInSuspense>
                  <PartnerHealtDetial />
                </WrapInSuspense>
              }
            />
          </Route>
          <Route path="profile">
            <Route
              path="createApi"
              element={
                <WrapInSuspense>
                  <CreateApi type="operator" />
                </WrapInSuspense>
              }
            />
            <Route
              path="apiDetails"
              element={
                <WrapInSuspense>
                  <ApiDetailPage type="operator" />
                </WrapInSuspense>
              }
            />
          </Route>
        </>
      )}

      {MSP_ROLES.includes(userRole) && (
        <>
          <Route
            index
            element={
              <WrapInSuspense>
                <MspHome />
              </WrapInSuspense>
            }
          />
          <Route path="UM">
            <Route path="mspUsers">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <MspUserList />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userEmail"
                element={
                  <WrapInSuspense>
                    <MspUserDetailPage />
                  </WrapInSuspense>
                }
              />
              <Route
                path="createUser"
                element={
                  <WrapInSuspense>
                    <CreateUser type="msp" />
                  </WrapInSuspense>
                }
              />
            </Route>

            <Route path="userGroups">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <UserGroups type="msp" />
                  </WrapInSuspense>
                }
              />

              <Route
                path=":userGroupId"
                element={
                  <WrapInSuspense>
                    <UserGroupDetailPage type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userGroupId/edit"
                element={
                  <WrapInSuspense>
                    <CreateUserGroup mode="edit" type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path="createUserGroup"
                element={
                  <WrapInSuspense>
                    <CreateUserGroup type="msp" />
                  </WrapInSuspense>
                }
              />
            </Route>
            <Route path="apiKeys">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <UserApiPage type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path="createApi"
                element={
                  <WrapInSuspense>
                    <CreateApi type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path="apiDetails"
                element={
                  <WrapInSuspense>
                    <ApiDetailPage type="msp" />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>

          <Route path="TM">
            <Route path="tenants">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <PartnersPage type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route path=":featureType/:partnerName">
                {urlDetails.featureType === "default" && (
                  <Route
                    index
                    element={
                      <WrapInSuspense>
                        <HomePageSection />
                      </WrapInSuspense>
                    }
                  />
                )}
                {urlDetails.featureType === "geo" ? geoRoutes() : TENANTS_VIEW()}
              </Route>
              <Route path="tenantDetails/:partnerName">
                <Route
                  index
                  element={
                    <WrapInSuspense>
                      <PartnerDetailPage type="msp" />
                    </WrapInSuspense>
                  }
                />
              </Route>
            </Route>
            <Route path="tenantUsers">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <TenantUserList type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":userEmail"
                element={
                  <WrapInSuspense>
                    <TenantUserDetail />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>
          <Route path="tenants-access">
            <Route
              index
              element={
                <WrapInSuspense>
                  <TenantAccessList />
                </WrapInSuspense>
              }
            />
            <Route
              path="createTenantAccess"
              element={
                <WrapInSuspense>
                  <CreateTenantAccessTemplate mode="new" />
                </WrapInSuspense>
              }
            />
            <Route
              path=":templateId"
              element={
                <WrapInSuspense>
                  <TenantAccessDetails />
                </WrapInSuspense>
              }
            />
            <Route
              path=":templateId/edit"
              element={
                <WrapInSuspense>
                  <CreateTenantAccessTemplate mode="edit" />
                </WrapInSuspense>
              }
            />
          </Route>

          <Route path="templates">
            <Route path="product-templates">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <ProductTemplateList type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":projectName"
                element={
                  <WrapInSuspense>
                    <ProductTemplateDetails />
                  </WrapInSuspense>
                }
              />

              <Route
                path="addTemplate"
                element={
                  <WrapInSuspense>
                    <AddProductTemplateProvider type="msp">
                      <CreateProductTemplate />
                    </AddProductTemplateProvider>
                  </WrapInSuspense>
                }
              />

              <Route
                path=":projectName/basic-edit"
                element={
                  <WrapInSuspense>
                    <BasicDetailEdit type="msp" />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":projectName/policy-edit"
                element={
                  <WrapInSuspense>
                    <PolicyEdit />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":projectName/topics-edit"
                element={
                  <WrapInSuspense>
                    <TopicsEdit />
                  </WrapInSuspense>
                }
              />

              <Route
                path=":projectName/settings-edit"
                element={
                  <WrapInSuspense>
                    <SettingsEdit />
                  </WrapInSuspense>
                }
              />
            </Route>

            <Route path="simulator-templates">
              <Route
                index
                element={
                  <WrapInSuspense>
                    <SimulatorTemplateList />
                  </WrapInSuspense>
                }
              />
              <Route
                path=":templateName"
                element={
                  <WrapInSuspense>
                    <SimulatorDetailPage />
                  </WrapInSuspense>
                }
              />
              <Route
                path="new"
                element={
                  <WrapInSuspense>
                    <CreateSimulation />
                  </WrapInSuspense>
                }
              />
            </Route>
          </Route>
          <Route path="profile">
            <Route
              path="createApi"
              element={
                <WrapInSuspense>
                  <CreateApi type="msp" />
                </WrapInSuspense>
              }
            />
            <Route
              path="apiDetails"
              element={
                <WrapInSuspense>
                  <ApiDetailPage type="msp" />
                </WrapInSuspense>
              }
            />
          </Route>
        </>
      )}

      {TENANT_ROLES.includes(userRole) && TENANTS_VIEW()}
    </>
  );
};

export default defaultRoutes;
