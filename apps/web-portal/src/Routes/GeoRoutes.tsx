/* eslint-disable react-refresh/only-export-components */
import { ReactElement, Suspense, lazy } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Route } from "react-router-dom";

import { MODE } from "@api/index";
import { Tenant } from "@src/features/features";
import { isBasicAssetTracking } from "@utils/utilities";
import { ReactFlowProvider } from "reactflow";
import PageLoadingSkeleton from "@components/Skeletons/PageLoadingSkeleton";
const InventoryPage = lazy(() => import("../pages/InventoryPage"));
const InventoryDetailPage = lazy(() => import("../pages/InventoryPage/InventoryDetailPage"));
const InventoryBulkThingList = lazy(
  () => import("@src/pages/InventoryPage/BulkInventoryThings/InventoryBulkThingList")
);
const InventoryBulkThingStatus = lazy(
  () => import("@src/pages/InventoryPage/BulkInventoryThings/InventoryBulkThingStatus")
);
const IncidentCategoryList = lazy(() => import("@src/pages/Incidents/Category"));
const TicketsList = lazy(() => import("@src/pages/Incidents/Tickets"));
const TicketDetails = lazy(() => import("@src/pages/Incidents/Tickets/TicketDetails"));
const CreateTicketSection = lazy(() => import("@src/pages/Incidents/Tickets/CreateTicketSection"));

const NotificationStatusLogs = lazy(() => import("@src/pages/NotificationPage/NotificationLogs"));
const NotificationLogsDetails = lazy(
  () => import("@src/pages/NotificationPage/NotificationLogs/NotificationLogsDetails")
);

const AddContactSection = lazy(
  () => import("@src/pages/NotificationPage/Contacts/AddContactSection")
);
const ContactTeamsDetails = lazy(
  () => import("@src/pages/NotificationPage/Contacts/ContactTeamsDetails")
);
const SchedulesList = lazy(() => import("@src/pages/NotificationPage/Schedules"));
const AddSchedulesSection = lazy(
  () => import("@src/pages/NotificationPage/Schedules/AddSchedulesSection")
);
const ScheduleDetailPage = lazy(
  () => import("@src/pages/NotificationPage/Schedules/ScheduleDetailPage")
);
const BusDetailsPage = lazy(
  () => import("@src/pages/UserTypes/Tracking/BusManagement/BusDetailsPage")
);
const CreateRuleNext = lazy(() => import("@src/pages/RulePage/Next"));
const BulkThingsList = lazy(() => import("@src/pages/DevicesPage/BulkCreate/BulkThingsList"));
const ListenerPage = lazy(() => import("@src/pages/SecurityPage/ListenerPage"));
const NotificationList = lazy(() => import("@src/pages/NotificationPage/Notification"));

const NotificationDetail = lazy(
  () => import("@src/pages/NotificationPage/Notification/NotificationDetails")
);
const AddNotificationTemplate = lazy(
  () => import("@src/pages/NotificationPage/Notification/AddNotification/AddNotificationTemplate")
);
const EscalationPage = lazy(() => import("../pages/NotificationPage/Escalations"));
const EscalationDetailPage = lazy(
  () => import("../pages/NotificationPage/Escalations/EscalationDetailPage")
);
const ReleasesOtaPage = lazy(() => import("@src/pages/DevicesPage/ReleasesOtaPage"));
const OtaDocDetail = lazy(() => import("@src/pages/DevicesPage/OtaDocDetail"));
const CreateOtaRelease = lazy(() => import("@src/pages/DevicesPage/CreateOtaRelease"));
const OtaJobDetial = lazy(() => import("@src/pages/DevicesPage/OtaJobDetial"));
const DeviceOtaPage = lazy(() => import("@src/pages/DevicesPage/DeviceOtaPage"));
const NotificationHistroyList = lazy(
  () => import("@src/pages/NotificationPage/History/NotificationHistroyList")
);
const DriversList = lazy(() => import("@src/pages/UserTypes/Tracking/Drivers"));
const CreateNewDriver = lazy(() => import("@src/pages/UserTypes/Tracking/Drivers/CreateNewDriver"));
const DriverDetailsPage = lazy(
  () => import("@src/pages/UserTypes/Tracking/Drivers/DriverDetailsPage")
);
const ClassesList = lazy(() => import("../pages/UserTypes/Tracking/ClassManagement/ClassesList"));
const StudentList = lazy(
  () => import("@src/pages/UserTypes/Tracking/ClassManagement/StudentManagement/StudentList")
);
const CreateStudentSection = lazy(
  () =>
    import("@src/pages/UserTypes/Tracking/ClassManagement/StudentManagement/CreateStudentSection")
);
const StudentDetialPage = lazy(
  () => import("@src/pages/UserTypes/Tracking/ClassManagement/StudentManagement/StudentDetialPage")
);
const BusesListPage = lazy(
  () => import("@src/pages/UserTypes/Tracking/BusManagement/BusesListPage")
);
const TypographyShowcase = lazy(() => import("../pages/Typography"));

const BulkCreateStatus = lazy(() => import("../pages/DevicesPage/BulkCreate/BulkCreateStatus"));
const GeoProductDetailPage = lazy(
  () => import("../pages/UserTypes/Tracking/ProductPage/ProductDetailSection")
);
const AssetHomeSection = lazy(() => import("../pages/UserTypes/Tracking/DevicePage"));
const TrackingGeofenceSection = lazy(() => import("../pages/UserTypes/Tracking/GeofencePage"));
const TrackingHistorySection = lazy(() => import("../pages/UserTypes/Tracking/HistoryPage"));
const TrackingCreateThing = lazy(
  () => import("../pages/UserTypes/Tracking/HomeSection/CreateThingSection")
);

const DeviceDetailPage = lazy(
  () => import("../pages/UserTypes/Tracking/HomeSection/DeviceDetialSection/DeviceDetailPage")
);
const ProfilePage = lazy(() => import("../pages/ProfilePage/ProfilePage"));
const OrganizationPage = lazy(() => import("../pages/OrganizationPage"));
const DynamicGroupList = lazy(
  () => import("../pages/ThingGroupsPage/DynamicGroups/DynamicGroupList")
);
const StaticGroupList = lazy(() => import("../pages/ThingGroupsPage/StaticGroups/StaticGroupList"));
const DynamicGroupDetail = lazy(
  () => import("../pages/ThingGroupsPage/DynamicGroups/DynamicGroupDetail")
);
const StaticGroupDetail = lazy(
  () => import("../pages/ThingGroupsPage/StaticGroups/StaticGroupDetail")
);
const TrackingHomePageSection = lazy(() => import("../pages/UserTypes/Tracking/HomeSection/index"));
const TrackingProductSection = lazy(() => import("../pages/UserTypes/Tracking/ProductPage"));

const TrackingCreateProduct = lazy(
  () => import("../pages/UserTypes/Tracking/ProductPage/AddProductSection")
);
const CreateDashboard = lazy(() => import("../pages/MonitorPage/CreateDashboard"));
const MonitorPage = lazy(() => import("../pages/MonitorPage"));
const LoggerPage = lazy(() => import("../pages/Logger/LoggerPage"));
const TenantUserDetail = lazy(() => import("../pages/PartnersPage/Tenants/TenantUserDetail"));
const TenantUserList = lazy(() => import("../pages/PartnersPage/Tenants/TenantUserList"));
const CreateUserGroup = lazy(() => import("../pages/PartnersPage/UserGroups/CreateUserGroup"));
const UserGroupDetailPage = lazy(
  () => import("../pages/PartnersPage/UserGroups/UserGroupDetailPage")
);
const UserGroups = lazy(() => import("../pages/PartnersPage/UserGroups"));
const ApiDetailPage = lazy(() => import("../pages/PartnersPage/UserApi/ApiDetailPage"));
const CreateApi = lazy(() => import("../pages/PartnersPage/UserApi/CreateApi"));
const CreateUser = lazy(() => import("../pages/PartnersPage/CreateUser"));
const UserApiPage = lazy(() => import("../pages/PartnersPage/UserApi"));
const RulePage = lazy(() => import("../pages/RulePage"));

const RuleDetailsPage = lazy(() => import("../pages/RulePage/RuleDetailsPage"));
const DestinationPage = lazy(() => import("../pages/RulePage/DestinationPage"));
const IntegraionPage = lazy(() => import("../pages/RulePage/IntegrationPage"));
const DestinationDetailPage = lazy(
  () => import("../pages/RulePage/DestinationPage/DestinationDetailPage")
);
const IntegrationDetialPage = lazy(
  () => import("../pages/RulePage/IntegrationPage/IntegrationDetialPage")
);

const ContactList = lazy(() => import("../pages/NotificationPage/Contacts"));
const ContactDetailPage = lazy(
  () => import("../pages/NotificationPage/Contacts/ContactDetailPage")
);

const Certificates = lazy(() => import("../pages/SecurityPage/ThingCertificate/Certificates"));
const PolicyTemplate = lazy(() => import("../pages/SecurityPage/PolicyTemplate"));
const Policy = lazy(() => import("../pages/SecurityPage/Policy/Policy"));
const CertificateAuth = lazy(() => import("../pages/SecurityPage/CertificateAuth/CertificateAuth"));
const UserAuth = lazy(() => import("../pages/SecurityPage/Authentication/Authentication"));
const CertificateDetail = lazy(
  () => import("../pages/SecurityPage/ThingCertificate/CertificateDetail")
);
const TemplateDetail = lazy(() => import("../pages/SecurityPage/PolicyTemplate/TemplateDetail"));
const PolicyDetail = lazy(() => import("../pages/SecurityPage/Policy/PolicyDetail"));
const CertAuthDetail = lazy(() => import("../pages/SecurityPage/CertificateAuth/CertAuthDetail"));
const AddCertAuth = lazy(() => import("../pages/SecurityPage/CertificateAuth/AddCertAuth"));
const AppUsers = lazy(() => import("../pages/AppUsers"));
const AppUserDetails = lazy(() => import("../pages/AppUsers/AppUserDetails"));

const ProductTopicsPage = lazy(
  () => import("../pages/ProductPage/ProductDetailSection/ProductTopicsModal")
);
const WhiteLabel = lazy(() => import("../pages/WhiteLabels"));
const TrackingRoute = lazy(() => import("../pages/UserTypes/Tracking/RoutesPage"));
const ChatBot = lazy(() => import("@components/Chatbot"));
const ReportRequestPage = lazy(() => import("@src/pages/ReportRequestPage"));

const PageLoader = () => {
  return <PageLoadingSkeleton />;
};

const WrapInSuspense = ({ children }: { children: ReactElement }) => {
  return <Suspense fallback={<PageLoader />}>{children}</Suspense>;
};

const geoRoutes = (subFeature: Tenant["subFeature"]) => {
  const basicAssetTracking = isBasicAssetTracking(subFeature);

  return (
    <>
      <Route
        index
        element={
          <WrapInSuspense>
            <TrackingHomePageSection />
          </WrapInSuspense>
        }
      />

      <Route path="things">
        <Route
          index
          element={
            <WrapInSuspense>
              <AssetHomeSection />
            </WrapInSuspense>
          }
        />

        <Route path=":thingName">
          <Route
            index
            element={
              <WrapInSuspense>
                <DeviceDetailPage />
              </WrapInSuspense>
            }
          />
          <Route
            path="ota"
            element={
              <WrapInSuspense>
                <DeviceOtaPage />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route
          path="addThing"
          element={
            <WrapInSuspense>
              {subFeature === "studentSafety" ? <CreateStudentSection /> : <TrackingCreateThing />}
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="products">
        <Route
          index
          element={
            <WrapInSuspense>
              <TrackingProductSection />
            </WrapInSuspense>
          }
        />
        <Route
          path=":productName"
          element={
            <WrapInSuspense>
              <GeoProductDetailPage />
            </WrapInSuspense>
          }
        />
        <Route
          path=":productName/editTopics"
          element={
            <WrapInSuspense>
              <ProductTopicsPage />
            </WrapInSuspense>
          }
        />
        <Route
          path="addProduct"
          element={
            <WrapInSuspense>
              <TrackingCreateProduct />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="geofences">
        <Route
          index
          element={
            <WrapInSuspense>
              <TrackingGeofenceSection />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route path="history">
        <Route
          index
          element={
            <WrapInSuspense>
              <TrackingHistorySection />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="classes">
        <Route
          index
          element={
            <WrapInSuspense>
              <ClassesList />
            </WrapInSuspense>
          }
        />

        <Route path=":nameOfClass">
          <Route
            index
            element={
              <WrapInSuspense>
                <StudentList />
              </WrapInSuspense>
            }
          />
          <Route
            path="addStudent"
            element={
              <WrapInSuspense>
                <CreateStudentSection />
              </WrapInSuspense>
            }
          />
          <Route
            path=":thingName"
            element={
              <WrapInSuspense>
                <StudentDetialPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":thingName/editStudent"
            element={
              <WrapInSuspense>
                <CreateStudentSection />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="buses-list">
        <Route
          index
          element={
            <WrapInSuspense>
              <BusesListPage />
            </WrapInSuspense>
          }
        />
        <Route
          path=":thingName"
          element={
            <WrapInSuspense>
              <BusDetailsPage />
            </WrapInSuspense>
          }
        />
      </Route>

      {!basicAssetTracking && (
        <Route path="drivers">
          <Route
            index
            element={
              <WrapInSuspense>
                <DriversList />
              </WrapInSuspense>
            }
          />
          <Route
            path="addDriver"
            element={
              <WrapInSuspense>
                <CreateNewDriver />
              </WrapInSuspense>
            }
          />
          <Route
            path=":driverId"
            element={
              <WrapInSuspense>
                <DriverDetailsPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":driverId/editDriver"
            element={
              <WrapInSuspense>
                <CreateNewDriver />
              </WrapInSuspense>
            }
          />
        </Route>
      )}
      {!basicAssetTracking && (
        <Route path="routes">
          <Route index element={<TrackingRoute />} />
        </Route>
      )}
      <Route path="inventory">
        <Route
          index
          element={
            <WrapInSuspense>
              <InventoryPage />
            </WrapInSuspense>
          }
        />
        <Route
          path=":thingName"
          element={
            <WrapInSuspense>
              <InventoryDetailPage />
            </WrapInSuspense>
          }
        />
      </Route>
      <Route path="thingGroup">
        <Route path="dynamic">
          <Route index element={<DynamicGroupList />} />
          <Route path=":thingGroupName" element={<DynamicGroupDetail />} />
        </Route>

        <Route path="static">
          <Route index element={<StaticGroupList />} />
          <Route path=":thingGroupName" element={<StaticGroupDetail />} />
        </Route>
      </Route>
      <Route path="monitor">
        <Route path="dashboard">
          <Route
            index
            element={
              <WrapInSuspense>
                <MonitorPage />
              </WrapInSuspense>
            }
          />
          <Route
            path="create"
            element={
              <WrapInSuspense>
                <DndProvider backend={HTML5Backend}>
                  <CreateDashboard />
                </DndProvider>
              </WrapInSuspense>
            }
          />
          <Route
            path=":dashboardId"
            element={
              <WrapInSuspense>
                <DndProvider backend={HTML5Backend}>
                  <CreateDashboard />
                </DndProvider>
              </WrapInSuspense>
            }
          />
        </Route>
        <Route
          path="logs"
          element={
            <WrapInSuspense>
              <LoggerPage />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="UM">
        <Route path="users">
          <Route
            index
            element={
              <WrapInSuspense>
                <TenantUserList type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path=":userEmail"
            element={
              <WrapInSuspense>
                <TenantUserDetail />
              </WrapInSuspense>
            }
          />
          <Route
            path="createUser"
            element={
              <WrapInSuspense>
                <CreateUser type="tenant" />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="userGroups">
          <Route
            index
            element={
              <WrapInSuspense>
                <UserGroups type="tenant" />
              </WrapInSuspense>
            }
          />

          <Route
            path=":userGroupId"
            element={
              <WrapInSuspense>
                <UserGroupDetailPage type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path=":userGroupId/edit"
            element={
              <WrapInSuspense>
                <CreateUserGroup mode="edit" type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path="createUserGroup"
            element={
              <WrapInSuspense>
                <CreateUserGroup type="tenant" />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="apiKeys">
          <Route
            index
            element={
              <WrapInSuspense>
                <UserApiPage type="tnt" />
              </WrapInSuspense>
            }
          />
          <Route
            path="createApi"
            element={
              <WrapInSuspense>
                <CreateApi type="tenant" />
              </WrapInSuspense>
            }
          />
          <Route
            path="apiDetails"
            element={
              <WrapInSuspense>
                <ApiDetailPage />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>

      <Route path="actions">
        <Route path="rules">
          <Route
            index
            element={
              <WrapInSuspense>
                <RulePage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":ruleId"
            element={
              <WrapInSuspense>
                <RuleDetailsPage />
              </WrapInSuspense>
            }
          />
          {/* <Route
            path="create"
            element={
              <WrapInSuspense>
                <RulesProvider>
                  <CreateRulePage />
                </RulesProvider>
              </WrapInSuspense>
            }
          /> */}
          <Route
            path="create-next"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
          <Route
            path=":ruleId/edit"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="workflow">
          <Route
            index
            element={
              <WrapInSuspense>
                <RulePage mode="workflows" />
              </WrapInSuspense>
            }
          />

          <Route
            path=":workflowId"
            element={
              <WrapInSuspense>
                <RuleDetailsPage />
              </WrapInSuspense>
            }
          />

          <Route
            path="create"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
          <Route
            path=":workflowId/edit"
            element={
              <WrapInSuspense>
                <ReactFlowProvider>
                  <CreateRuleNext />
                </ReactFlowProvider>
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="destinations">
          <Route
            index
            element={
              <WrapInSuspense>
                <DestinationPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":destinationId"
            element={
              <WrapInSuspense>
                <DestinationDetailPage />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="integrations">
          <Route
            index
            element={
              <WrapInSuspense>
                <IntegraionPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":integrationId"
            element={
              <WrapInSuspense>
                <IntegrationDetialPage />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="notifications">
        <Route path="templates">
          <Route
            index
            element={
              <WrapInSuspense>
                <NotificationList />
              </WrapInSuspense>
            }
          />
          <Route
            path="add-Notification"
            element={
              <WrapInSuspense>
                <AddNotificationTemplate />
              </WrapInSuspense>
            }
          />
          <Route
            path=":templateName"
            element={
              <WrapInSuspense>
                <NotificationDetail />
              </WrapInSuspense>
            }
          />
          <Route
            path=":templateName/edit"
            element={
              <WrapInSuspense>
                <AddNotificationTemplate />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="schedules">
          <Route
            index
            element={
              <WrapInSuspense>
                <SchedulesList />
              </WrapInSuspense>
            }
          />
          <Route
            path="add-schedules"
            element={
              <WrapInSuspense>
                <AddSchedulesSection />
              </WrapInSuspense>
            }
          />
          <Route
            path=":scheduleId"
            element={
              <WrapInSuspense>
                <ScheduleDetailPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":scheduleId/edit"
            element={
              <WrapInSuspense>
                <AddSchedulesSection />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="contacts">
          <Route
            index
            element={
              <WrapInSuspense>
                <ContactList />
              </WrapInSuspense>
            }
          />
          <Route
            path="add-contact"
            element={
              <WrapInSuspense>
                <AddContactSection />
              </WrapInSuspense>
            }
          />
          <Route path="contacts-teams">
            <Route
              path=":groupId"
              element={
                <WrapInSuspense>
                  <ContactTeamsDetails />
                </WrapInSuspense>
              }
            />
          </Route>
          <Route
            path=":contactId"
            element={
              <WrapInSuspense>
                <ContactDetailPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":contactId/edit"
            element={
              <WrapInSuspense>
                <AddContactSection />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="escalations">
          <Route
            index
            element={
              <WrapInSuspense>
                <EscalationPage />
              </WrapInSuspense>
            }
          />
          <Route
            path=":thingName"
            element={
              <WrapInSuspense>
                <EscalationDetailPage />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route
          path="history"
          element={
            <WrapInSuspense>
              <NotificationHistroyList />
            </WrapInSuspense>
          }
        />
        <Route path="status">
          <Route
            index
            element={
              <WrapInSuspense>
                <NotificationStatusLogs />
              </WrapInSuspense>
            }
          />
          <Route path=":logId" element={<NotificationLogsDetails />} />
        </Route>
      </Route>
      <Route path="incidents">
        <Route path="category">
          <Route index element={<IncidentCategoryList />} />
        </Route>

        <Route path="tickets">
          <Route index element={<TicketsList />} />
          <Route path="addTicket" element={<CreateTicketSection />} />
          <Route path=":ticketId" element={<TicketDetails />} />
        </Route>
      </Route>
      <Route path="jobs">
        <Route path="ota">
          <Route
            index
            element={
              <WrapInSuspense>
                <ReleasesOtaPage />
              </WrapInSuspense>
            }
          />

          <Route
            path="createOta"
            element={
              <WrapInSuspense>
                <CreateOtaRelease />
              </WrapInSuspense>
            }
          />
          <Route path=":otaId">
            <Route
              index
              element={
                <WrapInSuspense>
                  <OtaDocDetail />
                </WrapInSuspense>
              }
            />
            <Route
              path=":jobId"
              element={
                <WrapInSuspense>
                  <OtaJobDetial />
                </WrapInSuspense>
              }
            />
          </Route>
        </Route>
        <Route path="bulk-things">
          <Route
            index
            element={
              <WrapInSuspense>
                <BulkThingsList />
              </WrapInSuspense>
            }
          />
          <Route
            path=":docId"
            element={
              <WrapInSuspense>
                <BulkCreateStatus />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="inventory-bulk-things">
          <Route
            index
            element={
              <WrapInSuspense>
                <InventoryBulkThingList />
              </WrapInSuspense>
            }
          />
          <Route
            path=":docId"
            element={
              <WrapInSuspense>
                <InventoryBulkThingStatus />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route
          path="reports"
          element={
            <WrapInSuspense>
              <ReportRequestPage />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="settings">
        <Route
          path="white-labeling"
          element={
            <WrapInSuspense>
              <WhiteLabel />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="app-users">
        <Route
          index
          element={
            <WrapInSuspense>
              <AppUsers />
            </WrapInSuspense>
          }
        />
        <Route
          path=":email"
          element={
            <WrapInSuspense>
              <AppUserDetails />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route path="security">
        <Route path="certificate">
          <Route
            index
            element={
              <WrapInSuspense>
                <Certificates />
              </WrapInSuspense>
            }
          />
          <Route
            path=":certificateId"
            element={
              <WrapInSuspense>
                <CertificateDetail />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="template">
          <Route index element={<PolicyTemplate />} />
          <Route
            path=":templateName"
            element={
              <WrapInSuspense>
                <TemplateDetail />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="policy">
          <Route index element={<Policy />} />
          <Route
            path=":thingName"
            element={
              <WrapInSuspense>
                <PolicyDetail />
              </WrapInSuspense>
            }
          />
        </Route>

        <Route path="certificateAuth">
          <Route
            index
            element={
              <WrapInSuspense>
                <CertificateAuth />
              </WrapInSuspense>
            }
          />
          <Route
            path="addCertAuth"
            element={
              <WrapInSuspense>
                <AddCertAuth />
              </WrapInSuspense>
            }
          />
          <Route
            path=":caName"
            element={
              <WrapInSuspense>
                <CertAuthDetail />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="protocol_Services">
          <Route
            index
            element={
              <WrapInSuspense>
                <ListenerPage />
              </WrapInSuspense>
            }
          />
        </Route>
        <Route path="authentication">
          <Route
            index
            element={
              <WrapInSuspense>
                <UserAuth />
              </WrapInSuspense>
            }
          />
        </Route>
      </Route>
      <Route path="profile">
        <Route
          index
          element={
            <WrapInSuspense>
              <ProfilePage />
            </WrapInSuspense>
          }
        />
        <Route
          path="createApi"
          element={
            <WrapInSuspense>
              <CreateApi />
            </WrapInSuspense>
          }
        />
        <Route
          path="apiDetails"
          element={
            <WrapInSuspense>
              <ApiDetailPage />
            </WrapInSuspense>
          }
        />
      </Route>

      <Route
        path="chat-bot"
        element={
          <WrapInSuspense>
            <ChatBot view="largeScreen" />
          </WrapInSuspense>
        }
      />

      {MODE === "development" && <Route path="typography" element={<TypographyShowcase />} />}

      <Route
        path="organization"
        element={
          <WrapInSuspense>
            <OrganizationPage />
          </WrapInSuspense>
        }
      />
    </>
  );
};

export default geoRoutes;
