@layer components {
  /* -- FONTS ------------------------------------------------------ */
  /* .heading-1 {
    @apply text-4xl font-bold text-gray-700;
    @apply dark:text-gray-200;
  }
  .heading-2 {
    @apply text-3xl font-medium text-gray-700;
    @apply dark:text-gray-200;
  }
  .heading-3 {
    @apply text-2xl font-medium text-gray-700;
    @apply dark:text-gray-200;
  }
  .heading-4 {
    @apply text-xl font-medium text-gray-700;
    @apply dark:text-gray-200;
  }
  .heading-5 {
    @apply text-lg font-medium text-gray-700;
    @apply dark:text-gray-200;
  } */
  /* 

  .sub-heading {
    @apply text-xl font-medium text-gray-700;
    @apply dark:text-gray-200;
  }
  .sub-heading-2 {
    @apply text-lg font-medium text-gray-700;
    @apply dark:text-gray-200;
  } */

  /* Status Indicators */
  .status-online {
    @apply text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 border-green-300 dark:border-green-700;
  }

  .status-offline {
    @apply text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 border-red-300 dark:border-red-700;
  }

  .status-warning {
    @apply text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-700;
  }

  .status-error {
    @apply text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 border-red-300 dark:border-red-700;
  }

  .status-pending {
    @apply text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20 border-blue-300 dark:border-blue-700;
  }

  .status-success {
    @apply text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 border-green-300 dark:border-green-700;
  }

  .heading-lg {
    font-size: 1.563rem;
    font-weight: 500;
    line-height: 1.8rem;
  }

  .heading-1 {
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.5rem;
  }

  .heading-2,
  .sidebar-heading {
    @apply text-xl font-semibold tracking-tight;
  }

  .heading-2-bold {
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.313rem;
  }
  .heading-3-normal {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.313rem;
  }

  .heading-3,
  .sidebar-sub-heading {
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.313rem;
  }
  .heading-3-bold {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.313rem;
  }

  .heading-4 {
    font-size: 0.813rem;
    font-weight: 500;
    line-height: 1.25rem;
  }
  .heading-4-bold {
    font-size: 0.813rem;
    font-weight: 600;
    line-height: 1.25rem;
  }

  .sub-heading-1 {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.313rem;
  }
  .sub-heading-2 {
    font-size: 0.813rem;
    font-weight: 500;
    line-height: 1.25rem;
  }
  .sub-content-1 {
    font-size: 0.813rem;
    font-weight: 400;
    line-height: 1.313rem;
  }
  .sub-content-bold-1 {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.313rem;
  }
  .sub-content-2 {
    font-size: 0.813rem;
    font-weight: 500;
    line-height: 1.25rem;
  }
  .sub-content-bold-2 {
    font-size: 0.813rem;
    font-weight: 600;
    line-height: 1.25rem;
  }

  .content-1 {
    font-size: 0.813rem;
    font-weight: 400;
    line-height: 1.125rem;
  }
  .content-2 {
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.125rem;
  }
  .content-sm {
    font-size: 0.688rem;
    font-weight: 400;
    line-height: 0.9rem;
  }

  .page-title {
    @apply heading-4 md:heading-3;
  }
  .page-title-link {
    @apply text-blue-500 hover:text-blue-600;
    @apply dark:text-blue-400 dark:hover:text-blue-500;
  }

  .card-title {
    @apply sub-heading;
  }
  .card-data-text {
    @apply text-3xl font-light;
    @apply dark:text-gray-200;
  }
  .card-data-title {
    @apply font-bold text-lg;
    @apply dark:text-gray-300;
  }
  .card-data-sub-text {
    @apply text-xs text-gray-400;
  }
  .chartcard-title {
    @apply dark:text-gray-300;
    @apply text-lg;
  }
  .chartcard-text {
    @apply heading-1;
    @apply dark:text-gray-200;
  }
  .infocard-title {
    @apply font-medium text-gray-700;
    @apply dark:text-gray-300;
  }
  .infocard-text {
    @apply text-2xl text-gray-700;
    @apply dark:text-gray-200;
  }
  .infocard-footer-text {
    @apply text-lg text-gray-600;
    @apply dark:text-gray-400;
  }
  .project-card-title {
    @apply text-lg font-medium;
    @apply dark:text-gray-200;
  }
  .project-card-details {
    @apply text-gray-700 dark:text-gray-300;
  }
  .project-card-cell-title {
    @apply text-lg font-bold;
  }

  .modal-title {
    @apply heading-3 text-left;
  }

  .build-step-title {
    @apply heading-2 text-left;
  }

  .sidebar-text {
    @apply text-white;
  }
  .sidebar-brand-text {
    @apply text-2xl font-black;
  }
  .sidebar-link-text {
    @apply text-sm font-normal;
  }

  .sidebar-link-active .sidebar-link-text {
    @apply font-medium;
  }

  .sidebar-username {
    @apply sidebar-link-text;
  }
  .sidebar-buildlimit-title {
    @apply font-normal text-sm;
  }
  .sidebar-data-title {
    @apply text-xs text-white/60 font-medium;
  }
  .sidebar-data-text {
    @apply font-medium;
  }

  .table-text {
    @apply text-sm text-left;
  }
  .table-head-text {
    @apply text-sm font-bold text-white/90;
  }
  .table-body-text {
    @apply text-sm text-gray-700;
    @apply dark:text-gray-200;
  }
  .table-body-first-col {
    @apply content-bold text-gray-900;
    @apply dark:text-white;
  }

  .button-text {
    @apply content-bold;
  }
  .button-sm-text {
    @apply button-text text-xs;
  }

  .label-text {
    @apply font-normal text-xs truncate;
  }

  .input-label-text {
    @apply text-muted-foreground text-sm font-semibold;
  }

  .switch-label-text {
    @apply dark:text-gray-300 font-medium text-base;
  }

  .profile-name {
    @apply heading-4 text-blue-500 font-medium;
    @apply dark:text-blue-400;
  }
  .profile-company-name {
    @apply heading-4 text-gray-700;
  }
  .profile-partner-name {
    @apply heading-4 text-blue-500 font-medium;
    @apply dark:text-blue-300;
  }
  .profile-email {
    @apply font-light text-gray-600;
    @apply dark:text-gray-300;
  }

  .card-border {
    @apply border border-card-border;
  }

  /* -- Components ------------------------------------------------------ */
  /* Card - Updated to use CSS variables */
  .card {
    @apply bg-card text-card-foreground p-5 rounded-lg border-card-border border;
    @apply transition-all duration-200;
    @apply hover:shadow;
  }

  /* Button */
  .button {
    @apply center transition duration-200 rounded-md;
  }
  .button-xs {
    @apply button-sm-text h-6 px-2 py-1;
  }
  .button-sm {
    @apply button-sm-text h-8 rounded-md px-3;
  }

  .button-md {
    @apply button-text h-10 px-4 py-2;
  }
  .button-outlined {
    @apply border-2 border-blue-500 text-blue-500 hover:bg-blue-50;
  }
  .button-link {
    @apply text-blue-500 hover:text-blue-600;
    @apply hover:!shadow-none;
    @apply focus:ring-1 focus:ring-gray-200;
    @apply dark:focus:ring-gray-700;
  }
  .button-primary {
    @apply bg-gradient-to-t from-blue-600 to-blue-500 text-white shadow;
  }
  .button:hover {
    @apply shadow-md opacity-90;
  }

  /* label */
  .label {
    @apply center font-medium rounded-md w-fit label-text px-2 py-[.065rem];
  }

  /* Sidebar */
  .sidebar {
    @apply sidebar-text px-1;
  }
  .sidebar-brand-section {
    @apply p-2  sticky top-0 z-20;
    /* @apply border-b-2 border-white/20 shadow; */
  }
  .sidebar-brand-logo {
    max-width: 10rem;
  }
  .sidebar-user-avatar {
    height: 48px;
    width: 48px;
  }
  .sidebar-section {
    @apply ml-2 mb-3 py-1 rounded-xl gap-2 bg-[#222b45];
    max-width: 250px;
  }
  .sidebar-link {
    @apply bg-white bg-opacity-0;
    @apply mx-2 m-1 p-2 rounded-lg;
    @apply transition-all duration-200 text-muted-foreground hover:text-foreground;
    @apply active:px-4;
  }
  .sidebar-link-active {
    @apply bg-brandColor text-buttonText hover:text-black
    /* @apply bg-brandColor hover:bg-brandColor/80 !text-black; */;
  }
  .sidebar-link-children {
    /* @apply bg-gradient-to-t from-white/10 to-transparent; */
    @apply rounded-b-lg pb-1;
  }
  .sidebar-link-icon {
    width: 1.1rem;
    height: 1.1rem;
  }
  .sidebar-link-icon2 {
    width: 1.5rem;
    height: 1.5rem;
  }
  .sidebar-link-children-icon {
    width: 12px;
    height: 12px;
  }

  .sidebar-expand-btn {
    @apply text-white rounded-lg p-1;
    @apply opacity-60 hover:opacity-100;
  }

  .scroll-to-top {
    @apply text-white rounded-lg p-1;
    @apply opacity-60 hover:opacity-100;
  }

  /* Table */
  .table-style {
    @apply rounded-lg;
  }
  tr:not(.table-head):not(.custom-table) {
    @apply border-b  bg-card border-gray-200/60 dark:border-b-gray-800/60 transition-colors hover:bg-secondary/50 data-[state=selected]:bg-secondary/70;
  }

  /* .table-head {
    @apply text-left;
  } */

  /* Tab */
  .tab-container {
    /* @apply flex gap-2 max-w-fit bg-gray-200 rounded-xl; */
    @apply flex gap-1 max-w-fit bg-gray-200 rounded-md overflow-hidden p-1;
    @apply dark:bg-secondary;
  }
  .tab-primary {
    @apply text-gray-600 shadow-inner;
    @apply dark:text-gray-300;
  }
  .tab-outlined {
    @apply border border-brandColor text-white;
  }
  .tab-item {
    @apply center font-medium rounded-md;
    @apply transition-colors hover:bg-blue-400/20;
  }
  .tab-item-selected {
    @apply shadow bg-blue-500 text-white;
  }

  /* Help Modal */
  /* .help-modal-container {
    @apply prose prose-img:rounded-xl;
    @apply dark:prose-invert;
    @apply prose-a:text-blue-500 hover:prose-a:text-blue-600;
  } */
  .help-modal-btn {
    @apply rounded-full p-0.5;
    @apply text-gray-400 bg-gray-400/20 transition-colors;
    @apply hover:bg-gray-400/40;
  }
  .help-minimized-btn {
    @apply bg-blue-500 text-white font-bold;
    @apply rounded-l-lg p-2 transition-all ease-out;
    @apply opacity-70 hover:opacity-100 hover:pr-4;
  }

  /* Custom Toggle */
  .toggle {
    @apply font-bold  px-4 py-2;
    @apply bg-gray-400/10 rounded-xl;
    @apply transition duration-300 grayscale;
  }
  .toggle-enabled {
    @apply grayscale-0;
  }

  /* Custom Radio */
  .radio {
    @apply toggle;
    @apply border-2 border-green-500;
  }
  .radio-selected {
    @apply toggle-enabled;
  }
  /* Custom Switch */
  .switch {
    @apply p-1 rounded-full;
    min-width: 3.5rem;
  }
  .switch-toggle {
    @apply transition-all duration-200;
    @apply max-w-fit shadow-md rounded-full p-0.5;
  }
  .switch-toggle-enabled {
    @apply bg-white text-white;
    transform: translate(1.75rem, 0) rotate(360deg);
  }
  .switch-toggle-disabled {
    @apply bg-white dark:text-white;
  }
  .switch-enabled {
    @apply bg-brandColor;
  }
  .switch-disabled {
    @apply bg-gray-400/40;
  }

  .admin-layout {
    @apply bg-background transition-colors;
  }

  .header {
    @apply py-2  px-4 backdrop-blur-lg;
  }

  .log-display {
    @apply w-full h-56 rounded-lg p-4 overflow-y-auto;
    @apply bg-gray-400/40 text-gray-800 dark:text-gray-300;
  }

  /* hr */
  .hr {
    @apply border-[.063] border-gray-500/20 rounded;
  }

  /* input */
  .input {
    @apply flex px-3 py-2  rounded-md bg-transparent w-full  outline-none text-sm;
    @apply text-gray-900 dark:text-white placeholder:text-muted-foreground disabled:cursor-not-allowed  file:border-0 file:bg-transparent file:text-sm file:font-medium;
  }
  .input-disabled {
    @apply !text-gray-500 dark:!text-gray-300;
  }
  .input-container {
    @apply min-h-[2.5rem] rounded-lg w-full  border border-gray-300 dark:border-gray-600 shadow-sm bg-transparent  ring-offset-background;
  }
  .input-primary {
    @apply focus-within:outline-none focus-within:ring-1 focus-within:ring-brandColor;
  }
  .input-error {
    @apply border-red-300 focus-within:border-red-500 dark:border-red-600;
  }
  .input-icon {
    @apply text-gray-700 dark:text-gray-300;
  }
  .input-helper-text {
    @apply mt-1.5 ml-1 text-sm text-gray-400;
  }
  .input-helper-text-error {
    @apply !text-red-600 dark:!text-red-500;
  }

  /* -----Typography Starts----- */

  .test-heading1 {
    @apply text-[2.1rem] text-[#ced4da] font-semibold leading-6;
  }
  .test-heading2 {
    @apply text-[1.7rem] text-[#ced4da] font-semibold leading-6;
  }
  .test-heading3 {
    @apply text-[1.45rem] text-[#ced4da] font-medium leading-6;
  }

  .test-heading4 {
    @apply text-[1rem] text-gray-600 dark:text-[#ced4da] font-medium leading-6;
  }

  .test-heading5 {
    @apply text-[.9rem] text-[#ced4da] font-normal leading-6;
  }

  .test-heading6 {
    @apply text-[.8rem] text-[#ced4da] font-semibold leading-6;
  }

  .test-para1 {
    @apply text-[#ced4da];
  }

  .test-subheading-1 {
    @apply text-gray-400 font-medium;
  }
}

.title-heading-1 {
  @apply text-[3rem] font-semibold leading-tight;
}
.title-heading-2 {
  @apply text-[2.5rem] font-semibold leading-tight;
}
.title-heading-3 {
  @apply text-[2rem] font-medium leading-tight;
}
.title-heading-4 {
  @apply text-[1.5rem] font-medium leading-tight;
}
.title-heading-5 {
  @apply text-[1.25rem] font-medium leading-tight;
}
.title-heading-6 {
  @apply text-[1rem] font-medium leading-tight;
}
.title-heading-7 {
  @apply text-[.75rem] font-medium leading-tight;
}

.content-heading {
  @apply text-lg leading-6 font-medium;
}

.content-base {
  @apply leading-6 font-medium;
}

.description,
.detailcell-value,
.content {
  @apply text-sm;
}

.description-lg {
  @apply text-lg font-medium;
}

.not-found-text {
  @apply text-lg font-medium text-gray-500 dark:text-gray-400;
}
.helper-text {
  @apply text-sm leading-6 text-muted-foreground;
}
.detailcell-label {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.content-bold {
  @apply text-sm font-medium;
}

/* -----Typography Ends----- */

@layer utilities {
  .center {
    @apply flex justify-center items-center;
  }
  .between {
    @apply flex justify-between items-center;
  }
  .divide-items-x {
    @apply divide-x-2 divide-gray-500/20;
  }
  .divide-items-y {
    @apply divide-y-2 divide-gray-500/20;
  }
}

/* CUSTOM DROPDOWN */

.dropdown-container {
  @apply relative rounded-lg w-full;
}

.dropdown-menu {
  @apply p-1 absolute rounded-md max-h-[15rem] overflow-auto z-[9999] bg-card text-card-foreground border border-card-border shadow-card;
}

.dropdown-item {
  /* @apply cursor-pointer text-xs p-2 text-left text-black dark:text-white; */
  @apply relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50;
}
.dropdown-item:hover {
  @apply bg-brandColor/80 text-black;
}

/* .dropdown-item.selected {
  @apply bg-blue-500 !text-white font-medium hover:!bg-blue-600;
} */

.search-box {
  @apply p-3;
}

.dropdown-tag-item {
  @apply bg-brandColor !text-black rounded-md flex gap-1 items-center content-1 py-[2px] px-[4px];
}

.dropdown-tag-close {
  @apply flex items-center;
}

/* SIDE BAR RAW CSS */

.sidebar {
  @apply backdrop-blur-sm bg-card border border-border duration-300 ease-in-out fixed inset-y-4 left-4  rounded-xl   flex flex-col  w-64 z-[100];
  @apply border-r border-gray-300 dark:border-gray-800;
  transition: all 0.5s ease;
}

.sidebar .logo-details {
  @apply h-[60px] w-full flex items-center;
}
.sidebar .logo-details i {
  @apply text-white text-[30px] h-[50px] min-w-[80px] text-center leading-[50px];
}
.sidebar .logo-details .logo_name {
  @apply text-[22px] text-white font-medium;
  transition: 0.3s ease;
  transition-delay: 0.1s;

  /* font-size: 22px;
  color: #fff;
  font-weight: 600; */
}
.sidebar.close .logo-details .logo_name {
  transition-delay: 0s;
  opacity: 0;
  pointer-events: none;
}
.sidebar .nav-links {
  overflow: auto;
}

.sidebar .nav-links::-webkit-scrollbar {
  display: none;
}
.sidebar .nav-links li {
  position: relative;
  list-style: none;
  transition: all 0.4s ease;
}
/* .sidebar .nav-links li:hover{
  background: #1d1b31;
} */
.sidebar .nav-links li .iocn-link {
  display: flex;
  align-items: center;
  justify-content: center;
}
.sidebar.close .nav-links li .iocn-link {
  /* display: block */
}
.sidebar .nav-links li i {
  height: 50px;
  min-width: 78px;
  text-align: center;
  line-height: 50px;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.sidebar .nav-links li.showMenu i.arrow {
  transform: rotate(-180deg);
}
.sidebar.close .nav-links i.arrow {
  display: none;
}
.sidebar .nav-links li a {
  display: flex;
  align-items: center;
  text-decoration: none;
}
.sidebar .nav-links li a .link_name {
  font-size: 18px;
  font-weight: 400;
  color: #fff;
  transition: all 0.4s ease;
}
.sidebar.close .nav-links li a .link_name {
  opacity: 0;
  pointer-events: none;
}
.sidebar .nav-links li .sub-menu {
  @apply bg-card hidden text-foreground;
  padding: 6px 6px 6px 12px;
  margin-top: -10px;
}
.sidebar .nav-links li.showMenu .sub-menu {
  display: block;
}
.sidebar .nav-links li .sub-menu a {
  font-size: 15px;
  padding: 0.5rem;
  white-space: nowrap;
  transition: all 0.3s ease;
}
.sidebar .nav-links li .sub-menu a:hover {
  opacity: 1;
}
.sidebar.close .nav-links li .sub-menu {
  position: absolute;
  left: 90%;
  top: -10px;
  margin-top: 0;
  padding: 0.75rem;
  border-radius: 5px;
  opacity: 0;
  display: block;
  pointer-events: none;
  min-width: 12.5rem;
  transition: 0s;
}
.sidebar.close .nav-links li:hover .sub-menu {
  top: 0;
  opacity: 1;
  pointer-events: auto;
  transition: all 0.4s ease;
  /* left: 80%; */
}
.sidebar .nav-links li .sub-menu .link_name {
  display: none;
}
.sidebar.close .nav-links li .sub-menu .link_name {
  opacity: 1;
  display: block;
}
.sidebar .nav-links li .sub-menu.blank {
  opacity: 1;
  pointer-events: auto;
  padding: 3px 20px 6px 16px;
  opacity: 0;
  pointer-events: none;
}
.sidebar .nav-links li:hover .sub-menu.blank {
  top: 50%;
  transform: translateY(-50%);
}
.sidebar .profile-details {
  position: fixed;
  bottom: 0;
  width: 260px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #1d1b31;
  padding: 12px 0;
  transition: all 0.5s ease;
}
.sidebar.close .profile-details {
  background: none;
}
.sidebar.close .profile-details {
  width: 78px;
}
.sidebar .profile-details .profile-content {
  display: flex;
  align-items: center;
}
.sidebar .profile-details img {
  height: 52px;
  width: 52px;
  object-fit: cover;
  border-radius: 16px;
  margin: 0 14px 0 12px;
  background: #1d1b31;
  transition: all 0.5s ease;
}
.sidebar.close .profile-details img {
  padding: 10px;
}
.sidebar .profile-details .profile_name,
.sidebar .profile-details .job {
  color: #fff;
  font-size: 18px;
  font-weight: 500;
  white-space: nowrap;
}
.sidebar.close .profile-details i,
.sidebar.close .profile-details .profile_name,
.sidebar.close .profile-details .job {
  display: none;
}
.sidebar .profile-details .job {
  font-size: 12px;
}
.home-section {
  position: relative;
  margin-left: 17rem; /* w-64 (16rem) + left-4 (1rem) = 17rem */
  transition: all 0.5s ease;
  min-height: 100vh;
  width: calc(100% - 17rem);
}
.sidebar.close ~ .home-section {
  margin-left: 5rem; /* w-16 (4rem) + left-4 (1rem) = 5rem */
  width: calc(100% - 5rem);
}

.home-section .home-content {
  height: 60px;
  display: flex;
  align-items: center;
}
.home-section .home-content .bx-menu,
.home-section .home-content .text {
  color: #11101d;
  font-size: 35px;
}
.home-section .home-content .bx-menu {
  margin: 0 15px;
  cursor: pointer;
}
.home-section .home-content .text {
  font-size: 26px;
  font-weight: 600;
}
@media (max-width: 400px) {
  .sidebar.close .nav-links li .sub-menu {
    display: none;
  }
  .sidebar {
    width: 78px;
  }
  .sidebar.close {
    width: 0;
  }
  .home-section {
    left: 78px;
    width: calc(100% - 78px);
    z-index: 100;
  }
  .sidebar.close ~ .home-section {
    width: 100%;
    left: 0;
  }
}

.dot-pulse-cluster-sm {
  animation: dot-pulse-cluster-sm 1.5s infinite;
}
.dot-pulse-cluster-md {
  animation: dot-pulse-cluster-md 1.5s infinite;
}
.dot-pulse-cluster-lg {
  animation: dot-pulse-cluster-lg 1.5s infinite;
}

@keyframes dot-pulse-cluster-md {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 #f97316;
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 8px rgba(249, 115, 22, 0);
  }

  100% {
    transform: scale(0.9);
  }
}
@keyframes dot-pulse-cluster-lg {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 #10b981;
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
  }

  100% {
    transform: scale(0.9);
  }
}
@keyframes dot-pulse-cluster-sm {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 #dc2626;
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 8px rgba(14, 165, 233, 0);
  }

  100% {
    transform: scale(0.9);
  }
}

.dot-pulse-success {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: #4ade80;
  animation: dot-pulse-s 1.5s infinite;
}
.dot-pulse-danger {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: #f50057;
  animation: dot-pulse-d 1.5s infinite;
}
@keyframes dot-pulse-s {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 #4ade80;
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 8px rgba(229, 62, 62, 0);
  }

  100% {
    transform: scale(0.9);
  }
}
@keyframes dot-pulse-d {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 #f50057;
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 8px rgba(229, 62, 62, 0);
  }

  100% {
    transform: scale(0.9);
  }
}

/* MUI TABS STYLES  */
.MuiTab-root.Mui-selected {
  @apply !text-brandColor !font-medium;
}
.MuiTabs-indicator {
  @apply !bg-brandColor !h-[.125rem];
}
.MuiTabs-root {
  @apply !h-[3rem] !min-h-[3rem];
}
.MuiTab-root {
  @apply !text-sm !min-h-0;
}

.MuiStepIcon-root.Mui-completed {
  @apply !text-brandColor;
}
.MuiStepIcon-root.Mui-active {
  @apply !text-brandColor;
}

.Mui-active .MuiStepIcon-text {
  fill: black;
}
@keyframes dashdraw {
  from {
    stroke-dashoffset: 10;
  }
}

.pin1 {
  position: absolute;
  top: 40%;
  left: 50%;
  margin-left: -115px;

  border-radius: 50% 50% 50% 0;
  border: 4px solid #fff;
  width: 20px;
  height: 20px;
  transform: rotate(-45deg);
}

.pin1::after {
  position: absolute;
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  margin-left: -5px;
  margin-top: -5px;
  background-color: #fff;
}
