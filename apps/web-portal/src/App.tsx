import { createTheme, ThemeProvider } from "@mui/material";
import { MSP_ROLES, OPERATOR_ROLES } from "@utils/utilities";
import React, { lazy, Suspense, useEffect, useMemo, useState } from "react";
import { Navigate, Route, Routes, useLocation } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import colors from "tailwindcss/colors";
import defaultRoutes from "./Routes/DefaultRoutes";
import geoRoutes from "./Routes/GeoRoutes";

import { isInPublicSharedLink } from "@api/_helpers";
import { userActions } from "@frontend/shared/store/userSlice";
import { loadWhiteLabel } from "@hooks/whiteLabel/loadWhiteLabel";
import "chartjs-adapter-date-fns";
import { BadgeCheck, CircleAlert } from "lucide-react";
import { usePostHog } from "posthog-js/react";
import LogoLoader from "./components/LogoLoader";
import useDarkMode, { DarkModeContext } from "./hooks/useDarkMode";
import useScrollToTop from "./hooks/useScrollToTop";
import AdminLayout from "./layouts/Admin";
import { clearProductFromLocalStorage } from "./pages/ProductPage/AddProductSection/AddProductProvider";
import { useAppDispatch, useAppSelector } from "./store";

const AreaDetailsDashboard = lazy(() => import("./pages/site/AreaDetailsDashboard"));

const PublicLocationScreen = lazy(
  () => import("./pages/UserTypes/Tracking/HomeSection/PublicLocationScreen")
);
const PublicDashboardScreen = lazy(
  () => import("./pages/UserTypes/Tracking/HomeSection/PublicDashboardScreen")
);

const NotFoundPage = lazy(() => import("./pages/404"));

const LoginPage = lazy(() => import("./pages/LoginPage"));

const AcknowledgeNotification = lazy(() => import("./pages/Extras/AcknowledgeNotification"));
const DeleteAppAccount = lazy(() => import("./pages/Extras/DeleteAppAccount"));
const PrintDashboardPage = lazy(() => import("./pages/PrintDashboardPage"));

function RouteTracker() {
  const location = useLocation();
  const posthog = usePostHog();

  React.useEffect(() => {
    // Capture page view on route change
    posthog.capture("$pageview", {
      path: location.pathname,
      search: location.search,
      url: window.location.href
    });

    return () => {
      posthog.capture("$pageleave", {
        url: window.location.href,
        path: location.pathname
      });
    };
  }, [location]);

  return null;
}

const MemorizedRouteTracker = React.memo(RouteTracker);

// window.addEventListener("beforeunload", (ev) => {
//   ev.preventDefault();

//   return (ev.returnValue = "Are you sure you want to close?");
// });
clearProductFromLocalStorage();

// window.addEventListener("load", () => {
//   clearProductFromLocalStorage();
// });

const PageLoader = () => {
  return (
    <div className="center h-screen">
      <LogoLoader />
    </div>
  );
};

/**
 * @type {React.FC}
 */

function App() {
  useScrollToTop();
  const user = useAppSelector((state) => state.user.user);
  const tenant = useAppSelector((state) => state.user.tenant);
  const loading = useAppSelector((state) => state.user.loading);
  const [isWhiteLabelLoading, setIsWhiteLabelLoading] = useState(true);

  const userEmail = user?.email;

  const poasthog = usePostHog();

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (isInPublicSharedLink()) {
      dispatch(userActions.stopLoadingUserDetails({}));
      return;
    }
    // dispatch(getUserData());
  }, []);

  useEffect(() => {
    if (user) {
      poasthog.identify(user.email, {
        name: user.name,
        email: user.email,
        tenant: user.tenant,
        role: user.role
      });
    }
    return () => {};
  }, [userEmail]);

  useEffect(() => {
    const loadwhite = async () => {
      await loadWhiteLabel({ userLoggedIn: !!user });
      setIsWhiteLabelLoading(false);
    };
    loadwhite();
  }, [userEmail]);

  const [darkMode, setDarkMode] = useDarkMode();

  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: darkMode ? "dark" : "light",
          ...(darkMode
            ? {
                background: {
                  default: colors.gray[800],
                  paper: colors.gray[800]
                }
              }
            : {})
        }
      }),
    [darkMode]
  );

  if (loading || isWhiteLabelLoading) {
    return <PageLoader />;
  }

  return (
    <DarkModeContext.Provider value={[darkMode, setDarkMode]}>
      <ThemeProvider theme={theme}>
        <div className="App">
          <MemorizedRouteTracker />
          <Routes>
            <Route
              path="/login"
              element={
                user ? (
                  <Navigate replace to="/" />
                ) : (
                  <Suspense fallback={<PageLoader />}>
                    <LoginPage />
                  </Suspense>
                )
              }
            />
            <Route
              path="/ack/:ackId"
              element={
                <Suspense fallback={<PageLoader />}>
                  <AcknowledgeNotification />
                </Suspense>
              }
            />
            <Route
              path="/delete-app-account"
              element={
                <Suspense fallback={<PageLoader />}>
                  <DeleteAppAccount />
                </Suspense>
              }
            />
            <Route
              path="/location/:partner/:locationKey"
              element={
                <Suspense fallback={<PageLoader />}>
                  <PublicLocationScreen />
                </Suspense>
              }
            />

            <Route path="/shared/:tenant/:encodedId">
              <Route
                index
                element={
                  <Suspense fallback={<PageLoader />}>
                    <PublicDashboardScreen />
                  </Suspense>
                }
              />

              <Route
                path=":siteName/:areaName"
                element={
                  <Suspense fallback={<PageLoader />}>
                    <main className="p-4">
                      <AreaDetailsDashboard />
                    </main>
                  </Suspense>
                }
              />
            </Route>

            <Route
              path="print/:dashboardId/:duration"
              element={
                <Suspense fallback={<PageLoader />}>
                  <PrintDashboardPage />
                </Suspense>
              }
            />

            <Route path="/" element={!user ? <Navigate replace to="/login" /> : <AdminLayout />}>
              {[...OPERATOR_ROLES, ...MSP_ROLES].includes(user?.role || "")
                ? defaultRoutes(user?.role || "")
                : tenant?.featureType === "geo"
                  ? geoRoutes(tenant?.subFeature)
                  : defaultRoutes(user?.role || "")}
              <Route path="*" element={<NotFoundPage />} />
            </Route>
          </Routes>
        </div>
      </ThemeProvider>

      <ToastContainer
        theme={darkMode ? "dark" : "light"}
        style={{ zIndex: 100001 }}
        icon={({ type }) => {
          switch (type) {
            case "error":
              return <CircleAlert className="stroke-red-400 " />;
            case "success":
              return <BadgeCheck className="stroke-green-400  " />;
            default:
              return null;
          }
        }}
      />
    </DarkModeContext.Provider>
  );
}

export default App;
