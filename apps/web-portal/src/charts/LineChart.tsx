import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xis } from "recharts";
import {
  <PERSON><PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "../components/shadcn/components/chart";

function LineChart({ data }: any) {
  const name = data.name;
  const chartPlotData = data.chartPlotData;
  const chartConfig = data.chartConfig;
  const legends = data.legends;
  const width = data.width || "100%";
  const height = data.height || "100%";
  const containerStyle = {
    width: typeof width === "string" ? width : `${width}px`,
    height: typeof height === "string" ? height : `${height}px`
  };
  return (
    <div style={containerStyle}>
      <ChartContainer config={chartConfig} className="h-full w-full">
        <AreaChart data={chartPlotData} margin={{ top: 6, right: 0, left: 0, bottom: 1 }}>
          <defs>
            {name.map((seriesName: string) => {
              const color = chartConfig[seriesName]?.color || "#8884d8";

              return (
                <linearGradient
                  key={seriesName}
                  id={`gradient-${seriesName}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="0%" stopColor={color} stopOpacity={0.5} />
                  <stop offset="40%" stopColor={color} stopOpacity={0.2} />
                  <stop offset="100%" stopColor={color} stopOpacity={0} />
                </linearGradient>
              );
            })}
          </defs>
          <XAxis dataKey="label" hide axisLine={true} tickLine={true} tickCount={4} />
          <YAxis hide axisLine={true} tickLine={true} />
          <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
          {name.map((seriesName: string) => {
            const color = chartConfig[seriesName]?.color || "#8884d8";
            return (
              <Area
                key={seriesName}
                type="monotone"
                dataKey={seriesName}
                stroke={color || "#8884d8"}
                strokeWidth={2}
                fill={`url(#gradient-${seriesName})`}
                fillOpacity={1}
              />
            );
          })}
          {/* @ts-expect-error - TS doesn't know this exists */}
          {legends ? <ChartLegend content={<ChartLegendContent />} /> : null}
        </AreaChart>
      </ChartContainer>
    </div>
  );
}

export default LineChart;
