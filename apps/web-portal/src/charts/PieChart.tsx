import { <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import {
  <PERSON><PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartConfig
} from "../components/shadcn/components/chart";
import { COLOR_SCHEMES } from "@src/pages/MonitorPage/utils";

const PALETTE_MAP = { 1: "default", 2: "sapphire", 3: "ruby", 4: "emerald" } as const;

function PieChart({
  series,
  labels,
  className,
  type = "pie",
  width = 210,
  height = 210,
  palette = 1
}: {
  series: number[];
  labels: string[];
  className?: string;
  type: any;
  options?: any;
  width?: number | string;
  height?: number | string;
  palette?: number;
}) {
  // Limit to 9 items, combine rest into "others"
  const newSeries =
    series.length > 9
      ? [...series.slice(0, 9), series.slice(9).reduce((acc, current) => acc + current, 0)]
      : series;
  const newLabels = labels.length > 9 ? [...labels.slice(0, 9), "others"] : labels;

  // Get the color scheme based on palette number
  const colorScheme = PALETTE_MAP[palette as keyof typeof PALETTE_MAP] || "default";
  const colors = COLOR_SCHEMES[colorScheme];

  // Transform data for Recharts
  const chartData = newSeries.map((value, index) => ({
    name: newLabels[index],
    value: value,
    fill: colors[index % colors.length]
  }));

  // Create chart config for shadcn
  const chartConfig: ChartConfig = {};
  newLabels.forEach((label, index) => {
    chartConfig[label] = { label: label, color: colors[index % colors.length] };
  });

  const containerStyle = {
    width: typeof width === "string" ? width : `${width}px`,
    height: typeof height === "string" ? height : `${height}px`
  };

  const isDonut = type === "donut";

  return (
    <div className={className} style={containerStyle}>
      <ChartContainer config={chartConfig}>
        <RechartsPieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={isDonut ? "60%" : 0}
            outerRadius="100%"
            paddingAngle={0}
            dataKey="value"
            stroke="rgba(255, 255, 255, 0.5)"
            strokeWidth={1}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Pie>
          <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
        </RechartsPieChart>
      </ChartContainer>
    </div>
  );
}

export default PieChart;
