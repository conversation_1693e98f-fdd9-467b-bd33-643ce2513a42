import { PRODUCT_VAR, THING_VAR } from "@utils/featureLabels";
import { ALL_ROLES, MSP_ROLES, OPERATOR_ROLES, TENANT_ROLES } from "@utils/utilities";
import {
  BrainCircuit,
  ClipboardList,
  Factory,
  History,
  LocateFixed,
  Route,
  UserPen,
  UserRound,
  Activity,
  Airplay,
  AlertTriangle,
  Archive,
  Bell,
  Cpu,
  Database,
  DownloadCloud,
  Folder,
  Layout,
  Settings,
  Shield,
  Sliders,
  Wrench,
  User,
  Users,
  UserRoundCog
} from "lucide-react";
export const DEFAULT_SIDEBAR_OPTIONS = [
  {
    title: "Home",
    to: "/",
    Icon: Layout,
    availableRoles: ALL_ROLES
  },
  {
    title: `${THING_VAR} Management`,
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: `${PRODUCT_VAR}s`,
    to: "/products",
    Icon: Archive,
    availableRoles: TENANT_ROLES,
    featureName: "product"
  },

  // {
  //   title: "Gateways",
  //   to: "/Gateway",
  //   Icon: LogIn,
  //   availableRoles: TENANT_ROLES
  // },
  {
    title: `${THING_VAR}s`,
    to: "/things",
    Icon: Cpu,
    availableRoles: TENANT_ROLES,
    featureName: "thing"
  },
  {
    title: `Tracking`,
    Icon: Route,
    children: [
      {
        title: "Geofences",
        to: "/geofences",
        featureName: "geofence"
      },
      {
        title: "Drivers",
        to: "/drivers",
        featureName: "driver"

        // shouldShow: (subFeature) => subFeature === "fleetManagement" || subFeature === "studentSafety"
      },
      {
        title: "History",
        to: "/history",
        featureName: "geoHistory"
      },

      {
        title: "Routes",
        to: "/routes",

        featureName: "route"
        // shouldShow: (subFeature) => subFeature === "fleetManagement" || subFeature === "studentSafety"
      }
    ],
    availableRoles: TENANT_ROLES
  },

  {
    title: `${THING_VAR}s Inventory`,
    to: "/inventory",
    Icon: ClipboardList,
    availableRoles: TENANT_ROLES,
    featureName: "inventory"
  },
  {
    title: `${THING_VAR} Groups`,
    Icon: Database,
    children: [
      { title: "Dynamic Groups", to: "/thingGroup/dynamic", featureName: "thingGroup" },
      { title: "Static Groups", to: "/thingGroup/static", featureName: "thingGroup" }
    ],
    availableRoles: TENANT_ROLES
  },

  {
    title: `Security`,
    Icon: Shield,
    children: [
      { title: "Root Certificate", to: "/security/certificateAuth", featureName: "tls" },
      { title: `${THING_VAR} Certificate`, to: "/security/certificate", featureName: "tls" },
      { title: "Policy Template", to: "/security/template", featureName: "policy" },
      { title: `${THING_VAR} Policy`, to: "/security/policy", featureName: "policy" },
      { title: "Authentication", to: "/security/authentication", featureName: "authentication" },
      { title: "Protocol Services", to: "/security/protocol_Services", featureName: "listners" }
    ],
    availableRoles: TENANT_ROLES
  },
  {
    title: "Operations",
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Jobs",
    children: [
      { title: "OTA Releases", to: "/jobs/ota", featureName: "ota" },
      { title: `Bulk ${THING_VAR}`, to: "/jobs/bulk-things", featureName: "thing" },

      {
        title: `Bulk Inventory ${THING_VAR}`,
        to: "/jobs/inventory-bulk-things",
        featureName: "inventory"
      },
      {
        title: "Reports",
        to: "/jobs/reports",
        featureName: "reports"
      }
    ],
    Icon: DownloadCloud,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Actions",
    Icon: Wrench,
    children: [
      { title: "Rules", to: "/actions/rules", featureName: "rules" },
      { title: "Workflow", to: "/actions/workflow", featureName: "rules" },

      { title: "Destinations", to: "/actions/destinations", featureName: "destinations" },
      { title: "Credentials", to: "/actions/integrations", featureName: "destinations" }
    ],
    availableRoles: TENANT_ROLES
  },
  {
    title: "Monitor",
    Icon: Airplay,
    children: [
      { title: "Dashboard", to: "/monitor/dashboard", featureName: "dashboard" },
      { title: "Logs", to: "/monitor/logs", featureName: "dashboard" }
    ],
    availableRoles: TENANT_ROLES
  },
  {
    title: "Process IQ",
    Icon: Factory,
    availableRoles: TENANT_ROLES,
    featureName: "sites",
    children: [
      { title: "Sites", to: "/processIQ/sites", featureName: "sites" },
      { title: "Assets", to: "/processIQ/assets", featureName: "sites" }
    ]
  },
  {
    title: "Advance",
    Icon: BrainCircuit,
    availableRoles: TENANT_ROLES,
    // featureName: "site",
    children: [
      { title: "AI Agent", to: "/advance/ai-agent" },
      { title: "Digital Twin", to: "/advance/digital-twin", featureName: "sites" }
    ]
  },

  {
    title: "Notifications",
    Icon: Bell,
    children: [
      { title: "Schedules", to: "/notifications/schedules", featureName: "contacts" },
      { title: "Contacts", to: "/notifications/contacts", featureName: "contacts" },
      { title: "Escalations", to: "/notifications/escalations", featureName: "escalations" },
      { title: "Templates", to: "/notifications/templates", featureName: "notificationTemplates" },
      { title: "History", to: "/notifications/history", featureName: "notificationHistory" },
      { title: "Status", to: "/notifications/status", featureName: "notificationHistory" }
    ],
    availableRoles: TENANT_ROLES
  },

  {
    title: "Incidents",
    Icon: AlertTriangle,
    children: [
      { title: "Category", to: "/incidents/category", featureName: "incidents" },
      { title: "Tickets", to: "/incidents/tickets", featureName: "incidents" }
    ],
    availableRoles: TENANT_ROLES
  },
  {
    title: "Administration",
    isCategory: true,
    availableRoles: TENANT_ROLES
  },
  {
    title: "Account Settings",
    Icon: Settings,
    children: [
      {
        title: "White Labeling",
        to: "/settings/white-labeling",
        featureName: "whiteLabel"
      },
      {
        title: "Home Customization",
        to: "/settings/home-view",
        availableRoles: ["TenantSuperUser"]
        // featureName: "whiteLabel"
      }
    ],
    availableRoles: TENANT_ROLES
  },
  {
    title: "App Users",
    Icon: Users,
    availableRoles: TENANT_ROLES,
    children: [
      {
        title: "App Users",
        to: "/app-users",
        featureName: "appUser"
      }
    ]
  },

  // USER MANAGEMENT FOR OPERATOR VIEW
  {
    title: "User Management",
    Icon: User,
    children: [
      {
        title: "All Users",
        to: "/UM/users"
      },
      {
        title: "Operator Users",
        to: "/UM/opUsers"
      },
      {
        title: "User Groups",
        to: "/UM/userGroups"
      },
      {
        title: "Platform Groups",
        to: "/UM/platformGroups"
      }
    ],
    availableRoles: OPERATOR_ROLES
  },
  // USER MANAGEMENT FOR MSP VIEW

  {
    title: "User Management",
    Icon: User,
    children: [
      {
        title: "MSP Users",
        to: "/UM/mspUsers",
        featureName: "mspUSers"
      },
      {
        title: "User Groups",
        to: "/UM/userGroups",
        featureName: "mspUSers"
      },
      {
        title: "API Keys",
        to: "/UM/apiKeys",
        featureName: "mspKey"
      }
    ],
    availableRoles: MSP_ROLES
  },

  // USER MANAGEMENT FOR Tenant VIEW

  {
    title: "User Management",
    Icon: User,
    children: [
      {
        title: "Users",
        to: "/UM/users",
        featureName: "tntUsers"
      },
      {
        title: "User Groups",
        to: "/UM/userGroups",
        featureName: "tntUsers"
      },
      {
        title: "API Keys",
        to: "/UM/apiKeys",
        featureName: "tntKey"
      }
    ],
    availableRoles: TENANT_ROLES
  },
  {
    title: "MSP Management",
    Icon: UserRoundCog,
    children: [
      {
        title: "MSPs",
        to: "/mspManagement/msps"
      },
      {
        title: "MSP Users",
        to: "/mspManagement/mspUsers"
      }
    ],
    availableRoles: OPERATOR_ROLES
  },

  // Tenant MANAGEMENT FOR OPERATOR VIEW

  {
    title: "Tenant Management",
    Icon: Users,
    children: [
      {
        title: "Tenants",
        to: "/TM/tenants"
      },
      {
        title: "Tenant Users",
        to: "/TM/tenantUsers"
      }
    ],
    availableRoles: OPERATOR_ROLES
  },
  // Tenants FOR MSP VIEW

  // {
  //   title: "Tenants",
  //   Icon: Users,
  //   to: "/TM/tenants",
  //   availableRoles: MSP_ROLES
  // },
  {
    title: "Tenant Management",
    Icon: Users,
    children: [
      {
        title: "Tenants",
        to: "/TM/tenants"
      },
      {
        title: "Tenant Users",
        to: "/TM/tenantUsers"
      }
    ],
    availableRoles: MSP_ROLES
  },
  {
    title: "Tenants Access",
    Icon: UserPen,
    to: "/tenants-access",
    availableRoles: MSP_ROLES
  },

  {
    title: "Tenants Health",
    to: "/tenant-health",
    Icon: Activity,
    availableRoles: OPERATOR_ROLES
  },

  {
    title: "Usage",
    to: "/usage",
    Icon: Sliders,
    availableRoles: TENANT_ROLES
    // featureName: "stats"
  },
  {
    title: "Templates",
    Icon: Folder,
    children: [
      { title: "Products", to: "/templates/product-templates" },
      { title: "Simulator", to: "/templates/simulator-templates" }
      // { title: "White Labeling", to: "/templates/white-labeling" }
    ],
    availableRoles: [...OPERATOR_ROLES, ...MSP_ROLES]
  }
];
