import useSidebarOptions from "@hooks/useSidebarOptions";
import NoAccess from "../../assets/images/no-access.svg";
import { useLocation, useParams, useSearchParams } from "react-router-dom";
import { memo } from "react";
import { useAppSelector } from "@src/store";
import { Card } from "@components/ui";

const NoPermissionsModal = () => {
  const availableRoutes = useSidebarOptions();
  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();
  const is404 = searchParams.get("notfound");
  const user = useAppSelector((state) => state.user.user);

  const { partnerName, featureType } = useParams();

  const unRestrictedRoutes = [
    "/",
    "/profile",
    "/organization",
    `/TM/tenants/${featureType}/${partnerName}/`,
    `/TM/tenants/${featureType}/${partnerName}`
  ];

  if (is404 || !user) {
    return null;
  }

  const hasAccess = availableRoutes.find((item) => {
    if (item.to === "/") {
      return false;
    }
    if (item.to) {
      return pathname.includes(item.to);
    }
    if (item.children) {
      return item.children.find((child) => {
        if (child.to) {
          return pathname.includes(child.to);
        }
        return false;
      });
    }

    return false;
  });

  if (unRestrictedRoutes.includes(pathname)) {
    return null;
  }

  if (!hasAccess) {
    return (
      <div
        className="fixed top-0 left-0 bottom-0 right-0 z-[50] flex items-center justify-center"
        style={{
          backdropFilter: "blur(10px)",
          backgroundColor: "rgba(0, 0, 0, 0.5)"
        }}
      >
        <Card className="p-12 flex flex-col items-center gap-8">
          <h2 className=" text-2xl">Access to this feature is restricted</h2>
          <img src={NoAccess} alt="no access" className="w-80 h-auto" />

          <p className="content-base text-gray-700 dark:text-gray-300 max-w-[20rem] text-center font-semibold">
            Access to this feature is restricted. Please contact support for more information.
          </p>
        </Card>
      </div>
    );
  }

  return null;
};

export default memo(NoPermissionsModal);
