import useTenantDetails from "@hooks/classic/useTenantDetails";
import { getUserType } from "@hooks/classic/useUserGroupPermissions";
import { useAppSelector } from "@src/store";
import NoAccess from "../../assets/images/no-access.svg";
import { Card } from "@components/ui";

const NoAccessModal = () => {
  const { data } = useTenantDetails();
  const role = useAppSelector((state) => state.user.user?.role);
  const accountType = getUserType(role);

  if (
    (accountType === "msp" && data?.mspAccess === "none") ||
    (accountType === "operator" && data?.operatorAccess === "none")
  ) {
    return (
      <div
        className="fixed top-0 left-0 bottom-0 right-0 z-10 flex items-center justify-center"
        style={{
          backdropFilter: "blur(10px)",
          backgroundColor: "rgba(0, 0, 0, 0.5)"
        }}
      >
        <Card className="p-12 flex flex-col items-center gap-8">
          <h2 className=" text-2xl">You don't have access to this account</h2>
          <img src={NoAccess} alt="no access" className="w-80 h-auto" />

          <p className="content-base text-gray-700 dark:text-gray-300 max-w-[20rem] text-center font-semibold">
            Access to this account is restricted. Please contact tetant support for more
            information.
          </p>
        </Card>
      </div>
    );
  }

  return null;
};

export default NoAccessModal;
