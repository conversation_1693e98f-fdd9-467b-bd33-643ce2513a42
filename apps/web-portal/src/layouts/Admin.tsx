import { Suspense, useEffect, useState } from "react";
import clsx from "clsx";
import { matchRoutes, Outlet, useLocation, useParams } from "react-router-dom";
import Button from "../components/Button";
import NotificationDrawer from "../components/NotificationDrawer";
import Header from "../components/Header";
import routes from "../sidebarRoutes";
import { setupPartner } from "../api/user";
import { showErrorToast, showSuccessToast } from "../utils";
import { Card } from "@components/ui";
import UserSetupStepper from "../pages/UserSetupStepper";
import { CircularProgress } from "@mui/material";
import { useAppDispatch, useAppSelector } from "../store";
import { ErrorBoundary } from "react-error-boundary";
import Fallback from "../pages/ErrorPage";
import SidebarSection from "./SidebarSection";
import { UserWithoutTenant } from "@utils/utilities";
import { useQuery } from "@tanstack/react-query";
import { fetchPartner } from "@api/partner";
import { userActions } from "@frontend/shared/store/userSlice";
import NoAccessModal from "./NoAccessModal";
import { UserSetupFeatures } from "@src/pages/UserSetupFeatures";
import { Separator } from "@components/shadcn/components/separator";
import UserSetupProgressbar from "@src/pages/UserSetupProgressbar";
import useUserSetupStatus from "@hooks/tenant/useUserSetupStatus";
import TenantSetupFailedScreen from "./TenantSetupFailedScreen";
import NoPermissionsModal from "./NoPermissionsModal";

const PageLoader = () => {
  return (
    <div className="center h-screen">
      <CircularProgress />
    </div>
  );
};

/**
 *
 * @type {React.FC<{admin:any}>}
 */
function AdminLayout() {
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [showNotificationPanel, setShowNotificationPanel] = useState(false);
  const [userInProgress, setUserInProgress] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);

  const location = useLocation();
  const dispatch = useAppDispatch();
  const route = matchRoutes(routes, location)?.[0]?.route;
  const user = useAppSelector((state) => state.user.user);
  const tenant = useAppSelector((state) => state.user.tenant);

  const { partnerName, featureType } = useParams();
  const { data: tenantDetailData } = useQuery({
    queryKey: [`mspView-tenant-details-${partnerName}`, partnerName],
    queryFn: fetchPartner,
    enabled: !!partnerName && !!featureType
  });
  useEffect(() => {
    if (!!partnerName && !!featureType && tenantDetailData) {
      dispatch(userActions.addTenantDetails(tenantDetailData.data));
    }
  }, [tenantDetailData]);

  const checkDynamicLayout = () => {
    const path = useLocation().pathname;

    if (path.includes("/digitaltwin")) return "pt-0 px-6";

    if (tenant?.featureType === "geo" || featureType === "geo") {
      if (
        path === "/" ||
        path.endsWith(`/tenants/geo/${partnerName}`) ||
        path.endsWith(`/tenants/geo/${partnerName}/`)
      ) {
        return "p-3";
      }

      if (path.includes("/products") || path.includes("/things")) {
        return "p-3";
      }

      if (path.includes("/geofences")) {
        return "p-3";
      }
      if (path.includes("/history")) {
        return "p-3";
      }
      return "p-4";
    }
    return "p-4";
  };

  const {
    data: userSetupStatus,
    isLoading,
    refetch: userSetupStatusRefetch
  } = useUserSetupStatus({ enabled: Boolean(tenant?.setupStatus === "IN_PROGRESS") });

  useEffect(() => {
    if (tenant?.setupStatus === "IN_PROGRESS" && !isLoading) {
      userSetupStatusRefetch();
      const timer = setInterval(() => {
        if (userSetupStatus.completionPercentage < 100 && !isLoading) {
          userSetupStatusRefetch();
        } else {
          clearInterval(timer);
        }
      }, 15000);
      return () => {
        clearInterval(timer);
      };
    }
  }, []);

  const dashboardReload = () => {
    window.location.reload();
  };

  const setupPartnerHandler = async () => {
    setLoadingBtn(true);
    const resp = await setupPartner();
    setLoadingBtn(false);

    if (resp.status === "Success") {
      showSuccessToast("Tenant is setting up, Please wait it will take a while.");
      setUserInProgress(true);
      userSetupStatusRefetch();
      const timer = setInterval(() => {
        if (userSetupStatus.completionPercentage < 100) {
          userSetupStatusRefetch();
        } else {
          clearInterval(timer);
        }
      }, 15000);
    } else {
      showErrorToast("Error setting up User");
    }
  };
  return (
    <div className="admin-layout flex">
      <SidebarSection />
      <div
        className={clsx(
          "flex flex-col   home-section",
          !(tenant?.setupStatus === "COMPLETED" || UserWithoutTenant.includes(user?.role)) &&
            "!w-full !ml-0"
        )}
      >
        <Header
          route={route}
          onShowHelpModal={() => setShowHelpModal(true)}
          onShowNotificationPanel={() => setShowNotificationPanel(true)}
        />

        <div className={clsx(" h-full w-full", checkDynamicLayout())}>
          {tenant?.setupStatus === "COMPLETED" || UserWithoutTenant.includes(user?.role) ? (
            <Suspense fallback={<PageLoader />}>
              <ErrorBoundary FallbackComponent={Fallback} resetKeys={[location.pathname]}>
                <Outlet />
              </ErrorBoundary>
            </Suspense>
          ) : tenant?.setupStatus === "FAILED" ? (
            <TenantSetupFailedScreen />
          ) : (
            <section className="center h-full flex-col  gap-8">
              <Card className="  pb-8 w-[60rem] text-center space-y-8">
                <div className="text-center space-y-2">
                  <h1 className="text-3xl font-bold tracking-tight">
                    {userSetupStatus?.completionPercentage === 100
                      ? "Setup Complete!"
                      : " Account Setup"}
                  </h1>
                  <p className="text-muted-foreground">
                    {userSetupStatus?.completionPercentage === 100
                      ? "Welcome! You're all set up now."
                      : "Complete your account setup to get started"}
                  </p>
                </div>
                {tenant?.setupStatus === "PENDING" && !userInProgress && (
                  <UserSetupStepper
                    loadingBtn={loadingBtn}
                    setupPartnerHandler={setupPartnerHandler}
                  />
                )}
                {(tenant?.setupStatus === "IN_PROGRESS" || userInProgress) && (
                  <UserSetupProgressbar progress={userSetupStatus.completionPercentage} />
                )}

                {userSetupStatus.completionPercentage === 100 && (
                  <div className=" space-y-2 text-center">
                    <Button onClick={dashboardReload} className="mx-auto">
                      Go to Dashboard
                    </Button>
                  </div>
                )}
                <Separator />
                <UserSetupFeatures />
              </Card>
            </section>
          )}
        </div>
      </div>
      {/* Components to be available on every page */}

      {showNotificationPanel && (
        <NotificationDrawer
          open={showNotificationPanel}
          onClose={() => setShowNotificationPanel(false)}
        />
      )}
      <NoAccessModal />
      <NoPermissionsModal />
    </div>
  );
}

export default AdminLayout;
