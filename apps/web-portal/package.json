{"name": "web-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && cp -r assets/* dist/assets/", "lint": "eslint --quiet . && tsc --noEmit", "preview": "vite preview", "cy:run": "cypress run", "generate-types": "dts-bundle-generator  ./src/sharedPortalTypes.ts --out-file ./src/one-types-config/portal.d.ts"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@frontend/shared": "workspace:*", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^5.17.1", "@mui/lab": "5.0.0-alpha.176", "@mui/material": "^7.1.0", "@mui/system": "^7.1.0", "@mui/utils": "^7.1.0", "@mui/x-date-pickers": "^6.20.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@react-google-maps/api": "^2.20.6", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@reduxjs/toolkit": "^1.9.7", "@tailwindcss/vite": "^4.1.7", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/query-sync-storage-persister": "^5.76.1", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-persist-client": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.8", "@textea/json-viewer": "^3.5.0", "@types/leaflet-draw": "^1.0.12", "axios": "^0.27.2", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^1.2.1", "cmdk": "^1.1.1", "compare-versions": "^4.1.4", "copy-to-clipboard": "^3.3.3", "cron-parser": "^4.9.0", "cronstrue": "^2.61.0", "d3-color": "^3.1.0", "d3-interpolate": "^3.0.1", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "dayjs-plugin-utc": "^0.1.2", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^10.18.0", "gsap": "^3.13.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-geosearch": "^4.2.0", "leaflet-rotatedmarker": "^0.2.0", "leaflet.heat": "^0.2.0", "leaflet.markercluster": "^1.5.3", "lodash.isequal": "^4.5.0", "lottie-react": "^2.4.1", "lucide-react": "^0.469.0", "posthog-js": "^1.242.2", "react": "^18.3.1", "react-archer": "^4.4.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-easy-crop": "^5.4.1", "react-error-boundary": "^4.1.2", "react-gauge-component": "^1.2.64", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.56.3", "react-intersection-observer": "^9.16.0", "react-leaflet": "^4.2.1", "react-leaflet-fullscreen": "^4.1.1", "react-leaflet-markercluster": "3.0.0-rc1", "react-liquid-gauge": "^1.2.4", "react-phone-number-input": "^3.4.12", "react-redux": "^8.1.3", "react-resizable": "^3.0.5", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.30.0", "react-slick": "^0.30.3", "react-to-print": "^2.15.1", "react-toastify": "^9.1.3", "reactflow": "^11.11.4", "recharts": "^3.0.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.172.0", "use-react-router-breadcrumbs": "^4.0.1", "vite-plugin-svgr": "^4.3.0", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.26.0", "@tailwindcss/typography": "^0.5.16", "@types/leaflet": "^1.9.17", "@types/lodash.isequal": "^4.5.8", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@types/react-grid-layout": "^1.3.5", "@types/react-resizable": "^3.0.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cypress": "^13.17.0", "dts-bundle-generator": "^9.5.1", "eslint": "^9.26.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "express": "^4.21.2", "globals": "^15.15.0", "husky": "^8.0.3", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "react-scripts": "^5.0.1", "rollup-plugin-visualizer": "^5.14.0", "tailwind-scrollbar": "^1.3.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.2.0"}}