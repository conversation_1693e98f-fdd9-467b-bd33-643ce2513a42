export const POLICY_DATA = {
  name: "cypress-policy-template",
  topic: [
    "cypress/thingName/reported/+/tempSensor",
    "cypress/thingName/desired/#/GasSensor",
    "cypress/thingName/desired/#",
  ],
};

export const PRODUCT = {
  name: "cypress-product",
  description: "product-description",
  metadata: ["idk"],
  notificationTemplate: {
    name: "cypress-notification-template",
    description: "cypress-notification-description",
  },
  productTopics: ["celcious", "fehrenheit", "thingName"],
};

export const MANAGED_PRODUCT = {
  name: "cypress-managed-product",
  description: "cypress-product-description",
  project: "4CH-SwitchBoard",
  notificationTemplate: {
    name: "cypress-m-notification-template",
    description: "cypress-m-notification-description",
  },
};

export const MANAGED_THING = {
  name: "fw-cypress-m-thing",
  meta: {
    users: "<EMAIL>",
    owner: "<EMAIL>",
    thingFriendlyName: "cypress-m-thing",
  },
  newPassword: "S#wdf122d",
};
export const STANDARD_THING = {
  name: "fw-cypress-s-thing",
  meta: {
    users: "<EMAIL>",
    owner: "<EMAIL>",
    thingFriendlyName: "cypress-s-thing",
  },
  newPassword: "S#wdf122d",
};

export const DESTINATION_DATA = {
  mqttName: "test-mqtt",
  webhookName: "test-webhook",
};

export const RULE_DATA = {
  productTopicName: "cypress-test-product-rule",
  thingRuleName: "cypress-test-rule",
};
export const NOTIFICATION_CONTACT = {
  name: "cypress user",
  email: "<EMAIL>",
  phone: "9876543210",
  whatsApp: "9876543210",
  address: "Cypress at open source",
  pin: "700100",
  type: "all",
};

export const NOTIFICATION_ASSETS = {
  thingName: STANDARD_THING.name,
  productName: PRODUCT.name,
  users: [
    { email: NOTIFICATION_CONTACT.email, type: NOTIFICATION_CONTACT.type },
  ],
};
