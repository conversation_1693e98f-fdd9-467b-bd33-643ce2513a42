/// <reference types="cypress" />

import { POLICY_DATA } from "../fixtures/example";

Cypress.Commands.add("login", (email: string, password: string) => {
  cy.visit("/login");
  cy.get('[data-testid="login-email-input"]').clear();
  cy.get('[data-testid="login-email-input"]').type("<EMAIL>");
  cy.get('[data-testid="login-password-input"]').clear();
  cy.get('[data-testid="login-password-input"]').type("Qwertyuiop@123");
  cy.get('[data-testid="login-button"]').click();
});

Cypress.Commands.add("logout", () => {
  cy.get('[data-testid="account-popup"]').click();
  cy.get('[data-testid="logout-button"]').click();
  cy.get(".heading-4").contains("Login to your Account");
});

Cypress.Commands.add("createPolicy", () => {
  cy.get('[data-testid="inputTemplate Name"]').clear();
  cy.get('[data-testid="inputTemplate Name"]').type(POLICY_DATA.name);
  cy.get('[data-testid="inputTopic 1"]').clear();
  cy.get('[data-testid="inputTopic 1"]').type(POLICY_DATA.topic[2]);
  cy.get(
    '[data-testid="dropdown-Permission"] > .dropdown-container > [data-testid="dropdown-opener"]'
  ).click();
  cy.get('[data-testid="allow"]').click();
  cy.get(".dropdown-selected-value > .text-gray-500").click();
  cy.get('[data-testid="publish"]').click();
  cy.get('[data-testid="button-Add Topic"]').click();
  cy.get('[data-testid="inputTopic 1"]').clear();
  cy.get('[data-testid="inputTopic 1"]').type(POLICY_DATA.topic[1]);
  cy.get(
    ':nth-child(1) > .grid > [data-testid="dropdown-Permission"] > .dropdown-container > [data-testid="dropdown-opener"]'
  ).click();
  cy.get('[data-testid="allow"]').click();
  cy.get(".dropdown-selected-value > .text-gray-500").click();
  cy.get('[data-testid="all"]').click();
  cy.get('[data-testid="button-Add Topic"]').click();
  cy.get('[data-testid="inputTopic 1"]').clear();
  cy.get('[data-testid="inputTopic 1"]').type(POLICY_DATA.topic[0]);
  cy.get(
    ':nth-child(1) > .grid > [data-testid="dropdown-Permission"] > .dropdown-container > [data-testid="dropdown-opener"]'
  ).click();
  cy.get('[data-testid="allow"]').click();
  cy.get(".dropdown-selected-value > .text-gray-500").click();
  cy.get('[data-testid="subscribe"]').click();
  cy.get('[data-testid="button-Create"]').click();
});

declare namespace Cypress {
  interface Chainable {
    login(email: string, password: string): Chainable<void>;
    logout(): Chainable<void>;
    createPolicy(): Chainable<void>;
  }
}
