/// <reference types="cypress" />

import { POLICY_DATA, PRODUCT } from "../../fixtures/example";

describe("standard product flow", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create standard product", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Products"] > .ml-3').click();
    cy.get(`[data-testid="button-Add Product"]`).click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="Standard"]').click();
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputProduct Name"]').clear();
    cy.get('[data-testid="inputProduct Name"]').type(PRODUCT.name);
    cy.get('[data-testid="product-description"]').clear();
    cy.get('[data-testid="product-description"]').type(PRODUCT.description);

    cy.get('[data-testid="button-Next"]').click();
    cy.get(
      '[data-testid="dropdown-Data Management"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="timeSeries"]').click();
    cy.get(
      '[data-testid="dropdown-Authentication "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="Username/Password"]').click();
    cy.get(
      '[data-testid="dropdown-Policy Template"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();

    // ALREADY CREATED POLICY
    cy.get(`[data-testid="${POLICY_DATA.name}"]`).click();
    // CREATE POLICY
    // cy.get('[data-testid="new-dropdown-option"]').click();
    // cy.createPolicy();
    // cy.get(
    //   '[data-testid="dropdown-Policy Template"] > .dropdown-container > [data-testid="dropdown-opener"]'
    // ).click();
    // cy.get(`[data-testid="${POLICY_DATA.name}"]`).click();

    cy.get('[data-testid="switch-Create Notification Template"]').click();
    cy.get('[data-testid="switch-Create Product Topics"]').click();
    cy.get('[data-testid="product-add-meta"]').click();
    cy.get('[data-testid="inputKey 1"]').clear();
    cy.get('[data-testid="inputKey 1"]').type(PRODUCT.metadata[0]);
    cy.get('[data-testid="switch-Required"]').click();
    cy.get('[data-testid="button-Next"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="inputkey 1"]').clear();
    cy.get('[data-testid="inputkey 1"]').type(PRODUCT.productTopics[0]);
    cy.get('[data-testid="product-add-payload-topic"]').click();
    cy.get('[data-testid="inputkey 2"]').clear();
    cy.get('[data-testid="inputkey 2"]').type(PRODUCT.productTopics[1]);
    cy.get('[data-testid="product-add-payload-topic"]').click();
    cy.get('[data-testid="inputkey 3"]').clear();
    cy.get('[data-testid="inputkey 3"]').type(PRODUCT.productTopics[2]);
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputNotification Template Name"]').clear();
    cy.get('[data-testid="inputNotification Template Name"]').type(
      PRODUCT.notificationTemplate.name
    );
    cy.get('[data-testid="product-notification-description"]').clear();
    cy.get('[data-testid="product-notification-description"]').type(
      PRODUCT.notificationTemplate.description
    );

    cy.get('[data-testid="switch-Use Preferences"]').click();

    cy.get('[data-testid="switch-Updatable Conditions"]').click();
    cy.get('[data-testid="inputNotification  Time"]').clear();
    cy.get('[data-testid="inputNotification  Time"]').type("4");
    cy.get('[data-testid="inputTitle"]').clear();
    cy.get('[data-testid="inputTitle"]').type("fire");
    cy.get('[data-testid="product-notification-body"]').clear();
    cy.get('[data-testid="product-notification-body"]').type(
      PRODUCT.notificationTemplate.description
    );
    cy.get('[data-testid="button-Submit"]').click();
    cy.get(`[data-testid="${PRODUCT.name}"]`).click();
  });
  it("edit standard product", () => {
    cy.get('[data-testid="sidebar-Products"] > .ml-3').click();
    cy.get(`[data-testid="${PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Actions"]').click();
    cy.get('[data-testid="product-action-edit"]').click();
    cy.get('[data-testid="product-update-description"]').clear();
    cy.get('[data-testid="product-update-description"]').type(`${PRODUCT.description}-edited`);
    cy.get('[data-testid="button-Update"]').click();
    cy.get('[data-testid="product-details-description"]')
      .contains(`${PRODUCT.description}-edited`)
      .should("exist");
  });
  // it("create standard product with root ca ",() => {})
});
