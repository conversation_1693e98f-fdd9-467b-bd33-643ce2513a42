import { MANAGED_PRODUCT, POLICY_DATA } from "../../fixtures/example";

describe("managed product flow", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create managed product", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get('[data-testid="button-Add Product"]').click();
    cy.get('[data-testid="Managed"]').click();
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputProduct Name"]').clear();
    cy.get('[data-testid="inputProduct Name"]').type(MANAGED_PRODUCT.name);
    cy.get('[data-testid="product-description"]').clear();
    cy.get('[data-testid="product-description"]').type(MANAGED_PRODUCT.description);
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="4CH-SwitchBoard"]').click();
    cy.wait(3000);
    cy.get('[data-testid="button-Next"]').click();

    cy.get(
      '[data-testid="dropdown-Data Management"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="shadow"]').click();
    cy.get(
      '[data-testid="dropdown-Data Management"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="timeSeries"]').click();
    cy.get(
      '[data-testid="dropdown-Policy Template"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();

    // ALREADY CREATED POLICY
    // cy.get(`[data-testid="${POLICY_DATA.name}"]`).click();
    // CREATE POLICY
    cy.get('[data-testid="new-dropdown-option"]').click();
    cy.createPolicy();
    cy.get(
      '[data-testid="dropdown-Policy Template"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get(`[data-testid="${POLICY_DATA.name}"]`).click();

    cy.get(
      '[data-testid="dropdown-Authentication "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="Username/Password"]').click();

    cy.get('[data-testid="switch-Create Notification Template"]').click();
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputNotification Template Name"]').clear();
    cy.get('[data-testid="inputNotification Template Name"]').type(
      MANAGED_PRODUCT.notificationTemplate.name
    );
    cy.get('[data-testid="product-notification-description"]').clear();
    cy.get('[data-testid="product-notification-description"]').type(
      MANAGED_PRODUCT.notificationTemplate.description
    );

    cy.get('[data-testid="inputTitle"]').clear();
    cy.get('[data-testid="inputTitle"]').type("title");
    cy.get('[data-testid="product-notification-body"]').clear();
    cy.get('[data-testid="product-notification-body"]').type(
      MANAGED_PRODUCT.notificationTemplate.description
    );
    cy.get('[data-testid="button-Next"]').click();
    cy.wait(3000);
    cy.get('[data-testid="button-Submit"]').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).click();
    /* ==== End Cypress Studio ==== */
  });
  it("edit managed product", () => {
    cy.get('[data-testid="sidebar-Products"] > .ml-3').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Actions"]').click();
    cy.get('[data-testid="product-action-edit"]').click();
    cy.get('[data-testid="product-update-description"]').clear();
    cy.get('[data-testid="product-update-description"]').type(
      `${MANAGED_PRODUCT.description}-edited`
    );
    cy.get('[data-testid="button-Update"]').click();
    cy.get('[data-testid="product-details-description"]')
      .contains(`${MANAGED_PRODUCT.description}-edited`)
      .should("exist");
  });
});
