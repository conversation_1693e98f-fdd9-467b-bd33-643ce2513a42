describe("monitor page tests", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("Dashboard table widget", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"]').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Table"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");

    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="connection status"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();

    cy.get('[data-testid="widget-more-options"]').click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get('.text-lg > .dropdown-container > [data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="logs"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="duration-picker-body"] > .font-medium').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get('[data-testid="widget-more-options"]').click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(":nth-child(1) > .flex-wrap > :nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get(
      ':nth-child(2) > [style="width: 20px;"] > .MuiButtonBase-root > [data-testid="OpenWithIcon"]'
    ).click();
    cy.get(".absolute > .MuiButtonBase-root > svg").click();
    cy.get('[data-testid="widget-more-options"]').click();
    cy.get('[data-testid="clone-widget"]').click();
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="button-Save"]').click();
    cy.get('[data-testid="inputTitle"]').clear();
    cy.get('[data-testid="inputTitle"]').type("test table");
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("test-description");
    cy.get('[data-testid="button-Create"]').click();
    cy.get('[data-testid="EditIcon"]').click();
    cy.get(
      '[data-testid="widget-header-0"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="remove-widget"]').click();
    cy.get('[data-testid="button-Update"]').click();
    cy.get('[data-testid="button-Save"]').click();
    cy.get('[data-testid="EditIcon"]').click();
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("test table");
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(2000);
    cy.get('[data-testid="button-Create Dashboard"]').should("exist");
    /* ==== End Cypress Studio ==== */
  });

  it("Dashboard circular charts", () => {
    cy.get('[data-testid="sidebar-Monitor"]').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Pie"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="connection status"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();

    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="4CH-SwitchBoard"]').click();
    cy.get(".dropdown-selected-value").click();

    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="zoom-out-widget"]').click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="logs"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('.mt-auto > [data-testid="button-Save"]').click();

    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get(
      '[data-testid="widget-inside-0"] > #dashboard-table-container > .flex-col > div.h-full > .m-2 > .right-1 > [data-testid="dropdown-undefined"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="8CH-SwitchBoard"]').click();
    cy.get('[data-testid="button-Save"]').click();
    cy.get('[data-testid="inputTitle"]').clear();
    cy.get('[data-testid="inputTitle"]').type("pie-test");
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("pie");
    cy.get('[data-testid="button-Create"]').click();

    cy.get('[data-testid="button-Edit"]').click();
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("pie-test");
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });

  /* ==== Test Created with Cypress Studio ==== */
  it("bar-chart", function () {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"] > .ml-auto').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get(".\\!rounded-md > .ml-2").click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Bar"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="connection status"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="MoreVertIcon"] > path').click();
    cy.get('[data-testid="zoom-out-widget"]').click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(":nth-child(2) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="logs"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="duration-picker-body"] > .font-medium').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="zoom-in-widget"]').click();
    /* ==== Generated with Cypress Studio ==== */
    cy.get("canvas.dashboard-item-content").click();
    cy.get("canvas.dashboard-item-content").click();
    cy.get("canvas.dashboard-item-content").click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="duration-picker-body"] > div').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="remove-widget"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"]'
    ).click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"] > path'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="time series"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get(".dropdown-selected-value > .text-gray-500").click();
    cy.get('[data-testid="inputSearch..."]').clear();
    cy.get('[data-testid="inputSearch..."]').type("we");
    cy.get('[data-testid="weatherStation"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-3"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="shadow"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get(
      '[data-testid="dropdown-Select Product"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="inputSearch..."]').clear();
    cy.get('[data-testid="inputSearch..."]').type("wea");
    cy.get('[data-testid="weatherStation"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();

    /* ==== End Cypress Studio ==== */
  });
  it("bar-h-chart", function () {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"] > .ml-auto').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get(".\\!rounded-md > .ml-2").click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Bar-H"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="logs"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="duration-picker"]').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      ':nth-child(2) > .w-full > .absolute > .MuiButtonBase-root > [data-testid="RadioButtonUncheckedIcon"]'
    ).click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="MoreVertIcon"] > path').click();
    cy.get('[data-testid="zoom-out-widget"]').click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="connection status"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    /* ==== End Cypress Studio ==== */
  });
  it("line-chart", function () {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"] > .ml-auto').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get(".\\!rounded-md > .ml-2").click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Line"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="logs"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="duration-picker-body"] > .font-medium').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get("canvas.dashboard-item-content").click();
    cy.get('[data-testid="duration-picker-body"]').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="shadow"]').click();
    cy.get(":nth-child(2) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    /* ==== End Cypress Studio ==== */
  });
  it("area-chart", function () {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"] > .ml-auto').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get(".\\!rounded-md > .ml-2").click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Area"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="time series"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get(
      '[data-testid="dropdown-Select Product"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="inputSearch..."]').clear();
    cy.get('[data-testid="inputSearch..."]').type("we");
    cy.get('[data-testid="weatherStation"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="duration-picker-body"]').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get(".h-full > .dashboard-item-content").click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="remove-widget"]').click();
    /* ==== End Cypress Studio ==== */
  });
  it("count-chart", function () {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"] > .ml-auto').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get(".\\!rounded-md > .ml-2").click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Count"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="logs"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get('[data-testid="duration-picker-body"] > .font-medium').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get('[data-testid="MoreVertIcon"]').click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="connection status"]').click();
    cy.get(":nth-child(1) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '.flex-col > [data-testid="dropdown-undefined"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="version"]').click();
    cy.get(
      '[data-testid="dropdown-All version"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="1.0.0"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    cy.get(
      '[data-testid="widget-header-1"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"]'
    ).click();
    cy.get('[data-testid="clone-widget"]').click();
    cy.get(
      '[data-testid="widget-header-3"] > :nth-child(2) > [data-testid="widget-more-options"] > [data-testid="MoreVertIcon"]'
    ).click();
    cy.get('[data-testid="remove-widget"]').click();
    cy.get(
      '[data-testid="widget-header-2"] > :nth-child(2) > [data-testid="widget-more-options"]'
    ).click();
    cy.get('[data-testid="edit-widget"]').click();
    cy.get(
      '[data-testid="dropdown-Select data source"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="time series"]').click();
    cy.get(".py-4 > .flex > :nth-child(1) > .MuiChip-label").click();
    cy.get(".py-4 > .flex > :nth-child(3) > .MuiChip-label").click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    /* ==== End Cypress Studio ==== */
  });
  it("meter-chart", function () {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Monitor"] > .ml-auto').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get(".\\!rounded-md > .ml-2").click();
    cy.get('[data-testid="button-Create Dashboard"]').click();
    cy.get('[data-testid="chart-option-Meter"]').trigger("dragstart");
    cy.get('[data-testid="monitor-widget-drop"]').trigger("drop");
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get(
      '[data-testid="dropdown-Select Product"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="inputSearch..."]').clear();
    cy.get('[data-testid="inputSearch..."]').type("wea");
    cy.get('[data-testid="weatherStation"]').click();
    cy.get('[data-testid="button-Add"]').click();
    cy.get(":nth-child(4) > .flex > :nth-child(2) > .MuiChip-label").click();
    cy.get('[data-testid="button-Add"]').click();
    cy.get('.mt-auto > [data-testid="button-Save"]').click();
    /* ==== End Cypress Studio ==== */
  });
});
