import "../../support/commands";

describe("Logger specs", () => {
  /* ==== Test Created with Cypress Studio ==== */
  it("logger-working", function () {
    cy.login("<EMAIL>", "Qwertyuiop@123");
    cy.get('[data-testid="sidebar-Monitor"]').click();
    cy.get('[data-testid="sidebar-Logs"]').click();
    cy.get('[data-testid="duration-picker"]').click();
    cy.get('[data-testid="commonly-Last 1 year"]').click();
    cy.get('[data-testid="logger-list-item-1"]').click();
    cy.get('[data-testid="close-expanded-json"]').click();
    cy.get('[data-testid="duration-picker"]').click();
    cy.get("input.card").clear();
    cy.get("input.card").type("1");
    cy.get(".grid > .button-md").click();
    cy.get('[data-testid="logger-list-item-1"]').click();
    cy.get('[data-testid="close-expanded-json"]').click();
    cy.get('[data-testid="logger-list-item-4"]').click();
    cy.get('[data-testid="close-expanded-json"]').click();
    cy.get('[data-testid="duration-picker"]').click();

    cy.get('[data-testid="dropdown-filters"]').click();
    cy.get('[data-testid="days"]').click();
    cy.get('[data-testid="duration-quick-select-input"]').clear();
    cy.get('[data-testid="duration-quick-select-input"]').type("30");
    cy.get('[data-testid="button-Apply"]').click();
    cy.get('[data-testid="dropdown-Select logs"]').click();
    cy.get('[data-testid="Platform Logs"]').click();
    cy.get('[data-testid="logger-list-item-0"]').click();
    cy.get('[data-testid="close-expanded-json"]').click();
    /* ==== End Cypress Studio ==== */
  });
});
