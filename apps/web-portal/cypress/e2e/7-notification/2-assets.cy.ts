import { NOTIFICATION_ASSETS, NOTIFICATION_CONTACT } from "../../fixtures/example";

describe("notification assets", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create assets", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Assets"]').click();
    cy.get('[data-testid="button-New Asset"]').click();
    cy.get(
      '[data-testid="dropdown-Notification template"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get(`[data-testid="${NOTIFICATION_ASSETS.productName}"]`).click();
    cy.get(
      '[data-testid="dropdown-Thing name"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();

    cy.get(
      `[data-testid="dropdown-options"] > [data-testid="${NOTIFICATION_ASSETS.thingName}"]`
    ).click();

    cy.get(
      '[data-testid="dropdown-Contact Email"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();

    cy.get(`[data-testid="${NOTIFICATION_ASSETS.users[0].email}"]`).click();
    cy.get(
      '[data-testid="dropdown-Notification Type"] > .dropdown-container > [data-testid="dropdown-opener"] > .flex > .dropdown-selected-value'
    ).click();
    cy.get(`[data-testid="${NOTIFICATION_ASSETS.users[0].type}"]`).click();

    cy.get('[data-testid="button-Create"]').click();
    cy.wait(2000);
    cy.get(`[data-testid="${NOTIFICATION_ASSETS.thingName}"]`).should("exist");
  });
  it("update assets", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Assets"]').click();
    cy.get(`[data-testid="${NOTIFICATION_ASSETS.thingName}"]`).click();
    cy.wait(3000);
    cy.get('[data-testid="button-Update"]').click();
    cy.get(
      '[data-testid="dropdown-Notification Type"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="info"]').click();
    cy.get('.mt-4 > [data-testid="button-Update"]').click();
    cy.get(
      `[data-testid="${NOTIFICATION_ASSETS.users[0].email}-notification-asset-type"]`
    ).contains("info");

    /* ==== End Cypress Studio ==== */
  });
});
