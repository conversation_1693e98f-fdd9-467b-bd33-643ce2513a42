import { NOTIFICATION_CONTACT } from "../../fixtures/example";

describe("notification contact", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create contact", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Contacts"]').click();
    cy.get('[data-testid="button-New Contact"]').click();
    cy.get('[data-testid="inputContact Name"]').clear();
    cy.get('[data-testid="inputContact Name"]').type(NOTIFICATION_CONTACT.name);
    cy.get('[data-testid="inputEmail"]').clear();
    cy.get('[data-testid="inputEmail"]').type(NOTIFICATION_CONTACT.email);
    cy.get('[data-testid="inputPhone Number"]').clear();
    cy.get('[data-testid="inputPhone Number"]').type(NOTIFICATION_CONTACT.phone);
    cy.get('[data-testid="inputWhatsapp Number"]').clear();
    cy.get('[data-testid="inputWhatsapp Number"]').type(NOTIFICATION_CONTACT.whatsApp);
    cy.get('[data-testid="inputAddress"]').clear();
    cy.get('[data-testid="inputAddress"]').type(NOTIFICATION_CONTACT.address);
    cy.get('[data-testid="inputPincode"]').clear();
    cy.get('[data-testid="inputPincode"]').type(NOTIFICATION_CONTACT.pin);
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="all"]').click();

    cy.get('[data-testid="switch-Email"]').click();
    cy.get('[data-testid="switch-Sms"]').click();
    cy.get('[data-testid="switch-Phone"]').click();

    cy.get('[data-testid="button-Create"]').click();
    cy.get(`[data-testid="${NOTIFICATION_CONTACT.email}"]`).should("exist");
  });
  it("update contact", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Contacts"]').click();
    cy.get(`[data-testid="${NOTIFICATION_CONTACT.email}"]`).click();
    cy.wait(1500);
    cy.get('[data-testid="button-Edit"]').click();
    cy.get('[data-testid="inputcontact details"]').type("-edited");
    cy.get('[data-testid="switch-Sms"]').click();
    cy.get('[data-testid="button-Confirm Update"]').click();
    cy.get('[data-testid="contact-details"]').contains(`${NOTIFICATION_CONTACT.name}-edited`);
  });
});
