describe("page-visits", () => {
  /* ==== Test Created with Cypress Studio ==== */
  it("sidebar-pages", function () {
    cy.login("<EMAIL>", "Qwertyuiop@123");
    /* ==== Generated with Cypress Studio ==== */
    cy.get(':nth-child(1) > .iocn-link > [data-testid="sidebar-Dashboard"] > .ml-3').click();
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get('[data-testid="sidebar-Things"]').click();
    cy.get('[data-testid="sidebar-Thing Groups"]').click();
    cy.get('[data-testid="sidebar-Dynamic Groups"]').click();
    cy.get('[data-testid="sidebar-Static Groups"]').click();
    cy.get('[data-testid="sidebar-Inventory"]').click();
    cy.get('[data-testid="sidebar-Monitor"]').click();
    cy.get(':nth-child(2) > .iocn-link > [data-testid="sidebar-Dashboard"]').click();
    cy.get('[data-testid="sidebar-Logs"]').click();
    cy.get('[data-testid="sidebar-Incidents"]').click();
    cy.get('[data-testid="sidebar-User Management"] > .ml-3').click();
    cy.get('[data-testid="sidebar-Users"]').click();
    cy.get('[data-testid="sidebar-User Groups"]').click();
    cy.get('[data-testid="sidebar-API Keys"]').click();
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get('[data-testid="sidebar-Destinations"] > .ml-3').click();
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Templates"]').click();
    cy.get('[data-testid="sidebar-Contacts"]').click();
    cy.get('[data-testid="sidebar-Assets"]').click();
    cy.get('[data-testid="sidebar-OTA Releases"]').click();
    cy.get('[data-testid="sidebar-Security"]').click();
    cy.get('[data-testid="sidebar-Certificate"] > .ml-3').click();
    cy.get('[data-testid="sidebar-Policy Template"] > .ml-3').click();
    cy.get('[data-testid="sidebar-Policy"]').click();
    cy.get('[data-testid="sidebar-CA"]').click();
    cy.get('[data-testid="sidebar-User Auth"]').click();
    cy.get('[data-testid="sidebar-Thing Basic Auth"] > .ml-3').click();
    cy.get('[data-testid="sidebar-Usage"]').click();
    cy.logout();
  });
});
