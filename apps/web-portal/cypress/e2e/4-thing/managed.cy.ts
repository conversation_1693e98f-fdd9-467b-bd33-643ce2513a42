import { MANAGED_PRODUCT, MANAGED_THING } from "../../fixtures/example";

describe("managed thing flow", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create managed thing", () => {
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Manage Things"]').click();
    cy.get('[data-testid="button-Create Device"]').click();
    cy.get('[data-testid="button-Confirm"]').click();
    cy.get('[data-testid="Create single thing"]');
    cy.get('[data-testid="button-Next"]').click();
    cy.wait(2000);
    cy.get('[data-testid="inputThing Name"]').clear();
    cy.get('[data-testid="inputThing Name"]').type(MANAGED_THING.name);

    cy.get('[data-testid="inputusers"]').clear();
    cy.get('[data-testid="inputusers"]').type(MANAGED_THING.meta.users);

    cy.get('[data-testid="inputowner"]').clear();
    cy.get('[data-testid="inputowner"]').type(MANAGED_THING.meta.owner);

    cy.get('[data-testid="inputthingFriendlyName"]').clear();
    cy.get('[data-testid="inputthingFriendlyName"]').type(MANAGED_THING.meta.thingFriendlyName);

    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputPassword"]').clear();
    cy.get('[data-testid="inputPassword"]').type(MANAGED_THING.newPassword);
    cy.get('[data-testid="inputConfirm Password"]').clear();
    cy.get('[data-testid="inputConfirm Password"]').type(MANAGED_THING.newPassword);
    cy.get('[data-testid="button-Submit"]').click();
    cy.get('[data-testid="button-Done"]').click();
    cy.get(`[data-testid="${MANAGED_THING.name}"]`).should("exist");
    /* ==== End Cypress Studio ==== */
  });
  it("edit managed thing", () => {
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Manage Things"]').click();
    cy.get(`[data-testid="${MANAGED_THING.name}"]`).click();

    cy.get(`[data-testid="button-Actions"]`).click();
    cy.get(`[data-testid="Edit"]`).click();
    cy.get(`[data-testid="inputthingFriendlyName"]`).clear();
    cy.get(`[data-testid="inputthingFriendlyName"]`).type(
      `${MANAGED_THING.meta.thingFriendlyName}-edited`
    );
    cy.wait(1000);
    cy.get('[data-testid="button-Confirm Edit"]').click();
    cy.get(`[data-testid="thingFriendlyName"]`).contains(
      `${MANAGED_THING.meta.thingFriendlyName}-edited`
    );
  });
});
