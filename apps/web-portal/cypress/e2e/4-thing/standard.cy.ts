import { PRODUCT, STANDARD_THING } from "../../fixtures/example";

describe("standard thing flow", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create standard thing", () => {
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get(`[data-testid="${PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Manage Things"]').click();
    cy.get('[data-testid="button-Create Device"]').click();
    cy.get('[data-testid="Create single thing"]');
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputThing Name"]').clear();
    cy.get('[data-testid="inputThing Name"]').type(STANDARD_THING.name);
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="button-Next"]').click();
    cy.get('[data-testid="inputPassword"]').clear();
    cy.get('[data-testid="inputPassword"]').type(STANDARD_THING.newPassword);
    cy.get('[data-testid="inputConfirm Password"]').clear();
    cy.get('[data-testid="inputConfirm Password"]').type(STANDARD_THING.newPassword);
    cy.get('[data-testid="button-Submit"]').click();
    cy.get('[data-testid="button-Done"]').click();
    cy.get(`[data-testid="${STANDARD_THING.name}"]`).should("exist");
    /* ==== End Cypress Studio ==== */
  });
  it("edit standard thing", () => {
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get(`[data-testid="${PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Manage Things"]').click();
    cy.get(`[data-testid="${STANDARD_THING.name}"]`).click();
    cy.get('[data-testid="button-Actions"]').click();
    cy.get(`[data-testid="Edit"]`).click();
    cy.get('[data-testid="button-Confirm Edit"]').click();
    cy.wait(1000);
  });
});
