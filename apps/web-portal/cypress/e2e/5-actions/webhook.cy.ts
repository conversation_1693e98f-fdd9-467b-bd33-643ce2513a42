import { DESTINATION_DATA } from "../../fixtures/example";

describe("destination", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });

  it("destination with webhook", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Destinations"]').click();
    cy.get('[data-testid="button-Create Destination"]').click();
    cy.get('[data-testid="radio-WebHook"] > :nth-child(2) > .content').click();
    cy.get('[data-testid="inputDestination Name"]').clear();
    cy.get('[data-testid="inputDestination Name"]').type(DESTINATION_DATA.webhookName);
    cy.get(
      '[data-testid="dropdown-Enabled"] > .dropdown-container > [data-testid="dropdown-opener"] > .flex > .dropdown-selected-value > .text-gray-500'
    ).click();
    cy.get('[data-testid="false"]').click();
    cy.get('[data-testid="inputURL"]').click();
    cy.get('[data-testid="inputURL"]').type("https://platform-dev.firewires.net/");
    cy.get(".dropdown-selected-value > .text-gray-500").click();
    cy.get('[data-testid="post"]').click();
    cy.get(".MuiFab-root > svg").click();
    cy.get(':nth-child(3) > :nth-child(1) > .input-container > [data-testid="inputKey"]').clear();
    cy.get(':nth-child(3) > :nth-child(1) > .input-container > [data-testid="inputKey"]').type(
      "test"
    );
    cy.get('[data-testid="inputValue"]').clear();
    cy.get('[data-testid="inputValue"]').type("test");
    cy.get('[data-testid="button-Create"]').click();
    cy.wait(1000);

    /* ==== End Cypress Studio ==== */
  });
  it("update webhook destination", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Destinations"]').click();

    cy.get(`[data-testid="firewires_${DESTINATION_DATA.webhookName}"]`).click();
    cy.wait(3000);
    cy.get('[data-testid="button-Update"]').click();
    cy.get('[data-testid="inputValue 1"]').clear();
    cy.get('[data-testid="inputValue 1"]').type("test2");
    cy.get('[data-testid="inputURL"]').click();
    cy.get('[data-testid="inputURL"]').clear();
    cy.get('[data-testid="inputURL"]').type("https://platform-dev.firewires.com/");
    cy.get(
      '[data-testid="dropdown-Enabled"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="false"]').click();
    cy.get(':nth-child(1) > [data-testid="button-Update"]').click();
    cy.wait(1000);

    /* ==== End Cypress Studio ==== */
  });
});
