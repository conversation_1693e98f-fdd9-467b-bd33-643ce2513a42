import { DESTINATION_DATA } from "../../fixtures/example";

describe("destination", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create destination with mqtt", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Destinations"] > .ml-3').click();
    cy.get('[data-testid="button-Create Destination"]').click();
    cy.get('[data-testid="radio-MQTT Bridge"] > :nth-child(2) > .content').click();
    cy.get('[data-testid="inputDestination Name"]').clear();
    cy.get('[data-testid="inputDestination Name"]').type(DESTINATION_DATA.mqttName);
    cy.get('[data-testid="inputServer"]').clear();
    cy.get('[data-testid="inputServer"]').type("google");
    cy.get('[data-testid="inputUsername"]').clear();
    cy.get('[data-testid="inputUsername"]').type("cypress");
    cy.get(
      '[data-testid="dropdown-Enabled"] > .dropdown-container > [data-testid="dropdown-opener"] > .flex > .dropdown-selected-value > .text-gray-500'
    ).click();
    cy.get('[data-testid="true"]').click();
    cy.get('[data-testid="inputPassword"]').clear();
    cy.get('[data-testid="inputPassword"]').type("qwertyuiop");
    cy.get('[data-testid="inputConfirm Password"]').clear();
    cy.get('[data-testid="inputConfirm Password"]').type("qwertyuiop");
    cy.get('[data-testid="inputTopic"]').clear();
    cy.get('[data-testid="inputTopic"]').type("test/topic/name");
    cy.get(
      '[data-testid="dropdown-Qos"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="0"]').click();
    cy.get('[data-testid="inputPayload"]').clear();
    cy.get('[data-testid="inputPayload"]').type("testPayload");
    cy.get(
      '[data-testid="dropdown-Retain"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="true"]').click();
    cy.get('[data-testid="button-Create"]').click();

    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });
  it("update mqtt destination", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Destinations"] > .ml-3').click();
    cy.get(`[data-testid="firewires_${DESTINATION_DATA.mqttName}"]`).click();
    cy.wait(3000);
    cy.get('[data-testid="button-Update"]').click();
    cy.get('[data-testid="inputUsername"]').clear();
    cy.get('[data-testid="inputUsername"]').type("cypress-updated");
    cy.get(':nth-child(1) > [data-testid="button-Update"]').click();
    cy.wait(1000);

    /* ==== End Cypress Studio ==== */
  });
});
