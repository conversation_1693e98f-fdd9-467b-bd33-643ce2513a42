import {
  DESTINATION_DATA,
  MANAGED_PRODUCT,
  MANAGED_THING,
  NOTIFICATION_ASSETS,
  NOTIFICATION_CONTACT,
  POLICY_DATA,
  PRODUCT,
  RULE_DATA,
  STANDARD_THING
} from "../../fixtures/example";

describe("delete all data", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("delete product rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get(`[data-testid="${PRODUCT.name}_notification"]`).click();

    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
  });
  it("delete product topic rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get(`[data-testid="${RULE_DATA.productTopicName}"]`).click();
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });
  it("delete thing rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get(`[data-testid="${RULE_DATA.thingRuleName}"]`).click();

    /* ==== Generated with Cypress Studio ==== */
    cy.wait(2000);
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear("C");
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });
  it("delete mqtt destination", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Destinations"] > .ml-3').click();
    cy.get(`[data-testid="firewires_${DESTINATION_DATA.mqttName}"]`).click();
    cy.wait(3000);
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });
  it("delete webhook destination", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Destinations"]').click();

    cy.get(`[data-testid="firewires_${DESTINATION_DATA.webhookName}"]`).click();
    cy.wait(3000);

    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);

    /* ==== End Cypress Studio ==== */
  });

  //   it("delete destinations", () => {});
  it("delete thing standard", () => {
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get(`[data-testid="${PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Manage Things"]').click();
    cy.get(`[data-testid="${STANDARD_THING.name}"]`).click();
    cy.get(`[data-testid="button-Actions"]`).click();
    cy.get(`[data-testid="Delete"]`).click();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(2000);
    cy.get(`[data-testid="${STANDARD_THING.name}"]`).should("not.exist");
  });
  it("delete thing managed", () => {
    cy.get('[data-testid="sidebar-Products"]').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Manage Things"]').click();
    cy.get(`[data-testid="${MANAGED_THING.name}"]`).click();
    cy.get(`[data-testid="button-Actions"]`).click();
    cy.get(`[data-testid="Delete"]`).click();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(2000);
    cy.get(`[data-testid="${MANAGED_THING.name}"]`).should("not.exist");
  });

  it("delete managed thing auth", () => {
    cy.get('[data-testid="sidebar-Security"]').click();
    cy.get('[data-testid="sidebar-Thing Basic Auth"]').click();
    cy.get(`[data-testid="${MANAGED_THING.name}"]`).click();
    cy.wait(1000);
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
    cy.get(`[data-testid="${MANAGED_THING.name}"]`).should("not.exist");
  });
  it("delete standard thing auth", () => {
    cy.get('[data-testid="sidebar-Security"]').click();
    cy.get('[data-testid="sidebar-Thing Basic Auth"]').click();
    cy.get(`[data-testid="${STANDARD_THING.name}"]`).click();
    cy.wait(1000);
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(1000);
    cy.get(`[data-testid="${STANDARD_THING.name}"]`).should("not.exist");
  });

  it("delete product managed", () => {
    cy.get('[data-testid="sidebar-Products"] > .ml-3').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Actions"]').click();
    cy.get('[data-testid="product-action-delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Delete Only Product"]').click();
    cy.wait(2000);
    cy.get(`[data-testid="${MANAGED_PRODUCT.name}"]`).should("not.exist");
  });

  it("delete product standard", () => {
    cy.get('[data-testid="sidebar-Products"] > .ml-3').click();
    cy.get(`[data-testid="${PRODUCT.name}"]`).click();
    cy.get('[data-testid="button-Actions"]').click();
    cy.get('[data-testid="product-action-delete"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Delete Only Product"]').click();
    cy.wait(3000);
    cy.get(`[data-testid="${PRODUCT.name}"]`).should("not.exist");
  });
  it("delete policy", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Security"]').click();
    cy.get('[data-testid="sidebar-Policy Template"]').click();
    cy.get(`[data-testid="${POLICY_DATA.name}"]`).click();

    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(3000);
    cy.get(`[data-testid="${POLICY_DATA.name}"]`).should("not.exist");
  });
  it("delete managed notification template", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Templates"]').click();
    cy.get(`[data-testid="${MANAGED_PRODUCT.notificationTemplate.name}"]`).click();
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(3000);
    cy.get(`[data-testid="${MANAGED_PRODUCT.notificationTemplate.name}"]`).should("not.exist");
  });
  it("delete standard notification template", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Templates"]').click();
    cy.get(`[data-testid="${PRODUCT.notificationTemplate.name}"]`).click();
    cy.get('[data-testid="button-Delete"]').click();
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(3000);
    cy.get(`[data-testid="${PRODUCT.notificationTemplate.name}"]`).should("not.exist");
  });
  it("Delete notification contact", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Contacts"]').click();
    cy.get(`[data-testid="${NOTIFICATION_CONTACT.email}"]`).click();
    cy.get(`[data-testid="button-Delete"]`).click();
    cy.get('[data-testid="inputany"]').type("CONFIRM");
    cy.wait(500);
    cy.get('[data-testid="button-Confirm"]').click();
    cy.wait(2000);
    cy.get(`[data-testid="${NOTIFICATION_CONTACT.email}"]`).should("not.exist");
  });
  it("check notification assets is already deleted", () => {
    cy.get('[data-testid="sidebar-Notifications"]').click();
    cy.get('[data-testid="sidebar-Assets"]').click();
    cy.wait(3000);
    cy.get(`[data-testid="${NOTIFICATION_ASSETS.thingName}"]`).should("not.exist");
  });
  //   it("delete root ca", () => {});
});
