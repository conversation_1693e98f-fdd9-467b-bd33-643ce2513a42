import { DESTINATION_DATA, PRODUCT, RULE_DATA } from "../../fixtures/example";

describe("Rule test for product with topics", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });

  it("create product topic rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get('[data-testid="button-Create Rule"]').click();
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="button-Add Input"]').click();
    cy.get('[data-testid="radio-Product"] > :nth-child(2) > .content-base').click();
    cy.get('[data-testid="inputSearch for products"]').type("cypress");
    cy.wait(4000);
    cy.get(`[data-testid="rule-input-${PRODUCT.name}"]`).click();
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="timeSeries"]').click();
    cy.get('[data-testid="button-Add"]').click();
    cy.get('[data-testid="rule-add-trigger"] > svg').click();
    cy.get(
      '.space-y-2 > .space-y-4 > :nth-child(2) > [data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="celcious"]').click();
    cy.get(
      '.space-y-2 > .space-y-4 > :nth-child(2) > [data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="fehrenheit"]').click();
    cy.get('[data-testid="condition-switch"]').click();
    cy.get('.w-1\\/2 > .input-container > [data-testid="inputName"]').clear("fi");
    cy.get('.w-1\\/2 > .input-container > [data-testid="inputName"]').type("fire");
    cy.get(
      '.gap-2.items-center > [data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="celcious"]').click();
    cy.get(
      '[data-testid="dropdown-condition"] > .dropdown-container > [data-testid="dropdown-opener"] > .flex > .dropdown-selected-value'
    ).click();
    cy.get('[data-testid="greater equal"]').click();
    cy.get('[data-testid="inputenter value"]').clear("5");
    cy.get('[data-testid="inputenter value"]').type("50");
    cy.get('[data-testid="button-Add"]').click();
    cy.get('.MuiList-root > [tabindex="0"]').click();
    cy.get(
      ':nth-child(3) > [data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="fehrenheit"]').click();
    cy.get(
      ':nth-child(3) > [data-testid="dropdown-condition"] > .dropdown-container > [data-testid="dropdown-opener"] > .flex > .dropdown-selected-value'
    ).click();
    cy.get('[data-testid="greater"]').click();
    cy.get(
      ':nth-child(3) > :nth-child(3) > .input-container > [data-testid="inputenter value"]'
    ).clear("1");
    cy.get(
      ':nth-child(3) > :nth-child(3) > .input-container > [data-testid="inputenter value"]'
    ).type("100");
    cy.get('[data-testid="button-Save triggers"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="rule-add-action-plus"]').click();
    cy.get('[data-testid="radio-Webhook"] > :nth-child(2) > .content-base').click();
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get(`[data-testid="firewires_${DESTINATION_DATA.webhookName}"]`).click();
    cy.get('[data-testid="button-Add"]').click();
    cy.get('[data-testid="rule-add-action-plus"]').click();
    cy.get('[data-testid="radio-MQTT Bridge"] > :nth-child(2) > .content-base').click();
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get(`[data-testid="firewires_${DESTINATION_DATA.mqttName}"]`).click();
    cy.get('[data-testid="button-Add"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="button-Save, Rule"]').click();
    cy.get('[data-testid="inputRule Name"]').clear("cy");
    cy.get('[data-testid="inputRule Name"]').type(RULE_DATA.productTopicName);
    cy.get('[data-testid="inputany"]').clear("d");
    cy.get('[data-testid="inputany"]').type("description");
    cy.get('[data-testid="button-Create"]').click();
    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });

  it("update product topic rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get(`[data-testid="${RULE_DATA.productTopicName}"]`).click();
    cy.get('[data-testid="button-Edit"]').click();
    cy.wait(2000);
    cy.get('[data-testid="rule-add-action-plus"]').click();
    cy.get('[data-testid="radio-Republish"]').click();
    cy.get('[data-testid="inputTopic"]').clear("te");
    cy.get('[data-testid="inputTopic"]').type("test/publich");
    cy.get('[data-testid="button-Done"]').click();
    cy.get('[data-testid="button-Update, Rule"]').click();
    cy.get('[data-testid="inputany"]').clear();
    cy.get('[data-testid="inputany"]').type("description-test-updated");
    cy.get('[data-testid="button-Update"]').click();
    cy.wait(1000);

    /* ==== End Cypress Studio ==== */
  });
});
