import { PRODUCT } from "../../fixtures/example";

describe("Rule test for product", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });

  it("create product rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get('[data-testid="button-Create Rule"]').click();
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="button-Add Input"]').click();
    cy.get('[data-testid="radio-Product"]').click();
    cy.get('[data-testid="inputSearch for products"]').type("cypress");
    cy.wait(4000);
    cy.get(`[data-testid="rule-input-${PRODUCT.name}"]`).click();
    cy.get('[data-testid="rule-input-manual"]').click();
    cy.get('[data-testid="inputTopic"]').clear();
    cy.get('[data-testid="inputTopic"]').type("cypress/test/topic");
    cy.get('[data-testid="button-Add"]').click();
    cy.get('[data-testid="rule-add-trigger"] > svg').click();
    cy.get('[data-testid="inputField"]').clear("te");
    cy.get('[data-testid="inputField"]').type("temperature");
    cy.get(":nth-child(1) > .z-10").click();
    cy.get('[data-testid="button-Add Alias"]').click();
    cy.get('.items-end > :nth-child(1) > .input-container > [data-testid="inputName"]').clear("na");
    cy.get('.items-end > :nth-child(1) > .input-container > [data-testid="inputName"]').type(
      "name"
    );
    cy.get('[data-testid="inputValue"]').clear();
    cy.get('[data-testid="inputValue"]').type("string");
    cy.get('[data-testid="transform-switch"]').click();
    cy.get('[data-testid="button-Transform Data"]').click();
    cy.get(
      '[data-testid="dropdown-key"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="temperature"]').click();
    cy.get('[data-testid="inputvalue"]').clear("te");
    cy.get('[data-testid="inputvalue"]').type("temp");
    cy.get('[data-testid="condition-switch"]').click();
    cy.get('.w-1\\/2 > .input-container > [data-testid="inputName"]').clear("fi");
    cy.get('.w-1\\/2 > .input-container > [data-testid="inputName"]').type("fire");
    cy.get(
      '[data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="temp"]').click();
    cy.get(
      '[data-testid="dropdown-condition"] > .dropdown-container > [data-testid="dropdown-opener"] > .flex > .dropdown-selected-value'
    ).click();
    cy.get('[data-testid="greater equal"]').click();
    cy.get('[data-testid="inputenter value"]').clear("1");
    cy.get('[data-testid="inputenter value"]').type("100");
    cy.get('[data-testid="button-Save triggers"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="rule-add-action-plus"] > svg').click();
    cy.get('[data-testid="radio-Notification"]').click();
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="info"]').click();
    cy.get('[data-testid="switch-Email"]').click();
    cy.get('[data-testid="button-Done"]').click();
    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="rule-add-action-plus"] > svg').click();
    cy.get('[data-testid="radio-Republish"] > :nth-child(2) > .content').click();
    cy.get('[data-testid="inputTopic"]').clear("te");
    cy.get('[data-testid="inputTopic"]').type("test/rupudlish");
    cy.get('[data-testid="button-Done"]').click();
    cy.get('[data-testid="button-Save, Rule"]').click();
    cy.get('[data-testid="inputRule Name"]').clear("c");
    cy.get('[data-testid="inputRule Name"]').type("cypress-test-rule");
    cy.get('[data-testid="inputany"]').clear("te");
    cy.get('[data-testid="inputany"]').type("test-des ");
    cy.get('[data-testid="button-Create"]').click();

    cy.wait(1000);
  });
  it("update product rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();

    cy.get(`[data-testid="${PRODUCT.name}_notification"]`).click();

    cy.get('[data-testid="button-Edit"]').click();
    cy.wait(3000);
    cy.get('[data-testid="rule-action-edit-notification"] ').click();
    cy.get('[data-testid="switch-Sms"]').click();
    cy.get('[data-testid="dropdown-opener"]').click();
    cy.get('[data-testid="warn"]').click();
    cy.get('[data-testid="button-Done"]').click();
    cy.get('[data-testid="button-Update, Rule"]').click();
    cy.get('[data-testid="inputany"]').clear("test-des up");
    cy.get('[data-testid="inputany"]').type("test-des updated");
    cy.get('[data-testid="button-Update"]').click();
    cy.wait(1000);
  });
});
