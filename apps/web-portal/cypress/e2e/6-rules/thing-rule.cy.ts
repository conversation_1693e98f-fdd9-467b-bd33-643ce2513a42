import { RULE_DATA, STANDARD_THING } from "../../fixtures/example";

describe("Rule test for thing", () => {
  beforeEach(() => {
    cy.login("<EMAIL>", "Qwertyuiop@123");
  });
  it("create thing rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get('[data-testid="button-Create Rule"]').click();
    cy.get('[data-testid="button-Add Input"]').click();
    cy.get('[data-testid="radio-Device"] ').click();
    cy.get('[data-testid="inputSearch for devices particular device"]').clear("c");
    cy.get('[data-testid="inputSearch for devices particular device"]').type("cypress");
    cy.wait(5000);
    cy.get(`[data-testid="rule-input-${STANDARD_THING.name}"]`).click();
    cy.get('[data-testid="rule-input-manual"] > .z-10').click();

    cy.get('[data-testid="inputTopic"]').clear("c");
    cy.get('[data-testid="inputTopic"]').type("cypress/test/topic");
    cy.get('[data-testid="button-Add"]').click();

    cy.get('[data-testid="rule-add-trigger"] > svg').click();
    cy.get('[data-testid="inputField"]').clear("te");
    cy.get('[data-testid="inputField"]').type("temperature");
    cy.get(":nth-child(1) > .z-10").click();
    cy.get('[data-testid="button-Add Alias"]').click();
    cy.get('.items-end > :nth-child(1) > .input-container > [data-testid="inputName"]').clear("na");
    cy.get('.items-end > :nth-child(1) > .input-container > [data-testid="inputName"]').type(
      "name"
    );
    cy.get('[data-testid="inputValue"]').clear();
    cy.get('[data-testid="inputValue"]').type("firewires");
    cy.get('[data-testid="transform-switch"]').click();
    cy.get('[data-testid="button-Transform Data"]').click();
    cy.get(
      '[data-testid="dropdown-key"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="temperature"]').click();
    cy.get(".my-12 > .items-end > :nth-child(2)").click();
    cy.get('[data-testid="inputvalue"]').clear("te");
    cy.get('[data-testid="inputvalue"]').type("temp");
    cy.get('[data-testid="condition-switch"]').click();
    cy.get('.w-1\\/2 > .input-container > [data-testid="inputName"]').clear("f");
    cy.get('.w-1\\/2 > .input-container > [data-testid="inputName"]').type("fire");
    cy.get(
      '[data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"] > svg'
    ).click();
    cy.get('[data-testid="temp"]').click();
    cy.get(
      '[data-testid="dropdown-condition"] > .dropdown-container > [data-testid="dropdown-opener"] > svg '
    ).click();
    cy.get('[data-testid="greater equal"]').click();
    cy.get('[data-testid="inputenter value"]').clear("5");
    cy.get('[data-testid="inputenter value"]').type("50");
    cy.get('[data-testid="button-Add"]').click();
    cy.get('.MuiList-root > [tabindex="0"]').click();
    cy.get(
      ':nth-child(3) > [data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"] > svg '
    ).click();
    cy.get('[data-testid="temp"]').click();
    cy.get(
      ':nth-child(3) > [data-testid="dropdown-condition"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="greater"]').click();
    cy.get(
      ':nth-child(3) > :nth-child(3) > .input-container > [data-testid="inputenter value"]'
    ).clear("1");
    cy.get(
      ':nth-child(3) > :nth-child(3) > .input-container > [data-testid="inputenter value"]'
    ).type("100");
    cy.get('[data-testid="button-Save triggers"]').click();

    cy.get('[data-testid="rule-add-action-plus"] > svg').click();
    cy.get('[data-testid="radio-Republish"] > :nth-child(2) > .content').click();
    cy.get('[data-testid="inputTopic"]').clear("te");
    cy.get('[data-testid="inputTopic"]').type("test/rupblish");

    cy.get('[data-testid="rule-republish-advance-switch"]').click();
    cy.get('[data-testid="inputPayload"]').clear("te");
    cy.get('[data-testid="inputPayload"]').type("testpayload");
    cy.get(
      '[data-testid="dropdown-Retain"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="true"]').click();
    cy.get(
      '[data-testid="dropdown-Qos"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="0"]').click();
    cy.get('[data-testid="button-Done"]').click();

    cy.get('[data-testid="rule-add-action-plus"] > svg').click();
    cy.get('[data-testid="radio-MS Teams"] > :nth-child(2) > .content-base').click();
    cy.get('[data-testid="rule-webhook-url"]').click();
    cy.get('[data-testid="rule-webhook-url"]').type("https://www.microsoft.com/microsoft-teams");
    cy.get('[data-testid="button-Add"]').click();

    cy.get('[data-testid="button-Save, Rule"]').click();
    cy.get('[data-testid="inputRule Name"]').clear("c");
    cy.get('[data-testid="inputRule Name"]').type(RULE_DATA.thingRuleName);
    cy.get('[data-testid="inputany"]').clear("te");
    cy.get('[data-testid="inputany"]').type("test dewscription");
    cy.get('[data-testid="button-Create"] > .mr-2 > svg').click();

    cy.wait(1000);
  });
  it("update thing rule", () => {
    /* ==== Generated with Cypress Studio ==== */
    cy.get('[data-testid="sidebar-Actions"]').click();
    cy.get('[data-testid="sidebar-Rules"]').click();
    cy.get(`[data-testid="${RULE_DATA.thingRuleName}"]`).click();
    cy.get('[data-testid="button-Edit"] > .mr-2 > svg').click();
    cy.wait(3000);
    cy.get('[data-testid="rule-edit-input"]').click();
    cy.get('[data-testid="inputTopic"]').clear("cypress/test/topic/");
    cy.get('[data-testid="inputTopic"]').type("cypress/test/topic/updagted");
    cy.get('[data-testid="button-Update"]').click();

    cy.get('[data-testid="rule-edit-trigger"]').click();
    cy.get('[data-testid="button-Add Payload"]').click();
    cy.get(':nth-child(2) > .h-18 > .input-container > [data-testid="inputField"]').clear();
    cy.get(':nth-child(2) > .h-18 > .input-container > [data-testid="inputField"]').type("carbon");
    cy.get(":nth-child(2) > .tab-container > :nth-child(1) > .z-10").click();
    cy.get('[data-testid="button-Add Condition"]').click();
    cy.get(
      ':nth-child(3) > .items-start > .w-1\\/2 > .input-container > [data-testid="inputName"]'
    ).clear();
    cy.get(
      ':nth-child(3) > .items-start > .w-1\\/2 > .input-container > [data-testid="inputName"]'
    ).type("smoke");
    cy.get(
      ':nth-child(3) > .gap-2.items-center > [data-testid="dropdown-Select payload "] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="carbon"]').click();
    cy.get(
      ':nth-child(3) > .gap-2.items-center > [data-testid="dropdown-condition"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="greater equal"]').click();
    cy.get(
      ':nth-child(3) > .gap-2.items-center > :nth-child(3) > .input-container > [data-testid="inputenter value"]'
    ).clear();
    cy.get(
      ':nth-child(3) > .gap-2.items-center > :nth-child(3) > .input-container > [data-testid="inputenter value"]'
    ).type("70");
    cy.get('[data-testid="button-Save triggers"]').click();

    cy.get('[data-testid="rule-action-edit-republish"] ').click();
    cy.get('[data-testid="inputTopic"]').clear();
    cy.get('[data-testid="inputTopic"]').type("test/rupblish/updated");
    cy.get(
      '[data-testid="dropdown-Qos"] > .dropdown-container > [data-testid="dropdown-opener"]'
    ).click();
    cy.get('[data-testid="1"]').click();
    cy.get('[data-testid="button-Done"]').click();
    cy.get('[data-testid="rule-action-edit-teams"]').click();
    cy.get('[data-testid="rule-webhook-url"]').click();
    cy.get('[data-testid="rule-webhook-url"]').type("https://www.microsoft.com/microsoft-teams");
    cy.get('[data-testid="button-Add"]').click();

    cy.get('[data-testid="button-Update, Rule"]').click();
    cy.get('[data-testid="inputany"]').clear("test dewscription ");
    cy.get('[data-testid="inputany"]').type("test dewscription updatd");
    cy.get('[data-testid="button-Update"]').click();

    cy.wait(1000);
    /* ==== End Cypress Studio ==== */
  });
});
