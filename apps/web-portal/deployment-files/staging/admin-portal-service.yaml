apiVersion: v1
kind: Service
metadata:
  name: oneiot-cloud-portal-service
  labels:
    app: oneiot-cloud-portal
    oneiot.io/applicationName : oneiot-platform-app-staging
    oneiot.io/applicationStack: core
    oneiot.io/serviceType: ClusterIP 
    oneiot.io/applicationPriority: high
    oneiot.io/failureDomain: HighAvailability
    oneiot.io/computeType: compute-optimized
    oneiot.io/applicationOwner : oneiot
    oneiot.io/imageProvider: oci
    oneiot.io/multi-zone: "yes"
    oneiot.io/multi-cloud: "no"
    oneiot.io/cpu.architecture: x86_64   
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
    - name: https
      port: 443
      protocol: TCP
      targetPort: 80
  selector:
    app: oneiot-cloud-portal
  type: ClusterIP
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: oneiot-cloud-portal-ingress
  labels:
    app: oneiot-cloud-portal
    oneiot.io/applicationName : oneiot-platform-app-staging
    oneiot.io/applicationStack: core
    oneiot.io/serviceType: ClusterIP 
    oneiot.io/applicationPriority: high
    oneiot.io/failureDomain: HighAvailability
    oneiot.io/computeType: compute-optimized
    oneiot.io/applicationOwner : oneiot
    oneiot.io/imageProvider: oci
    oneiot.io/multi-zone: "yes"
    oneiot.io/multi-cloud: "no"
    oneiot.io/cpu.architecture: x86_64   
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`platform-dev.firewires.net`)
      kind: Rule
      services:
        - name: oneiot-cloud-portal-service
          port: 80
  tls:
    certResolver: cloudflare
