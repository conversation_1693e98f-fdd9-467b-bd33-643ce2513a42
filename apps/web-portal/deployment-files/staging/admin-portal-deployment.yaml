apiVersion: apps/v1
kind: Deployment
metadata:
  name: oneiot-cloud-portal
  labels:
    app: oneiot-cloud-portal
    oneiot.io/applicationName : oneiot-platform-app-staging
    oneiot.io/applicationStack: core
    oneiot.io/serviceType: ClusterIP 
    oneiot.io/applicationPriority: high
    oneiot.io/failureDomain: HighAvailability
    oneiot.io/computeType: compute-optimized
    oneiot.io/applicationOwner : oneiot
    oneiot.io/imageProvider: oci
    oneiot.io/multi-zone: "yes"
    oneiot.io/multi-cloud: "no"
    oneiot.io/cpu.architecture: x86_64 
    
spec:
  replicas: 1
  selector:
    matchLabels:
      app: oneiot-cloud-portal
  template:
    metadata:
      labels:
        app: oneiot-cloud-portal
        oneiot.io/applicationName : oneiot-platform-app-staging
        oneiot.io/applicationStack: core
        oneiot.io/serviceType: ClusterIP 
        oneiot.io/applicationPriority: high
        oneiot.io/failureDomain: HighAvailability
        oneiot.io/computeType: compute-optimized
        oneiot.io/applicationOwner : oneiot
        oneiot.io/imageProvider: oci
        oneiot.io/multi-zone: "yes"
        oneiot.io/multi-cloud: "no"
        oneiot.io/cpu.architecture: x86_64         
    spec:
      imagePullSecrets:
        - name: grs-firewires-portal
      containers:
        - name: oneiot-cloud-portal
          image: oci.firewires.net/services/oneiot-cloud-portal:dev-latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
