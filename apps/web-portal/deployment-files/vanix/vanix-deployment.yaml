apiVersion: apps/v1
kind: Deployment
metadata:
  name: vanix-cloud-portal
  labels:
    app: vanix-cloud-portal
    oneiot.io/applicationName : oneiot-platform-app-dev
    oneiot.io/applicationStack: core
    oneiot.io/serviceType: ClusterIP 
    oneiot.io/applicationPriority: high
    oneiot.io/failureDomain: HighAvailability
    oneiot.io/computeType: compute-optimized
    oneiot.io/applicationOwner : oneiot
    oneiot.io/imageProvider: oci
    oneiot.io/multi-zone: "yes"
    oneiot.io/multi-cloud: "no"
    oneiot.io/cpu.architecture: x86_64      
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vanix-cloud-portal
  template:
    metadata:
      labels:
        app: vanix-cloud-portal
        oneiot.io/applicationName : oneiot-platform-app-dev
        oneiot.io/applicationStack: core
        oneiot.io/serviceType: ClusterIP 
        oneiot.io/applicationPriority: high
        oneiot.io/failureDomain: HighAvailability
        oneiot.io/computeType: compute-optimized
        oneiot.io/applicationOwner : oneiot
        oneiot.io/imageProvider: oci
        oneiot.io/multi-zone: "yes"
        oneiot.io/multi-cloud: "no"
        oneiot.io/cpu.architecture: x86_64         
    spec:
      imagePullSecrets:
        - name: grs-firewires-portal
      containers:
        - name: vanix-cloud-portal
          image: oci.firewires.net/services/oneiot-platform-apps:vanix-0.0.1        
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          env:
            - name: VITE_APP_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: vanix-cloud-portal-secret
                  key: VITE_APP_SECRET_KEY
            - name: VITE_APP_ENV
              valueFrom:
                secretKeyRef:
                  name: vanix-cloud-portal-secret
                  key: VITE_APP_ENV
            - name: VITE_APP_API_URL
              valueFrom:
                secretKeyRef:
                  name: vanix-cloud-portal-secret
                  key: VITE_APP_API_URL
            - name: VITE_APP_PUBLIC_POSTHOG_KEY
              valueFrom:
                secretKeyRef:
                  name: vanix-cloud-portal-secret
                  key: VITE_APP_PUBLIC_POSTHOG_KEY
            - name: VITE_APP_PUBLIC_POSTHOG_HOST
              valueFrom:
                secretKeyRef:
                  name: vanix-cloud-portal-secret
                  key: VITE_APP_PUBLIC_POSTHOG_HOST

---
apiVersion: v1
kind: Secret
metadata:
  name: vanix-cloud-portal-secret
type: Opaque
data:
  VITE_APP_SECRET_KEY: UG04d0RNRDdWYzNqNDhzOHFRZ2EyI1dyY05TaGVKYTdyNUg1NW5XMA==
  VITE_APP_ENV: ZGV2
  VITE_APP_API_URL: aHR0cHM6Ly9zZXJ2aWNlcy1kZXYub25laW90Lmlv
  VITE_APP_PUBLIC_POSTHOG_KEY: cGhjX0lWY2pueXhUTmtkQXpPRzhaUlYyYUszT3RJRzdtR1JIOXN4amFCNUFmdlg=
  VITE_APP_PUBLIC_POSTHOG_HOST: aHR0cHM6Ly9ldS5pLnBvc3Rob2cuY29t
  