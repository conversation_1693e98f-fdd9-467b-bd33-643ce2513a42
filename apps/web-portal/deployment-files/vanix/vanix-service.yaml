apiVersion: v1
kind: Service
metadata:
  name: vanix-cloud-portal-service
  labels:
    app: vanix-cloud-portal
    oneiot.io/applicationName : oneiot-platform-app-dev
    oneiot.io/applicationStack: core
    oneiot.io/serviceType: ClusterIP 
    oneiot.io/applicationPriority: high
    oneiot.io/failureDomain: HighAvailability
    oneiot.io/computeType: compute-optimized
    oneiot.io/applicationOwner : oneiot
    oneiot.io/imageProvider: oci
    oneiot.io/multi-zone: "yes"
    oneiot.io/multi-cloud: "no"
    oneiot.io/cpu.architecture: x86_64   
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
    - name: https
      port: 443
      protocol: TCP
      targetPort: 80
  selector:
    app: vanix-cloud-portal
  type: ClusterIP
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: vanix-cloud-portal-ingress
  labels:
    app: vanix-cloud-portal
    oneiot.io/applicationName : oneiot-platform-app-dev
    oneiot.io/applicationStack: core
    oneiot.io/serviceType: ClusterIP 
    oneiot.io/applicationPriority: high
    oneiot.io/failureDomain: HighAvailability
    oneiot.io/computeType: compute-optimized
    oneiot.io/applicationOwner : oneiot
    oneiot.io/imageProvider: oci
    oneiot.io/multi-zone: "yes"
    oneiot.io/multi-cloud: "no"
    oneiot.io/cpu.architecture: x86_64     
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`vanix-dev.oneiot.io`)
      kind: Rule
      services:
        - name: vanix-cloud-portal-service
          port: 80
  tls:
    certResolver: cloudflare
