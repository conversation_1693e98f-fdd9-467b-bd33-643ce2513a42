/* eslint-disable no-unused-vars */
import {
  ListGeofenceResp,
  LocationShareLinksResp,
  PathDetailsResp,
  ProductConfig,
  ProductListResp,
  ThingDetailsResp,
  ThingListResp
} from "@onetypes/ontrack";
import React, { ReactElement } from "react";
import GridLayout, { Layout } from "react-grid-layout";

type ExtractSuccessResp<T> = T extends { status: "Success" } ? T : never;
type ExtractActiveThingType<T> = T extends { status: "inactive" } ? never : T;

export type GeofenceListItem = ExtractSuccessResp<ListGeofenceResp>["data"][number];
export type GeoThingList = ExtractSuccessResp<ThingListResp>["data"];
export type GeoThingDetails = ExtractSuccessResp<ThingDetailsResp>["data"];
export type SharedLinkItem = ExtractSuccessResp<LocationShareLinksResp>["data"][number];
export type GeofenceItem = ExtractSuccessResp<ListGeofenceResp>["data"][number];
export type GeoThingListItem = ExtractActiveThingType<
  ExtractSuccessResp<ThingListResp>["data"]["thingList"][number]
>;
export type GeoProduct = ExtractSuccessResp<ProductListResp>["data"][number];
export type ProductConfigItem = ExtractSuccessResp<ProductConfig>["data"];
export type HistoryItem = ExtractSuccessResp<PathDetailsResp>["data"][number];

export type ChartType =
  | "Bar"
  | "Area"
  | "Line"
  | "Step"
  | "Pie"
  | "Range"
  | "Donut"
  | "Table"
  | "Bar-H"
  | "Polar"
  | "Radar"
  | "Count"
  | "Logs"
  | "Meter"
  | "Map"
  | "Nested"
  | "Trigger"
  | "Scatter";

export type ChartSize = "1X" | "2X" | "4X";

export type GraphColor = "pink" | "blue" | "green" | "purple" | "amber";
export type Operation = "avg" | "min" | "max" | "latest";
export type ColorScheme = "default" | "retro" | "spring" | "bold";
export type LooseAutoComplete<T extends string> = T | Omit<string, T>;
export type DashboardGraphItem = {
  id: string;
  chartType: ChartType;
  dataIndex: string;
  dataKey: string;
  size: ChartSize;
  label: string;
  nestedDashboardId?: number;
  aggregation: {
    sum: string;
    average: string;
  };
  color: GraphColor;
  dataType: "string" | "number" | "boolean";
  expanded?: boolean;
  stacked?: boolean;
  filterBy: string;
  columnVisibility: Record<string, boolean>;
  gaugeColors?: { id: string; color: string; start: string }[];
  gaugeType?: string;
  unit: string;
  decimal: number;
  hidden: Record<string, boolean>;
  aggregationType: string;
  colorScheme: ColorScheme;
  widget?: boolean;
  iconScheme: string;
  filters: {
    field: LooseAutoComplete<"productName" | "event" | "thingName" | "field" | "operations">;
    operation: "is" | "is_not" | "contains" | "does_not_contain" | "exists" | "does_not_exist";
    value: string[];
    enabled?: boolean;
    id: string;
    ignoreIfNotPayload?: boolean;
    isFieldFilter?: boolean;
    mergeGraph?: boolean;
  }[];
  comparer?: string;
  isOverview?: boolean;
  assetFields?: number[];
  anomaly?: boolean;
  labelMapping?: Record<
    string,
    {
      label: string;
      unit: string;
    }
  >;
  displayName?: string;
  // trigger specific
  nextTrigger?: number;
  triggerType?: "toggle" | "range";
  triggerMinRange?: number;
  triggerMaxRange?: number;
};

export type SavedDashboards = {
  id: number;
  title: string;
  description: string;
  dashboardList: DashboardGraphItem[];
  layout: GridLayout.Layout[];
  primaryDashboard: boolean;
  siteId?: number;
};

export type Thing = {
  thingGroups: string[];
  version: string;
  _id: string;
  partner: string;
  thingName: string;
  productName: string;
  connectedTimeStamp: number;
  status: "connected" | "disconnected";
  timestamp: number;
  disconnectedTimeStamp: number;
  dissconnectedReason: string;
  createdAt: number;
  updatedAt: number;
};

export type WidgetFieldItem = {
  field: string;
  displayName: string;
  unit: string;
  color: string;
  id: string;
  decimal: number;
};

export type ThingWidget = {
  title: string;
  type:
    | "Bar"
    | "Line"
    | "Area"
    | "Gauge"
    | "Fill"
    | "Bar-H"
    | "Switch"
    | "Info"
    | "Scatter"
    | "Path"
    | "History"
    | "Heat Map"
    | "Count";
  id: string;
  field: string;
  operation: Operation;
  range: {
    id: string;
    color: string;
    start: string | number;
    compareTo: string;
    conditionKey: string;
    conditionValue: string;
    conditionOperator: string;
  }[];
  color: string;
  decimal: number;
  unit: string;
  source: string;
  product?: boolean;
  displayName?: string;
  showAxis?: boolean;
  layout?: Layout;
  size?: "1X" | "2X" | "4X";
  colorScheme?: string;
  fieldDetailsList?: WidgetFieldItem[];
  assetFields?: number[];
  iconScheme?: string;
};
type DropDownValues = string | Record<string, string | number> | any;

export type DropdownProps = {
  placeHolder?: string;
  label?: string;
  disabled?: boolean;
  isSearchable?: boolean;
  autoFocus?: boolean;
  optionsLoading?: boolean;
  optionItemClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  iconClassName?: string;
  menuClassName?: string;
  error?: boolean;
  required?: boolean;
  className?: string;
  validate?: (value: unknown) => string;
  emptyOption?: ReactElement | string;
  helperText?: string;
  description?: string;
  startIcon?: React.ReactElement;
  newOption?: {
    target: string;
    openCreateModal?: boolean;
    from?: string;
    placeHolder?: string;
  };
  getOptionDisabled?: (value: string) => boolean;
  deepSearch?: (value: string) => void;
  isAddField?: boolean;
  noTags?: boolean;
  options: DropDownValues[];
  getOptionLabel?: string;
  onAddField?: (data: string) => void;
  generateRandomWithPrefix?: string;
  fixedSuffix?: string;
  direction?: "top" | "bottom";
} & (
  | {
      isMulti: true;
      value?: DropDownValues[];
      defaultValue?: DropDownValues[];
      onChange: (value: DropDownValues[]) => void;
    }
  | {
      isMulti?: false;
      value?: DropDownValues;
      defaultValue?: DropDownValues;
      onChange: (value: DropDownValues) => void;
    }
);

export type ClassicThing = {
  thingName: string;
  productName: string;
  displayName: string;
  meta: Record<string, string>;
  version: string;
  imgURL: string;
  gatewayName: string | null;
  type: string;
  thingGroup: string[] | null;
  status: string | null;
  connectedTimeStamp: Date | null;
  disconnectedTimeStamp: Date | null;
  disconnectedReason: string | null;
  createdAt: Date;
  updatedAt: Date;
  tenant: any;
};

export type ClassicThingListItem = {
  thingName: string;
  productName: string;
  displayName: string;
  meta: Record<string, string>;
  version: string;
  imgURL: string;
  gatewayName: string | null;
  type: string;
  thingGroup: string[] | null;
  status: string | null;
  createdAt: any;
  connectedTimeStamp: Date | null;
  updatedAt: any;
};

export type ClassicProductListItem = {
  productName: string;
  otaType: "mqtt" | "http" | "none";
  imgURL: string;
  tenant: string;
  description: string;
  dataManagement: string[];
  productType: "gps" | "mqtt" | "managed" | "standard" | "gatewayManaged";
  authentication: {
    authType: string;
    authData: string;
    caName: string;
    type: string;
  } | null;
  authorization: {
    authType: string;
    authData: string;
    "policy-template": string;
  } | null;

  properties: {
    protocol: string;
    simulatorName: string;
  } | null;
  isNfTemplatePresent: boolean | null;
};

export type ClassicProduct = {
  productName: string;
  productType: "standard" | "managed" | "mqtt" | "gps" | "gatewayManaged";
  imgURL: string;
  dataManagement: string[];
  description: string;
  productType: "gps" | "mqtt" | "managed" | "standard" | "gatewayManaged";
  authentication: {
    authType: string;
    authData: string;
    caName: string;
    type: string;
  } | null;
  authorization: {
    authType: string;
    authData: string;
    "policy-template": string;
  } | null;
  documentLink: string | null;
  otaType: "none" | "mqtt" | "http";
  properties: {
    protocol: string;
    simulatorName: string;
  } | null;
  topics: object[] | null;
  isNfTemplatePresent: boolean | null;
  version: string | null;
  capabilities: string[] | null;
  category: string | null;
  provisioningEnabled: boolean;
  inventoryEnabled: boolean;
  metadata:
    | {
        key: string;
        required: boolean;
      }[]
    | null;
  widgets: object[] | null;
  monitorDataPoints: {
    id: string;
    icon: string;
    field: string | null;
    unit: string;
    displayName: string;
  }[];
  alexa: boolean | null;
  googleHome: boolean | null;
  tenant: string;
  createdAt: Date;
  updatedAt: Date;
};

type AppUserItem = {
  id: string;
  email: string;
  name: string;
  pincode: string;
  address: string;
  contactNumber: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
  identityID: string | null;
};

export type SiteListItem = {
  siteId: number;
  name: string;
  description: string;
  createdAt: Date;
  tenant: string;
  updatedAt: Date;
};

export type SiteDetailItem = {
  siteId: number;
  name: string;
  description: string;
  createdAt: Date;
  tenant: string;
  updatedAt: Date;
  areas: SiteArea[];
  widgets: SiteWidget[];
  hideConnector: boolean;
  orientation: "grid" | "inline";
};

export type SiteWidget = {
  type: "Ai_Chat" | "2D_Dt" | "3D_Dt" | "Recommendation";
  name: string;
  description: string;
  siteId: number;
  widgetId: number;
  digitalTwinId?: number;
  digitalTwin?: {
    id: number;
    name: string;
    type: "2D" | "3D";
  };
};

export type SiteArea = {
  areaId: number;
  name: string;
  description: string;
  imgURL: string;
  assets: {
    assetId: number;
    name: string;
  }[];
  metrics: SiteMetrics[];
};

export type WidgetFieldItem = {
  productName: string;
  thingName: string;
  color: string | null;
  widgetId: number;
  fieldId: number;
  displayName: string | null;
  unit: string | null;
  decimal: number | null;
  field: string;
};

export type AreaDetails = {
  siteId: number;
  areaId: number;
  name: string;
  description: string;
  imgURL: string;
  assets: {
    assetId: number;
    name: string;
  }[];
  metrics: SiteMetrics[];
};

export type MetricStatus = "OPTIMAL" | "NORMAL" | "CAUTION" | "WARNING" | "CRITICAL";

export type SiteMetrics = {
  id: number;
  name: string;
  siteId: number;
  areaId: number;
  formula: string;
  expectedValue?: number;
  chartType: "Area" | "Line" | "Bar";
  metrics: {
    color: string;
    min: number;
    max: number;
    id: number;
    status: MetricStatus;
  }[];
};

export type AssetListItem = {
  id: number;
  name: string;
  description: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
  imgURL: string;
};

export type AssetField = {
  id: number;
  productName: string;
  thingName: string;
  field: string;
  assetId: number;
  displayName: string;
  color: string;
  unit: string;
  decimal: number;
  type?: "ts" | "shadow";
};

export type AssetDetails = AssetListItem & {
  fields: AssetField[];
};

export type SiteDtListItem = {
  id: number;
  name: string;
  description: string;
  tenant: string;
  createdAt: string;
  updatedAt: string;
  videoUrl: string;
  thingName: string;
  productName: string;
  type: "2D" | "3D";
};

export type SiteDtDetails = SiteDtListItem & {
  assets: AssetDetails[];
};
