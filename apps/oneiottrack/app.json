{"name": "oneiot track", "displayName": "OneIoT Track", "expo": {"name": "OneIoT Track", "slug": "oneiottrack", "scheme": "oneiottrack", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "automatic", "icon": "./assets/images/logo.png", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#191015"}, "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/4dfe8875-fb7d-4356-9e5c-84a1826d551f"}, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/images/logo.png", "package": "com.oneiottrack", "googleServicesFile": "./android/app/google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundImage": "./assets/images/logo.png"}, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#191015"}, "config": {"googleMaps": {"apiKey": "AIzaSyC9ZWUgvBY5XnekrPR_Bd09p6HsZz2v2Xc"}}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}, "ios": {"icon": "./assets/images/logo.png", "supportsTablet": true, "bundleIdentifier": "com.oneiottracker", "minimumOsVersion": "13.4", "entitlements": {"aps-environment": "production"}, "infoPlist": {"UIBackgroundModes": ["remote-notification", "remote-notification"]}, "googleServicesFile": "./GoogleService-Info.plist", "splash": {"image": "./assets/images/splash.png", "tabletImage": "./assets/images/splash-logo-ios-tablet.png", "resizeMode": "contain", "backgroundColor": "#191015"}, "config": {"googleMapsApiKey": "AIzaSyDLEDb3aP99xkC_2b0SzxdIL7276ZghR-Y"}}, "web": {"favicon": "./assets/images/app-icon-web-favicon.png", "splash": {"image": "./assets/images/splash-logo-web.png", "resizeMode": "contain", "backgroundColor": "#191015"}, "bundler": "metro"}, "plugins": ["expo-localization", ["expo-build-properties", {"ios": {"newArchEnabled": true}, "android": {"newArchEnabled": true}}], "expo-font", ["expo-camera", {"cameraPermission": "Allow Oneiot app to access your camera to scan QR code"}], ["expo-notifications", {"icon": "./assets/images/notification_icon.png", "color": "#00E6C3", "defaultChannel": "default", "enableBackgroundRemoteNotifications": false}]], "experiments": {"tsconfigPaths": true}, "extra": {"eas": {"projectId": "4dfe8875-fb7d-4356-9e5c-84a1826d551f"}}, "runtimeVersion": "1.0.0"}, "ignite": {"version": "10.0.5"}}