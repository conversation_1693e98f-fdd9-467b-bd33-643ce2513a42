{"name": "oneiottrack", "version": "0.0.1", "private": true, "main": "index.js", "scripts": {"compile": "tsc --noEmit -p . --pretty", "format": "eslint . --fix", "lint": "eslint . --fix", "lint:check": "eslint .", "patch": "patch-package", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test .maestro/FavoritePodcast.yaml", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "postinstall": "patch-package", "bundle:visualize": "npx react-native-bundle-visualizer", "bundle:visualize:dev": "npx react-native-bundle-visualizer --dev", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:prod": "eas build --profile production --platform android --local", "start": "npx expo start", "android": "npx expo run:android", "ios": "cd ../../packages/shared && pnpm pack && cd ../../apps/oneiottrack && npx expo run:ios", "web": "npx expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx server dist", "prebuild:clean": "npx expo prebuild --clean"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.2.3", "@expo/metro-runtime": "~3.2.3", "@frontend/shared": "workspace:*", "@gorhom/bottom-sheet": "^5.1.5", "@react-native-firebase/app": "^21.14.0", "@react-native-firebase/messaging": "^21.14.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@reduxjs/toolkit": "^1.9.7", "@shopify/flash-list": "^1.8.0", "@tanstack/react-query": "^5.76.1", "apisauce": "3.0.1", "date-fns": "^4.1.0", "expo": "~51.0.39", "expo-asset": "~10.0.10", "expo-barcode-scanner": "~13.0.1", "expo-blur": "~13.0.3", "expo-build-properties": "~0.12.5", "expo-camera": "~15.0.16", "expo-device": "^6.0.2", "expo-font": "~12.0.10", "expo-linking": "~6.3.1", "expo-localization": "~15.0.3", "expo-notifications": "^0.28.19", "expo-splash-screen": "~0.27.7", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-updates": "~0.25.28", "i18next": "^23.16.8", "intl-pluralrules": "^2.0.1", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.5.1", "react-native": "0.74.5", "react-native-calendars": "^1.1312.1", "react-native-context-menu-view": "^1.19.0", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "2.16.2", "react-native-keyboard-controller": "^1.12.7", "react-native-maps": "1.14.0", "react-native-mmkv": "2.10.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.27.0", "react-native-web": "~0.19.13", "react-redux": "^8.1.3", "supercluster": "^8.0.1", "toastify-react-native": "^5.0.4", "zustand": "^4.5.7"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.1", "@testing-library/react-native": "^12.9.0", "@types/jest": "^29.5.14", "@types/react": "~18.2.79", "@types/supercluster": "^7.1.3", "babel-jest": "^29.7.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-config-expo": "^7.1.2", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-n": "^17.18.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-reactotron": "^0.1.7", "jest": "^29.7.0", "jest-expo": "~51.0.4", "patch-package": "^8.0.0", "postinstall-prepare": "1.0.1", "prettier": "^3.5.3", "react-test-renderer": "18.2.0", "reactotron-core-client": "^2.9.4", "reactotron-react-js": "^3.3.11", "reactotron-react-native": "^5.0.5", "reactotron-react-native-mmkv": "^0.2.6", "ts-jest": "^29.3.3", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "expo": {"autolinking": {"exclude": ["expo-file-system"]}}}