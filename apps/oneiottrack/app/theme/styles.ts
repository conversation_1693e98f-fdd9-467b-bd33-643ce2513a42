import { TextStyle, ViewStyle } from "react-native"

/* Use this file to define styles that are used in multiple places in your app. */
export const $styles = {
  row: { flexDirection: "row" } as ViewStyle,
  flex1: { flex: 1 } as ViewStyle,
  flexWrap: { flexWrap: "wrap" } as ViewStyle,
  fullHightWidth: { height: "100%", width: "100%" } as ViewStyle,
  justifyBetweenCenter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  } as ViewStyle,
  itemsCenter: {
    flexDirection: "row",
    alignItems: "center",
  } as ViewStyle,
  toggleInner: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  } as ViewStyle,
  centerInFlex1: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  } as ViewStyle,
  textCenter: {
    textAlign: "center",
  } as TextStyle,
  flexShrink: {
    flexShrink: 1,
  } as TextStyle,
  myAuto: {
    marginTop: "auto",
    marginBottom: "auto",
  } as ViewStyle,
}
