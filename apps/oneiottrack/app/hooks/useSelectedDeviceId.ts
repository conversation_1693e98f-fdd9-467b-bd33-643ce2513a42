import { storage } from "@frontend/shared"
import { create } from "zustand"

type State = {
  selectedDeviceId: string | undefined | null
  setSelectedDeviceId: (deviceId: string | null) => void
}

const useSelectedDeviceId = create<State>((set) => ({
  selectedDeviceId: storage.getItem("selectedDeviceId"),
  setSelectedDeviceId: (deviceId: string | null) => {
    if (deviceId) {
      storage.setItem("selectedDeviceId", deviceId)
    } else {
      storage.removeItem("selectedDeviceId")
    }
    set({ selectedDeviceId: deviceId })
  },
}))

export default useSelectedDeviceId
