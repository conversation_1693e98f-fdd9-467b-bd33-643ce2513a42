import { $styles, spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { storage } from "@frontend/shared"
import { useRef, useState } from "react"
import { Modal, Pressable, View, ViewStyle } from "react-native"
import { Dropdown } from "react-native-element-dropdown"
import { Button } from "./Button"
import { Text } from "./Text"

const URLS = [
  { env: "Dev", value: "https://services-dev.oneiot.io" },
  { env: "Alpha", value: "https://services-alpha.oneiot.io" },
  { env: "Beta", value: "https://services-beta.oneiot.io" },
  { env: "Ind-01", value: "https://services-ind-01.oneiot.io" },
]

export const EnvironmentSwitch = () => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()
  const [modalVisible, setModalVisible] = useState(false)
  const [url, setUrl] = useState(storage.getItem("rn-url") ?? URLS[0].value)
  const clickCount = useRef(0)
  const clickTimer = useRef<ReturnType<typeof setTimeout>>(null)

  const handlePress = () => {
    clickCount.current += 1

    // Reset click count after 500ms
    if (clickTimer.current) {
      clearTimeout(clickTimer.current)
    }

    // @ts-expect-error this is a  hack
    clickTimer.current = setTimeout(() => {
      clickCount.current = 0
    }, 1000)

    // Show modal after 3 clicks
    if (clickCount.current === 3) {
      setModalVisible(true)
      clickCount.current = 0
    }
  }

  const handleSubmit = () => {
    storage.setItem("rn-url", url)
    setModalVisible(false)
  }

  return (
    <>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={$modalContainer}>
          <View style={themed($modalWrapper)}>
            <View>
              <Text text="Select Environment" size="md" weight="medium" />
              <Dropdown
                renderItem={(item) => (
                  <View style={themed($row)}>
                    <Text text={item.value} size="sm" weight="medium" />
                  </View>
                )}
                containerStyle={{ borderColor: colors.border, backgroundColor: colors.card }}
                itemTextStyle={{ color: colors.text }}
                selectedTextStyle={{ color: colors.text }}
                style={themed($dropdown)}
                data={URLS}
                onChange={(item) => setUrl(item.value)}
                value={URLS.find((item) => item.value === url)}
                labelField="env"
                valueField="value"
              />

              <Text
                text="Close and open the app after submit to apply the changes"
                size="xs"
                weight="light"
              />
            </View>
            <View style={[$styles.justifyBetweenCenter, { gap: spacing.xl }]}>
              <Button
                text="Cancel"
                style={$styles.flex1}
                preset="reversed"
                onPress={() => setModalVisible(false)}
              />

              <Button text="Submit" preset="filled" style={$styles.flex1} onPress={handleSubmit} />
            </View>
          </View>
        </View>
      </Modal>

      <Pressable onPress={handlePress} style={$pressable}></Pressable>
    </>
  )
}

const $pressable: ViewStyle = {
  position: "absolute",
  bottom: 32,
  right: 16,
  width: 76,
  height: 76,
}

const $dropdown: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.sm,
  borderRadius: spacing.xs,
  marginTop: spacing.sm,
})

const $row: ThemedStyle<ViewStyle> = ({ colors }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  backgroundColor: colors.card,
})

const $modalContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: "rgba(0, 0, 0, 0.5)",
}

const $modalWrapper: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.card,
  padding: 20,
  borderRadius: 10,
  width: "80%",
  elevation: 5,
  gap: spacing.md,
})
