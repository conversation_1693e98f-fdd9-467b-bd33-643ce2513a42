import { StyleProp, TextStyle, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { Text } from "@/components/Text"
import { NotificationHistory } from "@frontend/shared/hooks/notification/useNotificationHistory"
import { dateUtils } from "@frontend/shared"
import { Icon } from "./Icon"

export interface NotificationItemProps {
  style?: StyleProp<ViewStyle>
  item: NotificationHistory
  index: number
}

/**
 * Describe your component here
 */

const ACTION_COLOR_MAP: Record<string, string> = {
  created: "green",
  updated: "yellow",
  deleted: "red",
  default: "blue",
}

const getNotificationColor = (category: string) => {
  const match = category.match(/^(product|thing|user)(Created|Updated|Deleted)$/i)

  const action = match?.[2]?.toLowerCase() || "default"
  // Get icon and color based on prefix and action

  const color = ACTION_COLOR_MAP[action] || ACTION_COLOR_MAP.default

  return color
}

export const NotificationItem = (props: NotificationItemProps) => {
  const { style, item } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()

  return (
    <View style={themed($styles)}>
      <Icon
        icon="bell"
        containerStyle={{
          backgroundColor: getNotificationColor(item.category),
          borderRadius: 10,
          padding: spacing.xs,
        }}
        size={20}
        color={colors.background}
      />
      <View style={{ flexShrink: 1 }}>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Text text={item.title} numberOfLines={1} style={{ flexShrink: 1 }} preset="formLabel" />
          <Text
            preset="formHelper"
            size="xxs"
            text={dateUtils.convertUTCToLocal(item.createdAt, "DD MMM HH:mm A")}
          />
        </View>
        <Text text={item.body} size="sm" numberOfLines={1} preset="formHelper" />
      </View>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.card,
  marginBottom: spacing.sm,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
  flexDirection: "row",
  gap: spacing.sm,
  alignItems: "center",
})

const $text: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.palette.primary500,
  flexDirection: "row",
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
})
