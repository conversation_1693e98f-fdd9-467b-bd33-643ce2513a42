import { ImageStyle, StyleProp, TouchableOpacity, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles, spacing, type ThemedStyle } from "@/theme"
import { Text } from "@/components/Text"
import { GeoThingListItem } from "@frontend/shared/types/geo"
import { AutoImage } from "./AutoImage"
import { dateUtils } from "@frontend/shared"

export interface DeviceListItemProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: StyleProp<ViewStyle>
  thing: GeoThingListItem
  onPress?: () => void
}

/**
 * Describe your component here
 */

export const DeviceListItem = (props: DeviceListItemProps) => {
  const { style, thing, onPress } = props

  const { themed } = useAppTheme()

  const backgroundColor =
    thing.status === "inactive" ? "grey" : thing.status === "disconnected" ? "red" : "green"

  return (
    <TouchableOpacity
      style={[themed($thingListItem), style]}
      key={thing.thingName}
      onPress={onPress}
    >
      <AutoImage
        source={{
          uri:
            thing.imgURL ||
            "https://platform-product-images.s3.ap-south-1.amazonaws.com/imgFolder/0bc28903-f6af-4d9c-ac2e-f4f4416f5db4.jpg",
        }}
        style={$thingPhoto}
        resizeMode="contain"
      />
      <View style={$DetialContainer}>
        <View style={$DetialRows}>
          <Text text={thing.thingName} style={$styles.flexShrink} weight="medium" />
          <View
            style={{
              backgroundColor,
              width: 12,
              height: 12,
              borderRadius: 6,
            }}
          />
        </View>
        <View style={$DetialRows}>
          <Text text={thing.productName} size="sm" weight="light" />
          <Text text={dateUtils.convertUTCToLocal(thing.latestUpdate)} size="xxs" weight="light" />
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $thingPhoto: ImageStyle = {
  height: 40,
  width: 48,
  borderRadius: spacing.sm,
}
const $DetialContainer: StyleProp<ViewStyle> = {
  flex: 1,
}
const $DetialRows: StyleProp<ViewStyle> = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  flex: 1,
}

const $thingListItem: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  gap: spacing.sm,
  alignItems: "center",
  margin: 8,
  backgroundColor: colors.card,
  padding: spacing.xs,
  borderRadius: spacing.sm,
  borderColor: colors.border,
  borderWidth: 1,
})
