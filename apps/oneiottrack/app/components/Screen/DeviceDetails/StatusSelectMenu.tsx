import { Icon } from "@/components/Icon"
import { Text } from "@/components/Text"
import { spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { useRef, useState } from "react"
import { View, TouchableOpacity, StyleSheet, Modal, FlatList, ViewStyle } from "react-native"

const DropdownMenu = ({
  onSelect,
  options,
  selected,
  style,
}: {
  onSelect: (value: string) => void
  options: Array<{ label: string; value: string; isDanger?: boolean }>
  selected?: string
  style?: ViewStyle
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 })
  const buttonRef = useRef<TouchableOpacity>(null)
  const { themed } = useAppTheme()

  const openMenu = () => {
    if (buttonRef.current) {
      buttonRef.current.measure((x, y, width, height, pageX, pageY) => {
        setMenuPosition({
          top: pageY + height - 40, // Offset below the button
          right: x + spacing.sm,
        })
        setIsVisible(true)
      })
    }
  }

  const closeMenu = () => setIsVisible(false)

  const handleSelect = (value: string) => {
    onSelect(value)
    closeMenu()
  }

  return (
    <View>
      {/* Anchor Button */}
      <TouchableOpacity ref={buttonRef} style={[themed($button), style]} onPress={openMenu}>
        <Icon icon="filter" size={16} />
        {selected && <View style={$indicator} />}
      </TouchableOpacity>

      {/* Dropdown Menu */}
      <Modal visible={isVisible} transparent>
        <TouchableOpacity style={styles.overlay} onPress={closeMenu}>
          <View style={[themed($dropdown), { top: menuPosition.top, right: menuPosition.right }]}>
            <FlatList
              data={options}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity style={themed($option)} onPress={() => handleSelect(item.value)}>
                  <Text
                    weight="medium"
                    style={[item.isDanger && { color: "red" }]}
                    text={item.label}
                  />
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
})

const $indicator: ViewStyle = {
  position: "absolute",
  top: 3,
  right: 3,
  height: 8,
  width: 8,
  backgroundColor: "orange",
  borderRadius: 4,
}

const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  padding: 8,
  borderRadius: spacing.xxs,
  // elevation: 2,
  borderWidth: 1,
  borderColor: colors.border,
  backgroundColor: colors.card,
  position: "relative",
})

const $option: ThemedStyle<ViewStyle> = ({ colors }) => ({
  padding: 12,
  borderBottomWidth: 1,
  borderColor: colors.border,
})

const $dropdown: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  backgroundColor: colors.card,
  borderRadius: 8,
  elevation: 5,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  width: 200,
  paddingVertical: 5,
})

export default DropdownMenu
