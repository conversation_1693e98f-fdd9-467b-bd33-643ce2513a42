import { AppStackParamList } from "@/navigators"
import useGeoThingDetails from "@frontend/shared/hooks/geo/useGeoThingDetails"
import { RouteProp, useRoute } from "@react-navigation/native"
import { memo, useEffect } from "react"
import { Marker } from "react-native-maps"
import TripsMap from "./TripsMap"
import StopsMap from "./StopsMap"
import EventsMap from "./EventsMap"
import TrackMap from "./TrackMap"
import useMapStore from "@/bears/useMapInDeviceDetails"

type DetailsScreenRouteProp = RouteProp<AppStackParamList, "DeviceDetails">

const DeviceDetailsMap = () => {
  const {
    params: { mode = "Activity", thingName },
  } = useRoute<DetailsScreenRouteProp>()

  const { data: thingDetails } = useGeoThingDetails({ thingName })

  const mapRef = useMapStore((store) => store.mapRef)

  const lat = thingDetails?.lat
  const long = thingDetails?.long

  useEffect(() => {
    if (mode === "Activity" && lat && long && mapRef) {
      setTimeout(() => {
        mapRef?.animateToRegion({
          latitude: lat,
          longitude: long,
          latitudeDelta: 0.1,
          longitudeDelta: 0.1,
        })
      })
    }
  }, [mode, lat, long, mapRef])

  if (!thingDetails) return null

  if (mode === "Trips") {
    return (
      <>
        <TripsMap thingName={thingName} />
        <Marker coordinate={{ latitude: thingDetails.lat, longitude: thingDetails.long }} />
      </>
    )
  }

  if (mode === "Stops") {
    return (
      <>
        <StopsMap thingName={thingName} />
        <Marker coordinate={{ latitude: thingDetails.lat, longitude: thingDetails.long }} />
      </>
    )
  }

  if (mode === "Events") {
    return (
      <>
        <EventsMap thingName={thingName} />
        <Marker coordinate={{ latitude: thingDetails.lat, longitude: thingDetails.long }} />
      </>
    )
  }

  if (mode === "Track") {
    return (
      <>
        <TrackMap thingName={thingName} />
        <Marker coordinate={{ latitude: thingDetails.lat, longitude: thingDetails.long }} />
      </>
    )
  }

  return <Marker coordinate={{ latitude: thingDetails.lat, longitude: thingDetails.long }} />
}

export default memo(DeviceDetailsMap)
