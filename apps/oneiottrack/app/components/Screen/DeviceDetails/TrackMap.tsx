import useDuration from "@/bears/useDuration"
import useMapStore from "@/bears/useMapInDeviceDetails"
import useThingPath from "@frontend/shared/hooks/geo/useThingPath"
import pollygon from "@frontend/shared/utils/decodePolygon"
import { useEffect, useMemo } from "react"
import { Polyline } from "react-native-maps"

export default function TrackMap({ thingName }: { thingName: string }) {
  const mapRef = useMapStore((store) => store.mapRef)
  const duration = useDuration((state) => state.duration)

  const { data: thingPathStr } = useThingPath({
    thingName,
    duration,
  })

  const decodedPath = useMemo(() => {
    if (!thingPathStr?.path) {
      return []
    }
    return pollygon
      .decodePolygon(thingPathStr.path)
      .map(({ lat, lng }) => ({ latitude: lat, longitude: lng }))
  }, [thingPathStr])

  useEffect(() => {
    if (decodedPath.length > 0 && mapRef) {
      setTimeout(() => {
        mapRef?.fitToCoordinates(decodedPath, {
          edgePadding: { top: 10, right: 10, bottom: 10, left: 10 },
          animated: true,
        })
      })
    }
  }, [decodedPath, mapRef])

  return (
    <>
      <Polyline coordinates={decodedPath} strokeColor="#60a5fa" strokeWidth={5} />
    </>
  )
}
