import { Text } from "@/components/Text"
import { AppStackParamList } from "@/navigators"
import { spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import useGeoThingDetails from "@frontend/shared/hooks/geo/useGeoThingDetails"
import { RouteProp, useRoute } from "@react-navigation/native"
import { View, ViewStyle } from "react-native"
import dateUtils from "@frontend/shared/utils/date"

type DetailsScreenRouteProp = RouteProp<AppStackParamList, "DeviceDetails">

export default function DeviceDetailsCard() {
  const {
    params: { thingName },
  } = useRoute<DetailsScreenRouteProp>()

  const { data: thingDetails } = useGeoThingDetails({ thingName })
  const { themed } = useAppTheme()

  if (!thingDetails) return null

  const options = [
    { title: "Name", value: thingDetails.displayName },
    { title: "Version", value: thingDetails.version },
    { title: "Status", value: thingDetails.status },
    { title: "Last Update", value: dateUtils.convertUTCToLocal(thingDetails.updatedAt) },
    { title: "Created at", value: dateUtils.convertUTCToLocal(thingDetails.createdAt) },
  ]

  return (
    <View style={themed($container)}>
      {options.map(({ title, value }) => (
        <View key={title} style={$optionContainer}>
          <Text text={`${title}:`} size="sm" weight="light" />
          <Text text={value} size="sm" weight="medium" />
        </View>
      ))}
      {/* Address is takes a lot of space */}
      <Text text={`Address:`} size="sm" weight="light" />
      <Text text={thingDetails.address} size="sm" weight="medium" />
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.card,
  padding: spacing.sm,
  marginHorizontal: spacing.md,
  borderRadius: spacing.xs,
  borderWidth: 1,
  borderColor: colors.border,
})

const $optionContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginBottom: spacing.xxs,
}
