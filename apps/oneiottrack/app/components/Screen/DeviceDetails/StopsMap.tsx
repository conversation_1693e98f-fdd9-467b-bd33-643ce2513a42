import useDuration from "@/bears/useDuration"
import useMapStore from "@/bears/useMapInDeviceDetails"
import useThingStops from "@frontend/shared/hooks/geo/useThingStops"
import { useEffect } from "react"
import { Marker } from "react-native-maps"

export default function StopsMap({ thingName }: { thingName: string }) {
  const mapRef = useMapStore((store) => store.mapRef)
  const duration = useDuration((state) => state.duration)

  const { data: stops } = useThingStops({ thingName, duration })

  useEffect(() => {
    const coordinates = stops?.stops.map((stop) => ({
      latitude: stop.lat,
      longitude: stop.lng,
    }))
    if (coordinates && coordinates.length > 0 && mapRef) {
      setTimeout(() => {
        mapRef?.fitToCoordinates(coordinates, {
          edgePadding: { top: 10, right: 10, bottom: 10, left: 10 },
          animated: true,
        })
      })
    }
  }, [stops, mapRef])

  return (
    <>
      {stops?.stops.map((stop) => (
        <Marker coordinate={{ latitude: stop.lat, longitude: stop.lng }} key={stop.id.toString()} />
      ))}
    </>
  )
}
