import useDuration from "@/bears/useDuration"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useThingStops from "@frontend/shared/hooks/geo/useThingStops"
import { ActivityIndicator, View } from "react-native"

const StopsTable = ({ thingName }: { thingName: string }) => {
  const duration = useDuration((state) => state.duration)

  const { data: stops, isLoading } = useThingStops({ thingName, duration })

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!stops?.stops?.length) {
    return <Text text="No Stops Available" />
  }

  return (
    <ScrollableTable
      headers={["startTime", "endTime", "distance"]}
      data={stops.stops || []}
      maxHeight={400}
    />
  )
}

export default StopsTable
