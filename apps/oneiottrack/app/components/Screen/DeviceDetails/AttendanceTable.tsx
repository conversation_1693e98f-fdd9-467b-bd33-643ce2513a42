import useDuration from "@/bears/useDuration"
import { Button } from "@/components/Button"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useStudentAttendance from "@frontend/shared/hooks/geo/useStudentAttendance"
import { useMemo } from "react"
import { ActivityIndicator, View } from "react-native"

const AttendanceTable = ({
  thingName,
  handleExport,
}: {
  thingName: string
  handleExport: () => void
}) => {
  const duration = useDuration((state) => state.duration)
  const {
    data: studentAttendance,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useStudentAttendance({ thingName, duration })

  const track = useMemo(() => {
    const res: {
      thingName: string
      eventType: "enter" | "exit"
      timestamp: Date
      latitude: number
      longitude: number
    }[] = []

    if (!studentAttendance) {
      return []
    }

    studentAttendance.pages.forEach((page) => {
      page.attendance.forEach((point) => {
        res.push(point)
      })
    })
    return res
  }, [studentAttendance])

  const onReachEnd = () => {
    if (hasNextPage) {
      fetchNextPage()
    }
  }

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!track?.length) {
    return <Text text="No Activity Available" />
  }

  return (
    <>
      <ScrollableTable
        headers={["timestamp", "eventType", "latitude", "longitude"]}
        data={track}
        maxHeight={400}
        onReachEnd={onReachEnd}
        footerLoading={isFetchingNextPage}
      />
      <Button onPress={handleExport} text="Export" />
    </>
  )
}

export default AttendanceTable
