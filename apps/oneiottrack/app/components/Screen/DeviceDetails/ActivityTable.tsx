import useDuration from "@/bears/useDuration"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useThingReports from "@frontend/shared/hooks/geo/useThingReports"
import { ActivityIndicator, View } from "react-native"

const ActivityTable = ({ thingName }: { thingName: string }) => {
  const headers = ["date", "startTime", "odometerStart", "odometerEnd"]

  const duration = useDuration((state) => state.duration)

  const { data: reports, isLoading } = useThingReports({
    thingName,
    duration,
  })

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!reports?.length) {
    return <Text text="No Activity Available" />
  }

  return <ScrollableTable headers={headers} data={reports} maxHeight={360} />
}

export default ActivityTable
