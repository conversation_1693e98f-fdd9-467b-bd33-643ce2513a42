import useDuration from "@/bears/useDuration"
import useMapStore from "@/bears/useMapInDeviceDetails"
import { pollyLine } from "@frontend/shared"
import useThingTrips from "@frontend/shared/hooks/geo/useThingTrips"
import { useEffect } from "react"
import { Polyline } from "react-native-maps"

const COLORS = [
  "#00e6c3",
  "#36a3ea",
  "#fe6282",
  "#4bc0c0",
  "#ff9e40",
  "#9967ff",
  "#ffcd56",
  "#c9cacf",
  "#87bc45",
  "#fd7f6f",
]

export default function TripsMap({ thingName }: { thingName: string }) {
  const mapRef = useMapStore((store) => store.mapRef)
  const mapViewMode = useMapStore((store) => store.mapViewMode)
  const duration = useDuration((state) => state.duration)

  const { data: deviceTrips } = useThingTrips({
    thingName,
    duration,
  })

  useEffect(() => {
    if (!deviceTrips || !mapRef) return

    const latLngArray: [number, number][] = []
    if (deviceTrips?.onGoingTrip) {
      latLngArray.push([deviceTrips.onGoingTrip?.startLat, deviceTrips.onGoingTrip?.startLong] as [
        number,
        number,
      ])
      latLngArray.push([deviceTrips.onGoingTrip.lat, deviceTrips.onGoingTrip.long])
    }
    deviceTrips.trips.forEach((trip) => {
      latLngArray.push([trip.startLat, trip.startLong] as [number, number])
      latLngArray.push([trip.endLat, trip.endLong] as [number, number])
    })

    setTimeout(() => {
      mapRef?.fitToCoordinates(
        latLngArray.map(([latitude, longitude]) => ({
          latitude,
          longitude,
        })),
        {
          edgePadding: { top: 10, right: 10, bottom: 10, left: 10 },
          animated: true,
        },
      )
    })
  }, [deviceTrips, mapRef, mapViewMode])

  if (!deviceTrips?.trips?.length) return null

  return (
    <>
      {deviceTrips?.trips.map((trip, i) => {
        const decodedPath = pollyLine
          .decodePolygonArray(trip.path)
          .map(([latitude, longitude]) => ({ latitude, longitude }))
        const key = trip.id

        return (
          <Polyline
            coordinates={decodedPath}
            key={key}
            strokeColor={COLORS[i % COLORS.length]}
            strokeWidth={5}
          />
        )
      })}
    </>
  )
}
