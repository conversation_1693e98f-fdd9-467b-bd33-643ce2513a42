import { Text } from "@/components/Text"
import { AppStackParamList } from "@/navigators"
import { RouteProp, useRoute } from "@react-navigation/native"
import { View, ViewStyle } from "react-native"
import ActivityTable from "./ActivityTable"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import TripsTable from "./TripsTable"
import StopsTable from "./StopsTable"
import EventsTable from "./EventsTable"
import TimeSeriesTable from "./TimeSeriesTable"
import TrackTable from "./TrackTable"
import { Dropdown } from "react-native-element-dropdown"
import useDuration, { availableOptions } from "@/bears/useDuration"
import AttendanceTable from "./AttendanceTable"
import ExportAttendanceSheet from "@/components/Layout/ExportAttendanceSheet"
import { ElementRef, useRef } from "react"

type DetailsScreenRouteProp = RouteProp<AppStackParamList, "DeviceDetails">

const DeviceDetailsTable = () => {
  const {
    params: { mode = "Activity", thingName },
  } = useRoute<DetailsScreenRouteProp>()
  const attendanceSheetRef = useRef<ElementRef<typeof ExportAttendanceSheet>>(null)
  const duration = useDuration((state) => state.duration)
  const setDuration = useDuration((state) => state.setDuration)

  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <>
      <View style={themed($container)}>
        <View style={$styles.justifyBetweenCenter}>
          <Text text={mode} size="lg" weight="medium" />
          <Dropdown
            data={availableOptions}
            style={themed($dropdown)}
            value={availableOptions.find((item) => item.value === duration)}
            labelField="title"
            valueField="value"
            itemTextStyle={{ color: colors.text }}
            selectedTextStyle={{ color: colors.text }}
            onChange={(item) => setDuration(item.value)}
            containerStyle={{ borderColor: colors.border, backgroundColor: colors.card }}
            renderItem={(item) => (
              <View style={themed($row)}>
                <Text text={item.value} size="sm" weight="medium" />
              </View>
            )}
          />
        </View>
        {mode === "Activity" && <ActivityTable thingName={thingName} />}
        {mode === "Trips" && <TripsTable thingName={thingName} />}
        {mode === "Stops" && <StopsTable thingName={thingName} />}
        {mode === "Events" && <EventsTable thingName={thingName} />}
        {mode === "TimeSeries" && <TimeSeriesTable thingName={thingName} />}
        {mode === "Track" && <TrackTable thingName={thingName} />}
        {mode === "Attendance" && (
          <AttendanceTable
            thingName={thingName}
            handleExport={() => attendanceSheetRef.current?.open()}
          />
        )}
      </View>
      {mode === "Attendance" && <ExportAttendanceSheet ref={attendanceSheetRef} />}
    </>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.card,
  marginHorizontal: spacing.md,
  paddingHorizontal: spacing.sm,
  gap: spacing.sm,
  paddingVertical: spacing.sm,
  borderRadius: spacing.xs,
  borderWidth: 1,
  borderColor: colors.border,
})

const $dropdown: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 160,
  backgroundColor: colors.background,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: spacing.xs,
})

const $row: ThemedStyle<ViewStyle> = ({ colors }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  backgroundColor: colors.card,
})

export default DeviceDetailsTable
