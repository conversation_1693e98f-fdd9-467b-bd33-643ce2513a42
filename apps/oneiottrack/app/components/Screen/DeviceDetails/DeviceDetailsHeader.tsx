import { AutoImage } from "@/components/AutoImage"
import { Icon } from "@/components/Icon"
import { Text } from "@/components/Text"
import { AppStackParamList } from "@/navigators"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import useGeoThingDetails from "@frontend/shared/hooks/geo/useGeoThingDetails"
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native"
import { View, ViewStyle } from "react-native"

type DetailsScreenRouteProp = RouteProp<AppStackParamList, "DeviceDetails">

export default function DeviceDetailsHeader() {
  const navigation = useNavigation()
  const {
    params: { thingName },
  } = useRoute<DetailsScreenRouteProp>()

  const $containerInsets = useSafeAreaInsetsStyle(["top"])
  const { themed } = useAppTheme()

  const { data: thingDetails } = useGeoThingDetails({ thingName })

  if (!thingDetails) return null

  return (
    <View style={themed([$containerInsets, $styles.justifyBetweenCenter, $headerContainer])}>
      <View style={$styles.itemsCenter}>
        <Icon
          icon="back"
          onPress={() => {
            navigation.goBack()
          }}
          size={20}
        />
        <AutoImage
          source={{ uri: thingDetails?.imgURL }}
          style={{ marginHorizontal: spacing.sm, borderRadius: spacing.xs }}
          maxHeight={40}
        />
        <View>
          <Text text={thingDetails?.thingName} weight="medium" size="md" />
          <Text text={thingDetails?.productName} size="sm" />
        </View>
      </View>

      <View style={themed($batteryLevel)}>
        <Text text={`${thingDetails.attributes?.batteryLevel || 0} %`} size="sm" />
      </View>
    </View>
  )
}

const $batteryLevel: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: 8,
  paddingVertical: 4,
  borderRadius: 4,
  borderWidth: 1,
  borderColor: colors.border,
  elevation: 1,
})

const $headerContainer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.card,
  padding: 12,
  borderBottomColor: colors.border,
  borderBottomWidth: 1,
  elevation: 4,
})
