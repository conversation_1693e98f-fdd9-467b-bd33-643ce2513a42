import useDuration from "@/bears/useDuration"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useThingTrack from "@frontend/shared/hooks/geo/useThingTrackPoints"
import { useMemo } from "react"
import { ActivityIndicator, View } from "react-native"

const TrackTable = ({ thingName }: { thingName: string }) => {
  const duration = useDuration((state) => state.duration)

  const {
    data: thingTrack,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useThingTrack({ thingName, duration })

  const track = useMemo(() => {
    const res: {
      timestamp: string
      latLng: string
      speed: number
      distance: number
    }[] = []

    if (!thingTrack) {
      return []
    }

    thingTrack.pages.forEach((page) => {
      page.points.forEach((point) => {
        res.push({
          timestamp: point.timestamp as any,
          latLng: `${point.latitude},${point.longitude}`,
          speed: point.speed || 0,
          distance: point.attributes?.distance || 0,
        })
      })
    })
    return res
  }, [thingTrack])

  const onReachEnd = () => {
    if (hasNextPage) {
      fetchNextPage()
    }
  }

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!track?.length) {
    return <Text text="No Activity Available" />
  }

  return (
    <ScrollableTable
      headers={["timestamp", "speed", "distance", "latLng"]}
      data={track}
      maxHeight={400}
      onReachEnd={onReachEnd}
      footerLoading={isFetchingNextPage}
    />
  )
}

export default TrackTable
