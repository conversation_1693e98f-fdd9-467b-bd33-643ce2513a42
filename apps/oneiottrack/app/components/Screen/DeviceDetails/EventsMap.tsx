import useDuration from "@/bears/useDuration"
import useMapStore from "@/bears/useMapInDeviceDetails"
import useEventList from "@frontend/shared/hooks/geo/useEventList"
import { useEffect } from "react"
import { Marker } from "react-native-maps"

export default function EventsMap({ thingName }: { thingName: string }) {
  const duration = useDuration((state) => state.duration)
  const { data: events } = useEventList({ thingName, duration })
  const mapRef = useMapStore((store) => store.mapRef)

  useEffect(() => {
    if (!events || !mapRef) return

    const latLngArray: {
      latitude: number
      longitude: number
    }[] = []

    events.forEach((trip) => {
      if (trip.latitude && trip.longitude) {
        latLngArray.push({
          latitude: trip.latitude,
          longitude: trip.longitude,
        })
      }
    })

    setTimeout(() => {
      mapRef?.fitToCoordinates(latLngArray, {
        edgePadding: { top: 10, right: 10, bottom: 10, left: 10 },
        animated: true,
      })
    }, 10)
  }, [events, mapRef])

  if (!events?.length) return null

  return (
    <>
      {events?.map((event) => {
        if (event.latitude && event.longitude) {
          return (
            <<PERSON>er
              key={event.id}
              coordinate={{ latitude: event.latitude, longitude: event.longitude }}
            />
          )
        }
        return null
      })}
    </>
  )
}
