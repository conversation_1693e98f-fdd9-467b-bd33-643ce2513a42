import useDuration from "@/bears/useDuration"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useSensorTableData from "@frontend/shared/hooks/geo/useSensorTableData"
import { DashboardGraphItem } from "@frontend/shared/store/store.types"
import { useMemo } from "react"
import { ActivityIndicator, View } from "react-native"

const TimeSeriesTable = ({ thingName }: { thingName: string }) => {
  const duration = useDuration((state) => state.duration)

  const filters = useMemo(() => {
    const res: DashboardGraphItem["filters"] = [
      {
        field: "thingName",
        operation: "is",
        value: [thingName],
        enabled: true,
        id: thingName!,
      },
    ]
    return res
  }, [thingName])

  const {
    isLoading,
    data: paginatedData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useSensorTableData({
    duration,
    filters,
  })

  const { data, keys } = useMemo(() => {
    const payloadKeys = new Set<string>()

    paginatedData?.pages.forEach((page, i) => {
      if (i !== 0) return
      page.data.forEach((item, index) => {
        if (index > 4) return
        Object.keys(item.payload).forEach((key) => {
          payloadKeys.add(key)
        })
      })
    })

    const payloadKeysArr = Array.from(payloadKeys)

    const res: Record<string, any>[] = []

    paginatedData?.pages.forEach((page) => {
      page.data.forEach((item) => {
        const { payload, uniqueId, timestamp } = item
        res.push({
          timestamp,
          uniqueId,
          ...payload,
        })
      })
    })

    return {
      keys: payloadKeysArr,
      data: res,
    }
  }, [paginatedData])

  const onReachEnd = () => {
    if (hasNextPage) {
      fetchNextPage()
    }
  }

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!data?.length) {
    return <Text text="No Activity Available" />
  }

  return (
    <ScrollableTable
      headers={["timestamp", ...keys]}
      data={data}
      maxHeight={400}
      footerLoading={isFetchingNextPage}
      onReachEnd={onReachEnd}
    />
  )
}

export default TimeSeriesTable
