import useDuration from "@/bears/useDuration"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useEventList from "@frontend/shared/hooks/geo/useEventList"
import { ActivityIndicator, View } from "react-native"

const EventsTable = ({ thingName }: { thingName: string }) => {
  const duration = useDuration((state) => state.duration)

  const { data: events, isLoading } = useEventList({ thingName, duration })

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!events?.length) {
    return <Text text="No Activity Available" />
  }

  return (
    <ScrollableTable
      headers={["type", "eventTime", "latitude", "longitude"]}
      data={events}
      maxHeight={400}
    />
  )
}

export default EventsTable
