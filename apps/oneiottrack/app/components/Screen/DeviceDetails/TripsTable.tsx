import useDuration from "@/bears/useDuration"
import { ScrollableTable } from "@/components/ScrollableTable"
import { Text } from "@/components/Text"
import { spacing } from "@/theme"
import useThingTrips from "@frontend/shared/hooks/geo/useThingTrips"
import { ActivityIndicator, View } from "react-native"

const TripsTable = ({ thingName }: { thingName: string }) => {
  const duration = useDuration((state) => state.duration)

  const { data: trips, isLoading } = useThingTrips({ thingName, duration })

  if (isLoading) {
    return (
      <View style={{ height: spacing.xxl }}>
        <ActivityIndicator />
      </View>
    )
  }

  if (!trips?.trips?.length) {
    return <Text text="No Trips Available" />
  }

  return (
    <ScrollableTable
      headers={["startTime", "endTime", "distance", "startAddress", "endAddress"]}
      data={trips.trips || []}
      maxHeight={400}
    />
  )
}

export default TripsTable
