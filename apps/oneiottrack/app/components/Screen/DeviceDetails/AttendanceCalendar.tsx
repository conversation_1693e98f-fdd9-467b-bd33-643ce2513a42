import { colors } from "@/theme"
import useStudentAttendance from "@frontend/shared/hooks/geo/useStudentAttendance"
import { useMemo, useState } from "react"
import { Calendar } from "react-native-calendars"
import { DateData, MarkedDates } from "react-native-calendars/src/types"

const AttendanceCalendar = ({ thingName }: { thingName: string }) => {
  const [duration, setDuration] = useState("this_month")
  const { data: studentAttendance } = useStudentAttendance({
    thingName,
    duration,
    limit: 100,
  })

  const dateColorMap = useMemo(() => {
    const res: MarkedDates = {}

    studentAttendance?.pages.forEach((page) => {
      page.attendance.forEach((point) => {
        if (point.eventType === "enter") {
          res[point.timestamp?.toString().split("T")[0]] = {
            selectedColor: colors.palette.success,
            selected: true,
          }
        }
      })
    })

    return res
  }, [studentAttendance])

  console.log(dateColorMap)

  return (
    <Calendar
      style={{ height: 400 }}
      onMonthChange={(month: DateData) => {
        const date = new Date(month.timestamp)
        setDuration(getMonthBoundaries(date))
      }}
      markedDates={dateColorMap}
    />
  )
}

function getMonthBoundaries(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() // 0-based

  const firstDayOfCurrentMonth = new Date(year, month, 1)
  const firstDayOfNextMonth = new Date(year, month + 1, 1)

  return `${firstDayOfCurrentMonth.toISOString()}TO${firstDayOfNextMonth.toISOString()}`
}
export default AttendanceCalendar
