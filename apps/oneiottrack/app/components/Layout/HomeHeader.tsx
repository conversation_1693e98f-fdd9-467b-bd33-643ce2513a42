import { getActiveRouteName } from "@/navigators"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useNavigation } from "@react-navigation/native"
import React, { ElementRef, useEffect, useRef, useState } from "react"
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  LayoutRectangle,
  View,
  ViewStyle,
} from "react-native"
import { DeviceListItem } from "../DeviceListItem"
import { Icon } from "../Icon"
import { TextField } from "../TextField"
import useDebounce from "@frontend/shared/hooks/utils/useDebounce"
import { EmptyState } from "../EmptyState"
import StatusSelectMenu from "../Screen/DeviceDetails/StatusSelectMenu"
import { GeoThingListItem } from "@frontend/shared/types/geo"
import { useAppSelector } from "redux/store"
import useGeoThingList from "@frontend/shared/hooks/geo/useGeoThingList"

const FILTER_OPTIONS = [
  { label: "Connected", value: "connected" },
  { label: "Disconnected", value: "disconnected" },
  { label: "Inactive", value: "inactive" },
  { label: "Clear Filter", value: "", isDanger: true },
]

export default function HomeHeader({
  onSelectThing,
  clearPrevious,
}: {
  onSelectThing: (thing: GeoThingListItem) => void
  clearPrevious: () => void
}) {
  const navigation = useNavigation()
  const $containerInsets = useSafeAreaInsetsStyle(["top"])
  const activeRoute = getActiveRouteName(navigation.getState())
  const [textInputLayout, setTextInputLayout] = useState<LayoutRectangle>()
  const {
    themed,
    theme: { colors },
  } = useAppTheme()
  const userType = useAppSelector(({ user }) => user.userType)

  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("")
  const [showModal, setShowModal] = useState(false)
  const tenantSubFeature = useAppSelector((state) => state.user.tenant?.subFeature)

  const debouncedSearchQuery = useDebounce(searchQuery, 500)

  const textInputRef = useRef<ElementRef<typeof TextField>>(null)

  const {
    data: thingList,
    refetch: refetchThingList,
    isLoading,
  } = useGeoThingList({
    search: debouncedSearchQuery,
    status: selectedStatus,
  })

  return (
    <View style={themed([$container, $containerInsets])}>
      <View
        style={$wrapper}
        onLayout={({ nativeEvent: { layout } }) => {
          setTextInputLayout(layout)
        }}
      >
        {showModal ? (
          <Icon
            icon="back"
            onPress={() => {
              textInputRef.current?.blur()
              setShowModal(false)
            }}
          />
        ) : activeRoute === "DeviceList" ? (
          <Icon icon="map" onPress={() => navigation.navigate("HomeMap")} />
        ) : (
          <Icon icon="menu" onPress={() => navigation.navigate("DeviceList")} />
        )}
        <TextField
          containerStyle={{ flex: 1, marginHorizontal: spacing.sm }}
          style={$styles.flex1}
          placeholder="Search for devices..."
          ref={textInputRef}
          value={searchQuery}
          onPressIn={() => {
            setShowModal(true)
            clearPrevious()
          }}
          onChangeText={setSearchQuery}
          RightAccessory={() => (
            <View
              style={{
                marginTop: "auto",
                marginBottom: "auto",
                marginRight: spacing.xs,
              }}
            >
              <StatusSelectMenu
                options={FILTER_OPTIONS}
                selected={selectedStatus}
                onSelect={setSelectedStatus}
                // style={{ borderWidth: 0 }}
              />
            </View>
          )}
        />
        {tenantSubFeature !== "studentSafety" && (
          <Icon
            icon="add"
            onPress={() => {
              if (userType === "portal") {
                navigation.navigate("AddDevice")
              } else {
                navigation.navigate("ScanDeviceAdd")
              }
            }}
            size={20}
          />
        )}
        <Icon icon="bell" onPress={() => navigation.navigate("NotificationList")} />
      </View>

      {showModal && textInputLayout && (
        <FlatList
          data={thingList?.thingList}
          style={{
            position: "absolute",
            top: textInputLayout.y + textInputLayout.height,
            width: textInputLayout.width,
            backgroundColor: colors.background,
            zIndex: 10,
            maxHeight: 320,
            borderBottomRightRadius: spacing.md,
            borderBottomLeftRadius: spacing.md,
            alignSelf: "center",
            elevation: 10,
          }}
          keyboardDismissMode="none"
          keyExtractor={(item) => item.thingName}
          renderItem={({ item }) => (
            <DeviceListItem
              style={{
                padding: spacing.xs,
                margin: spacing.xxs,
              }}
              onPress={() => {
                onSelectThing(item)
                setShowModal(false)
              }}
              thing={item}
            />
          )}
          ListEmptyComponent={
            isLoading ? (
              <ActivityIndicator />
            ) : (
              <EmptyState
                heading="No Devices Found"
                buttonOnPress={() => refetchThingList()}
                style={{
                  margin: spacing.xl,
                  marginTop: spacing.xxl,
                }}
              />
            )
          }
        />
      )}
    </View>
  )
}

const useKeyboardVisibility = () => {
  const [isKeyboardVisible, setKeyboardVisible] = useState(false)

  useEffect(() => {
    const showSubscription = Keyboard.addListener("keyboardWillShow", () => {
      setKeyboardVisible(true)
    })

    const hideSubscription = Keyboard.addListener("keyboardWillHide", () => {
      setKeyboardVisible(false)
    })

    // Cleanup subscriptions
    return () => {
      showSubscription.remove()
      hideSubscription.remove()
    }
  }, [])

  return isKeyboardVisible
}

const $wrapper: ViewStyle = {
  height: 56,
  alignItems: "center",
  flexDirection: "row",
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  width: "100%",
  paddingHorizontal: spacing.md,
  gap: spacing.md,
  backgroundColor: colors.card,
  borderBottomColor: colors.border,
  borderBottomWidth: 1,
  elevation: 4,
})

const $textFieldStyle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.card,
})
