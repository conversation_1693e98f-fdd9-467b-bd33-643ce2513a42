import { GestureHandlerRootView } from "react-native-gesture-handler"
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet"
import { ForwardedRef, forwardRef, useCallback, useImperativeHandle, useRef, useState } from "react"
import { Alert, InteractionManager, StyleSheet, View, ViewStyle } from "react-native"
import { $styles, colors, spacing, ThemedStyle } from "@/theme"
import { Text } from "../Text"
import { TextField } from "../TextField"
import { Button } from "../Button"
import { Dropdown } from "react-native-element-dropdown"
import { availableOptions } from "@/bears/useDuration"
import { useAppTheme } from "@/utils/useAppTheme"
import useAddReportMutation from "@frontend/shared/hooks/geo/useAddReportMutation"
import { Toast } from "toastify-react-native"

type Ref = {
  dismiss: () => void
  open: () => void
}

const ExportAttendanceSheet = (props: unknown, ref: ForwardedRef<Ref>) => {
  const bottomSheetRef = useRef<BottomSheet>(null)
  const [isActive, setIsActive] = useState(false)
  const [duration, setDuration] = useState<string>("last_30_days")
  const [email, setEmail] = useState("")
  const { themed } = useAppTheme()

  const addReportMutation = useAddReportMutation({
    onSuccess: () => {
      Toast.success("Report request sent.")
      dismiss()
    },
    onError: (error) => {
      Alert.alert("Error", error.message)
    },
  })
  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      dismiss()
    }
    console.log("handle sheet changes", index)
  }, [])

  const dismiss = useCallback(() => {
    InteractionManager.runAfterInteractions(() => {
      setIsActive(false)
      setEmail("")
    })

    bottomSheetRef.current?.close()
  }, [])

  const open = useCallback(() => {
    setIsActive(true)
    bottomSheetRef.current?.snapToIndex(-1)
  }, [])

  useImperativeHandle(
    ref,
    () => ({
      dismiss,
      open,
    }),
    [dismiss, open],
  )

  if (!isActive) return null

  return (
    <GestureHandlerRootView style={$container}>
      <BottomSheet
        snapPoints={[360]}
        enablePanDownToClose
        ref={bottomSheetRef}
        keyboardBehavior="fillParent"
        onChange={handleSheetChanges}
      >
        <BottomSheetView style={$contentContainer}>
          <TextField
            keyboardType="email-address"
            placeholder="Enter email"
            value={email}
            label="Email"
            autoCapitalize="none"
            onChangeText={setEmail}
          />

          <View
            style={{
              flexDirection: "row",
              alignItems: "flex-start",
              gap: spacing.sm,
              justifyContent: "space-between",
            }}
          >
            <Text text="Select duration" size="sm" weight="medium" />
            <Dropdown
              data={availableOptions}
              style={themed($dropdown)}
              value={availableOptions.find((item) => item.value === duration)}
              labelField="title"
              valueField="value"
              itemTextStyle={{ color: colors.text }}
              selectedTextStyle={{ color: colors.text }}
              onChange={(item) => setDuration(item.value)}
              containerStyle={{ borderColor: colors.border, backgroundColor: colors.card }}
              renderItem={(item) => (
                <View style={themed($row)}>
                  <Text text={item.value} size="sm" weight="medium" />
                </View>
              )}
            />
          </View>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              gap: spacing.sm,
            }}
          >
            <Button
              style={$styles.flex1}
              text="Export"
              preset="filled"
              loading={addReportMutation.isPending}
              onPress={() => {
                if (!email) {
                  Toast.error("Please enter an email")
                  return
                }
                addReportMutation.mutate({
                  title: "Export attendance",
                  duration,
                  type: "tracking",
                  filters: [{ field: "event", operation: "is", value: "geofenceEnter" }],
                  emails: [email],
                })
              }}
            />
            <Button style={$styles.flex1} preset="reversed" text="Cancel" onPress={dismiss} />
          </View>
        </BottomSheetView>
      </BottomSheet>
    </GestureHandlerRootView>
  )
}

const $container: ViewStyle = {
  ...StyleSheet.absoluteFillObject,
  zIndex: 10,
  backgroundColor: colors.palette.overlay20,
}

const $contentContainer: ViewStyle = {
  flex: 1,
  padding: spacing.md,
  gap: spacing.sm,
}

const $dropdown: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 160,
  backgroundColor: colors.background,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: spacing.xs,
})

const $row: ThemedStyle<ViewStyle> = ({ colors }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  backgroundColor: colors.card,
})

export default forwardRef(ExportAttendanceSheet)
