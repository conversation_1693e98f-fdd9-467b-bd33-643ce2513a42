import { FC, useRef } from "react"
import {
  View,
  ScrollView,
  ViewStyle,
  TextStyle,
  Dimensions,
  FlatList,
  ActivityIndicator,
} from "react-native"
import { $styles, colors, spacing, ThemedStyle } from "../theme"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"
import { convertToFormattableDate } from "@frontend/shared/utils/date"

interface TableData {
  [key: string]: any
}

function camelCaseToWords(s: string) {
  const result = s.replace(/([A-Z])/g, " $1")
  return result.charAt(0).toUpperCase() + result.slice(1)
}

interface ScrollableTableProps {
  /**
   * Array of column headers
   */
  headers: string[]
  /**
   * Array of data objects
   */
  data: TableData[]
  /**
   * Maximum height of the table
   */
  maxHeight?: number
  /**
   * Optional style override
   */
  style?: ViewStyle
  /**
   * Optional container style override
   */
  containerStyle?: ViewStyle
  onReachEnd?: () => void
  footerLoading?: boolean
}

export const ScrollableTable: FC<ScrollableTableProps> = ({
  headers,
  data,
  maxHeight = 400,
  style,
  containerStyle,
  onReachEnd,
  footerLoading,
}) => {
  const windowWidth = Dimensions.get("window").width
  const horizontalScrollRef = useRef<ScrollView>(null)
  const verticalScrollRef = useRef<FlatList>(null)
  const { themed } = useAppTheme()

  // Calculate column width - use at least 100 or window width divided by columns
  const columnWidth = Math.max(144, windowWidth / headers.length)

  return (
    <View style={[themed($container), { maxHeight }, containerStyle]}>
      <ScrollView
        ref={horizontalScrollRef}
        horizontal
        nestedScrollEnabled
        showsHorizontalScrollIndicator
        scrollEventThrottle={16}
      >
        <View>
          {/* Header Row */}
          <View style={themed($headerRow)}>
            {headers.map((header, index) => (
              <View key={`header-${index}`} style={[themed($headerCell), { width: columnWidth }]}>
                <Text
                  style={themed($headerText)}
                  text={camelCaseToWords(header)}
                  numberOfLines={2}
                />
              </View>
            ))}
          </View>

          {/* Table Body */}
          <FlatList
            ref={verticalScrollRef}
            nestedScrollEnabled
            showsVerticalScrollIndicator
            scrollEventThrottle={16}
            style={[$tableBody, style]}
            data={data}
            onEndReached={onReachEnd}
            renderItem={({ item: row, index: rowIndex }) => {
              return (
                <View key={`row-${rowIndex}`} style={themed($row)}>
                  {headers.map((header, colIndex) => (
                    <View
                      key={`cell-${rowIndex}-${colIndex}`}
                      style={[themed($cell), { width: columnWidth }]}
                    >
                      <Text
                        size="xs"
                        numberOfLines={3}
                        weight="medium"
                        style={$styles.textCenter}
                        text={
                          !row[header]
                            ? "N/A"
                            : header.includes("Time") || header === "timestamp"
                              ? convertToFormattableDate(row[header] as any)
                              : typeof row[header] === "number"
                                ? row[header].toFixed(2)
                                : row[header].toString()
                        }
                      />
                    </View>
                  ))}
                </View>
              )
            }}
            ListFooterComponent={() => {
              if (footerLoading) {
                return (
                  <View style={{ marginVertical: spacing.md }}>
                    <ActivityIndicator />
                  </View>
                )
              }
              return null
            }}
          />
        </View>
      </ScrollView>
    </View>
  )
}

// Styles using Ignite's pattern
const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: spacing.sm,
  overflow: "hidden",
})

const $headerRow: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  backgroundColor: colors.palette.neutral200,
  borderBottomWidth: 1,
  borderColor: colors.border,
})

const $headerCell: ThemedStyle<ViewStyle> = ({ colors }) => ({
  padding: spacing.sm,
  justifyContent: "center",
  alignItems: "center",
  borderRightWidth: 1,
  borderColor: colors.border,
})

const $headerText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  textAlign: "center",
})

const $tableBody: ViewStyle = {
  flexGrow: 0,
}

const $row: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  borderBottomWidth: 1,
  borderColor: colors.border,
})

const $cell: ThemedStyle<ViewStyle> = ({ colors }) => ({
  padding: spacing.xxs,
  justifyContent: "center",
  borderRightWidth: 1,
  borderColor: colors.border,
  alignItems: "center",
})
