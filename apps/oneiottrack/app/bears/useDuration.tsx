import { create } from "zustand"

interface DurationState {
  duration: string
  setDuration: (duration: string) => void
}

export const availableOptions = [
  { title: "Last 5 min", value: "last_5_mins" },
  { title: "Last 10 min", value: "last_10_mins" },
  { title: "Today", value: "today" },
  { title: "This Week", value: "this_week" },
  { title: "Last 15 minutes", value: "last_15_mins" },
  { title: "Last 30 minutes", value: "last_30_mins" },
  { title: "Last 1 hour", value: "last_60_mins" },
  { title: "Last 24 hour", value: "last_24_hours" },
  { title: "Last 7 days", value: "last_7_days" },
  { title: "Last 30 days", value: "last_30_days" },
  { title: "Last 90 days", value: "last_90_days" },
  { title: "Last 1 year", value: "last_12_Months" },
]

const useDuration = create<DurationState>((set) => ({
  duration: "last_30_days",
  setDuration: (duration) => set({ duration }),
}))

export default useDuration
