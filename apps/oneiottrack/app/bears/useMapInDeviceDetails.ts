import { ElementRef } from "react"
import MapView from "react-native-maps"
import { create } from "zustand"

type State = {
  mapRef: MapView | null
  setMapRef: (ref: ElementRef<typeof MapView>, mapViewMode: string) => void
  mapViewMode: string
}

const useMapStore = create<State>((set) => ({
  mapRef: null,
  setMapRef: (ref: MapView, mapViewMode: string) => set(() => ({ mapRef: ref, mapViewMode })),
  mapViewMode: "Activity",
}))

export default useMapStore
