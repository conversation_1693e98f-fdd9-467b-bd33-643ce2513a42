/**
 * The app navigator (formerly "AppNavigator" and "MainNavigator") is used for the primary
 * navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow which the user will use once logged in.
 */
import { NavigationContainer } from "@react-navigation/native"
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import * as Screens from "@/screens"
import Config from "../config"
import { navigationRef, useBackButtonHandler } from "./navigationUtilities"
import { useAppTheme, useThemeProvider } from "@/utils/useAppTheme"
import { ComponentProps, useCallback, useEffect } from "react"
import { useAppDispatch, useAppSelector } from "redux/store"
import { getAppUserData, getUserData } from "@frontend/shared/store/userSlice"
import { HomeTabNavigator } from "./HomeTabs"
import * as SplashScreen from "expo-splash-screen"
import { getTokens } from "@frontend/shared/utils/httpUtils"

export type DeviceDetailsMode =
  | "Activity"
  | "Trips"
  | "Stops"
  | "TimeSeries"
  | "Track"
  | "Geofences"
  | "Events"
  | "Attendance"

/**
 * This type allows TypeScript to know what routes are defined in this navigator
 * as well as what properties (if any) they might take when navigating to them.
 *
 * If no params are allowed, pass through `undefined`. Generally speaking, we
 * recommend using your MobX-State-Tree store(s) to keep application state
 * rather than passing state through navigation params.
 *
 * For more information, see this documentation:
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Welcome: undefined
  // 🔥 Your screens go here
  HomeMap: undefined
  Settings: undefined
  Login: undefined
  CommingSoon: undefined
  HomeTab: undefined
  DeviceList: undefined
  DeviceDetails: { thingName: string; productName: string; mode?: DeviceDetailsMode }
  AddDevice: undefined
  Register: undefined
  ScanDeviceAdd: undefined
  NotificationList: undefined
  // IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

/**
 * This is a list of all the route names that will exit the app if the back button
 * is pressed while in that screen. Only affects Android.
 */
const exitRoutes = Config.exitRoutes

export type AppStackScreenProps<T extends keyof AppStackParamList> = NativeStackScreenProps<
  AppStackParamList,
  T
>

const Stack = createNativeStackNavigator<AppStackParamList>()

const AppStack = () => {
  const {
    theme: { colors },
  } = useAppTheme()

  const featureType = useAppSelector((state) => state.user.tenant?.featureType)
  const isAuthenticated = useAppSelector(
    (state) => Boolean(state.user.user) || Boolean(state.user.appUser),
  )
  const loading = useAppSelector(({ user }) => user.loading)

  const dispatch = useAppDispatch()

  const loadUserData = useCallback(async () => {
    try {
      const userType = getTokens()?.userType

      if (userType === "appUser") {
        dispatch(getAppUserData())
      } else {
        dispatch(getUserData())
      }
    } catch (error) {
      console.log("login sync failed", error)
    }
  }, [])

  useEffect(() => {
    loadUserData()
  }, [])

  useEffect(() => {
    if (!loading) {
      SplashScreen.hideAsync()
    }
  }, [loading])

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        navigationBarColor: colors.card,
        contentStyle: {
          backgroundColor: colors.card,
        },
      }}
    >
      {!isAuthenticated ? (
        <>
          <Stack.Screen name="Login" component={Screens.LoginScreen} />
          <Stack.Screen name="Register" component={Screens.RegisterScreen} />
        </>
      ) : featureType !== "geo" ? (
        <>
          <Stack.Screen name="CommingSoon" component={Screens.CommingSoonScreen} />
        </>
      ) : (
        <>
          <Stack.Screen name="HomeTab" component={HomeTabNavigator} />
          <Stack.Screen name="DeviceList" component={Screens.DeviceListScreen} />
          <Stack.Screen name="DeviceDetails" component={Screens.DeviceDetailsScreen} />
          <Stack.Screen name="AddDevice" component={Screens.AddDeviceScreen} />

          <Stack.Screen name="ScanDeviceAdd" component={Screens.ScanDeviceAddScreen} />
          <Stack.Screen name="NotificationList" component={Screens.NotificationListScreen} />
          {/* IGNITE_GENERATOR_ANCHOR_APP_STACK_SCREENS */}
        </>
      )}

      <Stack.Screen name="Welcome" component={Screens.WelcomeScreen} />
      {/** 🔥 Your screens go here */}
    </Stack.Navigator>
  )
}

export interface NavigationProps extends Partial<ComponentProps<typeof NavigationContainer>> {}

export const AppNavigator = (props: NavigationProps) => {
  const { themeScheme, navigationTheme, setThemeContextOverride, ThemeProvider } =
    useThemeProvider()
  // usePushNotifications()

  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return (
    <ThemeProvider value={{ themeScheme, setThemeContextOverride }}>
      <NavigationContainer ref={navigationRef} theme={navigationTheme} {...props}>
        <AppStack />
      </NavigationContainer>
    </ThemeProvider>
  )
}
