import { QueryClient } from "@tanstack/react-query"
import { persistQueryClient } from "@tanstack/react-query-persist-client"
import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister"
import { storage } from "@frontend/shared"

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // default: true,
      gcTime: 1000 * 60 * 60 * 24, // 24 hours
    },
  },
})
queryClient.setQueryDefaults(["user"], { staleTime: Infinity })

const storagePersister = createSyncStoragePersister({
  storage,
})

persistQueryClient({
  queryClient,
  persister: storagePersister,
})
