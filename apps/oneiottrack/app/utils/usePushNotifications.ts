import { useState, useEffect, useRef } from "react"
import * as Device from "expo-device"
import * as Notifications from "expo-notifications"
import messaging from "@react-native-firebase/messaging"
import { Platform } from "react-native"
import { storage } from "@frontend/shared"

export interface PushNotificationState {
  expoPushToken?: Notifications.DevicePushToken
  notification?: Notifications.Notification
}

export const usePushNotifications = (): PushNotificationState => {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldPlaySound: true,
      shouldShowAlert: true,
      shouldSetBadge: false,
    }),
  })

  const [expoPushToken, setExpoPushToken] = useState<Notifications.DevicePushToken | undefined>()

  const [notification, setNotification] = useState<Notifications.Notification | undefined>()

  const notificationListener = useRef<Notifications.Subscription>()
  const responseListener = useRef<Notifications.Subscription>()

  async function registerForPushNotificationsAsync() {
    let token: Notifications.DevicePushToken | undefined
    if (Device.isDevice || __DEV__) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync()
      let finalStatus = existingStatus

      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync()
        finalStatus = status
      }
      if (finalStatus !== "granted") {
        alert("Failed to get push token for push notification")
        return
      }

      if (Platform.OS === "ios") {
        const fcmToken = await messaging().getToken()
        storage.setItem("expoPushToken", fcmToken)
        token = {
          type: "ios",
          data: fcmToken,
        }
      } else {
        token = await Notifications.getDevicePushTokenAsync()
        storage.setItem("expoPushToken", token.data)
      }
    }

    if (Platform.OS === "android") {
      Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      })
    }
    return token
  }

  useEffect(() => {
    registerForPushNotificationsAsync().then((token) => {
      setExpoPushToken(token)
    })

    notificationListener.current = Notifications.addNotificationReceivedListener((notification) => {
      console.log(notification)

      setNotification(notification)
    })

    responseListener.current = Notifications.addNotificationResponseReceivedListener((response) => {
      console.log("response", JSON.stringify(response, undefined, 2))
    })

    return () => {
      Notifications.removeNotificationSubscription(notificationListener.current!)

      Notifications.removeNotificationSubscription(responseListener.current!)
    }
  }, [])

  return {
    expoPushToken,
    notification,
  }
}
