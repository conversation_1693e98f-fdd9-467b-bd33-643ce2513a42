import { FC, useEffect, useState } from "react"
import { <PERSON><PERSON>, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@/components"
import { BarcodeScanningResult, CameraView, Camera } from "expo-camera"
import { $styles } from "@/theme"
import { spacing } from "@/theme/spacingDark"
import { useMutation } from "@tanstack/react-query"
import { fetchApi } from "@frontend/shared/utils/httpUtils"
import { URL_MAP } from "@frontend/shared"
import { queryClient } from "@/config/queryClient"
import { useAppSelector } from "redux/store"

export const addUserThing = async (body: {
  email: string
  thingName: string
  extras: Record<string, any>
}) => {
  const fetchResponse = await fetchApi(
    `/app-things/add-user-things`,
    { method: "POST", body },
    URL_MAP.APP_URL,
  )

  const res = await fetchResponse.json()

  if (res.status === "Success") {
    return res.data
  }
  throw new Error(res.message)
}

interface ScanDeviceAddScreenProps extends AppStackScreenProps<"ScanDeviceAdd"> {}

const getDataFromQRCode = (dataStr: string) => {
  try {
    if (dataStr.includes("http")) {
      dataStr = dataStr.split("://")[1]
    }
    const [thingTypeStr, thingNameStr] = dataStr.split(",")

    const thingType = thingTypeStr.split(":")[1]

    const thingName = thingNameStr.split(":")[1]
    return { thingType, thingName }
  } catch (error) {
    return { thingType: "", thingName: "" }
  }
}

export const ScanDeviceAddScreen: FC<ScanDeviceAddScreenProps> = ({ navigation }) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const [scanned, setScanned] = useState(false)
  const appUserEmail = useAppSelector(({ user }) => user.appUser?.email)

  const addDeviceMutation = useMutation({
    mutationFn: addUserThing,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["app-user-things"] })
      Alert.alert("Success", "Device added successfully", [
        {
          text: "Okay",
          onPress: () => navigation.navigate("DeviceList"),
        },
      ])
    },
    onError: (error) => {
      Alert.alert("Error in device registration", error.message)
    },
  })

  useEffect(() => {
    const getBarCodeScannerPermissions = async () => {
      const { status } = await Camera.requestCameraPermissionsAsync()
      setHasPermission(status === "granted")
    }

    getBarCodeScannerPermissions()
  }, [])

  const handleBarcodeScanned = ({ data }: BarcodeScanningResult) => {
    try {
      setScanned(true)
      const { thingName } = getDataFromQRCode(data)
      addDeviceMutation.mutate({
        email: appUserEmail!,
        thingName,
        extras: {
          type: "geo",
        },
      })
    } catch (error) {
      Alert.alert("Failed add device by scanning code", error.message)
    }
  }

  if (hasPermission === null) {
    return <Text>Requesting for camera permission</Text>
  }
  if (hasPermission === false) {
    return <Text>No access to camera</Text>
  }

  return (
    <View style={$root}>
      <Header
        title="Scan To Add Device"
        leftIcon="back"
        onLeftPress={() => {
          navigation.goBack()
        }}
      />
      {hasPermission === null ? (
        <View style={$styles.centerInFlex1}>
          <Text>Requesting for camera permission</Text>
        </View>
      ) : !hasPermission ? (
        <View style={$styles.centerInFlex1}>
          <Text>No access to camera</Text>
        </View>
      ) : (
        <CameraView
          onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ["qr", "pdf417"],
          }}
          style={$styles.fullHightWidth}
        />
      )}
      {scanned && (
        <Button
          text={"Tap to Scan Again"}
          style={{
            position: "absolute",
            bottom: 36,
            left: "auto",
            right: "auto",
            marginHorizontal: spacing.md,
          }}
          onPress={() => setScanned(false)}
        />
      )}
    </View>
  )
}

const $root: ViewStyle = {
  flex: 1,

  position: "relative",
}
