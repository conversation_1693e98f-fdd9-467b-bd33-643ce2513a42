import { DeviceListItem, EmptyState, Header, Icon, Screen, TextField } from "@/components"
import StatusSelectMenu from "@/components/Screen/DeviceDetails/StatusSelectMenu"
import { AppStackScreenProps } from "@/navigators"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import useInfiniteGeoThingList from "@frontend/shared/hooks/geo/useIfiniteGeoThingList"
import useDebounce from "@frontend/shared/hooks/utils/useDebounce"
import { FC, useMemo, useState } from "react"
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  RefreshControl,
  View,
  ViewStyle,
} from "react-native"

interface DeviceListScreenProps extends AppStackScreenProps<"DeviceList"> {}

const FILTER_OPTIONS = [
  { label: "Connected", value: "connected" },
  { label: "Disconnected", value: "disconnected" },
  { label: "Inactive", value: "inactive" },
  { label: "Clear Filter", value: "", isDanger: true },
]

export const DeviceListScreen: FC<DeviceListScreenProps> = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("")

  const debouncedSearchQuery = useDebounce(searchQuery, 500)

  const {
    theme: { colors },
    themed,
  } = useAppTheme()

  const {
    data: paginatedThingList,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    isLoading,
    isRefetching,
    error,
    refetch: refetchThingList,
  } = useInfiniteGeoThingList({
    thingName: debouncedSearchQuery,
    sidebar: true,
    status: selectedStatus,
  })
  console.log(paginatedThingList?.pages[0].thingList?.length)

  const onEndReached = () => {
    if (hasNextPage) {
      fetchNextPage()
    }
  }

  const thingList = useMemo(() => {
    if (!paginatedThingList) return []
    return paginatedThingList.pages.flatMap((page) => page.thingList)
  }, [paginatedThingList])

  return (
    <Screen style={[$root, { backgroundColor: colors.background }]}>
      <Header
        title="Device List"
        leftIcon="back"
        onLeftPress={() => {
          navigation.goBack()
        }}
      />

      <FlatList
        data={thingList}
        onEndReached={onEndReached}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching}
            onRefresh={() => {
              refetchThingList()
            }}
          />
        }
        keyboardDismissMode="none"
        ListHeaderComponent={
          <View style={$listHeader}>
            <TextField
              containerStyle={[$styles.flex1, themed($textFieldStyle)]}
              value={searchQuery}
              placeholder="Search thing..."
              LeftAccessory={() => (
                <Icon
                  icon="search"
                  containerStyle={[$styles.myAuto, { marginLeft: spacing.xs }]}
                  size={18}
                  color={colors.textDim}
                />
              )}
              RightAccessory={() => (
                <Icon
                  icon="x"
                  size={20}
                  color={colors.textDim}
                  onPress={() => {
                    Keyboard.dismiss()
                    setSearchQuery("")
                  }}
                  containerStyle={[$styles.myAuto, { marginRight: spacing.xs }]}
                />
              )}
              onChangeText={setSearchQuery}
            />
            <StatusSelectMenu
              options={FILTER_OPTIONS}
              selected={selectedStatus}
              onSelect={setSelectedStatus}
              style={$listHeaderRight}
            />
          </View>
        }
        ListEmptyComponent={
          isLoading ? (
            <ActivityIndicator />
          ) : (
            <EmptyState
              heading="No Devices Found"
              buttonOnPress={() => refetchThingList()}
              style={{
                margin: spacing.xl,
                marginTop: spacing.xxl,
              }}
            />
          )
        }
        renderItem={({ item: thing }) => (
          <DeviceListItem
            thing={thing}
            onPress={() => {
              Keyboard.dismiss()
              navigation.navigate("DeviceDetails", {
                thingName: thing.thingName,
                productName: thing.productName,
              })
            }}
          />
        )}
        keyExtractor={(item) => item.thingName}
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={{ marginVertical: spacing.md, marginBottom: spacing.xxxl * 2.3 }}>
              <ActivityIndicator />
            </View>
          ) : (
            <View style={{ height: spacing.xxl * 3 }} />
          )
        }
      />
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

const $listHeader: ViewStyle = {
  margin: spacing.sm,
  flexDirection: "row",
  gap: spacing.xs,
  alignItems: "center",
}
const $textFieldStyle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.card,
})

const $listHeaderRight: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
}
