import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { EmptyState, Screen } from "@/components"
import { useAppDispatch } from "redux/store"
import { logoutUser } from "@frontend/shared/store/userSlice"
import { spacing } from "@/theme"
// import { useNavigation } from "@react-navigation/native"

interface CommingSoonScreenProps extends AppStackScreenProps<"CommingSoon"> {}

export const CommingSoonScreen: FC<CommingSoonScreenProps> = () => {
  // Pull in navigation via hook
  // const navigation = useNavigation()

  const dispatch = useAppDispatch()

  const handleLogout = () => {
    dispatch(logoutUser())
  }

  return (
    <Screen style={$root}>
      <EmptyState
        heading="App features not available yet."
        content="This tenant does not have any app features enabled. Please contact your admin to enable the features."
        button="Log Out"
        buttonOnPress={handleLogout}
        style={{ marginHorizontal: spacing.lg }}
      />
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}
