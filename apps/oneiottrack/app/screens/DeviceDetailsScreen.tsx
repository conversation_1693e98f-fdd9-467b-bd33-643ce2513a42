import useMapStore from "@/bears/useMapInDeviceDetails"
import { Screen, Text } from "@/components"
import AttendanceCalendar from "@/components/Screen/DeviceDetails/AttendanceCalendar"
import DeviceDetailsCard from "@/components/Screen/DeviceDetails/DevicDetailsCard"
import DeviceDetailsHeader from "@/components/Screen/DeviceDetails/DeviceDetailsHeader"
import DeviceDetailsMap from "@/components/Screen/DeviceDetails/DeviceDetailsMap"
import DeviceDetailsTable from "@/components/Screen/DeviceDetails/DeviceDetailsTable"
import { AppStackParamList, AppStackScreenProps, DeviceDetailsMode } from "@/navigators"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import useGeoThingDetails from "@frontend/shared/hooks/geo/useGeoThingDetails"
import { RouteProp, useRoute } from "@react-navigation/native"
import { FC, memo, useEffect, useMemo, useRef } from "react"
import { ActivityIndicator, FlatList, TouchableOpacity, View, ViewStyle } from "react-native"
import MapView, { PROVIDER_GOOGLE } from "react-native-maps"
import { useAppSelector } from "redux/store"

interface DeviceDetailsScreenProps extends AppStackScreenProps<"DeviceDetails"> {}

const TABS: { title: DeviceDetailsMode }[] = [
  { title: "Activity" },
  { title: "Trips" },
  { title: "Stops" },
  { title: "Events" },
  { title: "TimeSeries" },
  { title: "Track" },
]

export const DeviceDetailsScreen: FC<DeviceDetailsScreenProps> = ({
  route: {
    params: { thingName, mode = "Activity" },
  },
  navigation,
}) => {
  // Pull in navigation via hook
  // const navigation = useNavigation()
  const tenant = useAppSelector((state) => state.user.tenant)

  const { data: thingDetails, isLoading } = useGeoThingDetails({ thingName })
  const { themed, theme } = useAppTheme()

  const bottomTabs = useMemo(() => {
    if (tenant?.subFeature === "studentSafety" && thingDetails?.productName === "student") {
      return [
        ...TABS,
        {
          title: "Attendance",
        },
      ]
    }
    return TABS
  }, [tenant?.subFeature, thingDetails?.productName])

  if (isLoading || !thingDetails) {
    return (
      <View style={$styles.centerInFlex1}>
        <ActivityIndicator />
      </View>
    )
  }

  const setMode = (mode: DeviceDetailsMode) => {
    navigation.setParams({
      mode,
    })
  }
  return (
    <>
      <Screen style={$root} preset="scroll" contentContainerStyle={$conatiner}>
        <DeviceDetailsHeader />

        <MemorizedMap lat={thingDetails.lat} long={thingDetails.long} />

        <DeviceDetailsCard />
        {(tenant?.subFeature === "studentSafety" && thingDetails.productName) === "student" ? (
          <AttendanceCalendar thingName={thingDetails.thingName} />
        ) : null}

        <DeviceDetailsTable />

        <View style={{ height: spacing.xxxl }} />
        <FlatList
          data={bottomTabs}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => setMode(item.title)}
              disabled={mode === item.title}
              style={[
                {
                  backgroundColor:
                    mode === item.title ? theme.colors.brandColor : theme.colors.background,
                },
                themed($flatListItem),
              ]}
            >
              <Text
                text={item.title}
                style={{
                  color: mode === item.title ? "black" : theme.colors.text,
                }}
                weight="medium"
              />
            </TouchableOpacity>
          )}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={$flatListContainer}
          style={themed($flatList)}
        />
      </Screen>
    </>
  )
}

type DetailsScreenRouteProp = RouteProp<AppStackParamList, "DeviceDetails">

const DeviceMap = ({ lat, long }: { lat: number; long: number }) => {
  const mapRef = useRef<MapView>(null)
  const setMapRef = useMapStore((state) => state.setMapRef)

  const {
    params: { mode = "Activity" },
  } = useRoute<DetailsScreenRouteProp>()

  useEffect(() => {
    if (!mapRef.current) return
    setMapRef(mapRef.current, mode)
  }, [mode, setMapRef, mapRef])

  return (
    <View style={$mapContainer}>
      <MapView
        zoomControlEnabled
        ref={mapRef}
        style={$map}
        key={`device-details-map-${mode}`}
        provider={PROVIDER_GOOGLE}
        initialRegion={{
          latitude: lat,
          longitude: long,
          latitudeDelta: 0.1,
          longitudeDelta: 0.1,
        }}
      >
        <DeviceDetailsMap />
      </MapView>
    </View>
  )
}

const MemorizedMap = memo(DeviceMap)

const $root: ViewStyle = {
  flex: 1,
}

const $flatList: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  bottom: 12,
  left: 12,
  right: 12,
  backgroundColor: colors.card,
  borderRadius: 12,
  borderWidth: 1,
  borderColor: colors.border,
})

const $mapContainer: ViewStyle = {
  paddingHorizontal: spacing.md,
}

const $map: ViewStyle = {
  height: 240,
  width: "100%",
  borderRadius: 16,
}

const $flatListItem: ThemedStyle<ViewStyle> = ({ colors }) => ({
  padding: 8,
  marginRight: 16,
  borderRadius: 4,
  paddingHorizontal: 16,
  borderWidth: 1,
  borderColor: colors.border,
})

const $conatiner: ViewStyle = {
  position: "relative",
  minHeight: "100%",
  gap: spacing.md,
}

const $flatListContainer: ViewStyle = {
  padding: 8,
  paddingRight: 0,
}
