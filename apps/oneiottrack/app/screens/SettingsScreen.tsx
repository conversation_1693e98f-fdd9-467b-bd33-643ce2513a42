import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Header, <PERSON>, Switch, Text } from "@/components"
import { DemoTabScreenProps } from "@/navigators/HomeTabs"
import { colors, spacing } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { usePushNotifications } from "@/utils/usePushNotifications"

import { logoutUser } from "@frontend/shared/store/userSlice"
import { FC, useMemo } from "react"
import { ImageStyle, View, ViewStyle } from "react-native"
import { useAppDispatch, useAppSelector } from "redux/store"
// import { useNavigation } from "@react-navigation/native"

interface SettingsScreenProps extends DemoTabScreenProps<"Settings"> {}

const PROFILE_IMAGE = "https://cdn-icons-png.flaticon.com/512/149/149071.png"

export const SettingsScreen: FC<SettingsScreenProps> = () => {
  const { expoPushToken } = usePushNotifications()
  const tenant = useAppSelector((state) => state.user.tenant)
  const appUser = useAppSelector((state) => state.user.appUser)
  const options = useMemo(() => {
    return [
      { label: "Name", value: appUser?.name ?? tenant?.name },
      { label: "Email", value: appUser?.email ?? tenant?.email },
      { label: "Phone", value: appUser?.phone ?? tenant?.phone },
      { label: "Address", value: appUser?.address ?? tenant?.address },
      { label: "Pincode", value: appUser?.pincode ?? tenant?.pincode },
    ]
  }, [tenant, appUser])

  const dispatch = useAppDispatch()
  const handleLogout = () => {
    dispatch(logoutUser(expoPushToken?.data))
  }

  const {
    setThemeContextOverride, // Function to set the theme
    themeContext, // The current theme context ("light" | "darK")
  } = useAppTheme()

  const toggleDarkMode = () => {
    setThemeContextOverride(themeContext === "dark" ? "light" : "dark")
  }

  return (
    <Screen style={$root} preset="scroll">
      <Header title="Settings" />
      <AutoImage style={$imageStyle} source={{ uri: PROFILE_IMAGE }} maxWidth={100} />

      <View style={{ marginBottom: spacing.md, marginHorizontal: spacing.md, gap: spacing.sm }}>
        {options.map((option) => (
          <View style={$row} key={option.label}>
            <Text
              text={option.label}
              style={{ minWidth: 5 * spacing.lg, color: colors.textDim }}
              size="xs"
              weight="medium"
            />
            <Text text={option.value} style={{ flexShrink: 1 }} size="xs" weight="medium" />
          </View>
        ))}

        <View style={$row}>
          <Text
            text="Dark mode"
            style={{ minWidth: 5 * spacing.lg, color: colors.textDim }}
            size="xs"
            weight="medium"
          />
          <Switch value={themeContext === "dark"} onValueChange={toggleDarkMode} />
        </View>
      </View>

      <Button
        text="Logout"
        onPress={handleLogout}
        style={{ marginHorizontal: spacing.md, marginTop: spacing.lg }}
      />
    </Screen>
  )
}
const $imageStyle: ImageStyle = {
  width: 100,
  height: 100,
  borderRadius: 100,
  marginLeft: "auto",
  marginRight: "auto",
  marginVertical: spacing.md,
}

const $row: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  height: 24,
}

const $root: ViewStyle = {
  flex: 1,
}
