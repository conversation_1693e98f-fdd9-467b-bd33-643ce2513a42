import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface AddDeviceScreenProps extends AppStackScreenProps<"AddDevice"> {}

export const AddDeviceScreen: FC<AddDeviceScreenProps> = ({ navigation }) => {
  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Header
        title="Device Details"
        leftIcon="back"
        onLeftPress={() => {
          navigation.goBack()
        }}
      />
      <Text text="addDevice" />
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
}
