import { FC, useState } from "react"
import { Alert, ImageStyle, ScrollView, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { AutoImage, Button, Icon, Screen, Text, TextField } from "@/components"
import { spacing, ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { addAppUserRegisterApi } from "@frontend/shared/api/user"
import { useMutation } from "@tanstack/react-query"
import { colors } from "@/theme/colorsDark"
// import { useNavigation } from "@react-navigation/native"

interface RegisterScreenProps extends AppStackScreenProps<"Register"> {}

const userDefaultValues = {
  userName: __DEV__ ? "supriyo2" : "",
  email: __DEV__ ? "<EMAIL>" : "",
  address: __DEV__ ? "India" : "",
  phone: __DEV__ ? "+************" : "",
  pincode: __DEV__ ? "999999" : "",
  password: __DEV__ ? "Qwertyuiop@123" : "",
  confirmPassword: __DEV__ ? "Qwertyuiop@123" : "",
  tenant: __DEV__ ? "rado" : "",
}

export const RegisterScreen: FC<RegisterScreenProps> = ({ navigation }) => {
  // Pull in navigation via hook
  // const navigation = useNavigation()

  const [userValues, setUserValues] = useState(userDefaultValues)
  const [showPassword, setShowPassword] = useState(true)

  const addUserMutation = useMutation({
    mutationFn: addAppUserRegisterApi,
    onSuccess: () => {
      Alert.alert("User registered successfully", "Please login to continue", [
        {
          text: "Login",
          onPress: () => navigation.navigate("Login"),
        },
      ])
    },
    onError: (error) => {
      Alert.alert("Error in user registration", error.message)
    },
  })

  const onChangeText = (key: keyof typeof userValues, value: string) => {
    setUserValues((prev) => ({ ...prev, [key]: value }))
  }

  const {
    themed,
    theme: { colors },
    themeContext,
  } = useAppTheme()

  const safeAreaInsetsStyle = useSafeAreaInsetsStyle(["top", "bottom"])

  return (
    <Screen
      style={[themed($root), safeAreaInsetsStyle]}
      contentContainerStyle={{
        gap: spacing.sm,
        flex: 1,
      }}
      preset="scroll"
    >
      <AutoImage
        source={
          themeContext === "light"
            ? require("../../assets/logos/brandLogoBlack.png")
            : require("../../assets/logos/brandLogoWhite.png")
        }
        style={$logoImage}
        resizeMode="contain"
      />
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          gap: spacing.sm,
          padding: spacing.sm,
        }}
        showsVerticalScrollIndicator={false}
      >
        <TextField
          label="Username"
          placeholder="Enter username"
          value={userValues.userName}
          onChangeText={(value) => onChangeText("userName", value)}
          LeftAccessory={(props) => (
            <Icon
              icon="menu"
              size={20}
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
        />
        <TextField
          label="Email"
          placeholder="Enter email"
          value={userValues.email}
          onChangeText={(value) => onChangeText("email", value)}
          LeftAccessory={(props) => (
            <Icon
              icon="mail"
              size={20}
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
        />
        <TextField
          label="Address"
          placeholder="Enter address"
          value={userValues.address}
          onChangeText={(value) => onChangeText("address", value)}
          LeftAccessory={(props) => (
            <Icon
              icon="map"
              size={20}
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
        />
        <TextField
          label="Phone"
          placeholder="Enter phone number"
          value={userValues.phone}
          onChangeText={(value) => onChangeText("phone", value)}
          LeftAccessory={(props) => (
            <Icon
              icon="phone"
              size={20}
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
        />
        <TextField
          label="Pincode"
          placeholder="Enter pincode"
          value={userValues.pincode}
          onChangeText={(value) => onChangeText("pincode", value)}
          LeftAccessory={(props) => (
            <Icon
              icon="map"
              size={20}
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
        />

        <TextField
          label="Tenant"
          placeholder="Enter tenant"
          value={userValues.tenant}
          onChangeText={(value) => onChangeText("tenant", value)}
          LeftAccessory={(props) => (
            <Icon
              icon="menu"
              size={20}
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
        />

        <TextField
          label="Password"
          placeholder="Enter password"
          value={userValues.password}
          secureTextEntry={showPassword}
          LeftAccessory={(props) => (
            <Icon
              icon="lock"
              containerStyle={props.style}
              color={props.editable ? colors.brandColor : colors.text}
            />
          )}
          RightAccessory={(props) => (
            <Icon
              icon={showPassword ? "hidden" : "view"}
              containerStyle={props.style}
              color={props.editable ? colors.textDim : colors.text}
              onPress={() => setShowPassword((prev) => !prev)}
            />
          )}
          onChangeText={(value) => onChangeText("password", value)}
        />
      </ScrollView>
      <Button
        text="Register"
        preset="filled"
        style={$button}
        disabled={addUserMutation.isPending}
        loading={addUserMutation.isPending}
        onPress={() => addUserMutation.mutate(userValues)}
      />

      <View style={$footer}>
        <Text text="Already have an account?" />
        <Button
          textStyle={{ color: colors.brandColor }}
          preset="none"
          text="Login"
          onPress={() => navigation.navigate("Login")}
        />
      </View>
    </Screen>
  )
}

const $root: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  backgroundColor: colors.dark2,
})

const $button: ViewStyle = {
  marginTop: spacing.sm,
  backgroundColor: colors.brandColor,
}

const $footer: ViewStyle = {
  flexDirection: "row",
  gap: spacing.md,
  alignItems: "center",
  justifyContent: "center",
  marginVertical: spacing.md,
  marginBottom: spacing.xl,
}

const $logoImage: ImageStyle = { height: 40, width: "auto", marginVertical: spacing.md }
