import { AutoImage, Button, EnvironmentSwitch, Icon, Screen, Text, TextField } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { colors } from "@/theme/colorsDark"
import { useAppTheme } from "@/utils/useAppTheme"
import { usePushNotifications } from "@/utils/usePushNotifications"
import { appUserLoginApi, login } from "@frontend/shared/api/user"
import { getAppUserData, getUserData } from "@frontend/shared/store/userSlice"
import { setTokens } from "@frontend/shared/utils/httpUtils"
import { FC, useState } from "react"
import { Alert, ImageStyle, Platform, TouchableOpacity, View, ViewStyle } from "react-native"
import { useAppDispatch } from "redux/store"

interface LoginScreenProps extends AppStackScreenProps<"Login"> {}

export const LoginScreen: FC<LoginScreenProps> = ({ navigation }) => {
  const { themed } = useAppTheme()
  const { expoPushToken } = usePushNotifications()
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState(__DEV__ ? "<EMAIL>" : "")
  const [password, setPassword] = useState(__DEV__ ? "Admin.vanix@33" : "")
  const [isLoading, setIsLoading] = useState(false)

  const dispatch = useAppDispatch()
  const {
    themeContext,
    theme: { colors },
  } = useAppTheme()

  const handleLogin = async () => {
    if (!username || !password) {
      Alert.alert("Error", "Please fill in all fields")
      return
    }

    setIsLoading(true)

    try {
      try {
        const appUserLoginResp = await appUserLoginApi({
          username,
          password,
        })

        if (appUserLoginResp) {
          setTokens({
            accessToken: appUserLoginResp.token,
            refreshToken: appUserLoginResp.refreshToken,
            userType: "appUser",
          })

          dispatch(getAppUserData({ token: expoPushToken?.data, deviceOS: Platform.OS }))
          setIsLoading(false)
          return
        }
      } catch (error) {
        console.log("failed to login as app user")
      }

      const resp = await login({
        email: username,
        password,
      })

      if (resp.status === "Success") {
        setTokens({ accessToken: resp.token, refreshToken: resp.refreshToken })
        dispatch(getUserData())
        return
      }

      throw new Error(`Login failed. ${resp.message}`)
    } catch (error) {
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Login failed. Please try again.",
      )
    } finally {
      setIsLoading(false)
    }
  }

  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <>
      <Screen style={themed($root)}>
        <AutoImage
          source={
            themeContext === "light"
              ? require("../../assets/logos/brandLogoBlack.png")
              : require("../../assets/logos/brandLogoWhite.png")
          }
          style={$logoImage}
          resizeMode="contain"
        />

        <TextField
          value={username}
          onChangeText={setUsername}
          placeholder="Enter username"
          autoCapitalize="none"
          autoComplete="username"
          autoCorrect={false}
          label="Email"
          LeftAccessory={() => (
            <Icon
              icon="mail"
              size={20}
              color={colors.brandColor}
              containerStyle={[$styles.myAuto, { marginLeft: spacing.xs }]}
            />
          )}
          textContentType="emailAddress"
          containerStyle={$textField}
        />

        <TextField
          value={password}
          onChangeText={setPassword}
          placeholder="Enter password"
          autoCapitalize="none"
          autoComplete="password"
          autoCorrect={false}
          secureTextEntry={showPassword}
          LeftAccessory={() => (
            <Icon
              icon="lock"
              size={20}
              color={colors.brandColor}
              containerStyle={[$styles.myAuto, { marginLeft: spacing.xs }]}
            />
          )}
          RightAccessory={() => (
            <TouchableOpacity
              onPress={() => {
                setShowPassword(!showPassword)
              }}
              style={[$styles.myAuto, { marginRight: spacing.xs }]}
            >
              {showPassword ? (
                <Icon icon="hidden" size={20} color={colors.textDim} />
              ) : (
                <Icon icon="view" size={20} color={colors.textDim} />
              )}
            </TouchableOpacity>
          )}
          containerStyle={$textField}
          label="Password"
        />

        <Button
          text="Login"
          preset="filled"
          style={$button}
          onPress={handleLogin}
          disabled={isLoading}
          loading={isLoading}
        />

        <View style={$footer}>
          <Text text="Don't have an account?" />
          <Button
            textStyle={{ color: colors.brandColor }}
            preset="none"
            text="Register"
            onPress={() => navigation.navigate("Register")}
          />
        </View>
      </Screen>
      <EnvironmentSwitch />
    </>
  )
}

const $root: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  backgroundColor: colors.dark2,
  justifyContent: "center",
})

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $button: ViewStyle = {
  marginTop: spacing.md,
  backgroundColor: colors.brandColor,
}

const $footer: ViewStyle = {
  flexDirection: "row",
  gap: spacing.md,
  alignItems: "center",
  justifyContent: "center",
  marginTop: spacing.xl,
}

const $logoImage: ImageStyle = { height: 60, width: "auto", marginBottom: spacing.xl * 2 }
