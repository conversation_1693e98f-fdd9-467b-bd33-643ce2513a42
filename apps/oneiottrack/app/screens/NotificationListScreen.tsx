import { EmptyState, Header, NotificationItem, Screen } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { spacing } from "@/theme/spacingDark"
import { notification } from "@frontend/shared"
import { useNavigation } from "@react-navigation/native"
import { FC } from "react"
import { FlatList, RefreshControl, ViewStyle } from "react-native"

// import { useNavigation } from "@react-navigation/native"

interface NotificationListScreenProps extends AppStackScreenProps<"NotificationList"> {}

export const NotificationListScreen: FC<NotificationListScreenProps> = () => {
  const navigation = useNavigation()
  const {
    data: notificationList,
    refetch: refetchNotificationList,
    isRefetching,
    isLoading,
  } = notification.useNotificationHistory({
    page: 1,
    limit: 100,
  })

  return (
    <Screen style={$root} preset="fixed">
      <Header title="Notification List" leftIcon="back" onLeftPress={() => navigation.goBack()} />
      <FlatList
        data={notificationList?.notificationHistoryList || []}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching && !isLoading}
            onRefresh={refetchNotificationList}
          />
        }
        contentContainerStyle={{ paddingTop: spacing.sm }}
        ListEmptyComponent={
          <EmptyState
            imageSource={require("../../assets/images/no-notification.png")}
            heading="No Notification Available"
            content=""
            button=""
          />
        }
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item, index }) => <NotificationItem item={item} index={index} />}
      />
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
}
