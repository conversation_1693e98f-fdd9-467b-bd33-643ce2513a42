import { Icon, Screen, Text } from "@/components"
import { ElementRef, FC, memo, useCallback, useEffect, useMemo, useRef, useState } from "react"
import { Image, TouchableOpacity, View, ViewStyle } from "react-native"
// import { useNavigation } from "@react-navigation/native"
import HomeHeader from "@/components/Layout/HomeHeader"
import { DemoTabScreenProps } from "@/navigators/HomeTabs"
import { $styles, spacing, ThemedStyle } from "@/theme"
import { geoHooks } from "@frontend/shared"
import { GeoThingListItem } from "@frontend/shared/types/geo"
import { useNavigation } from "@react-navigation/native"
import MapView, { Marker, PROVIDER_GOOGLE } from "react-native-maps"
import Animated, { FadeIn, FadeOutDown } from "react-native-reanimated"
import { useAppTheme } from "@/utils/useAppTheme"
import useSelectedDeviceId from "@/hooks/useSelectedDeviceId"
const greenMarkerImage = require("../../assets/geo-images/circlePointGreen.png")
const redMarkerImage = require("../../assets/geo-images/circlePointRed.png")

interface HomeMapScreenProps extends DemoTabScreenProps<"HomeMap"> {}

export const HomeMapScreen: FC<HomeMapScreenProps> = () => {
  // Pull in navigation via hook
  // const navigation = useNavigation()
  const {
    data: thingsList,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = geoHooks.useInfiniteGeoThingList()

  const mapRef = useRef<ElementRef<typeof MapView>>(null)
  const { themed } = useAppTheme()

  const selectedDeviceId = useSelectedDeviceId((store) => store.selectedDeviceId)
  const setSelectedDeviceId = useSelectedDeviceId((store) => store.setSelectedDeviceId)

  useEffect(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [isFetchingNextPage, hasNextPage, fetchNextPage])

  const allMarkers = useMemo(() => {
    const _allMarkers: GeoThingListItem[] = []

    if (!thingsList?.pages?.length) {
      return []
    }

    for (let i = 0; i < thingsList.pages.length; i++) {
      const page = thingsList.pages[i]

      if (!page?.thingList) {
        // eslint-disable-next-line no-continue
        continue
      }

      for (let j = 0; j < page?.thingList?.length; j++) {
        const currentThing = page?.thingList[j]
        if (currentThing?.status === "disconnected" || currentThing?.status === "connected") {
          if (currentThing.lat === 0 && currentThing.long === 0) continue
          _allMarkers.push(currentThing)
        }
      }
    }

    return _allMarkers
  }, [thingsList])

  useEffect(() => {
    if (!allMarkers?.length) return
    if (!selectedDeviceId && selectedDeviceId !== null) {
      setSelectedDeviceId(allMarkers[0].thingName)
    }
    const coordinates = allMarkers.map((marker) => ({
      latitude: marker.lat,
      longitude: marker.long,
    }))

    mapRef.current?.fitToCoordinates(coordinates, {
      edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
      animated: true,
    })
  }, [allMarkers, selectedDeviceId])

  const selectedMarker = useMemo(() => {
    if (!selectedDeviceId) {
      return undefined
    }

    return allMarkers.find((marker) => marker.thingName === selectedDeviceId)
  }, [allMarkers, selectedDeviceId])

  const handleZoomIn = async () => {
    const camera = await mapRef.current?.getCamera()

    const { zoom: zoomLevel } = camera || {}
    if (!zoomLevel) return
    if (zoomLevel < 20) {
      const newZoom = zoomLevel + 1

      mapRef.current?.animateCamera({ zoom: newZoom }, { duration: 300 })
    }
  }

  const onMarkerPress = useCallback((marker: GeoThingListItem) => {
    setSelectedDeviceId(marker.thingName)
    mapRef.current?.animateCamera({
      center: {
        latitude: marker.lat,
        longitude: marker.long,
      },
      zoom: 12,
    })
  }, [])

  const onClose = useCallback(() => {
    setSelectedDeviceId(null)
    const coordinates = allMarkers.map((marker) => ({
      latitude: marker.lat,
      longitude: marker.long,
    }))

    mapRef.current?.fitToCoordinates(coordinates, {
      edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
      animated: true,
    })
  }, [allMarkers])

  const handleZoomOut = async () => {
    const camera = await mapRef.current?.getCamera()

    const { zoom: zoomLevel } = camera || {}
    if (!zoomLevel) return
    if (zoomLevel > 1) {
      const newZoom = zoomLevel - 1
      mapRef.current?.animateCamera({ zoom: newZoom }, { duration: 300 })
    }
  }

  const renderMarkers = useCallback(() => {
    return allMarkers.map((marker) => (
      <CustomMarker key={marker.thingName} marker={marker} onMarkerPress={onMarkerPress} />
    ))
  }, [allMarkers, onMarkerPress])

  return (
    <Screen style={$root}>
      <HomeHeader onSelectThing={onMarkerPress} clearPrevious={onClose} />
      <MapView
        provider={PROVIDER_GOOGLE}
        zoomControlEnabled
        key={"home-map"}
        ref={mapRef}
        initialRegion={{
          latitude: 16.75196192774172,
          latitudeDelta: 43.58162118824247,
          longitude: 80.52314149215817,
          longitudeDelta: 21.10152941197157,
        }}
        style={$styles.fullHightWidth}
      >
        {renderMarkers()}
      </MapView>

      <View style={$buttonContainer}>
        <TouchableOpacity style={themed($button)} onPress={handleZoomIn}>
          <Text text="+" weight="bold" size="lg" />
        </TouchableOpacity>
        <TouchableOpacity style={themed($button)} onPress={handleZoomOut}>
          <Text text="-" weight="bold" size="lg" />
        </TouchableOpacity>
      </View>

      <SelectedMarkerDetailModal selectedMarker={selectedMarker} onClose={onClose} />
    </Screen>
  )
}

const MarkerWithoutMemo = ({
  marker,
  onMarkerPress,
}: {
  marker: GeoThingListItem
  onMarkerPress: (marker: GeoThingListItem) => void
}) => {
  const markerRef = useRef<typeof Marker>(null)

  const doRedraw = () => {
    markerRef.current?.redraw()
  }

  return (
    <Marker
      ref={markerRef}
      tracksViewChanges={false}
      key={marker.thingName}
      coordinate={{ latitude: marker.lat, longitude: marker.long }}
      title={marker.thingName}
      onPress={() => onMarkerPress(marker)}
    >
      <View>
        <Image
          fadeDuration={0}
          onLoadEnd={doRedraw}
          source={marker.status === "connected" ? greenMarkerImage : redMarkerImage}
          style={{ width: 24, height: 24, resizeMode: "contain" }}
        />
      </View>
    </Marker>
  )
}

const CustomMarker = memo(MarkerWithoutMemo)

const SelectedMarkerDetailModal = ({
  selectedMarker,
  onClose,
}: {
  selectedMarker?: GeoThingListItem
  onClose: () => void
}) => {
  const navigation = useNavigation()

  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  if (!selectedMarker) return null

  const options = [
    { label: "Display Name :", value: selectedMarker.displayName },
    { label: "Product Name :", value: selectedMarker.productName },
    { label: "Last Updated :", value: new Date(selectedMarker.latestUpdate).toDateString() },
    { label: "Battery :", value: `${selectedMarker.attributes?.batteryLevel}%` || "N/A" },
  ]

  const statusColor =
    selectedMarker.status === "inactive"
      ? "grey"
      : selectedMarker.status === "connected"
        ? "green"
        : "red"

  return (
    <Animated.View entering={FadeIn.duration(1000)} exiting={FadeOutDown} style={themed($popup)}>
      <View style={$styles.justifyBetweenCenter}>
        <View style={[$styles.itemsCenter, { gap: spacing.sm }]}>
          <Text text={selectedMarker.thingName} weight="medium" size="md" />
          <View
            style={{
              width: spacing.md,
              height: spacing.md,
              borderRadius: spacing.md / 2,
              backgroundColor: statusColor,
            }}
          />
        </View>
        <View style={[$styles.itemsCenter, { gap: spacing.sm }]}>
          <Icon
            icon="navigate"
            size={15}
            color={colors.tint}
            onPress={() =>
              navigation.navigate("DeviceDetails", {
                thingName: selectedMarker.thingName,
                productName: selectedMarker.productName,
              })
            }
          />
          <Icon icon="x" size={16} onPress={onClose} />
        </View>
      </View>
      <View style={{ marginTop: spacing.xxs }}>
        {options.map((option) => (
          <View style={$styles.row} key={option.label}>
            <Text
              text={option.label}
              style={{ minWidth: 5 * spacing.lg, color: colors.textDim }}
              size="xs"
              weight="medium"
            />
            <Text text={option.value} size="xs" weight="medium" />
          </View>
        ))}
      </View>
    </Animated.View>
  )
}

const $root: ViewStyle = {
  flex: 1,
  position: "relative",
}

const $popup: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  bottom: 132,
  left: spacing.md,
  right: spacing.md,
  backgroundColor: colors.card,
  padding: spacing.xs,
  borderRadius: spacing.xs,
})

const $buttonContainer: ViewStyle = {
  position: "absolute",
  top: 120,
  right: 10,
  alignItems: "center",
}
const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
  borderRadius: 5,
  height: 30,
  width: 30,
  alignItems: "center",
  justifyContent: "center",
  marginVertical: 5,
  elevation: 3,
})
