import userSlice from "@frontend/shared/store/userSlice"
import { configureStore } from "@reduxjs/toolkit"
import { useDispatch, useSelector } from "react-redux"

const store = configureStore({
  reducer: {
    user: userSlice,
  },
})

export const useAppDispatch: () => typeof store.dispatch = useDispatch

export const useAppSelector: import("react-redux").TypedUseSelectorHook<
  ReturnType<typeof store.getState>
> = useSelector

export default store
