{"compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "jsx": "react-native", "module": "es2015", "moduleResolution": "node", "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "sourceMap": true, "target": "esnext", "lib": ["esnext", "dom"], "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./app/*"], "assets/*": ["./assets/*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "extends": "expo/tsconfig.base", "ts-node": {"compilerOptions": {"module": "commonjs"}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "test/**/*"]}