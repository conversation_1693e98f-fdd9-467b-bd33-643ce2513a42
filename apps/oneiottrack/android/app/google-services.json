{"project_info": {"project_number": "471959109711", "project_id": "firewires-smart-dev", "storage_bucket": "firewires-smart-dev.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:471959109711:android:c61b9d9a2195cd34046a3a", "android_client_info": {"package_name": "com.firewires"}}, "oauth_client": [{"client_id": "471959109711-ulgfopn10jqsbvej1571jeeund407g9c.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.firewires", "certificate_hash": "7fb171978c3dc77721ffd319453d986aab67d7c1"}}, {"client_id": "471959109711-2bdsv3pe2fpqukepqisjtbkphogurmct.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDt0firtWgwEbQ7iHe6y6wuv1g92jnXkPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "471959109711-2bdsv3pe2fpqukepqisjtbkphogurmct.apps.googleusercontent.com", "client_type": 3}, {"client_id": "471959109711-hiai98r92nbciljc7r896t93j1oef9ja.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.firewires.smart"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:471959109711:android:cf128f22765c3de0046a3a", "android_client_info": {"package_name": "com.oneiot"}}, "oauth_client": [{"client_id": "471959109711-2bdsv3pe2fpqukepqisjtbkphogurmct.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDt0firtWgwEbQ7iHe6y6wuv1g92jnXkPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "471959109711-2bdsv3pe2fpqukepqisjtbkphogurmct.apps.googleusercontent.com", "client_type": 3}, {"client_id": "471959109711-hiai98r92nbciljc7r896t93j1oef9ja.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.firewires.smart"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:471959109711:android:2abf1da8ffcfa1cd046a3a", "android_client_info": {"package_name": "com.oneiottrack"}}, "oauth_client": [{"client_id": "471959109711-2bdsv3pe2fpqukepqisjtbkphogurmct.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDt0firtWgwEbQ7iHe6y6wuv1g92jnXkPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "471959109711-2bdsv3pe2fpqukepqisjtbkphogurmct.apps.googleusercontent.com", "client_type": 3}, {"client_id": "471959109711-hiai98r92nbciljc7r896t93j1oef9ja.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.firewires.smart"}}]}}}], "configuration_version": "1"}