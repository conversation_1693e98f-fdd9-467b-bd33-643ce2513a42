import "@expo/metro-runtime"
import * as SplashScreen from "expo-splash-screen"
import messaging from "@react-native-firebase/messaging"
import { Platform } from "react-native"
import App from "@/app"

SplashScreen.preventAutoHideAsync()

if (Platform.OS === "ios") {
  messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    console.log("Message handled in the background!", remoteMessage)
  })
}

function IgniteApp() {
  return <App />
}

export default IgniteApp
